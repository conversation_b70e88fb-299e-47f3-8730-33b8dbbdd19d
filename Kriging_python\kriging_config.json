{"input_dir": "D:/pythondata/spatial_interpolation/input_another/2009-1", "terrain_dir": "D:/pythondata/spatial_interpolation/terrain/90", "output_dir": "D:/pythondata/spatial_interpolation/output/Kriging/2009-1", "stations_file": "D:/pythondata/spatial_interpolation/stations.csv", "variogram_model": "spherical", "neighbor_count": 3, "min_triangle_angle": 20.0, "kriging_type": "ordinary", "enable_parameter_optimization": true, "optimization_iterations": 5, "enable_moran_weighting": true, "moran_weight": 0.3, "distance_weight": 0.7, "enable_terrain_enhancement": false, "elevation_weight": 0.2, "slope_weight": 0.1, "aspect_weight": 0.1, "rainfall_threshold": 0.5, "num_cores": 24, "batch_size": 50, "memory_efficient": true, "output_raster": true, "output_delaunay_plot": true, "output_variogram_plot": true, "output_weight_info": true, "output_evaluation": true, "enable_batch_processing": true, "batch_input_root": "D:/pythondata/spatial_interpolation/input_another", "batch_output_root": "D:/pythondata/spatial_interpolation/output/Kriging", "batch_folders": null, "nse_threshold": 0.75, "max_optimization_attempts": 3, "debug_mode": false, "verbose_logging": true}
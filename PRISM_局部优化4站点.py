import os
import time
import multiprocessing
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay
import rasterio
from scipy.interpolate import LinearNDInterpolator
import re
import datetime
import traceback
import logging
from sklearn.neighbors import KDTree

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("prism_interpolation.log"),
        logging.StreamHandler()
    ]
)

# 设置中文字体
try:
    # 添加中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong']
    plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题
    
    # 检查字体是否可用
    if not any(f.name == 'SimHei' for f in plt.font_manager.fontManager.ttflist):
        logging.warning("系统中找不到 SimHei 字体，中文显示可能不正确")
    else:
        logging.info("中文字体设置成功")
except:
    logging.warning("设置中文字体时出错，图表中的中文可能无法正确显示")

# 全局变量
INPUT_DIR = r"D:/pythondata/spatial_interpolation/input_another/2015-3"
TERRAIN_DIR = r"D:/pythondata/spatial_interpolation/terrain"
OUTPUT_DIR = r"D:/pythondata/spatial_interpolation/output/PRISM/2015-3"
STATIONS_FILE = r"D:/pythondata/spatial_interpolation/stations.csv"

# 确保输出目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 设置并行计算的核心数
NUM_CORES = 20

# 栅格输出标志
OUTPUT_RASTER = False  # 设置为True以输出栅格文件

# =============================================================================
# 工具函数
# =============================================================================

def safe_filename(filename):
    """创建安全的文件名，移除不合法的字符"""
    # 替换冒号和空格
    safe_name = re.sub(r'[\\/*?:"<>|]', "_", filename)
    safe_name = safe_name.replace(" ", "_").replace(":", "-")
    return safe_name

def calculate_distances(point1, point2):
    """计算两点之间的欧几里得距离的安全方法"""
    try:
        # 确保点是数组形式
        point1 = np.array(point1, dtype=float).reshape(-1)
        point2 = np.array(point2, dtype=float).reshape(-1)
        
        # 计算差异向量
        diff = point1 - point2
        
        # 计算欧几里得距离
        return np.sqrt(np.sum(diff * diff))
    except Exception as e:
        logging.error(f"距离计算失败: {e}")
        return 1.0  # 返回默认距离

def create_raster(stations_with_terrain, interpolated_values, dem_meta, dem_transform, output_file):
    """创建栅格文件"""
    try:
        # 提取站点坐标和值
        coords = stations_with_terrain[['经度', '纬度']].values
        values = interpolated_values
        
        # 创建插值器
        interpolator = LinearNDInterpolator(coords, values, fill_value=0)
        
        # 创建栅格坐标网格
        height, width = dem_meta['height'], dem_meta['width']
        rows, cols = np.mgrid[0:height, 0:width]
        
        # 转换为地理坐标
        xs, ys = rasterio.transform.xy(dem_transform, rows, cols)
        xs = np.array(xs)
        ys = np.array(ys)
        
        # 插值到栅格
        grid_points = np.vstack((xs.flatten(), ys.flatten())).T
        grid_values = interpolator(grid_points)
        grid_values = grid_values.reshape(height, width)
        
        # 确保没有负值
        grid_values[grid_values < 0] = 0
        
        # 创建栅格文件
        new_meta = dem_meta.copy()
        with rasterio.open(output_file, 'w', **new_meta) as dst:
            dst.write(grid_values.astype(rasterio.float32), 1)
        
        logging.info(f"栅格文件已保存至 {output_file}")
    except Exception as e:
        logging.error(f"创建栅格文件失败: {e}")
        traceback.print_exc()
        
# =============================================================================
# 优化的Delaunay三角网构建
# =============================================================================

def create_constrained_delaunay(stations, min_angle=20):
    """
    创建具有质量约束的Delaunay三角网，避免过度细长的三角形
    
    参数:
    stations: 包含站点经纬度的DataFrame
    min_angle: 三角形最小角度约束(度)
    
    返回:
    dict: 包含Delaunay三角网对象和额外信息的字典
    """
    try:
        # 提取站点坐标
        points = stations[['经度', '纬度']].values
        
        # 创建标准Delaunay三角网
        tri = Delaunay(points)
        
        # 计算三角形的质量并过滤低质量的三角形
        simplices = tri.simplices.copy()
        quality_triangles = []
        low_quality_triangles = []
        
        for i, simplex in enumerate(simplices):
            # 获取三角形的顶点
            p1 = points[simplex[0]]
            p2 = points[simplex[1]]
            p3 = points[simplex[2]]
            
            # 计算三角形的边长
            a = np.linalg.norm(p2 - p3)
            b = np.linalg.norm(p1 - p3)
            c = np.linalg.norm(p1 - p2)
            
            # 计算三角形的面积
            s = (a + b + c) / 2
            area = np.sqrt(s * (s - a) * (s - b) * (s - c))
            
            # 计算三角形的质量（角度等）
            cos_A = (b**2 + c**2 - a**2) / (2 * b * c)
            cos_B = (a**2 + c**2 - b**2) / (2 * a * c)
            cos_C = (a**2 + b**2 - c**2) / (2 * a * b)
            
            # 将余弦值转换为角度（度）
            A = np.rad2deg(np.arccos(np.clip(cos_A, -1.0, 1.0)))
            B = np.rad2deg(np.arccos(np.clip(cos_B, -1.0, 1.0)))
            C = np.rad2deg(np.arccos(np.clip(cos_C, -1.0, 1.0)))
            
            # 检查最小角度
            min_triangle_angle = min(A, B, C)
            
            if min_triangle_angle >= min_angle:
                quality_triangles.append(simplex)
            else:
                low_quality_triangles.append(simplex)
        
        # 记录高质量三角形与低质量三角形
        logging.info(f"创建了Delaunay三角网: 总三角形数 {len(simplices)}, 高质量三角形 {len(quality_triangles)}, 低质量三角形 {len(low_quality_triangles)}")
        
        # 创建KDTree用于空间查询
        kdtree = KDTree(points)
        
        # 创建三角形质量评分字典
        triangle_quality = {}
        for i, simplex in enumerate(simplices):
            p1 = points[simplex[0]]
            p2 = points[simplex[1]]
            p3 = points[simplex[2]]
            
            # 计算三角形的质心
            centroid = (p1 + p2 + p3) / 3
            
            # 计算三角形的平均边长
            avg_side = (np.linalg.norm(p2 - p3) + np.linalg.norm(p1 - p3) + np.linalg.norm(p1 - p2)) / 3
            
            # 存储三角形质量信息
            triangle_quality[i] = {
                'simplex': simplex,
                'centroid': centroid,
                'avg_side': avg_side
            }
        
        # 返回三角网和额外信息
        return {
            'delaunay': tri,
            'quality_triangles': quality_triangles,
            'low_quality_triangles': low_quality_triangles,
            'kdtree': kdtree,
            'triangle_quality': triangle_quality
        }
    
    except Exception as e:
        logging.error(f"创建优化Delaunay三角网时出错: {e}")
        traceback.print_exc()
        # 退回到基本的Delaunay三角网
        tri = Delaunay(points)
        return {'delaunay': tri}

def visualize_delaunay_with_quality(stations, tri_info, output_dir):
    """可视化Delaunay三角网，区分高质量和低质量三角形"""
    try:
        plt.figure(figsize=(14, 10))
        
        # 提取站点坐标
        points = stations[['经度', '纬度']].values
        
        # 绘制所有三角形
        tri = tri_info['delaunay']
        
        # 如果存在质量分类，则按质量绘制
        if 'quality_triangles' in tri_info and 'low_quality_triangles' in tri_info:
            # 绘制高质量三角形
            for simplex in tri_info['quality_triangles']:
                plt.triplot(points[simplex, 0], points[simplex, 1], 'g-', alpha=0.7)
            
            # 绘制低质量三角形
            for simplex in tri_info['low_quality_triangles']:
                plt.triplot(points[simplex, 0], points[simplex, 1], 'r--', alpha=0.5)
                
            # 添加图例
            plt.plot([], [], 'g-', label='高质量三角形')
            plt.plot([], [], 'r--', label='低质量三角形')
        else:
            # 如果没有质量分类，绘制所有三角形
            for simplex in tri.simplices:
                plt.triplot(points[simplex, 0], points[simplex, 1], 'b-', alpha=0.7)
        
        # 绘制站点
        plt.plot(points[:, 0], points[:, 1], 'ko', markersize=5)
        
        # 标注站点
        for i, station in stations.iterrows():
            plt.text(station['经度'], station['纬度'], station['站点'], 
                    fontsize=8, ha='right', va='bottom')
        
        plt.title('优化后的Delaunay三角网')
        plt.xlabel('经度')
        plt.ylabel('纬度')
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # 绘制图例
        plt.plot([], [], 'ko', label='雨量站')
        plt.legend()
        
        # 保存图像
        output_path = os.path.join(output_dir, 'optimized_delaunay_triangulation.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logging.info(f"优化的Delaunay三角网可视化已保存至 {output_path}")
    except Exception as e:
        logging.error(f"可视化三角网时出错: {e}")
        traceback.print_exc()

def find_optimal_neighbors(station_idx, stations, tri_info, rainfall_data=None, time_point=None, neighbor_count=3):
    """
    寻找最优的邻近站点组合
    
    参数:
    station_idx: 目标站点索引
    stations: 包含站点经纬度和地形信息的DataFrame
    tri_info: 三角网信息字典
    rainfall_data: 包含降雨数据的DataFrame
    time_point: 当前时间点
    neighbor_count: 需要的邻站数量
    
    返回:
    list: 最优邻近站点索引列表
    """
    try:
        # 提取信息
        tri = tri_info['delaunay']
        points = stations[['经度', '纬度']].values
        test_point = points[station_idx]
        
        # 策略1: 使用Delaunay三角网的邻站
        neighbor_indices = set()
        for simplex in tri.simplices:
            if station_idx in simplex:
                for idx in simplex:
                    if idx != station_idx:
                        neighbor_indices.add(idx)
        
        # 如果三角网邻站不足，使用KDTree找最近的站点
        if len(neighbor_indices) < neighbor_count:
            if 'kdtree' in tri_info:
                kdtree = tri_info['kdtree']
                _, indices = kdtree.query([test_point], k=neighbor_count+1)
                # 移除测试站点本身
                for idx in indices[0]:
                    if idx != station_idx:
                        neighbor_indices.add(idx)
            else:
                # 如果没有KDTree，手动计算距离
                distances = []
                for i, point in enumerate(points):
                    if i != station_idx:
                        distances.append((i, np.linalg.norm(point - test_point)))
                nearest_indices = [idx for idx, _ in sorted(distances, key=lambda x: x[1])[:neighbor_count]]
                neighbor_indices.update(nearest_indices)
        
        # 如果有降雨数据，使用降雨相关性进行二次筛选
        if rainfall_data is not None and time_point is not None:
            # 如果存在足够的邻站，可以基于降雨相关性进行筛选
            if len(neighbor_indices) > neighbor_count:
                # 获取当前时间段的降雨数据
                time_col = rainfall_data.columns[0]
                
                # 找到当前时间点的索引
                time_idx = None
                for i, t in enumerate(rainfall_data[time_col]):
                    if t == time_point:
                        time_idx = i
                        break
                
                if time_idx is not None:
                    # 获取之前最多20个时刻的数据进行相关性分析
                    start_idx = max(0, time_idx - 20)
                    recent_data = rainfall_data.iloc[start_idx:time_idx+1]
                    
                    # 计算目标站点与潜在邻站的降雨相关性
                    station_name = stations.iloc[station_idx]['站点']
                    correlations = {}
                    
                    for idx in neighbor_indices:
                        neighbor_name = stations.iloc[idx]['站点']
                        # 检查站点是否在降雨数据中
                        if station_name in recent_data.columns and neighbor_name in recent_data.columns:
                            # 计算皮尔逊相关系数
                            station_data = recent_data[station_name].values
                            neighbor_data = recent_data[neighbor_name].values
                            
                            # 如果数据都为零，视为高相关
                            if np.sum(station_data) == 0 and np.sum(neighbor_data) == 0:
                                correlations[idx] = 1.0
                            else:
                                # 计算相关系数
                                try:
                                    corr = np.corrcoef(station_data, neighbor_data)[0, 1]
                                    correlations[idx] = corr if not np.isnan(corr) else 0
                                except:
                                    correlations[idx] = 0
                    
                    # 根据相关性和距离的综合评分排序
                    if correlations:
                        # 计算距离因子
                        distances = {}
                        for idx in neighbor_indices:
                            distances[idx] = np.linalg.norm(points[idx] - test_point)
                        
                        # 综合评分: 0.7*相关性 + 0.3*距离因子(归一化)
                        scores = {}
                        max_dist = max(distances.values()) if distances else 1
                        for idx in neighbor_indices:
                            # 如果没有相关性数据，使用-1作为低相关性的标志
                            corr = correlations.get(idx, -1)
                            norm_dist = 1 - (distances[idx] / max_dist) if max_dist > 0 else 0
                            scores[idx] = 0.7 * (corr + 1) / 2 + 0.3 * norm_dist  # 将相关性从[-1, 1]映射到[0, 1]
                        
                        # 根据评分选择最佳邻站
                        best_neighbors = sorted(scores.keys(), key=lambda x: scores[x], reverse=True)[:neighbor_count]
                        return best_neighbors
        
        # 如果没有降雨数据或相关性分析失败，选择最近的邻站
        if len(neighbor_indices) > neighbor_count:
            distances = {}
            for idx in neighbor_indices:
                distances[idx] = np.linalg.norm(points[idx] - test_point)
            
            best_neighbors = sorted(distances.keys(), key=lambda x: distances[x])[:neighbor_count]
            return best_neighbors
        else:
            return list(neighbor_indices)[:neighbor_count]
    
    except Exception as e:
        logging.error(f"寻找最优邻站时出错: {e}")
        traceback.print_exc()
        # 简单返回最近的几个站点
        points = stations[['经度', '纬度']].values
        test_point = points[station_idx]
        distances = []
        for i, point in enumerate(points):
            if i != station_idx:
                distances.append((i, np.linalg.norm(point - test_point)))
        
        nearest_indices = [idx for idx, _ in sorted(distances, key=lambda x: x[1])[:neighbor_count]]
        return nearest_indices

# =============================================================================
# 改进的权重计算方法
# =============================================================================

def calculate_improved_idw_weights(test_point, neighbor_points, p=2):
    """改进的IDW权重计算，提高了健壮性"""
    try:
        # 处理特殊情况
        n_neighbors = len(neighbor_points) if hasattr(neighbor_points, '__len__') else 1
        if n_neighbors == 0:
            return np.array([])
        
        # 确保我们有可用的邻居
        if n_neighbors == 1:
            return np.array([1.0])
        
        # 计算每个邻居到测试点的距离
        distances = []
        for i in range(n_neighbors):
            if hasattr(neighbor_points, '__getitem__'):
                neighbor = neighbor_points[i]
            else:
                neighbor = neighbor_points
            distance = calculate_distances(test_point, neighbor)
            distances.append(distance)
        
        # 转换为数组
        distances = np.array(distances)
        
        # 处理距离为0的情况
        if np.any(distances == 0):
            weights = np.zeros_like(distances)
            weights[distances == 0] = 1.0
            return weights / np.sum(weights)
        
        # 计算IDW权重
        weights = 1.0 / np.power(distances, p)
        
        # 归一化权重
        weights_sum = np.sum(weights)
        if weights_sum > 0:
            weights = weights / weights_sum
        else:
            # 如果权重和为0，使用均等权重
            weights = np.ones_like(distances) / len(distances)
        
        return weights
    except Exception as e:
        logging.error(f"计算IDW权重失败: {e}")
        traceback.print_exc()
        # 返回均等权重
        try:
            if hasattr(neighbor_points, '__len__'):
                return np.ones(len(neighbor_points)) / len(neighbor_points)
            else:
                return np.array([1.0])
        except:
            return np.array([1.0])

def calculate_improved_prism_weights(test_point, neighbor_points, test_elev, neighbor_elevs,
                                    test_slope, neighbor_slopes, test_aspect, neighbor_aspects,
                                    test_moran=None, neighbor_morans=None,
                                    rainfall_values=None, rainfall_threshold=0.5):
    """
    改进的PRISM权重计算，针对有降雨情况进行专门优化
    
    参数:
    test_point, neighbor_points: 测试点和邻居点坐标
    test_elev, neighbor_elevs: 测试点和邻居点高程
    test_slope, neighbor_slopes: 测试点和邻居点坡度
    test_aspect, neighbor_aspects: 测试点和邻居点坡向
    test_moran, neighbor_morans: 测试点和邻居点莫兰指数
    rainfall_values: 邻居站点的降雨值
    rainfall_threshold: 降雨阈值，区分微量降雨和明显降雨
    
    返回:
    array: 改进的PRISM权重
    """
    try:
        # 处理特殊情况
        n_neighbors = len(neighbor_points) if hasattr(neighbor_points, '__len__') else 1
        if n_neighbors == 0:
            return np.array([])
        
        # 确保我们有可用的邻居
        if n_neighbors == 1:
            return np.array([1.0])
        
        # 判断是否为有明显降雨的情况
        has_rainfall = False
        if rainfall_values is not None and np.max(rainfall_values) > rainfall_threshold:
            has_rainfall = True
        
        # 计算距离权重
        distances = []
        for i in range(n_neighbors):
            if hasattr(neighbor_points, '__getitem__'):
                neighbor = neighbor_points[i]
            else:
                neighbor = neighbor_points
            
            distance = calculate_distances(test_point, neighbor)
            distances.append(distance)
        
        distances = np.array(distances)
        
        # 处理距离为0的情况
        if np.any(distances == 0):
            weights = np.zeros_like(distances)
            weights[distances == 0] = 1.0
            return weights / np.sum(weights)
        
        # 计算基础权重因子
        distance_weights = 1.0 / np.power(distances, 2)
        
        # 高程权重
        neighbor_elevs = np.array(neighbor_elevs) if hasattr(neighbor_elevs, '__len__') else np.array([neighbor_elevs])
        elev_diffs = np.abs(neighbor_elevs - test_elev)
        max_elev_diff = np.max(elev_diffs) if np.max(elev_diffs) > 0 else 1
        elev_weights = 1.0 - (elev_diffs / max_elev_diff)
        
        # 坡度权重
        neighbor_slopes = np.array(neighbor_slopes) if hasattr(neighbor_slopes, '__len__') else np.array([neighbor_slopes])
        slope_diffs = np.abs(neighbor_slopes - test_slope)
        max_slope_diff = np.max(slope_diffs) if np.max(slope_diffs) > 0 else 1
        slope_weights = 1.0 - (slope_diffs / max_slope_diff)
        
        # 坡向权重 - 使用余弦相似度
        neighbor_aspects = np.array(neighbor_aspects) if hasattr(neighbor_aspects, '__len__') else np.array([neighbor_aspects])
        # 将角度转换为弧度
        test_aspect_rad = np.deg2rad(test_aspect)
        neighbor_aspects_rad = np.deg2rad(neighbor_aspects)
        
        # 计算余弦相似度 (1表示完全相同, -1表示完全相反)
        aspect_similarity = np.cos(neighbor_aspects_rad - test_aspect_rad)
        # 将相似度映射到[0,1]区间
        aspect_weights = (aspect_similarity + 1) / 2
        
        # 莫兰指数权重（如果提供）
        if test_moran is not None and neighbor_morans is not None:
            neighbor_morans = np.array(neighbor_morans) if hasattr(neighbor_morans, '__len__') else np.array([neighbor_morans])
            # 计算莫兰指数的相关性
            moran_diffs = np.abs(neighbor_morans - test_moran)
            max_moran_diff = np.max(moran_diffs) if np.max(moran_diffs) > 0 else 1
            moran_weights = 1.0 - (moran_diffs / max_moran_diff)
        else:
            moran_weights = np.ones_like(distance_weights)
        
        # 根据是否有降雨调整权重比例
        if has_rainfall:
            # 有明显降雨时，更注重莫兰指数和距离因子
            combined_weights = (
                distance_weights * 0.35 +  # 增加距离权重
                elev_weights * 0.25 +      # 减少高程权重
                slope_weights * 0.15 +     # 减少坡度权重
                aspect_weights * 0.05 +    # 减少坡向权重
                moran_weights * 0.20       # 增加莫兰指数权重
            )
            
            # 如果有降雨数据，进一步调整权重
            if rainfall_values is not None:
                rainfall_values = np.array(rainfall_values)
                non_zero_rainfall = rainfall_values > 0
                
                # 如果有站点有降雨，增加有降雨站点的权重
                if np.any(non_zero_rainfall):
                    # 归一化降雨值作为权重增强因子
                    rainfall_factor = rainfall_values / (np.max(rainfall_values) or 1.0)
                    # 应用降雨因子，增强有降雨站点的权重
                    combined_weights = combined_weights * (1.0 + rainfall_factor)
        else:
            # 无明显降雨或微量降雨时，使用标准PRISM权重
            combined_weights = (
                distance_weights * 0.3 +
                elev_weights * 0.3 +
                slope_weights * 0.2 +
                aspect_weights * 0.1 +
                moran_weights * 0.1
            )
        
        # 归一化权重
        weights_sum = np.sum(combined_weights)
        if weights_sum > 0:
            final_weights = combined_weights / weights_sum
        else:
            # 如果权重和为0，使用距离权重
            final_weights = distance_weights / np.sum(distance_weights)
        
        return final_weights
    
    except Exception as e:
        logging.error(f"计算改进PRISM权重时出错: {e}")
        traceback.print_exc()
        # 返回简单的距离权重
        try:
            distances = np.array(distances)
            inv_dist = 1.0 / np.power(distances, 2)
            return inv_dist / np.sum(inv_dist)
        except:
            # 最后的备选方案是均等权重
            return np.ones(n_neighbors) / n_neighbors

# =============================================================================
# 数据加载与预处理
# =============================================================================

def load_stations(stations_file):
    """加载站点信息"""
    logging.info("正在加载站点信息...")
    try:
        # 尝试不同编码加载文件
        encodings = ['utf_8', 'gbk', 'gb2312', 'utf_8_sig']
        for encoding in encodings:
            try:
                stations = pd.read_csv(stations_file, encoding=encoding)
                logging.info(f"成功加载站点信息，共 {len(stations)} 个站点")
                return stations
            except UnicodeDecodeError:
                continue
        
        # 如果所有编码都失败，抛出错误
        raise ValueError("无法识别文件编码，请手动指定正确的编码")
    except Exception as e:
        logging.error(f"加载站点信息失败: {e}")
        raise

def load_terrain_data(terrain_dir):
    """加载地形数据(高程、坡度、坡向)"""
    logging.info("正在加载地形数据...")
    try:
        # 加载DEM数据
        dem_file = os.path.join(terrain_dir, "dem.asc")
        with rasterio.open(dem_file) as src:
            dem_data = src.read(1)
            dem_meta = src.meta
            dem_transform = src.transform
        
        # 加载坡度数据
        slope_file = os.path.join(terrain_dir, "slope.asc")  # 注意这里是原文中的拼写
        with rasterio.open(slope_file) as src:
            slope_data = src.read(1)
            slope_meta = src.meta
        
        # 加载坡向数据
        aspect_file = os.path.join(terrain_dir, "aspect.asc")
        with rasterio.open(aspect_file) as src:
            aspect_data = src.read(1)
            aspect_meta = src.meta
        
        logging.info("地形数据加载成功")
        return (dem_data, dem_meta, dem_transform), (slope_data, slope_meta), (aspect_data, aspect_meta)
    except Exception as e:
        logging.error(f"加载地形数据失败: {e}")
        raise

def extract_terrain_features(stations, dem_data, dem_transform, slope_data, aspect_data):
    """为每个站点提取地形特征(高程、坡度、坡向)"""
    logging.info("正在为站点提取地形特征...")
    try:
        # 创建站点数据的副本，确保经纬度列是数值型
        stations_with_terrain = stations.copy()
        stations_with_terrain['经度'] = pd.to_numeric(stations_with_terrain['经度'], errors='coerce')
        stations_with_terrain['纬度'] = pd.to_numeric(stations_with_terrain['纬度'], errors='coerce')
        
        # 提取每个站点的地形特征
        elevations = []
        slopes = []
        aspects = []
        
        for _, station in stations_with_terrain.iterrows():
            try:
                # 获取站点坐标
                lon, lat = station['经度'], station['纬度']
                
                # 将地理坐标转换为栅格坐标
                row, col = ~dem_transform * (lon, lat)
                row, col = int(row), int(col)
                
                # 确保坐标在有效范围内
                if (0 <= row < dem_data.shape[1] and 0 <= col < dem_data.shape[0]):
                    # 提取地形特征
                    elevation = dem_data[col, row]
                    slope = slope_data[col, row]
                    aspect = aspect_data[col, row]
                else:
                    # 如果站点在栅格外，使用插值或默认值
                    elevation = np.nanmean(dem_data)
                    slope = np.nanmean(slope_data)
                    aspect = np.nanmean(aspect_data)
            except Exception as e:
                logging.warning(f"处理站点 {station['站点']} 地形特征时出错: {e}")
                elevation = np.nanmean(dem_data)
                slope = np.nanmean(slope_data)
                aspect = np.nanmean(aspect_data)
            
            elevations.append(elevation)
            slopes.append(slope)
            aspects.append(aspect)
        
        # 添加地形特征到站点数据
        stations_with_terrain['高程'] = elevations
        stations_with_terrain['坡度'] = slopes
        stations_with_terrain['坡向'] = aspects
        
        # 确保数据都是数值型
        for col in ['高程', '坡度', '坡向']:
            stations_with_terrain[col] = pd.to_numeric(stations_with_terrain[col], errors='coerce')
            # 对缺失值使用平均值填充
            if stations_with_terrain[col].isna().any():
                mean_val = stations_with_terrain[col].mean()
                stations_with_terrain[col] = stations_with_terrain[col].fillna(mean_val)
        
        logging.info(f"地形特征提取完成，有效站点数: {len(stations_with_terrain)}")
        return stations_with_terrain
    except Exception as e:
        logging.error(f"提取地形特征失败: {e}")
        traceback.print_exc()
        raise

def load_rainfall_data(input_dir, stations):
    """加载所有站点的降雨数据"""
    logging.info("正在加载站点降雨数据...")
    try:
        rainfall_data = {}
        for station_name in stations['站点']:
            file_path = os.path.join(input_dir, f"{station_name}.csv")
            if os.path.exists(file_path):
                # 尝试不同编码加载文件
                encodings = ['utf_8', 'gbk', 'gb2312', 'utf_8_sig']
                for encoding in encodings:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        rainfall_data[station_name] = df
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        logging.warning(f"加载站点 {station_name} 数据时出错: {e}")
                        break
            else:
                logging.warning(f"警告: 站点 {station_name} 的数据文件不存在")
        
        logging.info(f"成功加载 {len(rainfall_data)} 个站点的降雨数据")
        return rainfall_data
    except Exception as e:
        logging.error(f"加载降雨数据失败: {e}")
        raise

def align_rainfall_data(rainfall_data):
    """对齐所有站点的降雨数据，确保使用相同的时间索引"""
    logging.info("正在对齐站点降雨数据...")
    try:
        # 提取所有站点数据的时间列
        all_dfs = list(rainfall_data.values())
        if not all_dfs:
            raise ValueError("没有可用的降雨数据")
        
        # 检查数据结构，识别时间列
        first_df = all_dfs[0]
        time_col = None
        for col in first_df.columns:
            if '时间' in col or 'time' in col.lower() or 'date' in col.lower():
                time_col = col
                break
        
        if not time_col:
            # 如果找不到明确的时间列，假设第一列是时间
            time_col = first_df.columns[0]
        
        # 创建一个新的DataFrame用于存储对齐后的数据
        aligned_data = pd.DataFrame()
        
        # 收集所有站点的时间
        all_times = set()
        for df in all_dfs:
            if time_col in df.columns:
                all_times.update(df[time_col].astype(str))
        
        # 转换为有序列表
        all_times = sorted(list(all_times))
        aligned_data[time_col] = all_times
        
        # 添加每个站点的降雨数据
        for station, df in rainfall_data.items():
            if time_col in df.columns:
                # 找到降雨数据列
                rain_cols = [col for col in df.columns if col != time_col]
                if rain_cols:
                    rain_col = rain_cols[0]  # 使用第一个非时间列作为降雨数据
                    # 创建时间到降雨的映射
                    time_to_rain = dict(zip(df[time_col].astype(str), df[rain_col]))
                    # 填充对齐数据
                    aligned_data[station] = aligned_data[time_col].map(time_to_rain).fillna(0)
        
        # 确保所有数据都是数值型
        for col in aligned_data.columns:
            if col != time_col:
                aligned_data[col] = pd.to_numeric(aligned_data[col], errors='coerce').fillna(0)
        
        logging.info(f"数据对齐完成，共 {len(aligned_data)} 个时间点，{len(rainfall_data)} 个站点")
        return aligned_data
    except Exception as e:
        logging.error(f"对齐降雨数据失败: {e}")
        raise

def calculate_moran_i(stations, rainfall_data, time_point):
    """计算局部莫兰指数"""
    try:
        # 创建包含降雨数据的站点副本
        stations_with_rain = stations.copy()
        
        # 提取当前时间点的降雨数据
        time_col = rainfall_data.columns[0]
        if time_point in rainfall_data[time_col].values:
            rain_row = rainfall_data[rainfall_data[time_col] == time_point].iloc[0]
            
            # 为每个站点添加降雨数据
            for station in stations['站点']:
                if station in rain_row.index:
                    stations_with_rain.loc[stations_with_rain['站点'] == station, '降雨量'] = rain_row[station]
        else:
            # 如果找不到时间点，填充零值
            stations_with_rain['降雨量'] = 0
        
        # 确保降雨量是数值型
        stations_with_rain['降雨量'] = pd.to_numeric(stations_with_rain['降雨量'], errors='coerce').fillna(0)
        
        # 检查是否有足够的非零降雨数据计算莫兰指数
        if stations_with_rain['降雨量'].nunique() <= 1:
            stations_with_rain['莫兰指数'] = 0
            return stations_with_rain
        
        # 创建距离权重矩阵
        coords = stations_with_rain[['经度', '纬度']].values
        
        try:
            # 尝试使用PySAL库
            from libpysal.weights import DistanceBand
            from esda.moran import Moran_Local
            
            # 创建权重矩阵
            threshold = 1.0  # 阈值，单位与坐标一致
            w = DistanceBand(coords, threshold=threshold, binary=False)
            
            # 如果权重矩阵为空，使用knn
            if w.n == 0:
                from libpysal.weights import KNN
                w = KNN(coords, k=3)
            
            # 计算局部莫兰指数
            y = stations_with_rain['降雨量'].values
            lm = Moran_Local(y, w)
            stations_with_rain['莫兰指数'] = lm.Is
            
        except ImportError:
            logging.warning("无法导入PySAL库，使用简化版莫兰指数计算")
            # 如果没有PySAL，自己实现简单版本
            # 创建距离矩阵
            n = len(coords)
            dist_matrix = np.zeros((n, n))
            for i in range(n):
                for j in range(n):
                    if i != j:
                        dist_matrix[i, j] = np.sqrt(np.sum((coords[i] - coords[j])**2))
            
            # 基于距离创建权重矩阵
            # 使用逆距离权重
            weights = np.zeros((n, n))
            for i in range(n):
                for j in range(n):
                    if i != j and dist_matrix[i, j] > 0:
                        weights[i, j] = 1.0 / dist_matrix[i, j]
            
            # 行标准化
            weights_sum = weights.sum(axis=1)
            weights_sum[weights_sum == 0] = 1  # 避免除以零
            weights = weights / weights_sum[:, np.newaxis]
            
            # 计算莫兰指数
            y = stations_with_rain['降雨量'].values
            y_mean = np.mean(y)
            y_std = np.std(y)
            
            if y_std > 0:
                z = (y - y_mean) / y_std
                local_moran = np.zeros(n)
                
                for i in range(n):
                    local_moran[i] = z[i] * np.sum(weights[i] * z)
                
                stations_with_rain['莫兰指数'] = local_moran
            else:
                stations_with_rain['莫兰指数'] = 0
        
        return stations_with_rain
    except Exception as e:
        logging.error(f"计算莫兰指数失败: {e}")
        # 返回没有莫兰指数的数据
        stations['莫兰指数'] = 0
        return stations

# =============================================================================
# 增强的PRISM插值方法
# =============================================================================

def interpolate_with_improved_prism(test_idx, stations_with_terrain, rainfall_data, time_point, tri_info):
    """
    使用改进的PRISM方法进行插值
    
    参数:
    test_idx: 测试站点索引
    stations_with_terrain: 带地形特征的站点DataFrame
    rainfall_data: 降雨数据DataFrame
    time_point: 当前时间点
    tri_info: 三角网信息字典
    
    返回:
    tuple: (真实值, 插值结果, 使用的方法, 权重信息)
    """
    try:
        # 获取测试站点信息
        test_station = stations_with_terrain.iloc[test_idx]
        test_station_name = test_station['站点']
        
        # 获取测试点的真实降雨值
        time_col = rainfall_data.columns[0]
        rain_row = rainfall_data[rainfall_data[time_col] == time_point].iloc[0]
        true_value = rain_row[test_station_name] if test_station_name in rain_row else 0
        
        # 检查是否为全零降雨
        station_cols = [col for col in rain_row.index if col != time_col]
        all_zero = all(rain_row[col] == 0 for col in station_cols)
        
        if all_zero:
            # 全零降雨直接返回0
            return true_value, 0, "零降雨", {"站点": [], "权重": []}
        
        # 找出最优邻站组合
        neighbor_indices = find_optimal_neighbors(
            test_idx, 
            stations_with_terrain, 
            tri_info, 
            rainfall_data, 
            time_point, 
            neighbor_count=3
        )
        
        # 确保至少有3个邻站
        while len(neighbor_indices) < 3:
            # 使用KDTree找最近的站点
            points = stations_with_terrain[['经度', '纬度']].values
            test_point = points[test_idx]
            
            distances = []
            for i, point in enumerate(points):
                if i != test_idx and i not in neighbor_indices:
                    distances.append((i, np.linalg.norm(point - test_point)))
            
            if not distances:
                break  # 没有更多站点可选
                
            nearest_idx = min(distances, key=lambda x: x[1])[0]
            neighbor_indices.append(nearest_idx)
        
        # 提取邻居信息
        neighbors = stations_with_terrain.iloc[neighbor_indices]
        neighbor_names = neighbors['站点'].values
        neighbor_values = np.array([rain_row[name] if name in rain_row else 0 for name in neighbor_names])
        
        # 检查是否有非零降雨
        has_rainfall = np.sum(neighbor_values) > 0
        
        # 提取空间坐标和地形信息
        test_point = stations_with_terrain.iloc[test_idx][['经度', '纬度']].values
        neighbor_points = neighbors[['经度', '纬度']].values
        
        test_elev = test_station['高程']
        neighbor_elevs = neighbors['高程'].values
        
        test_slope = test_station['坡度']
        neighbor_slopes = neighbors['坡度'].values
        
        test_aspect = test_station['坡向']
        neighbor_aspects = neighbors['坡向'].values
        
        # 莫兰指数（如果存在）
        if '莫兰指数' in stations_with_terrain.columns:
            test_moran = test_station['莫兰指数']
            neighbor_morans = neighbors['莫兰指数'].values
        else:
            test_moran = None
            neighbor_morans = None
        
        # 使用改进的PRISM权重计算
        weights = calculate_improved_prism_weights(
            test_point, neighbor_points,
            test_elev, neighbor_elevs,
            test_slope, neighbor_slopes,
            test_aspect, neighbor_aspects,
            test_moran, neighbor_morans,
            rainfall_values=neighbor_values
        )
        
        # 计算插值结果
        interpolated = np.sum(neighbor_values * weights)
        
        # 确保不是负值
        interpolated = max(0, interpolated)
        
        # 返回结果
        method = "增强PRISM" if has_rainfall else "常规PRISM"
        
        weight_info = {
            "站点": neighbor_names.tolist(),
            "权重": weights.tolist()
        }
        
        return true_value, interpolated, method, weight_info
    except Exception as e:
        logging.error(f"使用改进PRISM方法插值失败 (测试点:{test_idx}): {e}")
        traceback.print_exc()
        # 返回安全值
        return 0, 0, "失败", {"站点": [], "权重": []}

def interpolate_for_timepoint(time_point, stations_with_terrain, rainfall_data, tri_info):
    """针对单个时间点对所有站点进行留一法插值"""
    try:
        # 首先快速检查，如果该时间点所有站点降雨量都为零，直接返回零值结果
        time_col = rainfall_data.columns[0]
        rain_row = rainfall_data[rainfall_data[time_col] == time_point].iloc[0]
        station_cols = [col for col in rain_row.index if col != time_col]
        
        if all(rain_row[col] == 0 for col in station_cols):
            logging.info(f"时间点 {time_point} 的所有降雨值为零，跳过详细插值")
            n_stations = len(stations_with_terrain)
            return (np.zeros(n_stations), np.zeros(n_stations), 
                   ['零降雨'] * n_stations, stations_with_terrain['站点'].values,
                   [{"站点": [], "权重": []} for _ in range(n_stations)])
        
        # 计算局部莫兰指数
        stations_with_moran = calculate_moran_i(stations_with_terrain, rainfall_data, time_point)
        
        # 初始化结果数组
        n_stations = len(stations_with_moran)
        true_values = np.zeros(n_stations)
        interpolated_values = np.zeros(n_stations)
        methods = [''] * n_stations
        station_names = stations_with_moran['站点'].values
        weight_infos = [{} for _ in range(n_stations)]
        
        # 对每个站点进行留一法插值
        for i in range(n_stations):
            true, interp, method, weight_info = interpolate_with_improved_prism(
                i, stations_with_moran, rainfall_data, time_point, tri_info
            )
            true_values[i] = true
            interpolated_values[i] = interp
            methods[i] = method
            weight_infos[i] = weight_info
        
        return true_values, interpolated_values, methods, station_names, weight_infos
    except Exception as e:
        logging.error(f"时间点 {time_point} 的插值失败: {e}")
        traceback.print_exc()
        # 返回空结果
        n_stations = len(stations_with_terrain)
        return (np.zeros(n_stations), np.zeros(n_stations), 
                ['失败'] * n_stations, stations_with_terrain['站点'].values,
                [{} for _ in range(n_stations)])

# =============================================================================
# 评价与可视化
# =============================================================================

def evaluate_interpolation(true_values, interpolated_values):
    """评估插值结果"""
    try:
        # 如果输入是空数组，返回默认指标
        if len(true_values) == 0 or len(interpolated_values) == 0:
            return {'MAE': 0, 'RMSE': 0, 'NSE': 0, 'CORR': 0}
        
        # 计算MAE
        mae = np.mean(np.abs(true_values - interpolated_values))
        
        # 计算RMSE
        rmse = np.sqrt(np.mean((true_values - interpolated_values) ** 2))
        
        # 计算纳什系数
        # NSE = 1 - Σ(Obs-Sim)²/Σ(Obs-mean(Obs))²
        ss_res = np.sum((true_values - interpolated_values) ** 2)
        ss_tot = np.sum((true_values - np.mean(true_values)) ** 2) if np.mean(true_values) != 0 else 1
        nse = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        # 计算相关系数
        if np.std(true_values) > 0 and np.std(interpolated_values) > 0:
            corr = np.corrcoef(true_values, interpolated_values)[0, 1]
        else:
            corr = 1 if np.array_equal(true_values, interpolated_values) else 0
        
        return {
            'MAE': mae,
            'RMSE': rmse,
            'NSE': nse,
            'CORR': corr
        }
    except Exception as e:
        logging.error(f"评估插值结果失败: {e}")
        return {'MAE': 0, 'RMSE': 0, 'NSE': 0, 'CORR': 0}

def create_interpolated_rainfall_file(stations, rainfall_data, all_results):
    """创建插值后的降雨文件"""
    try:
        # 提取时间列
        time_col = rainfall_data.columns[0]
        time_values = rainfall_data[time_col].values
        
        # 创建每个站点的插值结果DataFrame
        interpolated_data = {}
        station_names = stations['站点'].values
        
        # 对每个站点，创建一个包含所有时间点的DataFrame
        for i, station in enumerate(station_names):
            # 创建该站点的DataFrame
            df = pd.DataFrame({time_col: time_values})
            
            # 添加插值结果
            interpolated_values = []
            
            # 对每个时间点
            for time_point in time_values:
                if time_point in all_results and all_results[time_point] is not None:
                    result = all_results[time_point]
                    
                    # 检查结果是否有效且包含插值值
                    if isinstance(result, dict) and 'interpolated_values' in result:
                        # 确保索引有效
                        if i < len(result['interpolated_values']):
                            interpolated_values.append(result['interpolated_values'][i])
                        else:
                            # 索引超出范围，使用原始值
                            time_idx = rainfall_data[rainfall_data[time_col] == time_point].index
                            if len(time_idx) > 0 and station in rainfall_data.columns:
                                interpolated_values.append(rainfall_data.loc[time_idx[0], station])
                            else:
                                interpolated_values.append(0)
                    else:
                        # 使用原始值
                        time_idx = rainfall_data[rainfall_data[time_col] == time_point].index
                        if len(time_idx) > 0 and station in rainfall_data.columns:
                            interpolated_values.append(rainfall_data.loc[time_idx[0], station])
                        else:
                            interpolated_values.append(0)
                else:
                    # 如果时间点没有结果，使用原始值
                    time_idx = rainfall_data[rainfall_data[time_col] == time_point].index
                    if len(time_idx) > 0 and station in rainfall_data.columns:
                        interpolated_values.append(rainfall_data.loc[time_idx[0], station])
                    else:
                        interpolated_values.append(0)
            
            df[station] = interpolated_values
            interpolated_data[station] = df
        
        return interpolated_data
    except Exception as e:
        logging.error(f"创建插值降雨文件失败: {e}")
        traceback.print_exc()
        return {}

def create_evaluation_file(all_metrics, all_methods, rainfall_data=None):
    """创建评价指标文件"""
    try:
        # 创建评价指标DataFrame
        time_points = sorted(all_metrics.keys())
        
        # 初始化结果DataFrame
        eval_df = pd.DataFrame({
            'TimePoint': time_points,
            'MAE': [all_metrics[t]['MAE'] for t in time_points],
            'RMSE': [all_metrics[t]['RMSE'] for t in time_points],
            'NSE': [all_metrics[t]['NSE'] for t in time_points],
            'CORR': [all_metrics[t]['CORR'] for t in time_points]
        })
        
        # 计算不同方法的数量
        method_counts = {}
        for t in time_points:
            methods = all_methods[t]
            enhanced_prism_count = methods.count('增强PRISM')
            standard_prism_count = methods.count('常规PRISM')
            idw_count = methods.count('IDW')
            zero_count = methods.count('零降雨')
            method_counts[t] = {
                '增强PRISM': enhanced_prism_count,
                '常规PRISM': standard_prism_count, 
                'IDW': idw_count,
                '零降雨': zero_count
            }
        
        # 添加方法计数
        eval_df['增强PRISM_Count'] = [method_counts[t]['增强PRISM'] for t in time_points]
        eval_df['常规PRISM_Count'] = [method_counts[t]['常规PRISM'] for t in time_points]
        eval_df['IDW_Count'] = [method_counts[t]['IDW'] for t in time_points]
        eval_df['零降雨_Count'] = [method_counts[t]['零降雨'] for t in time_points]
        
        # 计算总体指标
        overall = {
            'TimePoint': 'Overall',
            'MAE': eval_df['MAE'].mean(),
            'RMSE': eval_df['RMSE'].mean(),
            'NSE': eval_df['NSE'].mean(),
            'CORR': eval_df['CORR'].mean(),
            '增强PRISM_Count': eval_df['增强PRISM_Count'].sum(),
            '常规PRISM_Count': eval_df['常规PRISM_Count'].sum(),
            'IDW_Count': eval_df['IDW_Count'].sum(),
            '零降雨_Count': eval_df['零降雨_Count'].sum()
        }
        
        # 添加总体指标行
        eval_df = pd.concat([eval_df, pd.DataFrame([overall])], ignore_index=True)
        
        # 添加非零降雨评价（如果提供了降雨数据）
        if rainfall_data is not None:
            # 筛选出有降雨的时间点
            time_col = rainfall_data.columns[0]
            non_zero_rain_times = []
            for t in time_points:
                rain_row = rainfall_data[rainfall_data[time_col] == t]
                if len(rain_row) > 0:
                    station_cols = [col for col in rain_row.columns if col != time_col]
                    if not all(rain_row[col].iloc[0] == 0 for col in station_cols):
                        non_zero_rain_times.append(t)
            
            # 如果有非零降雨时间点，计算这些时间点的评价指标
            if non_zero_rain_times:
                non_zero_metrics = {
                    'MAE': np.mean([all_metrics[t]['MAE'] for t in non_zero_rain_times]),
                    'RMSE': np.mean([all_metrics[t]['RMSE'] for t in non_zero_rain_times]),
                    'NSE': np.mean([all_metrics[t]['NSE'] for t in non_zero_rain_times]),
                    'CORR': np.mean([all_metrics[t]['CORR'] for t in non_zero_rain_times]),
                    '增强PRISM_Count': sum([method_counts[t]['增强PRISM'] for t in non_zero_rain_times]),
                    '常规PRISM_Count': sum([method_counts[t]['常规PRISM'] for t in non_zero_rain_times]),
                    'IDW_Count': sum([method_counts[t]['IDW'] for t in non_zero_rain_times]),
                    '零降雨_Count': sum([method_counts[t]['零降雨'] for t in non_zero_rain_times])
                }
                non_zero_overall = {
                    'TimePoint': 'NonZeroRain',
                    **non_zero_metrics
                }
                eval_df = pd.concat([eval_df, pd.DataFrame([non_zero_overall])], ignore_index=True)
        
        return eval_df
    except Exception as e:
        logging.error(f"创建评价指标文件失败: {e}")
        traceback.print_exc()
        return pd.DataFrame()

def create_weight_info_file(all_weight_infos, all_methods, station_names):
    """创建权重信息文件"""
    try:
        # 初始化结果列表
        weight_records = []
        
        # 对每个时间点
        for time_point in all_weight_infos:
            weight_infos = all_weight_infos[time_point]
            methods = all_methods[time_point]
            
            # 对每个站点
            for i, station in enumerate(station_names):
                if i < len(weight_infos):  # 确保索引有效
                    weight_info = weight_infos[i]
                    method = methods[i] if i < len(methods) else "未知"
                    
                    # 创建记录
                    record = {
                        'TimePoint': time_point,
                        'TestStation': station,
                        'Method': method
                    }
                    
                    # 添加权重信息
                    if isinstance(weight_info, dict) and '站点' in weight_info and '权重' in weight_info:
                        neighbor_stations = weight_info['站点']
                        weights = weight_info['权重']
                        
                        for j, (neighbor, weight) in enumerate(zip(neighbor_stations, weights)):
                            record[f'Neighbor{j+1}'] = neighbor
                            record[f'Weight{j+1}'] = weight
                    
                    weight_records.append(record)
        
        # 创建DataFrame
        weight_df = pd.DataFrame(weight_records)
        
        return weight_df
    except Exception as e:
        logging.error(f"创建权重信息文件失败: {e}")
        traceback.print_exc()
        return pd.DataFrame()

# 在 save_output_files 函数中添加打印功能
def save_output_files(interpolated_data, eval_df, weight_df, output_dir):
    """保存输出文件并打印评价指标文件和权重信息文件的第一行和最后一行"""
    logging.info("开始保存输出文件")
    try:
        # 保存每个站点的插值结果
        for station, df in interpolated_data.items():
            safe_station = station.replace('/', '_').replace('\\', '_')
            safe_station = ''.join(c for c in safe_station if c.isalnum() or c in ['_', '-'])
            output_file = os.path.join(output_dir, f"{safe_station}.csv")
            df.to_csv(output_file, index=False, encoding='utf_8')
            logging.debug(f"站点 {station} 的插值结果已保存至 {output_file}")
        
        # 保存评价指标
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        eval_file = os.path.join(output_dir, f"evaluation_{timestamp}.csv")
        eval_df.to_csv(eval_file, index=False, encoding='utf_8')
        logging.info(f"评价指标已保存至 {eval_file}")
        
        # 打印评价指标文件的第一行、最后第二行和最后一行
        try:
            with open(eval_file, 'r', encoding='utf_8') as f:
                lines = f.readlines()
                if len(lines) >= 2:
                    logging.info(f"评价指标文件的首行: {lines[0].strip()}")
                    logging.info(f"评价指标文件的倒数第二行: {lines[-2].strip()}")
                    logging.info(f"评价指标文件的末行: {lines[-1].strip()}")
                elif len(lines) == 1:
                    logging.info(f"评价指标文件的首行: {lines[0].strip()}")
                    logging.info("评价指标文件仅有一行，无法打印末行")
                else:
                    logging.info("评价指标文件为空")
        except Exception as e:
            logging.error(f"读取评价指标文件失败: {e}")
        
        # 保存权重信息
        weight_file = os.path.join(output_dir, f"weights_{timestamp}.csv")
        weight_df.to_csv(weight_file, index=False, encoding='utf_8')
        logging.info(f"权重信息已保存至 {weight_file}")
        
        # 打印权重信息文件的第一行和最后一行
        try:
            with open(weight_file, 'r', encoding='utf_8') as f:
                lines = f.readlines()
                if len(lines) >= 2:
                    logging.info(f"权重信息文件的首行: {lines[0].strip()}")
                    logging.info(f"权重信息文件的末行: {lines[-1].strip()}")
                elif len(lines) == 1:
                    logging.info(f"权重信息文件的首行: {lines[0].strip()}")
                    logging.info("权重信息文件仅有一行，无法打印末行")
                else:
                    logging.info("权重信息文件为空")
        except Exception as e:
            logging.error(f"读取权重信息文件失败: {e}")
        
    except Exception as e:
        logging.error(f"保存输出文件失败: {e}")
        traceback.print_exc()
# =============================================================================
# 主程序和调试
# =============================================================================

def process_time_point(time_point, stations_with_terrain, rainfall_data, tri_info, dem_data=None):
    """处理单个时间点"""
    try:
        logging.info(f"\n处理时间点: {time_point}")
        
        # 进行插值
        true_values, interpolated_values, methods, station_names, weight_infos = interpolate_for_timepoint(
            time_point, stations_with_terrain, rainfall_data, tri_info
        )
        
        # 评估结果
        metrics = evaluate_interpolation(true_values, interpolated_values)
        logging.info(f"评价指标: MAE={metrics['MAE']:.4f}, RMSE={metrics['RMSE']:.4f}, NSE={metrics['NSE']:.4f}")
        
        # 如果需要输出栅格且提供了DEM数据
        if OUTPUT_RASTER and dem_data is not None:
            dem_meta, dem_transform = dem_data[1], dem_data[2]
            safe_time = safe_filename(time_point)
            output_file = os.path.join(OUTPUT_DIR, f"rainfall_grid_{safe_time}.asc")
            create_raster(stations_with_terrain, interpolated_values, dem_meta, dem_transform, output_file)
        
        # 返回结果
        return {
            'true_values': true_values,
            'interpolated_values': interpolated_values,
            'methods': methods,
            'metrics': metrics,
            'station_names': station_names,
            'weight_infos': weight_infos
        }
    except Exception as e:
        logging.error(f"处理时间点 {time_point} 失败: {e}")
        traceback.print_exc()
        return None

def main():
    """主程序"""
    start_time = time.time()
    logging.info("开始增强版PRISM降雨空间插值...")
    
    try:
        # 步骤1: 加载站点信息
        stations = load_stations(STATIONS_FILE)
        logging.info("\n站点信息示例:")
        logging.info(stations.head())
        
        # 步骤2: 加载地形数据
        dem_data, slope_data, aspect_data = load_terrain_data(TERRAIN_DIR)
        logging.info("\n已加载地形数据")
        
        # 步骤3: 为站点提取地形特征
        stations_with_terrain = extract_terrain_features(
            stations, dem_data[0], dem_data[2], slope_data[0], aspect_data[0]
        )
        logging.info("\n站点地形特征示例:")
        logging.info(stations_with_terrain.head())
        
        # 步骤4: 加载降雨数据
        rainfall_data_dict = load_rainfall_data(INPUT_DIR, stations)
        logging.info(f"\n共加载 {len(rainfall_data_dict)} 个站点的降雨数据")
        
        # 步骤5: 对齐降雨数据
        aligned_rainfall = align_rainfall_data(rainfall_data_dict)
        logging.info("\n对齐后的降雨数据示例:")
        logging.info(aligned_rainfall.head())
        
        # 步骤6: 构建优化的Delaunay三角网
        tri_info = create_constrained_delaunay(stations_with_terrain)
        
        # 步骤7: 可视化优化的Delaunay三角网
        visualize_delaunay_with_quality(stations_with_terrain, tri_info, OUTPUT_DIR)
        
        # 步骤8: 多进程处理每个时间点
        time_col = aligned_rainfall.columns[0]
        time_points = aligned_rainfall[time_col].values
        
        # 准备多进程参数
        dem_for_raster = dem_data if OUTPUT_RASTER else None
        process_args = [(t, stations_with_terrain, aligned_rainfall, tri_info, dem_for_raster) for t in time_points]
        
        # 使用多进程处理
        logging.info(f"\n开始多进程处理 {len(time_points)} 个时间点，使用 {NUM_CORES} 个核心...")
        with multiprocessing.Pool(NUM_CORES) as pool:
            all_results = pool.starmap(process_time_point, process_args)
        
        # 步骤9: 整理结果
        results_dict = {}
        for i, result in enumerate(all_results):
            if result is not None:
                results_dict[time_points[i]] = result
        
        logging.info(f"\n成功处理 {len(results_dict)} 个时间点")
        
        if not results_dict:
            logging.error("错误: 没有成功处理的时间点，程序终止")
            return
        
        # 准备评价指标和权重信息
        all_metrics = {t: results_dict[t]['metrics'] for t in results_dict}
        all_methods = {t: results_dict[t]['methods'] for t in results_dict}
        all_weight_infos = {t: results_dict[t]['weight_infos'] for t in results_dict}
        
        # 步骤10: 创建输出文件
        logging.info("\n创建插值结果文件...")
        interpolated_data = create_interpolated_rainfall_file(stations_with_terrain, aligned_rainfall, results_dict)
        
        logging.info("创建评价指标文件...")
        eval_df = create_evaluation_file(all_metrics, all_methods, aligned_rainfall)
        logging.info("\n评价指标总体结果:")
        logging.info(eval_df.tail(1))
        
        logging.info("创建权重信息文件...")
        weight_df = create_weight_info_file(all_weight_infos, all_methods, stations_with_terrain['站点'].values)
        
        # 步骤11: 保存输出文件
        logging.info("\n保存输出文件...")
        save_output_files(interpolated_data, eval_df, weight_df, OUTPUT_DIR)
        
        # 完成
        elapsed_time = time.time() - start_time
        logging.info(f"\n增强版PRISM降雨空间插值完成，用时 {elapsed_time:.2f} 秒")
        
    except Exception as e:
        logging.error(f"程序执行失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    print("请选择运行模式:")
    print("1. 调试模式 (处理一个时间点)")
    print("2. 完整运行 (处理所有时间点)")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        if choice == "1":
            # 调试模式
            logging.info("开始增强版PRISM降雨空间插值调试...")
            
            # 步骤1-7: 与主程序相同，加载数据并构建三角网
            stations = load_stations(STATIONS_FILE)
            dem_data, slope_data, aspect_data = load_terrain_data(TERRAIN_DIR)
            stations_with_terrain = extract_terrain_features(
                stations, dem_data[0], dem_data[2], slope_data[0], aspect_data[0]
            )
            rainfall_data_dict = load_rainfall_data(INPUT_DIR, stations)
            aligned_rainfall = align_rainfall_data(rainfall_data_dict)
            tri_info = create_constrained_delaunay(stations_with_terrain)
            visualize_delaunay_with_quality(stations_with_terrain, tri_info, OUTPUT_DIR)
            
            # 步骤8: 只处理第一个时间点
            time_col = aligned_rainfall.columns[0]
            first_time_point = aligned_rainfall[time_col].values[0]
            
            # 执行插值并评估
            result = process_time_point(first_time_point, stations_with_terrain, aligned_rainfall, tri_info, dem_data)
            
            # 可视化调试结果
            if result:
                plt.figure(figsize=(12, 8))
                plt.scatter(result['true_values'], result['interpolated_values'])
                plt.plot([0, max(max(result['true_values']), 0.1)], [0, max(max(result['true_values']), 0.1)], 'r--')
                plt.xlabel('真实值')
                plt.ylabel('插值值')
                plt.title('插值结果对比')
                plt.grid(True)
                plt.savefig(os.path.join(OUTPUT_DIR, 'debug_interpolation_comparison.png'))
                plt.close()
            
            logging.info("\n调试完成，检查输出目录中的结果文件")
            
        elif choice == "2":
            main()
        else:
                    print("无效选择，默认使用调试模式")
                    # 内联实现调试模式，不调用未定义的函数
                    logging.info("开始增强版PRISM降雨空间插值调试...")
                    
                    # 步骤1-7: 与主程序相同，加载数据并构建三角网
                    stations = load_stations(STATIONS_FILE)
                    dem_data, slope_data, aspect_data = load_terrain_data(TERRAIN_DIR)
                    stations_with_terrain = extract_terrain_features(
                        stations, dem_data[0], dem_data[2], slope_data[0], aspect_data[0]
                    )
                    rainfall_data_dict = load_rainfall_data(INPUT_DIR, stations)
                    aligned_rainfall = align_rainfall_data(rainfall_data_dict)
                    tri_info = create_constrained_delaunay(stations_with_terrain)
                    visualize_delaunay_with_quality(stations_with_terrain, tri_info, OUTPUT_DIR)
                    
                    # 步骤8: 只处理第一个时间点
                    time_col = aligned_rainfall.columns[0]
                    first_time_point = aligned_rainfall[time_col].values[0]
                    
                    # 执行插值并评估
                    result = process_time_point(first_time_point, stations_with_terrain, aligned_rainfall, tri_info, dem_data)
                    
                    # 可视化调试结果
                    if result:
                        plt.figure(figsize=(12, 8))
                        plt.scatter(result['true_values'], result['interpolated_values'])
                        plt.plot([0, max(max(result['true_values']), 0.1)], [0, max(max(result['true_values']), 0.1)], 'r--')
                        plt.xlabel('真实值')
                        plt.ylabel('插值值')
                        plt.title('插值结果对比')
                        plt.grid(True)
                        plt.savefig(os.path.join(OUTPUT_DIR, 'debug_interpolation_comparison.png'))
                        plt.close()
                    
                    logging.info("\n调试完成，检查输出目录中的结果文件")
    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        traceback.print_exc()
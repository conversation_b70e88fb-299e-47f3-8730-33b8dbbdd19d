#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay三角剖分空间插值系统主程序

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0

使用说明:
1. 确保已安装所需的Python包：pandas, numpy, matplotlib, seaborn, scipy
2. 确保Delaunay分析结果文件存在：output/Delaunay/delaunay_analysis_summary_with_names.csv
3. 确保洪水事件数据存在：input_another/目录下的各个事件文件夹
4. 运行此脚本开始插值分析

功能特点:
- 基于Delaunay三角剖分的权重插值
- 支持多进程并行处理
- 自动生成评价指标和分析报告
- 生成可视化图表
- 支持多种输出格式（CSV、Excel）
"""

import sys
import os
from pathlib import Path
import logging
import traceback
import time
from datetime import datetime

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from config import config
    from delaunay_interpolation import DelaunayInterpolator
    from visualization import DelaunayVisualizer
    from utils import setup_logging
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必要的Python文件都在当前目录中")
    sys.exit(1)

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'pandas', 'numpy', 'matplotlib', 'seaborn', 'scipy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下必要的Python包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_input_data():
    """检查输入数据是否存在"""
    errors = []
    
    # 检查Delaunay分析结果文件
    if not config.delaunay_summary_file.exists():
        errors.append(f"Delaunay分析结果文件不存在: {config.delaunay_summary_file}")
    
    # 检查输入数据目录
    if not config.input_dir.exists():
        errors.append(f"输入数据目录不存在: {config.input_dir}")
    else:
        # 检查是否有洪水事件数据
        flood_events = config.get_flood_events()
        if not flood_events:
            errors.append(f"输入数据目录中没有洪水事件数据: {config.input_dir}")
        else:
            print(f"发现 {len(flood_events)} 个洪水事件:")
            for i, event in enumerate(flood_events[:5], 1):  # 只显示前5个
                print(f"  {i}. {event}")
            if len(flood_events) > 5:
                print(f"  ... 还有 {len(flood_events) - 5} 个事件")
    
    if errors:
        print("数据检查失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    return True

def print_system_info():
    """打印系统信息"""
    print("="*80)
    print("Delaunay三角剖分空间插值系统")
    print("="*80)
    print(f"版本: 1.0")
    print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {Path.cwd()}")
    print(f"输出目录: {config.output_dir}")
    print("="*80)

def print_configuration():
    """打印配置信息"""
    print("\n配置信息:")
    print("-"*50)
    print(f"输入目录: {config.input_dir}")
    print(f"Delaunay结果文件: {config.delaunay_summary_file}")
    print(f"输出目录: {config.output_dir}")
    print(f"并行处理: {'是' if config.parallel_params['use_multiprocessing'] else '否'}")
    if config.parallel_params['use_multiprocessing']:
        print(f"最大工作进程数: {config.parallel_params['max_workers']}")
    print(f"输出格式: {', '.join(config.output_params['export_format'])}")
    print(f"生成可视化: {'是' if config.output_params['save_visualization'] else '否'}")
    print("-"*50)

def run_interpolation():
    """运行插值分析"""
    try:
        print("\n开始插值分析...")
        start_time = time.time()
        
        # 创建插值器
        interpolator = DelaunayInterpolator()
        
        # 运行所有事件的插值分析
        results = interpolator.run_all_events()
        
        # 统计结果
        successful_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"\n插值分析完成!")
        print(f"总耗时: {elapsed_time:.2f} 秒")
        print(f"总事件数: {total_count}")
        print(f"成功处理: {successful_count}")
        print(f"成功率: {(successful_count/total_count)*100:.1f}%")
        
        return True, results
        
    except Exception as e:
        print(f"\n插值分析失败: {e}")
        traceback.print_exc()
        return False, []

def generate_visualizations():
    """生成可视化图表"""
    try:
        if not config.output_params['save_visualization']:
            print("跳过可视化生成（配置中已禁用）")
            return True
        
        print("\n生成可视化图表...")
        
        # 创建可视化器
        visualizer = DelaunayVisualizer(config.output_dir)
        
        # 获取输出路径
        output_paths = config.get_output_paths()
        
        # 生成所有可视化图表
        visualizer.generate_all_visualizations(
            output_paths['results'], 
            output_paths['metrics']
        )
        
        print("可视化图表生成完成!")
        return True
        
    except Exception as e:
        print(f"可视化生成失败: {e}")
        traceback.print_exc()
        return False

def print_output_summary():
    """打印输出文件汇总"""
    print("\n输出文件汇总:")
    print("-"*50)
    
    output_paths = config.get_output_paths()
    
    for category, path in output_paths.items():
        if path.exists():
            files = list(path.glob('*'))
            if files:
                print(f"{category.upper()}目录 ({len(files)}个文件):")
                for file in sorted(files)[:3]:  # 只显示前3个文件
                    print(f"  - {file.name}")
                if len(files) > 3:
                    print(f"  ... 还有 {len(files) - 3} 个文件")
    
    print(f"\n所有结果已保存至: {config.output_dir}")
    print("-"*50)

def main():
    """主函数"""
    try:
        # 打印系统信息
        print_system_info()
        
        # 检查依赖包
        print("检查依赖包...")
        if not check_dependencies():
            return 1
        print("✓ 依赖包检查通过")
        
        # 检查输入数据
        print("\n检查输入数据...")
        if not check_input_data():
            return 1
        print("✓ 输入数据检查通过")
        
        # 验证配置
        print("\n验证配置...")
        try:
            config.validate_config()
            print("✓ 配置验证通过")
        except Exception as e:
            print(f"✗ 配置验证失败: {e}")
            return 1
        
        # 打印配置信息
        print_configuration()
        
        # 询问用户是否继续
        response = input("\n是否开始插值分析? (y/n): ").strip().lower()
        if response not in ['y', 'yes', '是']:
            print("用户取消操作")
            return 0
        
        # 运行插值分析
        success, results = run_interpolation()
        if not success:
            return 1
        
        # 生成可视化图表
        generate_visualizations()
        
        # 打印输出汇总
        print_output_summary()
        
        print("\n" + "="*80)
        print("程序执行完成!")
        print("="*80)
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n用户中断程序执行")
        return 1
    except Exception as e:
        print(f"\n程序执行失败: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

"""
站点级别分析可视化器
使用站点名称进行详细的站点级别分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
from matplotlib.gridspec import GridSpec
import os
import logging
from typing import Dict, List
from datetime import datetime

# 使用支持中文的字体，但优先使用英文字体
matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'SimHei', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['figure.dpi'] = 300
matplotlib.rcParams['savefig.dpi'] = 300

logger = logging.getLogger(__name__)


class StationVisualizer:
    """站点级别分析可视化器"""
    
    def __init__(self, output_dir: str = "../output/IDW"):
        """初始化"""
        self.output_dir = output_dir
        self.viz_dir = os.path.join(output_dir, "station_analysis")
        os.makedirs(self.viz_dir, exist_ok=True)
        
        # 定义配色方案
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'accent': '#F18F01',
            'success': '#2E8B57',
            'warning': '#F4A261',
            'info': '#264653',
            'light': '#E9C46A',
            'dark': '#2A2A2A'
        }
        
        # 加载站点名称映射
        self.station_names = self._load_station_names()
        
        logger.info(f"Station visualizer initialized: {self.viz_dir}")
    
    def _load_station_names(self) -> Dict[str, str]:
        """加载站点名称映射"""
        try:
            excel_file = "../水晏泰森.xlsx"
            if not os.path.exists(excel_file):
                logger.warning(f"Excel station file not found: {excel_file}")
                return {}
            
            # 读取Excel文件
            df = pd.read_excel(excel_file)
            
            # 检查必要列
            if 'PSTCD' not in df.columns or 'NAME' not in df.columns:
                logger.warning("Excel file missing PSTCD or NAME columns")
                return {}
            
            # 创建站点代码到名称的映射
            station_names = {}
            for _, row in df.iterrows():
                station_code = str(row['PSTCD'])
                station_name = str(row['NAME'])
                if pd.notna(station_code) and pd.notna(station_name):
                    station_names[station_code] = station_name
            
            logger.info(f"Loaded {len(station_names)} station name mappings")
            return station_names
            
        except Exception as e:
            logger.error(f"Failed to load station names: {e}")
            return {}
    
    def get_station_display_name(self, station_code: str) -> str:
        """获取站点显示名称"""
        try:
            # 如果有中文名称，使用中文名称，否则使用原代码
            return self.station_names.get(str(station_code), str(station_code))
        except:
            return str(station_code)
    
    def create_station_performance_analysis(self, all_results: List[Dict]) -> str:
        """创建站点表现分析图"""
        try:
            logger.info("Creating station performance analysis...")
            
            # 收集所有站点的验证数据
            station_data = {}
            
            for result in all_results:
                if result.get('success') and result.get('results'):
                    event_name = result['event']
                    
                    for time_result in result['results']:
                        if (time_result.get('validation') and 
                            time_result['validation'].get('valid')):
                            
                            validation = time_result['validation']
                            station_results = validation.get('station_results', {})
                            
                            for station_code, station_result in station_results.items():
                                if station_code not in station_data:
                                    station_data[station_code] = {
                                        'observed': [],
                                        'predicted': [],
                                        'errors': [],
                                        'events': []
                                    }
                                
                                station_data[station_code]['observed'].append(station_result['observed'])
                                station_data[station_code]['predicted'].append(station_result['predicted'])
                                station_data[station_code]['errors'].append(station_result['error'])
                                station_data[station_code]['events'].append(event_name)
            
            if not station_data:
                logger.warning("No station data available for analysis")
                return ""
            
            # 计算每个站点的统计指标
            station_stats = []
            for station_code, data in station_data.items():
                if len(data['observed']) > 0:
                    observed = np.array(data['observed'])
                    predicted = np.array(data['predicted'])
                    errors = np.array(data['errors'])
                    
                    # 计算指标
                    mae = np.mean(np.abs(errors))
                    rmse = np.sqrt(np.mean(errors**2))
                    bias = np.mean(errors)
                    
                    # 计算NSE
                    ss_res = np.sum(errors**2)
                    ss_tot = np.sum((observed - np.mean(observed))**2)
                    nse = 1 - (ss_res / ss_tot) if ss_tot != 0 else np.nan
                    
                    # 计算R²
                    corr_matrix = np.corrcoef(observed, predicted)
                    r2 = corr_matrix[0, 1]**2 if not np.isnan(corr_matrix[0, 1]) else 0
                    
                    station_stats.append({
                        'station_code': station_code,
                        'station_name': self.get_station_display_name(station_code),
                        'sample_count': len(observed),
                        'MAE': mae,
                        'RMSE': rmse,
                        'BIAS': bias,
                        'NSE': nse,
                        'R2': r2
                    })
            
            if not station_stats:
                logger.warning("Cannot calculate station statistics")
                return ""
            
            # 转换为DataFrame
            df = pd.DataFrame(station_stats)
            
            # 创建图形
            fig = plt.figure(figsize=(20, 16))
            gs = GridSpec(4, 3, figure=fig, hspace=0.3, wspace=0.3)
            
            # 1. 站点NSE排名 (占据第一行)
            ax1 = fig.add_subplot(gs[0, :])
            self._plot_station_nse_ranking(ax1, df)
            
            # 2. 站点MAE分布
            ax2 = fig.add_subplot(gs[1, 0])
            self._plot_station_metric_distribution(ax2, df, 'MAE', 'MAE Distribution')
            
            # 3. 站点RMSE分布
            ax3 = fig.add_subplot(gs[1, 1])
            self._plot_station_metric_distribution(ax3, df, 'RMSE', 'RMSE Distribution')
            
            # 4. 站点样本数分布
            ax4 = fig.add_subplot(gs[1, 2])
            self._plot_station_sample_count(ax4, df)
            
            # 5. 站点误差散点图
            ax5 = fig.add_subplot(gs[2, :2])
            self._plot_station_error_scatter(ax5, df)
            
            # 6. 站点统计摘要
            ax6 = fig.add_subplot(gs[2, 2])
            self._plot_station_statistics(ax6, df)
            
            # 7. 最佳/最差站点对比 (占据第四行)
            ax7 = fig.add_subplot(gs[3, :])
            self._plot_best_worst_stations(ax7, df)
            
            # 设置总标题
            fig.suptitle('IDW Interpolation Station-Level Performance Analysis', 
                        fontsize=18, fontweight='bold', y=0.98)
            
            # 添加生成信息
            fig.text(0.99, 0.01, f'Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', 
                    ha='right', va='bottom', fontsize=10, alpha=0.7)
            
            # 保存图形
            output_path = os.path.join(self.viz_dir, 'station_performance_analysis.png')
            plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            plt.close()
            
            logger.info(f"Station performance analysis saved: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to create station performance analysis: {e}")
            return ""
    
    def _plot_station_nse_ranking(self, ax, df):
        """绘制站点NSE排名"""
        try:
            # 按NSE排序
            df_sorted = df.sort_values('NSE', ascending=True).dropna(subset=['NSE'])
            
            if len(df_sorted) == 0:
                ax.text(0.5, 0.5, 'No NSE data available', ha='center', va='center', 
                       transform=ax.transAxes)
                return
            
            # 选择显示的站点数量
            max_stations = min(20, len(df_sorted))
            df_display = df_sorted.tail(max_stations)  # 显示最好的站点
            
            # 创建条形图
            colors = [self.colors['success'] if nse >= 0.5 else 
                     self.colors['warning'] if nse >= 0.0 else 
                     self.colors['accent'] for nse in df_display['NSE']]
            
            bars = ax.barh(range(len(df_display)), df_display['NSE'], 
                          color=colors, alpha=0.8)
            
            # 设置标签 - 使用站点名称
            ax.set_yticks(range(len(df_display)))
            ax.set_yticklabels(df_display['station_name'], fontsize=10)
            ax.set_xlabel('NSE')
            ax.set_title(f'Station NSE Performance Ranking (Top {max_stations})', fontweight='bold', fontsize=14)
            
            # 添加数值标签
            for i, (bar, value) in enumerate(zip(bars, df_display['NSE'])):
                ax.text(value + 0.005 if value >= 0 else value - 0.005, i, f'{value:.3f}',
                       va='center', ha='left' if value >= 0 else 'right',
                       fontweight='bold', fontsize=9)
            
            # 添加参考线
            ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)
            ax.axvline(x=0.5, color='orange', linestyle='--', alpha=0.5, label='Acceptable (0.5)')
            ax.axvline(x=0.7, color='green', linestyle='--', alpha=0.5, label='Good (0.7)')
            ax.legend(fontsize=9)
            ax.grid(True, alpha=0.3, axis='x')
            
        except Exception as e:
            logger.warning(f"Failed to plot station NSE ranking: {e}")
    
    def _plot_station_metric_distribution(self, ax, df, metric, title):
        """绘制站点指标分布"""
        try:
            values = df[metric].dropna()
            if len(values) == 0:
                ax.text(0.5, 0.5, f'No {metric} data', ha='center', va='center', 
                       transform=ax.transAxes)
                return
            
            # 绘制直方图
            ax.hist(values, bins=15, alpha=0.7, color=self.colors['primary'], 
                   edgecolor='black')
            
            # 添加统计线
            mean_val = np.mean(values)
            median_val = np.median(values)
            
            ax.axvline(mean_val, color='red', linestyle='--', linewidth=2, 
                      label=f'Mean: {mean_val:.3f}')
            ax.axvline(median_val, color='orange', linestyle='--', linewidth=2, 
                      label=f'Median: {median_val:.3f}')
            
            ax.set_xlabel(metric)
            ax.set_ylabel('Number of Stations')
            ax.set_title(title, fontweight='bold')
            ax.legend(fontsize=9)
            ax.grid(True, alpha=0.3)
            
        except Exception as e:
            logger.warning(f"Failed to plot {metric} distribution: {e}")
    
    def _plot_station_sample_count(self, ax, df):
        """绘制站点样本数分布"""
        try:
            sample_counts = df['sample_count'].dropna()
            if len(sample_counts) == 0:
                ax.text(0.5, 0.5, 'No sample data', ha='center', va='center', 
                       transform=ax.transAxes)
                return
            
            # 绘制直方图
            ax.hist(sample_counts, bins=15, alpha=0.7, color=self.colors['info'], 
                   edgecolor='black')
            
            # 添加统计信息
            mean_count = np.mean(sample_counts)
            ax.axvline(mean_count, color='red', linestyle='--', linewidth=2, 
                      label=f'Average: {mean_count:.1f}')
            
            ax.set_xlabel('Sample Count')
            ax.set_ylabel('Number of Stations')
            ax.set_title('Station Sample Count Distribution', fontweight='bold')
            ax.legend(fontsize=9)
            ax.grid(True, alpha=0.3)
            
        except Exception as e:
            logger.warning(f"Failed to plot sample count distribution: {e}")
    
    def _plot_station_error_scatter(self, ax, df):
        """绘制站点误差散点图"""
        try:
            if 'MAE' not in df.columns or 'RMSE' not in df.columns:
                ax.text(0.5, 0.5, 'Missing error data', ha='center', va='center', 
                       transform=ax.transAxes)
                return
            
            valid_data = df[['MAE', 'RMSE', 'station_name']].dropna()
            
            if len(valid_data) == 0:
                ax.text(0.5, 0.5, 'No valid error data', ha='center', va='center', 
                       transform=ax.transAxes)
                return
            
            # 创建散点图
            scatter = ax.scatter(valid_data['MAE'], valid_data['RMSE'], 
                               c=range(len(valid_data)), cmap='viridis', 
                               alpha=0.7, s=60, edgecolors='black', linewidth=0.5)
            
            # 添加站点名称标注（只标注几个代表性的）
            if len(valid_data) <= 10:
                for _, row in valid_data.iterrows():
                    ax.annotate(row['station_name'], 
                               (row['MAE'], row['RMSE']),
                               xytext=(5, 5), textcoords='offset points',
                               fontsize=8, alpha=0.8)
            
            ax.set_xlabel('MAE (mm)')
            ax.set_ylabel('RMSE (mm)')
            ax.set_title('Station Error Relationship (MAE vs RMSE)', fontweight='bold')
            ax.grid(True, alpha=0.3)
            
            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax, shrink=0.8)
            cbar.set_label('Station Index', fontsize=9)
            
        except Exception as e:
            logger.warning(f"Failed to plot error scatter: {e}")
    
    def _plot_station_statistics(self, ax, df):
        """绘制站点统计摘要"""
        try:
            # 计算统计信息
            stats_text = f"Station Statistics Summary\n\n"
            stats_text += f"Total Stations: {len(df)}\n\n"
            
            for metric in ['NSE', 'MAE', 'RMSE', 'R2']:
                if metric in df.columns:
                    values = df[metric].dropna()
                    if len(values) > 0:
                        stats_text += f"{metric}:\n"
                        stats_text += f"  Mean: {np.mean(values):.4f}\n"
                        stats_text += f"  Std: {np.std(values):.4f}\n"
                        stats_text += f"  Min: {np.min(values):.4f}\n"
                        stats_text += f"  Max: {np.max(values):.4f}\n\n"
            
            # 显示统计文本
            ax.text(0.05, 0.95, stats_text, transform=ax.transAxes,
                   verticalalignment='top', fontsize=10, fontfamily='monospace',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))
            
            ax.set_title('Statistical Summary', fontweight='bold')
            ax.axis('off')
            
        except Exception as e:
            logger.warning(f"Failed to plot statistics: {e}")
    
    def _plot_best_worst_stations(self, ax, df):
        """绘制最佳/最差站点对比"""
        try:
            # 按NSE排序
            df_sorted = df.sort_values('NSE', ascending=False).dropna(subset=['NSE'])
            
            if len(df_sorted) == 0:
                ax.text(0.5, 0.5, 'No NSE data', ha='center', va='center', 
                       transform=ax.transAxes)
                return
            
            # 选择前5和后5
            top_5 = df_sorted.head(5)
            bottom_5 = df_sorted.tail(5)
            
            # 合并数据
            comparison_data = pd.concat([top_5, bottom_5])
            
            # 创建条形图
            colors = [self.colors['success'] if nse >= 0 else self.colors['accent'] 
                     for nse in comparison_data['NSE']]
            
            bars = ax.barh(range(len(comparison_data)), comparison_data['NSE'],
                          color=colors, alpha=0.8)
            
            # 设置标签 - 使用站点名称
            ax.set_yticks(range(len(comparison_data)))
            ax.set_yticklabels(comparison_data['station_name'], fontsize=10)
            ax.set_xlabel('NSE')
            ax.set_title('Best/Worst Stations NSE Comparison (Using Station Names)', fontweight='bold', fontsize=14)
            
            # 添加数值标签
            for i, (bar, value) in enumerate(zip(bars, comparison_data['NSE'])):
                ax.text(value + 0.01 if value >= 0 else value - 0.01, i, f'{value:.3f}',
                       va='center', ha='left' if value >= 0 else 'right',
                       fontweight='bold', fontsize=9)
            
            # 添加分隔线
            if len(top_5) > 0 and len(bottom_5) > 0:
                ax.axhline(y=len(top_5) - 0.5, color='red', linestyle='-', 
                          alpha=0.5, linewidth=2)
                ax.text(0.02, len(top_5) - 0.3, 'Best Stations', transform=ax.get_yaxis_transform(),
                       fontsize=12, fontweight='bold', color='green')
                ax.text(0.02, len(top_5) + 0.3, 'Worst Stations', transform=ax.get_yaxis_transform(),
                       fontsize=12, fontweight='bold', color='red')
            
            # 添加参考线
            ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)
            ax.grid(True, alpha=0.3, axis='x')
            
        except Exception as e:
            logger.warning(f"Failed to plot best/worst stations: {e}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Delaunay三角剖分的空间插值系统

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0

功能：
1. 基于Delaunay三角剖分结果进行空间插值
2. 使用包围站点权重进行时序插值
3. 计算NSE、MAE、RMSE等评价指标
4. 生成详细的分析报告
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

from config import config
from utils import (
    setup_logging, load_delaunay_summary, load_flood_event_data,
    get_station_rainfall_at_time, get_surrounding_stations_info,
    calculate_weighted_interpolation, validate_data_quality,
    create_progress_bar, save_dataframe_to_multiple_formats
)
from evaluation import InterpolationEvaluator

class DelaunayInterpolator:
    """基于Delaunay三角剖分的空间插值器"""
    
    def __init__(self):
        # 设置日志
        self.logger = setup_logging(
            config.logging_params['log_file'],
            config.logging_params['log_level'],
            config.logging_params['console_output']
        )
        
        # 初始化评价器
        self.evaluator = InterpolationEvaluator()
        
        # 加载Delaunay分析结果
        self.delaunay_summary = load_delaunay_summary(config.delaunay_summary_file)
        
        # 获取所有验证站点
        self.validation_stations = self.delaunay_summary['验证站点代码'].tolist()
        
        self.logger.info(f"初始化完成，共{len(self.validation_stations)}个验证站点")
    
    def interpolate_single_timestamp(self, 
                                   station_data: Dict[str, pd.DataFrame],
                                   target_station: str,
                                   timestamp: pd.Timestamp) -> Dict[str, Any]:
        """
        对单个时间点进行插值
        
        Args:
            station_data: 所有站点数据字典
            target_station: 目标站点代码
            timestamp: 目标时间戳
            
        Returns:
            插值结果字典
        """
        try:
            # 获取包围站点信息
            surrounding_info = get_surrounding_stations_info(self.delaunay_summary, target_station)
            if not surrounding_info:
                return {
                    'target_station': target_station,
                    'timestamp': timestamp,
                    'observed_value': np.nan,
                    'interpolated_value': np.nan,
                    'success': False,
                    'error': '未找到包围站点信息'
                }
            
            # 获取观测值
            observed_value = get_station_rainfall_at_time(station_data, target_station, timestamp)
            
            # 获取包围站点的降雨值和权重
            surrounding_values = []
            surrounding_weights = []
            valid_stations = []
            
            for station_info in surrounding_info['surrounding_stations']:
                station_code = station_info['code']
                weight = station_info['weight']
                
                rainfall_value = get_station_rainfall_at_time(station_data, station_code, timestamp)
                
                if not np.isnan(rainfall_value):
                    surrounding_values.append(rainfall_value)
                    surrounding_weights.append(weight)
                    valid_stations.append(station_code)
            
            # 检查是否有足够的有效站点
            if len(surrounding_values) < config.interpolation_params['min_valid_stations']:
                return {
                    'target_station': target_station,
                    'timestamp': timestamp,
                    'observed_value': observed_value,
                    'interpolated_value': np.nan,
                    'success': False,
                    'error': f'有效包围站点不足（{len(surrounding_values)}个）'
                }
            
            # 执行加权插值
            interpolated_value = calculate_weighted_interpolation(surrounding_values, surrounding_weights)
            
            return {
                'target_station': target_station,
                'timestamp': timestamp,
                'observed_value': observed_value,
                'interpolated_value': interpolated_value,
                'surrounding_stations': valid_stations,
                'surrounding_values': surrounding_values,
                'surrounding_weights': surrounding_weights,
                'success': True,
                'error': None
            }
            
        except Exception as e:
            self.logger.error(f"插值失败 - 站点: {target_station}, 时间: {timestamp}, 错误: {e}")
            return {
                'target_station': target_station,
                'timestamp': timestamp,
                'observed_value': np.nan,
                'interpolated_value': np.nan,
                'success': False,
                'error': str(e)
            }
    
    def interpolate_flood_event(self, event_name: str) -> Dict[str, Any]:
        """
        对单个洪水事件进行插值分析
        
        Args:
            event_name: 洪水事件名称
            
        Returns:
            插值结果字典
        """
        self.logger.info(f"开始处理洪水事件: {event_name}")
        
        # 加载事件数据
        event_dir = config.input_dir / event_name
        all_station_codes = set(self.validation_stations)
        
        # 添加所有包围站点代码
        for _, row in self.delaunay_summary.iterrows():
            all_station_codes.add(row['包围站点1代码'])
            all_station_codes.add(row['包围站点2代码'])
            all_station_codes.add(row['包围站点3代码'])
        
        station_data = load_flood_event_data(event_dir, list(all_station_codes))
        
        if not station_data:
            self.logger.error(f"无法加载事件数据: {event_name}")
            return {'event_name': event_name, 'success': False, 'error': '无法加载数据'}
        
        # 获取所有时间戳（取第一个站点的时间序列作为参考）
        reference_station = list(station_data.keys())[0]
        timestamps = station_data[reference_station]['时间'].tolist()
        
        self.logger.info(f"事件 {event_name} 共有 {len(timestamps)} 个时间点")
        
        # 执行插值
        interpolation_results = []
        progress_bar = create_progress_bar(len(timestamps) * len(self.validation_stations), 
                                         f"插值进度 - {event_name}")
        
        processed_count = 0
        
        for timestamp in timestamps:
            for target_station in self.validation_stations:
                result = self.interpolate_single_timestamp(station_data, target_station, timestamp)
                interpolation_results.append(result)
                
                processed_count += 1
                progress_bar(processed_count)
        
        # 计算评价指标
        self.logger.info(f"计算事件 {event_name} 的评价指标")
        event_metrics = self.calculate_event_metrics(interpolation_results)
        
        # 保存结果
        self.save_event_results(event_name, interpolation_results, event_metrics)
        
        return {
            'event_name': event_name,
            'success': True,
            'total_interpolations': len(interpolation_results),
            'successful_interpolations': sum(1 for r in interpolation_results if r['success']),
            'metrics': event_metrics
        }
    
    def calculate_event_metrics(self, interpolation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算事件的评价指标"""
        # 按站点分组计算指标
        station_metrics = {}
        
        for station in self.validation_stations:
            station_results = [r for r in interpolation_results 
                             if r['target_station'] == station and r['success']]
            
            if not station_results:
                continue
            
            observed = np.array([r['observed_value'] for r in station_results])
            predicted = np.array([r['interpolated_value'] for r in station_results])
            
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if np.sum(mask) < 2:
                continue
            
            metrics = self.evaluator.evaluate_comprehensive(observed[mask], predicted[mask])
            station_metrics[station] = metrics
        
        # 计算整体指标
        all_observed = []
        all_predicted = []
        
        for result in interpolation_results:
            if result['success'] and not np.isnan(result['observed_value']) and not np.isnan(result['interpolated_value']):
                all_observed.append(result['observed_value'])
                all_predicted.append(result['interpolated_value'])
        
        overall_metrics = {}
        if all_observed:
            overall_metrics = self.evaluator.evaluate_comprehensive(
                np.array(all_observed), np.array(all_predicted)
            )
        
        return {
            'station_metrics': station_metrics,
            'overall_metrics': overall_metrics,
            'summary': self.evaluator.generate_evaluation_summary(list(station_metrics.values()))
        }
    
    def save_event_results(self, event_name: str, 
                          interpolation_results: List[Dict[str, Any]], 
                          event_metrics: Dict[str, Any]):
        """保存事件结果"""
        output_paths = config.get_output_paths()
        
        # 保存详细插值结果
        if config.output_params['save_detailed_results']:
            results_df = pd.DataFrame([
                {
                    '事件名称': event_name,
                    '目标站点': r['target_station'],
                    '时间': r['timestamp'],
                    '观测值': r['observed_value'],
                    '插值值': r['interpolated_value'],
                    '成功': r['success'],
                    '错误信息': r.get('error', '')
                }
                for r in interpolation_results
            ])
            
            file_path = output_paths['results'] / f"{event_name}_interpolation_results"
            save_dataframe_to_multiple_formats(
                results_df, file_path, 
                config.output_params['export_format'],
                config.output_params['decimal_places']
            )
        
        # 保存评价指标
        if event_metrics['station_metrics']:
            metrics_df = pd.DataFrame.from_dict(event_metrics['station_metrics'], orient='index')
            metrics_df.index.name = '站点代码'
            metrics_df.reset_index(inplace=True)
            metrics_df.insert(0, '事件名称', event_name)
            
            file_path = output_paths['metrics'] / f"{event_name}_metrics"
            save_dataframe_to_multiple_formats(
                metrics_df, file_path,
                config.output_params['export_format'],
                config.output_params['decimal_places']
            )
        
        self.logger.info(f"事件 {event_name} 结果保存完成")
    
    def run_all_events(self):
        """运行所有洪水事件的插值分析"""
        flood_events = config.get_flood_events()
        self.logger.info(f"开始处理 {len(flood_events)} 个洪水事件")
        
        all_results = []
        
        if config.parallel_params['use_multiprocessing'] and len(flood_events) > 1:
            # 并行处理
            with ProcessPoolExecutor(max_workers=config.parallel_params['max_workers']) as executor:
                future_to_event = {
                    executor.submit(self.interpolate_flood_event, event): event 
                    for event in flood_events
                }
                
                for future in as_completed(future_to_event):
                    event = future_to_event[future]
                    try:
                        result = future.result()
                        all_results.append(result)
                        self.logger.info(f"事件 {event} 处理完成")
                    except Exception as e:
                        self.logger.error(f"事件 {event} 处理失败: {e}")
        else:
            # 串行处理
            for event in flood_events:
                try:
                    result = self.interpolate_flood_event(event)
                    all_results.append(result)
                except Exception as e:
                    self.logger.error(f"事件 {event} 处理失败: {e}")
        
        # 生成总体报告
        self.generate_comprehensive_report(all_results)

        return all_results

    def generate_comprehensive_report(self, all_results: List[Dict[str, Any]]):
        """生成综合分析报告"""
        self.logger.info("生成综合分析报告")

        output_paths = config.get_output_paths()

        # 收集所有事件的指标
        all_event_metrics = []
        successful_events = []

        for result in all_results:
            if result['success'] and 'metrics' in result:
                event_metrics = result['metrics']
                if event_metrics.get('overall_metrics'):
                    metrics_dict = event_metrics['overall_metrics'].copy()
                    metrics_dict['事件名称'] = result['event_name']
                    metrics_dict['插值总数'] = result['total_interpolations']
                    metrics_dict['成功插值数'] = result['successful_interpolations']
                    all_event_metrics.append(metrics_dict)
                    successful_events.append(result['event_name'])

        if not all_event_metrics:
            self.logger.warning("没有有效的事件指标数据")
            return

        # 创建综合指标DataFrame
        comprehensive_df = pd.DataFrame(all_event_metrics)

        # 计算统计汇总
        numeric_columns = ['NSE', 'RMSE', 'MAE', 'Correlation', 'R_squared', 'Bias', 'Relative_Bias']
        summary_stats = {}

        for col in numeric_columns:
            if col in comprehensive_df.columns:
                values = comprehensive_df[col].dropna()
                if len(values) > 0:
                    summary_stats[col] = {
                        '平均值': values.mean(),
                        '标准差': values.std(),
                        '最小值': values.min(),
                        '最大值': values.max(),
                        '中位数': values.median(),
                        '有效样本数': len(values)
                    }

        # 保存综合指标
        file_path = output_paths['reports'] / "comprehensive_metrics"
        save_dataframe_to_multiple_formats(
            comprehensive_df, file_path,
            config.output_params['export_format'],
            config.output_params['decimal_places']
        )

        # 生成汇总统计报告
        summary_df = pd.DataFrame.from_dict(summary_stats, orient='index')
        file_path = output_paths['reports'] / "summary_statistics"
        save_dataframe_to_multiple_formats(
            summary_df, file_path,
            config.output_params['export_format'],
            config.output_params['decimal_places']
        )

        # 生成文本报告
        self.generate_text_report(all_results, summary_stats, successful_events)

        self.logger.info("综合分析报告生成完成")

    def generate_text_report(self, all_results: List[Dict[str, Any]],
                           summary_stats: Dict[str, Dict[str, float]],
                           successful_events: List[str]):
        """生成文本格式的分析报告"""
        output_paths = config.get_output_paths()
        report_file = output_paths['reports'] / "delaunay_interpolation_report.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# Delaunay三角剖分空间插值分析报告\n\n")
            f.write(f"**生成时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 基本信息
            f.write("## 1. 基本信息\n\n")
            f.write(f"- **总事件数**: {len(all_results)}\n")
            f.write(f"- **成功处理事件数**: {len(successful_events)}\n")
            f.write(f"- **验证站点数**: {len(self.validation_stations)}\n")
            f.write(f"- **插值方法**: Delaunay三角剖分权重插值\n\n")

            # 成功处理的事件列表
            f.write("## 2. 成功处理的洪水事件\n\n")
            for i, event in enumerate(successful_events, 1):
                f.write(f"{i}. {event}\n")
            f.write("\n")

            # 评价指标统计
            f.write("## 3. 评价指标统计汇总\n\n")
            f.write("| 指标 | 平均值 | 标准差 | 最小值 | 最大值 | 中位数 | 样本数 |\n")
            f.write("|------|--------|--------|--------|--------|--------|---------|\n")

            for metric, stats in summary_stats.items():
                f.write(f"| {metric} | {stats['平均值']:.4f} | {stats['标准差']:.4f} | "
                       f"{stats['最小值']:.4f} | {stats['最大值']:.4f} | "
                       f"{stats['中位数']:.4f} | {stats['有效样本数']} |\n")
            f.write("\n")

            # NSE性能分析
            if 'NSE' in summary_stats:
                nse_stats = summary_stats['NSE']
                f.write("## 4. NSE性能分析\n\n")
                f.write(f"- **平均NSE**: {nse_stats['平均值']:.4f}\n")
                f.write(f"- **NSE标准差**: {nse_stats['标准差']:.4f}\n")
                f.write(f"- **最佳NSE**: {nse_stats['最大值']:.4f}\n")
                f.write(f"- **最差NSE**: {nse_stats['最小值']:.4f}\n\n")

                # NSE性能等级分布
                nse_values = [result['metrics']['overall_metrics']['NSE']
                            for result in all_results
                            if result['success'] and 'metrics' in result
                            and 'overall_metrics' in result['metrics']
                            and not np.isnan(result['metrics']['overall_metrics']['NSE'])]

                performance_counts = {}
                for nse in nse_values:
                    performance = self.evaluator.classify_performance(nse)
                    performance_counts[performance] = performance_counts.get(performance, 0) + 1

                f.write("### NSE性能等级分布\n\n")
                for performance, count in performance_counts.items():
                    percentage = (count / len(nse_values)) * 100
                    f.write(f"- **{performance}**: {count}个事件 ({percentage:.1f}%)\n")
                f.write("\n")

            # 技术说明
            f.write("## 5. 技术说明\n\n")
            f.write("### 插值方法\n")
            f.write("- 基于Delaunay三角剖分确定每个验证站点的包围站点\n")
            f.write("- 使用预计算的权重进行加权插值\n")
            f.write("- 采用留一法交叉验证评估插值精度\n\n")

            f.write("### 评价指标说明\n")
            f.write("- **NSE (Nash-Sutcliffe Efficiency)**: 纳什效率系数，范围(-∞,1]，越接近1越好\n")
            f.write("- **RMSE (Root Mean Square Error)**: 均方根误差，越小越好\n")
            f.write("- **MAE (Mean Absolute Error)**: 平均绝对误差，越小越好\n")
            f.write("- **Correlation**: 皮尔逊相关系数，范围[-1,1]，越接近1越好\n")
            f.write("- **R²**: 决定系数，范围[0,1]，越接近1越好\n")
            f.write("- **Bias**: 偏差，正值表示高估，负值表示低估\n\n")

            # 结论
            f.write("## 6. 结论\n\n")
            if 'NSE' in summary_stats:
                avg_nse = summary_stats['NSE']['平均值']
                if avg_nse > 0.7:
                    f.write("- Delaunay三角剖分插值方法在本研究区域表现**优秀**\n")
                elif avg_nse > 0.5:
                    f.write("- Delaunay三角剖分插值方法在本研究区域表现**良好**\n")
                else:
                    f.write("- Delaunay三角剖分插值方法在本研究区域表现**一般**\n")

            f.write("- 该方法适用于站点分布不均匀的降雨插值场景\n")
            f.write("- 建议结合地形因子进一步优化插值精度\n\n")

        self.logger.info(f"文本报告已保存至: {report_file}")


def main():
    """主程序入口"""
    try:
        # 验证配置
        config.validate_config()

        # 创建插值器
        interpolator = DelaunayInterpolator()

        # 运行所有事件的插值分析
        results = interpolator.run_all_events()

        # 输出最终统计
        successful_count = sum(1 for r in results if r['success'])
        total_count = len(results)

        print(f"\n{'='*60}")
        print("Delaunay三角剖分插值分析完成")
        print(f"{'='*60}")
        print(f"总事件数: {total_count}")
        print(f"成功处理: {successful_count}")
        print(f"成功率: {(successful_count/total_count)*100:.1f}%")
        print(f"结果保存至: {config.output_dir}")
        print(f"{'='*60}")

    except Exception as e:
        logging.error(f"程序执行失败: {e}")
        raise


if __name__ == "__main__":
    main()

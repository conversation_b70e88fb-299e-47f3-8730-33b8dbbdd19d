"""
生成IDW插值结果汇总图表

这个脚本读取批量处理结果并生成汇总可视化图表
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_batch_results(summary_file):
    """加载批量处理结果"""
    try:
        df = pd.read_csv(summary_file, encoding='utf-8')
        return df
    except Exception as e:
        print(f"加载结果文件失败: {e}")
        return None

def create_summary_plots(df, output_dir):
    """创建汇总图表"""
    try:
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建图形
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. MAE柱状图
        events = df['Event'].values
        mae_values = df['MAE'].values
        
        bars1 = ax1.bar(range(len(events)), mae_values, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.set_xticks(range(len(events)))
        ax1.set_xticklabels(events, rotation=45, ha='right', fontsize=8)
        ax1.set_ylabel('MAE (mm)', fontsize=12)
        ax1.set_title('Mean Absolute Error by Event', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, bar in enumerate(bars1):
            height = bar.get_height()
            if not np.isnan(height):
                ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{height:.1f}', ha='center', va='bottom', fontsize=6)
        
        # 2. RMSE柱状图
        rmse_values = df['RMSE'].values
        
        bars2 = ax2.bar(range(len(events)), rmse_values, alpha=0.7, color='lightcoral', edgecolor='black')
        ax2.set_xticks(range(len(events)))
        ax2.set_xticklabels(events, rotation=45, ha='right', fontsize=8)
        ax2.set_ylabel('RMSE (mm)', fontsize=12)
        ax2.set_title('Root Mean Square Error by Event', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 3. NSE柱状图
        nse_values = df['NSE'].values
        
        # 根据NSE值设置颜色
        colors = ['green' if nse > 0 else 'red' for nse in nse_values]
        bars3 = ax3.bar(range(len(events)), nse_values, alpha=0.7, color=colors, edgecolor='black')
        ax3.set_xticks(range(len(events)))
        ax3.set_xticklabels(events, rotation=45, ha='right', fontsize=8)
        ax3.axhline(y=0, color='black', linestyle='--', alpha=0.8, linewidth=2)
        ax3.set_ylabel('NSE', fontsize=12)
        ax3.set_title('Nash-Sutcliffe Efficiency by Event', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3)
        
        # 4. 综合性能散点图
        correlation_values = df['Correlation'].values
        
        # 过滤有效数据
        valid_mask = ~(np.isnan(mae_values) | np.isnan(nse_values))
        valid_mae = mae_values[valid_mask]
        valid_nse = nse_values[valid_mask]
        valid_events = events[valid_mask]
        
        if len(valid_mae) > 0:
            scatter = ax4.scatter(valid_mae, valid_nse, s=100, alpha=0.7, 
                                c=range(len(valid_mae)), cmap='viridis', edgecolors='black')
            
            # 添加事件标签（只显示部分以避免重叠）
            for i in range(0, len(valid_events), max(1, len(valid_events)//10)):
                ax4.annotate(valid_events[i], (valid_mae[i], valid_nse[i]), 
                           xytext=(5, 5), textcoords='offset points', fontsize=8)
            
            ax4.axhline(y=0, color='red', linestyle='--', alpha=0.8, linewidth=2)
            ax4.set_xlabel('MAE (mm)', fontsize=12)
            ax4.set_ylabel('NSE', fontsize=12)
            ax4.set_title('Overall Performance Assessment', fontsize=14, fontweight='bold')
            ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = os.path.join(output_dir, 'IDW_metrics_summary.png')
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"汇总图表已保存: {output_file}")
        
        # 创建统计汇总图
        create_statistics_plot(df, output_dir)
        
        return output_file
        
    except Exception as e:
        print(f"创建汇总图表失败: {e}")
        return None

def create_statistics_plot(df, output_dir):
    """创建统计汇总图"""
    try:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
        
        # 1. 指标分布箱线图
        metrics_data = [df['MAE'].dropna(), df['RMSE'].dropna(), df['NSE'].dropna()]
        metrics_labels = ['MAE (mm)', 'RMSE (mm)', 'NSE']
        
        ax1.boxplot(metrics_data, labels=metrics_labels)
        ax1.set_title('Metrics Distribution', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        
        # 2. NSE分布直方图
        nse_values = df['NSE'].dropna()
        ax2.hist(nse_values, bins=15, alpha=0.7, color='lightgreen', edgecolor='black')
        ax2.axvline(x=0, color='red', linestyle='--', alpha=0.8, linewidth=2)
        ax2.set_xlabel('NSE', fontsize=12)
        ax2.set_ylabel('Frequency', fontsize=12)
        ax2.set_title('NSE Distribution', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 添加统计信息
        nse_mean = nse_values.mean()
        nse_positive = (nse_values > 0).sum()
        nse_total = len(nse_values)
        stats_text = f'Mean NSE: {nse_mean:.3f}\nPositive NSE: {nse_positive}/{nse_total} ({nse_positive/nse_total*100:.1f}%)'
        ax2.text(0.05, 0.95, stats_text, transform=ax2.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # 3. 相关系数 vs NSE
        correlation_values = df['Correlation'].dropna()
        nse_for_corr = df.loc[df['Correlation'].notna(), 'NSE']
        
        ax3.scatter(correlation_values, nse_for_corr, alpha=0.7, s=50)
        ax3.axhline(y=0, color='red', linestyle='--', alpha=0.8)
        ax3.axvline(x=0, color='red', linestyle='--', alpha=0.8)
        ax3.set_xlabel('Correlation Coefficient', fontsize=12)
        ax3.set_ylabel('NSE', fontsize=12)
        ax3.set_title('Correlation vs NSE', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3)
        
        # 4. 处理时间分析
        processing_times = df['Processing_Time'].dropna()
        n_stations = df['N_Stations'].dropna()
        
        ax4.scatter(n_stations, processing_times, alpha=0.7, s=50, color='orange')
        ax4.set_xlabel('Number of Stations', fontsize=12)
        ax4.set_ylabel('Processing Time (seconds)', fontsize=12)
        ax4.set_title('Processing Time vs Station Count', fontsize=14, fontweight='bold')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = os.path.join(output_dir, 'IDW_statistics_summary.png')
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"统计汇总图已保存: {output_file}")
        
    except Exception as e:
        print(f"创建统计图表失败: {e}")

def print_summary_statistics(df):
    """打印汇总统计信息"""
    try:
        print("\n" + "="*60)
        print("IDW插值系统性能统计")
        print("="*60)
        
        # 基本统计
        total_events = len(df)
        successful_events = len(df.dropna(subset=['MAE']))
        
        print(f"总事件数: {total_events}")
        print(f"成功处理: {successful_events}")
        print(f"成功率: {successful_events/total_events*100:.1f}%")
        
        # 精度统计
        mae_mean = df['MAE'].mean()
        mae_std = df['MAE'].std()
        rmse_mean = df['RMSE'].mean()
        rmse_std = df['RMSE'].std()
        nse_mean = df['NSE'].mean()
        nse_std = df['NSE'].std()
        
        print(f"\n精度指标统计:")
        print(f"MAE: {mae_mean:.2f} ± {mae_std:.2f} mm")
        print(f"RMSE: {rmse_mean:.2f} ± {rmse_std:.2f} mm")
        print(f"NSE: {nse_mean:.3f} ± {nse_std:.3f}")
        
        # NSE分析
        nse_positive = (df['NSE'] > 0).sum()
        nse_good = (df['NSE'] > 0.5).sum()
        nse_excellent = (df['NSE'] > 0.75).sum()
        
        print(f"\nNSE性能分析:")
        print(f"NSE > 0: {nse_positive}/{total_events} ({nse_positive/total_events*100:.1f}%)")
        print(f"NSE > 0.5: {nse_good}/{total_events} ({nse_good/total_events*100:.1f}%)")
        print(f"NSE > 0.75: {nse_excellent}/{total_events} ({nse_excellent/total_events*100:.1f}%)")
        
        # 最佳和最差事件
        best_nse_idx = df['NSE'].idxmax()
        worst_nse_idx = df['NSE'].idxmin()
        
        print(f"\n最佳事件: {df.loc[best_nse_idx, 'Event']} (NSE: {df.loc[best_nse_idx, 'NSE']:.3f})")
        print(f"最差事件: {df.loc[worst_nse_idx, 'Event']} (NSE: {df.loc[worst_nse_idx, 'NSE']:.3f})")
        
        print("="*60)
        
    except Exception as e:
        print(f"打印统计信息失败: {e}")

def main():
    """主函数"""
    # 设置路径
    summary_file = "../output/IDW/batch_processing_summary.csv"
    output_dir = "../output/IDW/plots"
    
    print("IDW插值结果汇总可视化生成器")
    print("="*50)
    
    # 加载数据
    print("加载批量处理结果...")
    df = load_batch_results(summary_file)
    
    if df is None:
        print("无法加载结果文件，请检查路径")
        return
    
    print(f"成功加载{len(df)}个事件的结果")
    
    # 生成图表
    print("生成汇总图表...")
    create_summary_plots(df, output_dir)
    
    # 打印统计信息
    print_summary_statistics(df)
    
    print("\n汇总可视化生成完成！")

if __name__ == "__main__":
    main()

"""
Kriging插值系统并行处理模块
支持多核并行计算，提高处理效率
"""

import os
import time
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Callable
from multiprocessing import Pool, cpu_count
from functools import partial
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class ParallelProcessor:
    """并行处理器"""
    
    def __init__(self, config):
        """初始化并行处理器"""
        self.config = config
        self.num_cores = min(config.num_cores, cpu_count())
        
        logger.info(f"并行处理器初始化: 使用 {self.num_cores} 个核心")
    
    def parallel_leave_one_out_validation(self, stations_df: pd.DataFrame,
                                        rainfall_data: Dict[str, pd.DataFrame],
                                        kriging_func: Callable,
                                        max_time_points: int = 50) -> List[Dict]:
        """并行留一法交叉验证"""
        try:
            logger.info("开始并行留一法交叉验证...")
            
            # 获取时间点列表
            first_station = list(rainfall_data.keys())[0]
            first_df = rainfall_data[first_station]
            
            if len(first_df) == 0:
                raise ValueError("降雨数据为空")
            
            # 选择时间点
            time_indices = np.linspace(0, len(first_df)-1, 
                                     min(max_time_points, len(first_df)), dtype=int)
            
            # 准备并行任务
            tasks = []
            station_names = stations_df['站点'].tolist()
            
            for time_idx in time_indices:
                time_point = first_df.iloc[time_idx]['时间']
                
                # 构建该时间点的降雨数据
                rainfall_at_time = {}
                for station_name in station_names:
                    if str(station_name) in rainfall_data:
                        station_df = rainfall_data[str(station_name)]
                        matching_rows = station_df[station_df['时间'] == time_point]
                        
                        if len(matching_rows) > 0:
                            rainfall_at_time[station_name] = matching_rows['雨量'].iloc[0]
                        else:
                            rainfall_at_time[station_name] = 0.0
                    else:
                        rainfall_at_time[station_name] = 0.0
                
                # 为每个站点创建任务
                for target_station in station_names:
                    task = {
                        'target_station': target_station,
                        'rainfall_at_time': rainfall_at_time,
                        'time_point': time_point,
                        'stations_df': stations_df,
                        'config': self.config
                    }
                    tasks.append(task)
            
            logger.info(f"创建了 {len(tasks)} 个并行任务")
            
            # 执行并行处理
            if self.num_cores > 1:
                with Pool(processes=self.num_cores) as pool:
                    results = pool.map(self._validate_single_station_worker, tasks)
            else:
                # 单核处理
                results = [self._validate_single_station_worker(task) for task in tasks]
            
            # 过滤有效结果
            valid_results = [r for r in results if r is not None]
            
            logger.info(f"并行留一法验证完成: {len(valid_results)} 个有效结果")
            
            return valid_results
            
        except Exception as e:
            logger.error(f"并行留一法验证失败: {e}")
            return []
    
    @staticmethod
    def _validate_single_station_worker(task: Dict) -> Optional[Dict]:
        """单站点验证工作函数（用于并行处理）"""
        try:
            target_station = task['target_station']
            rainfall_at_time = task['rainfall_at_time']
            time_point = task['time_point']
            stations_df = task['stations_df']
            config = task['config']
            
            # 导入必要的模块（在工作进程中）
            from delaunay_triangulation import DelaunayTriangulation
            from kriging_core import KrigingCore
            from moran_index import MoranIndex
            
            # 获取目标站点的观测值
            observed_value = rainfall_at_time.get(target_station, 0.0)
            
            # 创建临时的三角网对象
            delaunay_tri = DelaunayTriangulation(config)
            delaunay_tri.build_triangulation(stations_df)
            
            # 找到邻近站点
            neighbor_indices = delaunay_tri.find_neighbors_by_triangulation(
                target_station, stations_df
            )
            
            if len(neighbor_indices) == 0:
                return None
            
            # 获取邻近站点信息
            neighbor_stations = stations_df.iloc[neighbor_indices]
            neighbor_names = neighbor_stations['站点'].tolist()
            
            # 获取邻近站点的降雨值
            neighbor_values = []
            neighbor_coords = []
            
            for _, neighbor_row in neighbor_stations.iterrows():
                neighbor_name = neighbor_row['站点']
                neighbor_value = rainfall_at_time.get(neighbor_name, 0.0)
                
                neighbor_values.append(neighbor_value)
                neighbor_coords.append([neighbor_row['经度'], neighbor_row['纬度']])
            
            neighbor_values = np.array(neighbor_values)
            neighbor_coords = np.array(neighbor_coords)
            
            # 获取目标站点坐标
            target_info = stations_df[stations_df['站点'] == target_station].iloc[0]
            target_coords = np.array([target_info['经度'], target_info['纬度']])
            
            # 执行Kriging插值
            kriging_core = KrigingCore(config)
            predicted_value, kriging_variance = kriging_core.ordinary_kriging(
                target_coords, neighbor_coords, neighbor_values
            )
            
            # 应用莫兰指数权重（如果启用）
            if config.enable_moran_weighting:
                moran_calculator = MoranIndex(config)
                moran_calculator.calculate_spatial_weights(stations_df)
                
                target_idx = stations_df[stations_df['站点'] == target_station].index[0]
                moran_weights = moran_calculator.calculate_moran_weights(
                    target_idx, neighbor_indices, neighbor_values
                )
                
                # 结合权重
                moran_prediction = np.sum(moran_weights * neighbor_values)
                predicted_value = (config.distance_weight * predicted_value + 
                                 config.moran_weight * moran_prediction)
            
            # 确保预测值非负
            predicted_value = max(0.0, predicted_value)
            
            return {
                'station': target_station,
                'time': time_point,
                'observed': float(observed_value),
                'predicted': float(predicted_value),
                'kriging_variance': float(kriging_variance),
                'neighbor_count': len(neighbor_indices),
                'neighbor_stations': neighbor_names
            }
            
        except Exception as e:
            # 在并行环境中，不要使用logger
            return None
    
    def parallel_batch_processing(self, folders: List[str], 
                                 process_func: Callable) -> Dict:
        """并行批量处理"""
        try:
            logger.info(f"开始并行批量处理 {len(folders)} 个文件夹...")
            
            if self.num_cores > 1 and len(folders) > 1:
                # 并行处理
                with Pool(processes=min(self.num_cores, len(folders))) as pool:
                    results = pool.map(process_func, folders)
            else:
                # 串行处理
                results = [process_func(folder) for folder in folders]
            
            # 整理结果
            batch_results = {}
            for i, result in enumerate(results):
                if result is not None:
                    batch_results[folders[i]] = result
            
            logger.info(f"并行批量处理完成: {len(batch_results)} 个成功")
            
            return batch_results
            
        except Exception as e:
            logger.error(f"并行批量处理失败: {e}")
            return {}
    
    def parallel_raster_interpolation(self, grid_points: np.ndarray,
                                    stations_df: pd.DataFrame,
                                    rainfall_at_time: Dict[str, float],
                                    kriging_func: Callable) -> np.ndarray:
        """并行栅格插值"""
        try:
            logger.info(f"开始并行栅格插值: {len(grid_points)} 个栅格点...")
            
            # 分批处理
            batch_size = max(1, len(grid_points) // (self.num_cores * 4))
            batches = [grid_points[i:i+batch_size] 
                      for i in range(0, len(grid_points), batch_size)]
            
            # 准备并行任务
            tasks = []
            for batch in batches:
                task = {
                    'grid_points': batch,
                    'stations_df': stations_df,
                    'rainfall_at_time': rainfall_at_time,
                    'config': self.config
                }
                tasks.append(task)
            
            # 执行并行处理
            if self.num_cores > 1:
                with Pool(processes=self.num_cores) as pool:
                    batch_results = pool.map(self._raster_interpolation_worker, tasks)
            else:
                batch_results = [self._raster_interpolation_worker(task) for task in tasks]
            
            # 合并结果
            interpolated_values = []
            for batch_result in batch_results:
                if batch_result is not None:
                    interpolated_values.extend(batch_result)
            
            logger.info(f"并行栅格插值完成: {len(interpolated_values)} 个插值点")
            
            return np.array(interpolated_values)
            
        except Exception as e:
            logger.error(f"并行栅格插值失败: {e}")
            return np.array([])
    
    @staticmethod
    def _raster_interpolation_worker(task: Dict) -> Optional[List[float]]:
        """栅格插值工作函数（用于并行处理）"""
        try:
            grid_points = task['grid_points']
            stations_df = task['stations_df']
            rainfall_at_time = task['rainfall_at_time']
            config = task['config']
            
            # 导入必要的模块
            from delaunay_triangulation import DelaunayTriangulation
            from kriging_core import KrigingCore
            
            # 创建临时对象
            delaunay_tri = DelaunayTriangulation(config)
            delaunay_tri.build_triangulation(stations_df)
            kriging_core = KrigingCore(config)
            
            results = []
            
            for grid_point in grid_points:
                try:
                    # 找到最近的站点作为邻居
                    distances = np.sqrt(
                        (stations_df['经度'] - grid_point[0])**2 + 
                        (stations_df['纬度'] - grid_point[1])**2
                    )
                    
                    # 选择最近的几个站点
                    neighbor_indices = distances.nsmallest(config.neighbor_count).index.tolist()
                    
                    if len(neighbor_indices) == 0:
                        results.append(0.0)
                        continue
                    
                    # 获取邻近站点信息
                    neighbor_stations = stations_df.iloc[neighbor_indices]
                    neighbor_values = []
                    neighbor_coords = []
                    
                    for _, neighbor_row in neighbor_stations.iterrows():
                        neighbor_name = neighbor_row['站点']
                        neighbor_value = rainfall_at_time.get(neighbor_name, 0.0)
                        
                        neighbor_values.append(neighbor_value)
                        neighbor_coords.append([neighbor_row['经度'], neighbor_row['纬度']])
                    
                    neighbor_values = np.array(neighbor_values)
                    neighbor_coords = np.array(neighbor_coords)
                    
                    # 执行Kriging插值
                    predicted_value, _ = kriging_core.ordinary_kriging(
                        grid_point, neighbor_coords, neighbor_values
                    )
                    
                    results.append(max(0.0, predicted_value))
                    
                except Exception:
                    results.append(0.0)
            
            return results
            
        except Exception:
            return None
    
    def get_optimal_batch_size(self, total_items: int) -> int:
        """获取最优批处理大小"""
        try:
            # 基于核心数和总项目数计算最优批大小
            if total_items <= self.num_cores:
                return 1
            
            # 每个核心处理的基本项目数
            base_size = total_items // self.num_cores
            
            # 确保批大小在合理范围内
            min_batch_size = max(1, self.config.batch_size // 4)
            max_batch_size = self.config.batch_size * 2
            
            optimal_size = max(min_batch_size, min(base_size, max_batch_size))
            
            return optimal_size
            
        except Exception as e:
            logger.warning(f"计算最优批大小失败: {e}")
            return self.config.batch_size
    
    def get_performance_info(self) -> Dict:
        """获取并行处理性能信息"""
        return {
            'num_cores': self.num_cores,
            'cpu_count': cpu_count(),
            'config_cores': self.config.num_cores,
            'memory_efficient': self.config.memory_efficient
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            # 并行处理器通常不需要特殊清理
            logger.debug("并行处理器清理完成")
        except Exception as e:
            logger.warning(f"并行处理器清理失败: {e}")

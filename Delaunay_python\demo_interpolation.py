#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay三角剖分插值系统演示脚本

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0

功能：
1. 演示基于Delaunay三角剖分的插值功能
2. 处理单个洪水事件进行快速测试
3. 生成简化的分析报告
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
import logging
import time

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config import config
from delaunay_interpolation import DelaunayInterpolator
from utils import setup_logging

def demo_single_event():
    """演示单个洪水事件的插值分析"""
    print("="*80)
    print("Delaunay三角剖分插值系统演示")
    print("="*80)
    
    # 设置日志
    logger = setup_logging(
        config.output_dir / 'logs' / 'demo.log',
        'INFO',
        True
    )
    
    try:
        # 创建插值器
        print("初始化插值器...")
        interpolator = DelaunayInterpolator()
        print(f"✓ 验证站点数: {len(interpolator.validation_stations)}")
        
        # 获取洪水事件列表
        flood_events = config.get_flood_events()
        print(f"✓ 发现 {len(flood_events)} 个洪水事件")
        
        if not flood_events:
            print("✗ 没有找到洪水事件数据")
            return False
        
        # 选择第一个事件进行演示
        demo_event = flood_events[0]
        print(f"✓ 选择演示事件: {demo_event}")
        
        # 执行插值分析
        print("\n开始插值分析...")
        start_time = time.time()
        
        result = interpolator.interpolate_flood_event(demo_event)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # 显示结果
        print(f"\n插值分析完成!")
        print(f"耗时: {elapsed_time:.2f} 秒")
        print(f"事件: {result['event_name']}")
        print(f"成功: {result['success']}")
        print(f"总插值次数: {result['total_interpolations']}")
        print(f"成功插值次数: {result['successful_interpolations']}")
        print(f"成功率: {(result['successful_interpolations']/result['total_interpolations'])*100:.1f}%")
        
        # 显示评价指标
        if 'metrics' in result and result['metrics'].get('overall_metrics'):
            metrics = result['metrics']['overall_metrics']
            print(f"\n整体评价指标:")
            print(f"NSE: {metrics.get('NSE', 'N/A'):.4f}")
            print(f"RMSE: {metrics.get('RMSE', 'N/A'):.4f}")
            print(f"MAE: {metrics.get('MAE', 'N/A'):.4f}")
            print(f"相关系数: {metrics.get('Correlation', 'N/A'):.4f}")
            print(f"样本数: {metrics.get('Sample_Count', 'N/A')}")
        
        # 显示站点指标统计
        if 'metrics' in result and result['metrics'].get('station_metrics'):
            station_metrics = result['metrics']['station_metrics']
            print(f"\n站点指标统计:")
            print(f"有效站点数: {len(station_metrics)}")
            
            # 计算NSE统计
            nse_values = [m['NSE'] for m in station_metrics.values() if not np.isnan(m['NSE'])]
            if nse_values:
                print(f"NSE - 平均: {np.mean(nse_values):.4f}, 最大: {np.max(nse_values):.4f}, 最小: {np.min(nse_values):.4f}")
        
        # 显示输出文件
        print(f"\n结果文件保存在: {config.output_dir}")
        output_paths = config.get_output_paths()
        
        for category, path in output_paths.items():
            if path.exists():
                files = list(path.glob('*'))
                if files:
                    print(f"{category}: {len(files)} 个文件")
        
        print("\n" + "="*80)
        print("演示完成!")
        print("="*80)
        
        return True
        
    except Exception as e:
        print(f"\n演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_sample_results():
    """显示样本结果"""
    try:
        # 查找结果文件
        results_dir = config.output_dir / 'results'
        result_files = list(results_dir.glob('*_interpolation_results.csv'))
        
        if not result_files:
            print("没有找到结果文件")
            return
        
        # 读取第一个结果文件
        result_file = result_files[0]
        df = pd.read_csv(result_file)
        
        print(f"\n样本结果 (来自 {result_file.name}):")
        print("-" * 60)
        
        # 显示前10行
        print(df.head(10).to_string(index=False))
        
        # 显示统计信息
        print(f"\n统计信息:")
        print(f"总记录数: {len(df)}")
        print(f"成功插值: {df['成功'].sum()}")
        print(f"成功率: {(df['成功'].sum() / len(df)) * 100:.1f}%")
        
        # 显示观测值和插值值的基本统计
        valid_data = df[df['成功'] == True]
        if len(valid_data) > 0:
            print(f"\n有效数据统计:")
            print(f"观测值 - 平均: {valid_data['观测值'].mean():.2f}, 最大: {valid_data['观测值'].max():.2f}")
            print(f"插值值 - 平均: {valid_data['插值值'].mean():.2f}, 最大: {valid_data['插值值'].max():.2f}")
        
    except Exception as e:
        print(f"显示样本结果失败: {e}")

def main():
    """主函数"""
    try:
        # 运行演示
        success = demo_single_event()
        
        if success:
            # 显示样本结果
            show_sample_results()
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return 1
    except Exception as e:
        print(f"程序执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

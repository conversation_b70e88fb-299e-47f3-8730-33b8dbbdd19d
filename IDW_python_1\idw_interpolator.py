"""
IDW空间插值核心模块
基于Delaunay三角剖分的IDW插值实现

主要功能：
1. 基于Delaunay三角剖分的站点选择
2. IDW权重计算和插值
3. 留一法验证
4. 插值精度评估

作者：空间插值系统
版本：1.0
"""

import numpy as np
import pandas as pd
from scipy.spatial import cKDTree
from scipy.spatial.distance import cdist
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class IDWInterpolator:
    """IDW插值器
    
    基于Delaunay三角剖分权重的IDW空间插值
    """
    
    def __init__(self, stations_df: pd.DataFrame, delaunay_weights: Dict = None,
                 power: float = 2.0, min_stations: int = 3, max_stations: int = 10,
                 search_radius: float = 0.5):
        """
        初始化IDW插值器
        
        参数:
            stations_df: 站点数据框，包含站点、经度、纬度列
            delaunay_weights: Delaunay三角剖分权重字典
            power: IDW权重指数
            min_stations: 最少使用站点数
            max_stations: 最多使用站点数
            search_radius: 搜索半径（度）
        """
        self.stations_df = stations_df.copy()
        self.delaunay_weights = delaunay_weights or {}
        self.power = power
        self.min_stations = min_stations
        self.max_stations = max_stations
        self.search_radius = search_radius
        
        # 构建站点坐标和KDTree
        self.station_coords = self.stations_df[['经度', '纬度']].values
        self.station_names = self.stations_df['站点'].values
        self.kdtree = cKDTree(self.station_coords)
        
        # 创建站点名称到索引的映射
        self.station_to_index = {
            str(station): idx for idx, station in enumerate(self.station_names)
        }
        
        logger.info(f"IDW插值器初始化完成，共{len(self.stations_df)}个站点")
        logger.info(f"Delaunay权重覆盖{len(self.delaunay_weights)}个站点")
    
    def interpolate_point(self, target_point: np.ndarray, rainfall_data: Dict[str, float]) -> Dict:
        """
        对单个点进行IDW插值
        
        参数:
            target_point: 目标点坐标 [经度, 纬度]
            rainfall_data: 站点降雨数据字典
            
        返回:
            Dict: 插值结果，包含插值值、使用的站点、权重等信息
        """
        try:
            # 过滤有效的站点数据
            valid_stations = []
            valid_values = []
            valid_coords = []
            
            for station, value in rainfall_data.items():
                station_str = str(station)
                if station_str in self.station_to_index and not np.isnan(value):
                    station_idx = self.station_to_index[station_str]
                    valid_stations.append(station_str)
                    valid_values.append(value)
                    valid_coords.append(self.station_coords[station_idx])
            
            if len(valid_stations) == 0:
                logger.warning("没有有效的站点数据")
                return {
                    'interpolated_value': np.nan,
                    'used_stations': [],
                    'weights': [],
                    'distances': [],
                    'method': 'no_data'
                }
            
            valid_coords = np.array(valid_coords)
            valid_values = np.array(valid_values)
            
            # 尝试使用Delaunay权重
            if self.delaunay_weights:
                delaunay_result = self._try_delaunay_interpolation(
                    target_point, valid_stations, valid_values
                )
                if delaunay_result['success']:
                    return delaunay_result
            
            # 回退到距离权重方法
            return self._distance_based_interpolation(
                target_point, valid_stations, valid_values, valid_coords
            )
            
        except Exception as e:
            logger.error(f"插值失败: {e}")
            return {
                'interpolated_value': np.nan,
                'used_stations': [],
                'weights': [],
                'distances': [],
                'method': 'error'
            }
    
    def _try_delaunay_interpolation(self, target_point: np.ndarray,
                                  valid_stations: List[str],
                                  valid_values: np.ndarray) -> Dict:
        """尝试使用Delaunay权重进行插值"""
        try:
            # 查找目标点的Delaunay权重
            target_station = None
            min_distance = float('inf')
            
            # 找到最近的站点作为目标站点
            for station in valid_stations:
                if station in self.station_to_index:
                    station_idx = self.station_to_index[station]
                    station_coord = self.station_coords[station_idx]
                    distance = np.linalg.norm(target_point - station_coord)
                    if distance < min_distance:
                        min_distance = distance
                        target_station = station
            
            # 如果找到目标站点且有Delaunay权重
            if target_station and target_station in self.delaunay_weights:
                delaunay_info = self.delaunay_weights[target_station]
                surrounding_stations = delaunay_info['stations']
                delaunay_weights_values = delaunay_info['weights']
                
                # 检查包围站点是否在有效站点中
                used_stations = []
                used_values = []
                used_weights = []
                
                for i, station in enumerate(surrounding_stations):
                    if station in valid_stations:
                        station_idx = valid_stations.index(station)
                        used_stations.append(station)
                        used_values.append(valid_values[station_idx])
                        used_weights.append(delaunay_weights_values[i])
                
                if len(used_stations) >= 2:  # 至少需要2个有效站点
                    # 归一化权重
                    used_weights = np.array(used_weights)
                    used_weights = used_weights / np.sum(used_weights)
                    
                    # 计算插值值
                    interpolated_value = np.sum(np.array(used_values) * used_weights)
                    
                    # 计算距离
                    distances = []
                    for station in used_stations:
                        station_idx = self.station_to_index[station]
                        station_coord = self.station_coords[station_idx]
                        dist = np.linalg.norm(target_point - station_coord)
                        distances.append(dist)
                    
                    return {
                        'interpolated_value': interpolated_value,
                        'used_stations': used_stations,
                        'weights': used_weights.tolist(),
                        'distances': distances,
                        'method': 'delaunay',
                        'success': True
                    }
            
            return {'success': False}
            
        except Exception as e:
            logger.debug(f"Delaunay插值失败: {e}")
            return {'success': False}
    
    def _distance_based_interpolation(self, target_point: np.ndarray,
                                    valid_stations: List[str],
                                    valid_values: np.ndarray,
                                    valid_coords: np.ndarray) -> Dict:
        """基于距离的IDW插值"""
        try:
            # 计算距离
            distances = cdist([target_point], valid_coords)[0]
            
            # 找到最近的站点
            nearest_indices = np.argsort(distances)[:self.max_stations]
            
            # 选择在搜索半径内的站点
            selected_indices = []
            for idx in nearest_indices:
                if distances[idx] <= self.search_radius:
                    selected_indices.append(idx)
                if len(selected_indices) >= self.max_stations:
                    break
            
            # 如果搜索半径内站点不足，使用最近的站点
            if len(selected_indices) < 1:  # 至少使用1个站点
                selected_indices = nearest_indices[:max(1, len(nearest_indices))]
            
            # 提取选中的站点信息
            selected_distances = distances[selected_indices]
            selected_values = valid_values[selected_indices]
            selected_stations = [valid_stations[i] for i in selected_indices]
            
            # 计算IDW权重
            # 避免除零错误
            min_distance = 1e-10
            safe_distances = np.maximum(selected_distances, min_distance)
            
            # IDW权重计算
            weights = 1.0 / (safe_distances ** self.power)
            
            # 检查权重是否合理
            if np.any(np.isnan(weights)) or np.any(np.isinf(weights)):
                logger.warning("权重计算异常，使用等权重")
                weights = np.ones(len(selected_distances)) / len(selected_distances)
            else:
                # 归一化权重
                weight_sum = np.sum(weights)
                if weight_sum > 0:
                    weights = weights / weight_sum
                else:
                    weights = np.ones(len(selected_distances)) / len(selected_distances)
            
            # 计算插值值
            interpolated_value = np.sum(selected_values * weights)
            
            return {
                'interpolated_value': interpolated_value,
                'used_stations': selected_stations,
                'weights': weights.tolist(),
                'distances': selected_distances.tolist(),
                'method': 'distance_based'
            }
            
        except Exception as e:
            logger.error(f"距离插值失败: {e}")
            return {
                'interpolated_value': np.nan,
                'used_stations': [],
                'weights': [],
                'distances': [],
                'method': 'error'
            }
    
    def validate_interpolation(self, rainfall_data: Dict[str, float]) -> Dict:
        """
        留一法验证插值精度
        
        参数:
            rainfall_data: 站点降雨数据字典
            
        返回:
            Dict: 验证结果，包含各种评估指标
        """
        try:
            observed_values = []
            predicted_values = []
            station_results = {}
            
            logger.info("开始留一法验证...")
            
            # 对每个站点进行留一法验证
            for target_station, observed_value in rainfall_data.items():
                if np.isnan(observed_value):
                    continue
                
                # 获取目标站点坐标
                target_station_str = str(target_station)
                if target_station_str not in self.station_to_index:
                    continue
                
                station_idx = self.station_to_index[target_station_str]
                target_coord = self.station_coords[station_idx]
                
                # 创建不包含目标站点的数据
                validation_data = {k: v for k, v in rainfall_data.items() 
                                 if str(k) != target_station_str}
                
                # 进行插值
                result = self.interpolate_point(target_coord, validation_data)
                predicted_value = result['interpolated_value']
                
                if not np.isnan(predicted_value):
                    observed_values.append(observed_value)
                    predicted_values.append(predicted_value)
                    
                    station_results[target_station_str] = {
                        'observed': observed_value,
                        'predicted': predicted_value,
                        'error': predicted_value - observed_value,
                        'abs_error': abs(predicted_value - observed_value),
                        'used_stations': result['used_stations'],
                        'method': result['method']
                    }
            
            # 计算评估指标
            if len(observed_values) > 0:
                observed_array = np.array(observed_values)
                predicted_array = np.array(predicted_values)
                
                # 计算各种指标
                mae = np.mean(np.abs(predicted_array - observed_array))
                rmse = np.sqrt(np.mean((predicted_array - observed_array) ** 2))
                
                # 计算NSE (Nash-Sutcliffe Efficiency)
                mean_observed = np.mean(observed_array)
                ss_res = np.sum((observed_array - predicted_array) ** 2)
                ss_tot = np.sum((observed_array - mean_observed) ** 2)
                nse = 1 - (ss_res / ss_tot) if ss_tot > 0 else -np.inf
                
                # 计算相关系数
                correlation = np.corrcoef(observed_array, predicted_array)[0, 1]
                
                # 计算偏差
                bias = np.mean(predicted_array - observed_array)
                
                validation_results = {
                    'mae': mae,
                    'rmse': rmse,
                    'nse': nse,
                    'correlation': correlation,
                    'bias': bias,
                    'n_stations': len(observed_values),
                    'station_results': station_results,
                    'observed_values': observed_values,
                    'predicted_values': predicted_values
                }
                
                logger.info(f"验证完成: MAE={mae:.3f}, RMSE={rmse:.3f}, NSE={nse:.3f}")
                return validation_results
            else:
                logger.warning("没有有效的验证数据")
                return {}
                
        except Exception as e:
            logger.error(f"验证失败: {e}")
            return {}

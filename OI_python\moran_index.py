# -*- coding: utf-8 -*-
"""
莫兰指数权重计算模块

实现基于莫兰指数的空间权重计算
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict
import logging
from scipy.spatial.distance import pdist, squareform

def calculate_spatial_weights_matrix(stations: pd.DataFrame, method: str = 'inverse_distance') -> np.ndarray:
    """
    计算空间权重矩阵
    
    Args:
        stations: 站点信息DataFrame
        method: 权重计算方法 ('inverse_distance', 'exponential', 'gaussian')
    
    Returns:
        np.ndarray: 空间权重矩阵
    """
    try:
        n_stations = len(stations)
        coords = stations[['longitude', 'latitude']].values
        
        # 计算距离矩阵
        distances = squareform(pdist(coords, metric='euclidean'))
        
        # 避免除零错误，将对角线元素设为一个很小的值
        np.fill_diagonal(distances, 1e-10)
        
        # 根据方法计算权重
        if method == 'inverse_distance':
            # 反距离权重
            weights = 1.0 / distances
        elif method == 'exponential':
            # 指数衰减权重
            weights = np.exp(-distances)
        elif method == 'gaussian':
            # 高斯权重
            sigma = np.mean(distances) / 2  # 使用平均距离的一半作为标准差
            weights = np.exp(-distances**2 / (2 * sigma**2))
        else:
            raise ValueError(f"不支持的权重计算方法: {method}")
        
        # 将对角线元素设为0（站点与自身的权重为0）
        np.fill_diagonal(weights, 0)
        
        # 行标准化
        row_sums = weights.sum(axis=1)
        row_sums[row_sums == 0] = 1  # 避免除零
        weights = weights / row_sums[:, np.newaxis]
        
        logging.info(f"成功计算空间权重矩阵，方法: {method}")
        return weights
    
    except Exception as e:
        logging.error(f"计算空间权重矩阵时出错: {e}")
        raise

def calculate_moran_index(values: np.ndarray, weights: np.ndarray) -> float:
    """
    计算莫兰指数
    
    Args:
        values: 观测值数组
        weights: 空间权重矩阵
    
    Returns:
        float: 莫兰指数值
    """
    try:
        n = len(values)
        
        # 移除缺失值
        valid_mask = ~np.isnan(values)
        if not np.any(valid_mask):
            return 0.0
        
        valid_values = values[valid_mask]
        valid_weights = weights[np.ix_(valid_mask, valid_mask)]
        n_valid = len(valid_values)
        
        if n_valid < 2:
            return 0.0
        
        # 计算均值
        mean_val = np.mean(valid_values)
        
        # 计算偏差
        deviations = valid_values - mean_val
        
        # 计算分子：空间滞后项
        numerator = 0.0
        for i in range(n_valid):
            for j in range(n_valid):
                numerator += valid_weights[i, j] * deviations[i] * deviations[j]
        
        # 计算分母：方差的总和
        denominator = np.sum(deviations**2)
        
        # 计算权重总和
        W = np.sum(valid_weights)
        
        if denominator == 0 or W == 0:
            return 0.0
        
        # 计算莫兰指数
        moran_i = (n_valid / W) * (numerator / denominator)
        
        return moran_i
    
    except Exception as e:
        logging.error(f"计算莫兰指数时出错: {e}")
        return 0.0

def calculate_moran_weights_for_triangle(target_station_id: str, neighbor_station_ids: List[str], 
                                       stations: pd.DataFrame, rainfall_data: pd.DataFrame,
                                       time_index: pd.Timestamp) -> Dict[str, float]:
    """
    为三角形中的站点计算基于莫兰指数的权重
    
    Args:
        target_station_id: 目标站点ID
        neighbor_station_ids: 邻近站点ID列表
        stations: 站点信息DataFrame
        rainfall_data: 降雨数据DataFrame
        time_index: 当前时间索引
    
    Returns:
        Dict[str, float]: 每个邻近站点的权重字典
    """
    try:
        # 获取所有相关站点（目标站点+邻近站点）
        all_station_ids = [target_station_id] + neighbor_station_ids
        
        # 过滤出存在于stations和rainfall_data中的站点
        valid_station_ids = []
        for station_id in all_station_ids:
            if (station_id in stations['station_id'].values and 
                station_id in rainfall_data.columns):
                valid_station_ids.append(station_id)
        
        if len(valid_station_ids) < 2:
            # 如果有效站点少于2个，使用等权重
            equal_weight = 1.0 / len(neighbor_station_ids) if neighbor_station_ids else 0.0
            return {station_id: equal_weight for station_id in neighbor_station_ids}
        
        # 获取相关站点的信息
        relevant_stations = stations[stations['station_id'].isin(valid_station_ids)].copy()
        relevant_stations = relevant_stations.reset_index(drop=True)
        
        # 计算空间权重矩阵
        spatial_weights = calculate_spatial_weights_matrix(relevant_stations, method='inverse_distance')
        
        # 获取当前时间的降雨数据
        current_rainfall = rainfall_data.loc[time_index, valid_station_ids].values
        
        # 计算莫兰指数
        moran_i = calculate_moran_index(current_rainfall, spatial_weights)
        
        # 基于莫兰指数和距离计算权重
        target_coords = stations[stations['station_id'] == target_station_id][['longitude', 'latitude']].values[0]
        
        weights = {}
        total_weight = 0.0
        
        for neighbor_id in neighbor_station_ids:
            if neighbor_id not in valid_station_ids:
                weights[neighbor_id] = 0.0
                continue
            
            # 获取邻近站点坐标
            neighbor_coords = stations[stations['station_id'] == neighbor_id][['longitude', 'latitude']].values[0]
            
            # 计算距离
            distance = np.sqrt(np.sum((target_coords - neighbor_coords)**2))
            
            # 基于距离的基础权重
            if distance > 0:
                base_weight = 1.0 / distance
            else:
                base_weight = 1.0
            
            # 考虑莫兰指数的影响
            # 如果莫兰指数为正（正相关），增加权重；如果为负（负相关），减少权重
            moran_factor = 1.0 + 0.5 * moran_i  # 调整因子，可以根据需要调整
            moran_factor = max(0.1, moran_factor)  # 确保权重不会变成负数或过小
            
            final_weight = base_weight * moran_factor
            weights[neighbor_id] = final_weight
            total_weight += final_weight
        
        # 标准化权重
        if total_weight > 0:
            for neighbor_id in weights:
                weights[neighbor_id] /= total_weight
        else:
            # 如果总权重为0，使用等权重
            equal_weight = 1.0 / len(neighbor_station_ids) if neighbor_station_ids else 0.0
            weights = {station_id: equal_weight for station_id in neighbor_station_ids}
        
        logging.debug(f"站点 {target_station_id} 的莫兰权重: {weights}, 莫兰指数: {moran_i:.4f}")
        
        return weights
    
    except Exception as e:
        logging.error(f"计算莫兰权重时出错: {e}")
        # 出错时返回等权重
        equal_weight = 1.0 / len(neighbor_station_ids) if neighbor_station_ids else 0.0
        return {station_id: equal_weight for station_id in neighbor_station_ids}

def calculate_simple_distance_weights(target_station_id: str, neighbor_station_ids: List[str], 
                                    stations: pd.DataFrame) -> Dict[str, float]:
    """
    计算简单的基于距离的权重（作为莫兰权重的备选方案）
    
    Args:
        target_station_id: 目标站点ID
        neighbor_station_ids: 邻近站点ID列表
        stations: 站点信息DataFrame
    
    Returns:
        Dict[str, float]: 每个邻近站点的权重字典
    """
    try:
        if not neighbor_station_ids:
            return {}
        
        # 获取目标站点坐标
        target_coords = stations[stations['station_id'] == target_station_id][['longitude', 'latitude']].values[0]
        
        weights = {}
        total_weight = 0.0
        
        for neighbor_id in neighbor_station_ids:
            # 获取邻近站点坐标
            neighbor_row = stations[stations['station_id'] == neighbor_id]
            if neighbor_row.empty:
                weights[neighbor_id] = 0.0
                continue
            
            neighbor_coords = neighbor_row[['longitude', 'latitude']].values[0]
            
            # 计算距离
            distance = np.sqrt(np.sum((target_coords - neighbor_coords)**2))
            
            # 反距离权重
            if distance > 0:
                weight = 1.0 / distance
            else:
                weight = 1.0  # 如果距离为0，设置为最大权重
            
            weights[neighbor_id] = weight
            total_weight += weight
        
        # 标准化权重
        if total_weight > 0:
            for neighbor_id in weights:
                weights[neighbor_id] /= total_weight
        else:
            # 如果总权重为0，使用等权重
            equal_weight = 1.0 / len(neighbor_station_ids)
            weights = {station_id: equal_weight for station_id in neighbor_station_ids}
        
        return weights
    
    except Exception as e:
        logging.error(f"计算距离权重时出错: {e}")
        # 出错时返回等权重
        equal_weight = 1.0 / len(neighbor_station_ids) if neighbor_station_ids else 0.0
        return {station_id: equal_weight for station_id in neighbor_station_ids}

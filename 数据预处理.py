##D:/pythondata/spatial_interpolation/input/2012-2/rainfall_relationships.png
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
import geopandas as gpd
from shapely.geometry import Point
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib支持中文显示
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'FangSong']
    plt.rcParams['axes.unicode_minus'] = False
except:
    print("警告: 无法设置中文字体，将使用英文标签")
    use_english = True
else:
    use_english = False

# 1. 数据加载
print("1. 加载数据...")
rains_df = pd.read_csv('D:/pythondata/spatial_interpolation/input/all/rain.csv')
stations_df = pd.read_csv('stations.csv')

# 时间格式转换
rains_df['时间'] = pd.to_datetime(rains_df['时间'])

# 检查缺失值情况
print("降雨数据缺失值情况:")
print(rains_df.isnull().sum())
print("\n站点数据缺失值情况:")
print(stations_df.isnull().sum())

# 创建预处理后的降雨数据副本
processed_rains_df = rains_df.copy()

# 2. 温和的异常值检测与处理
print("\n2. 执行温和的异常值处理...")
# 提取数值型站点列
numeric_station_cols = [col for col in rains_df.columns[1:] 
                       if pd.api.types.is_numeric_dtype(rains_df[col])]

# 使用更保守的Z分数阈值进行异常值检测
for station in numeric_station_cols:
    # 只处理非零值，保留零降雨记录
    nonzero_values = rains_df[rains_df[station] > 0][station]
    if len(nonzero_values) < 5:  # 如果非零值太少，跳过
        continue
        
    # 计算非零值的Z分数
    mean_val = nonzero_values.mean()
    std_val = nonzero_values.std()
    if std_val == 0:  # 避免除以零
        continue
        
    # 只处理极端异常值 (Z分数 > 5)
    for idx, value in enumerate(rains_df[station]):
        if value > 0:  # 只检查非零值
            z_score = (value - mean_val) / std_val
            if z_score > 5:  # 非常保守的阈值
                print(f"站点 {station} 在索引 {idx} 处发现极端异常值: {value}, Z分数: {z_score:.2f}")
                # 温和修正：将极端值缩小到较合理范围，但保留其显著性
                processed_rains_df.iloc[idx, processed_rains_df.columns.get_loc(station)] = mean_val + 3.5 * std_val

# 3. 轻微的空间一致性检查
print("\n3. 执行轻微的空间一致性检查...")
# 构建站点坐标列表
station_coords = []
for station in numeric_station_cols:
    if station in stations_df['站点'].values:
        station_data = stations_df[stations_df['站点'] == station].iloc[0]
        station_coords.append((station, station_data['经度'], station_data['纬度']))

# 计算站点间距离矩阵
dist_matrix = np.zeros((len(station_coords), len(station_coords)))
for i in range(len(station_coords)):
    for j in range(len(station_coords)):
        if i != j:
            dist_matrix[i, j] = np.sqrt(
                (station_coords[i][1] - station_coords[j][1])**2 + 
                (station_coords[i][2] - station_coords[j][2])**2
            )
        else:
            dist_matrix[i, j] = np.inf  # 自身距离设为无穷大

# 创建站点名称列表，与距离矩阵对应
station_names = [s[0] for s in station_coords]

# 空间一致性检查 - 只处理明显不合理的空间异常
spatial_corrections = 0
for idx, row in processed_rains_df.iterrows():
    for i, station in enumerate(station_names):
        current_value = row[station]
        # 只检查较大降雨值(>10mm)
        if current_value > 10:
            # 找出最近的3个站点
            nearest_indices = np.argsort(dist_matrix[i])[:4]  # 包含自己
            nearest_stations = [station_names[j] for j in nearest_indices if station_names[j] != station][:3]
            
            # 如果能找到至少2个邻近站点
            if len(nearest_stations) >= 2:
                neighbor_values = [row[s] for s in nearest_stations]
                max_neighbor = max(neighbor_values)
                
                # 只处理明显不合理的情况：当前值是最大邻居的5倍以上
                if current_value > max_neighbor * 5 and max_neighbor < current_value / 10:
                    # 非常温和的修正：降低到邻居最大值的3倍
                    new_value = max(max_neighbor * 3, current_value * 0.6)
                    processed_rains_df.loc[idx, station] = new_value
                    spatial_corrections += 1
                    print(f"修正空间异常: 站点 {station} 在 {row['时间']} 从 {current_value} 改为 {new_value}")

print(f"执行了 {spatial_corrections} 次空间一致性修正")

# 4. 保存处理后的数据
print("\n4. 保存预处理后的数据...")
processed_rains_df.to_csv('D:/pythondata/spatial_interpolation/input/all/rainfall_data.csv', index=False)

# 5. 评估预处理效果
print("\n5. 评估预处理效果...")
# 计算原始数据和处理后数据的统计指标
stats_comparison = pd.DataFrame(index=numeric_station_cols, 
                               columns=['原始均值', '处理后均值', '原始标准差', '处理后标准差', 
                                       '原始最大值', '处理后最大值', '原始零值比例', '处理后零值比例'])

for station in numeric_station_cols:
    orig_series = rains_df[station]
    proc_series = processed_rains_df[station]
    
    stats_comparison.loc[station, '原始均值'] = orig_series.mean()
    stats_comparison.loc[station, '处理后均值'] = proc_series.mean()
    stats_comparison.loc[station, '原始标准差'] = orig_series.std()
    stats_comparison.loc[station, '处理后标准差'] = proc_series.std()
    stats_comparison.loc[station, '原始最大值'] = orig_series.max()
    stats_comparison.loc[station, '处理后最大值'] = proc_series.max()
    stats_comparison.loc[station, '原始零值比例'] = (orig_series == 0).mean()
    stats_comparison.loc[station, '处理后零值比例'] = (proc_series == 0).mean()

print("统计指标对比 (取平均值):")
print(stats_comparison.mean())

# 6. 可视化预处理前后对比
print("\n6. 生成预处理前后对比图...")
# 选择几个有代表性的站点进行可视化
sample_stations = np.random.choice(numeric_station_cols, min(3, len(numeric_station_cols)), replace=False)

plt.figure(figsize=(15, 10))
for i, station in enumerate(sample_stations):
    plt.subplot(len(sample_stations), 1, i+1)
    plt.plot(rains_df['时间'], rains_df[station], 'b-', alpha=0.6, label='原始数据')
    plt.plot(processed_rains_df['时间'], processed_rains_df[station], 'r-', alpha=0.6, label='处理后数据')
    plt.title(f'站点 {station} 降雨量时间序列对比')
    plt.ylabel('降雨量 (mm)')
    plt.legend()

plt.tight_layout()
plt.savefig('D:/pythondata/spatial_interpolation/input/all/rainfall_preprocessing_comparison.png')

print("\n预处理完成! 结果已保存到 preprocessed_rainfall_data.csv")

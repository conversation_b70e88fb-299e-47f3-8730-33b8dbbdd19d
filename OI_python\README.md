# OI插值系统使用指南

## 系统简介

本系统实现了基于最优插值法(OI)的点雨量到面雨量的空间插值，主要功能包括：

1. **Delaunay三角网构建**：自动构建站点间的三角网络
2. **莫兰指数权重计算**：基于空间自相关性计算权重
3. **最优插值算法**：实现高精度的空间插值
4. **留一法验证**：自动验证插值精度
5. **评价指标计算**：计算MAE、RMSE、NSE、R²等指标
6. **栅格输出**：生成ASC格式的栅格文件
7. **批量处理**：支持多个洪水事件的批量处理

## 快速开始

### 方法一：直接运行（推荐新手）

1. 确保您的数据文件按以下结构组织：
```
D:/pythondata/spatial_interpolation/
├── input_another/2009-1/          # 点雨量数据文件夹
│   ├── 80606500.csv               # 站点雨量文件
│   ├── 80607800.csv
│   └── ...
├── stations.csv                   # 站点信息文件
├── terrain/90/mask.asc            # 流域掩膜文件
└── output/OI/2009-1/              # 输出目录（自动创建）
```

2. 运行主程序：
```bash
python OI_python/run_oi.py
```

3. 选择"使用默认配置运行"，系统会自动处理

### 方法二：交互式配置

1. 运行主程序：
```bash
python OI_python/run_oi.py
```

2. 选择"交互式配置运行"

3. 根据提示输入您的文件路径和参数

### 方法三：使用配置文件

1. 创建配置文件：
```bash
python OI_python/run_oi.py
```
选择"创建配置文件模板"

2. 编辑配置文件 `oi_config.json`

3. 使用配置文件运行：
```bash
python -m OI_python.easy_run --config oi_config.json
```

## 数据格式要求

### 1. 站点信息文件 (stations.csv)
```csv
站点,经度,纬度
80606500,110.606944,24.090833
80607800,110.513055,24.231666
...
```

### 2. 点雨量文件 (如 80606500.csv)
```csv
时间,雨量
2009-04-16 03:00,0.0
2009-04-16 04:00,0.0
2009-04-16 05:00,4.0
...
```

### 3. 掩膜文件 (mask.asc)
标准ASC格式的栅格文件，定义流域边界

## 主要参数配置

### 基础参数
- `input_dir`: 输入数据目录
- `stations_file`: 站点信息文件路径
- `mask_file`: 掩膜文件路径
- `output_dir`: 输出目录

### 处理参数
- `num_processes`: 并行进程数（建议设为CPU核心数的80%）
- `batch_size`: 批处理大小（用于内存管理）
- `debug_mode`: 调试模式（只处理少量数据）

### 算法参数
- `observation_error`: 观测误差（默认0.01）
- `use_distance_weights`: 是否使用距离权重
- `zero_ratio_threshold`: 零值比例阈值（默认0.8）

### 输出控制
- `generate_raster`: 是否生成栅格输出
- `generate_point_output`: 是否生成点雨量输出
- `generate_delaunay_plot`: 是否生成三角网图

## 输出文件说明

### 主要输出文件
1. **validation_results.csv**: 留一法验证结果
2. **interpolated_rainfall.csv**: 插值后的降雨数据
3. **evaluation_metrics_*.json**: 评价指标文件
4. **delaunay_triangulation.png**: Delaunay三角网图
5. **station_relationships.csv**: 站点关系表

### 点雨量输出
- `point_rainfall/`: 文件夹，包含每个站点的插值结果CSV文件

### 栅格输出（可选）
- `raster_output/`: 文件夹，包含每个时间步的ASC栅格文件
- `areal_rainfall.csv`: 流域面雨量数据

### 日志文件
- `logs/`: 文件夹，包含详细的处理日志

## 批量处理

### 启用批量处理
1. 设置 `batch_processing = True`
2. 配置 `batch_root_dir`（包含多个事件文件夹的根目录）
3. 配置 `batch_output_root`（批量输出根目录）

### 批量处理结构
```
batch_root_dir/
├── 2009-1/                       # 事件文件夹
│   ├── 80606500.csv
│   └── ...
├── 2009-2/
│   ├── 80606500.csv
│   └── ...
└── ...

batch_output_root/
├── 2009-1/                       # 对应的输出文件夹
│   ├── validation_results.csv
│   └── ...
├── 2009-2/
└── ...
```

### 批量处理输出
- `batch_results_*.csv`: 所有事件的评价指标汇总
- `batch_results_*.json`: 详细的批量处理结果

## 命令行使用

### 基本命令
```bash
# 使用默认配置
python -m OI_python.easy_run

# 使用配置文件
python -m OI_python.easy_run --config config.json

# 指定输入输出目录
python -m OI_python.easy_run --input /path/to/input --output /path/to/output

# 批量处理模式
python -m OI_python.easy_run --batch

# 调试模式
python -m OI_python.easy_run --debug

# 交互式模式
python -m OI_python.easy_run --interactive
```

### 创建配置文件
```bash
python -m OI_python.easy_run --create-config my_config.json
```

## 常见问题

### 1. 内存不足
- 减少 `batch_size` 参数
- 减少 `num_processes` 参数
- 启用 `debug_mode` 先测试少量数据

### 2. 处理速度慢
- 增加 `num_processes` 参数（不超过CPU核心数）
- 检查硬盘读写速度
- 考虑使用SSD存储

### 3. 插值精度低
- 检查站点分布是否合理
- 调整 `observation_error` 参数
- 检查数据质量

### 4. 文件路径错误
- 确保所有路径使用正斜杠 `/` 或双反斜杠 `\\`
- 检查文件是否存在
- 确保有足够的读写权限

## 技术支持

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. 数据格式是否正确
3. 文件路径是否存在
4. 系统内存是否充足

## 系统要求

- Python 3.7+
- 必需的Python包：pandas, numpy, scipy, matplotlib
- 推荐：多核CPU，8GB以上内存
- 操作系统：Windows/Linux/macOS

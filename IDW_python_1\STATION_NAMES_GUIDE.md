# IDW空间插值系统 - 站点名称功能使用指南

## 🎯 功能概述

本系统已集成水晏泰森.xlsx文件中的NAME列，可以在可视化中使用中文站点名称替代站点代码，提供更直观的分析结果。

## 📊 主要功能

### 1. 自动站点名称映射
- **数据源**: 水晏泰森.xlsx文件
- **映射关系**: PSTCD（站点代码）→ NAME（中文站点名称）
- **自动加载**: 系统启动时自动读取映射关系

### 2. 站点级别可视化
- **专门的可视化器**: `StationVisualizer`
- **中文站点名称**: 在图表中显示真实的中文站点名称
- **多维度分析**: 7个专业分析图表

## 🛠️ 使用方法

### 基本使用

#### 1. 运行完整的IDW插值系统
```bash
cd IDW_python
python main.py
```

#### 2. 测试站点名称功能
```bash
cd IDW_python
python test_station_names.py
```

#### 3. 生成站点级别分析图
```python
from station_visualizer import StationVisualizer

# 创建可视化器
visualizer = StationVisualizer()

# 生成站点分析图（需要结果数据）
analysis_file = visualizer.create_station_performance_analysis(results)
```

### 高级使用

#### 获取单个站点名称
```python
from data_processor import DataProcessor

# 初始化数据处理器
data_processor = DataProcessor(
    input_dir="../input_another",
    stations_file="../stations.csv",
    delaunay_weights_file="../output/Delaunay/delaunay_analysis_summary.csv"
)

# 获取站点名称
station_name = data_processor.get_station_name('80606500')
print(station_name)  # 输出: 大化
```

#### 查看所有站点映射
```python
from station_visualizer import StationVisualizer

visualizer = StationVisualizer()
print(visualizer.station_names)
# 输出: {'80606500': '大化', '80607800': '茶山', ...}
```

## 📈 生成的可视化

### 站点表现分析图
**文件位置**: `output/IDW/station_analysis/station_performance_analysis.png`

**包含内容**:
1. **Station NSE Performance Ranking**: 使用中文站点名称的NSE排名
2. **MAE Distribution**: 所有站点的MAE分布
3. **RMSE Distribution**: 所有站点的RMSE分布  
4. **Station Sample Count Distribution**: 各站点样本数分布
5. **Station Error Relationship**: MAE vs RMSE散点图
6. **Statistical Summary**: 详细统计摘要
7. **Best/Worst Stations NSE Comparison**: 最佳/最差站点对比

### 特色功能
- **中文站点名称**: 显示"大化"、"茶山"、"蒙山"等中文名称
- **英文标签**: 除站点名称外，所有标签使用英文
- **专业设计**: 现代化配色和布局
- **高分辨率**: 300 DPI输出质量

## 🔧 技术细节

### 站点名称映射逻辑
```python
def _load_station_names(self) -> Dict[str, str]:
    """从Excel文件加载站点名称映射"""
    df = pd.read_excel("../水晏泰森.xlsx")
    station_names = {}
    for _, row in df.iterrows():
        station_code = str(row['PSTCD'])
        station_name = str(row['NAME'])
        station_names[station_code] = station_name
    return station_names
```

### 智能显示逻辑
```python
def get_station_display_name(self, station_code: str) -> str:
    """如果有中文名称就用中文，否则用原代码"""
    return self.station_names.get(str(station_code), str(station_code))
```

### 容错机制
- 如果Excel文件不存在，系统仍可正常运行
- 如果某个站点没有名称映射，使用原站点代码
- 如果Excel文件格式错误，记录警告但不影响主功能

## 📁 相关文件

### 核心文件
```
IDW_python/
├── data_processor.py          # 数据处理器（含站点名称功能）
├── station_visualizer.py      # 站点级别可视化器
├── test_station_names.py      # 站点名称功能测试
└── STATION_NAMES_GUIDE.md     # 本使用指南

水晏泰森.xlsx                   # 站点名称数据源
├── PSTCD列: 站点代码
└── NAME列: 中文站点名称
```

### 输出文件
```
output/IDW/
└── station_analysis/
    └── station_performance_analysis.png  # 站点表现分析图
```

## ✅ 验证方法

### 1. 运行测试脚本
```bash
cd IDW_python
python test_station_names.py
```

**期望输出**:
```
INFO:__main__:Station name mappings:
INFO:__main__:  80606500 -> 大化
INFO:__main__:  80607800 -> 茶山
INFO:__main__:  80608500 -> 蒙山
INFO:__main__:  80628800 -> 六樟
INFO:__main__:  80629000 -> 壬山
INFO:__main__:✅ All tests passed!
```

### 2. 检查生成的图表
查看 `output/IDW/station_analysis/station_performance_analysis.png`，确认：
- Y轴标签显示中文站点名称（大化、茶山等）
- 其他标签使用英文
- 图表清晰可读

## 🎉 主要优势

### 1. 用户友好
- **直观显示**: 使用中文站点名称，更容易理解
- **自动映射**: 无需手动配置，自动读取Excel文件
- **容错设计**: 即使配置有问题也不影响主功能

### 2. 专业质量
- **高分辨率**: 300 DPI适合学术发表
- **现代设计**: 专业的配色和布局
- **多维分析**: 7个不同角度的站点分析

### 3. 易于维护
- **集中管理**: 所有站点名称在一个Excel文件中
- **简单更新**: 修改Excel文件即可更新站点名称
- **向后兼容**: 不影响现有功能

---

**功能完成**: ✅ 已完全集成  
**测试状态**: ✅ 已通过测试  
**文档状态**: ✅ 已完善  

现在您可以在IDW插值系统中使用真实的中文站点名称进行分析和可视化！

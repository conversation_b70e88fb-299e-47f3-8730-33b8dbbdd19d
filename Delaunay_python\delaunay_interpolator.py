#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay插值器核心模块

基于Delaunay三角剖分分析结果进行空间插值
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import warnings
import gc

warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class DelaunayInterpolator:
    """Delaunay插值器"""
    
    def __init__(self, config, data_loader, evaluation_metrics):
        self.config = config
        self.data_loader = data_loader
        self.evaluation_metrics = evaluation_metrics
        self.interpolation_cache = {}
        
    def interpolate_station_at_time(self, target_station_id: str, timestamp: pd.Timestamp, 
                                  station_data: Dict[str, pd.DataFrame]) -> Dict:
        """
        对指定站点在指定时间进行插值
        
        Args:
            target_station_id: 目标站点ID
            timestamp: 时间戳
            station_data: 所有站点数据
            
        Returns:
            插值结果字典
        """
        try:
            # 获取目标站点信息
            station_info = self.data_loader.get_station_info(target_station_id)
            if not station_info:
                logger.warning(f"未找到站点{target_station_id}的Delaunay分析信息")
                return {'success': False, 'error': 'Station not found in Delaunay analysis'}
            
            # 获取包围站点信息
            surrounding_stations = station_info['surrounding_stations']
            
            # 收集包围站点在指定时间的降雨数据
            surrounding_values = []
            surrounding_weights = []
            valid_stations = []
            
            for station_info_item in surrounding_stations:
                station_id = station_info_item['id']
                weight = station_info_item['weight']
                
                # 检查站点数据是否存在
                if station_id not in station_data:
                    logger.debug(f"包围站点{station_id}数据不存在")
                    continue
                
                station_df = station_data[station_id]
                
                # 检查时间戳是否存在
                if timestamp not in station_df.index:
                    logger.debug(f"包围站点{station_id}在时间{timestamp}无数据")
                    continue
                
                # 获取降雨值
                rainfall_value = station_df.loc[timestamp, '雨量']
                
                # 检查是否为有效值
                if pd.isna(rainfall_value):
                    logger.debug(f"包围站点{station_id}在时间{timestamp}降雨值为NaN")
                    continue
                
                surrounding_values.append(rainfall_value)
                surrounding_weights.append(weight)
                valid_stations.append(station_id)
            
            # 检查是否有足够的有效站点
            if len(valid_stations) < self.config.MIN_STATIONS:
                logger.debug(f"站点{target_station_id}在时间{timestamp}的有效包围站点不足")
                return {
                    'success': False, 
                    'error': f'Insufficient valid surrounding stations: {len(valid_stations)}'
                }
            
            # 进行加权插值
            surrounding_values = np.array(surrounding_values)
            surrounding_weights = np.array(surrounding_weights)
            
            # 归一化权重（确保权重和为1）
            weight_sum = np.sum(surrounding_weights)
            if weight_sum > 0:
                normalized_weights = surrounding_weights / weight_sum
            else:
                # 如果权重和为0，使用等权重
                normalized_weights = np.ones(len(surrounding_weights)) / len(surrounding_weights)
            
            # 计算插值结果
            interpolated_value = np.sum(surrounding_values * normalized_weights)
            
            # 获取实际观测值（如果存在）
            actual_value = np.nan
            if target_station_id in station_data:
                target_df = station_data[target_station_id]
                if timestamp in target_df.index:
                    actual_value = target_df.loc[timestamp, '雨量']
            
            # 返回结果
            result = {
                'success': True,
                'target_station_id': target_station_id,
                'timestamp': timestamp,
                'interpolated_value': interpolated_value,
                'actual_value': actual_value,
                'surrounding_stations': valid_stations,
                'surrounding_values': surrounding_values.tolist(),
                'weights': normalized_weights.tolist(),
                'original_weights': surrounding_weights.tolist(),
                'error': interpolated_value - actual_value if not pd.isna(actual_value) else np.nan
            }
            
            return result
            
        except Exception as e:
            logger.error(f"站点{target_station_id}在时间{timestamp}插值失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def interpolate_station_timeseries(self, target_station_id: str,
                                     station_data: Dict[str, pd.DataFrame],
                                     memory_efficient: bool = True) -> pd.DataFrame:
        """
        对指定站点的整个时间序列进行插值（内存优化版本）

        Args:
            target_station_id: 目标站点ID
            station_data: 所有站点数据
            memory_efficient: 是否使用内存优化模式

        Returns:
            插值结果DataFrame
        """
        try:
            logger.info(f"开始对站点{target_station_id}进行时间序列插值")

            # 获取公共时间范围
            start_time, end_time = self.data_loader.get_common_time_range(station_data)
            if start_time is None or end_time is None:
                logger.warning(f"无法确定公共时间范围")
                return pd.DataFrame()

            # 生成时间序列
            time_range = pd.date_range(start=start_time, end=end_time, freq='H')

            if memory_efficient and hasattr(self.config, 'MEMORY_EFFICIENT_MODE') and self.config.MEMORY_EFFICIENT_MODE:
                # 内存优化模式：分块处理时间序列
                chunk_size = getattr(self.config, 'CHUNK_SIZE', 100)
                all_results = []
                successful_interpolations = 0

                for i in range(0, len(time_range), chunk_size):
                    chunk_times = time_range[i:i + chunk_size]
                    chunk_results = []

                    for timestamp in chunk_times:
                        # 进行插值
                        result = self.interpolate_station_at_time(target_station_id, timestamp, station_data)

                        if result['success']:
                            chunk_results.append({
                                'timestamp': timestamp,
                                'observed': np.float32(result['actual_value']) if not pd.isna(result['actual_value']) else np.nan,
                                'predicted': np.float32(result['interpolated_value']),
                                'error': np.float32(result['error']) if not pd.isna(result['error']) else np.nan,
                                'surrounding_stations_count': len(result['surrounding_stations'])
                            })
                            successful_interpolations += 1
                        else:
                            # 记录失败的插值
                            chunk_results.append({
                                'timestamp': timestamp,
                                'observed': np.nan,
                                'predicted': np.nan,
                                'error': np.nan,
                                'surrounding_stations_count': 0
                            })

                    all_results.extend(chunk_results)

                    # 定期清理内存
                    if i % (chunk_size * 5) == 0:
                        gc.collect()

                # 转换为DataFrame
                results_df = pd.DataFrame(all_results, dtype={'observed': 'float32', 'predicted': 'float32', 'error': 'float32'})
                results_df = results_df.set_index('timestamp')

            else:
                # 原始模式
                results = []
                successful_interpolations = 0

                for timestamp in time_range:
                    # 进行插值
                    result = self.interpolate_station_at_time(target_station_id, timestamp, station_data)

                    if result['success']:
                        results.append({
                            'timestamp': timestamp,
                            'observed': result['actual_value'],
                            'predicted': result['interpolated_value'],
                            'error': result['error'],
                            'surrounding_stations_count': len(result['surrounding_stations'])
                        })
                        successful_interpolations += 1
                    else:
                        # 记录失败的插值
                        results.append({
                            'timestamp': timestamp,
                            'observed': np.nan,
                            'predicted': np.nan,
                            'error': np.nan,
                            'surrounding_stations_count': 0
                        })

                # 转换为DataFrame
                results_df = pd.DataFrame(results)
                results_df = results_df.set_index('timestamp')

            logger.info(f"站点{target_station_id}插值完成: {successful_interpolations}/{len(time_range)}个时刻成功")

            return results_df

        except Exception as e:
            logger.error(f"站点{target_station_id}时间序列插值失败: {e}")
            return pd.DataFrame()
    
    def interpolate_flood_event(self, event_name: str, memory_efficient: bool = True) -> Dict[str, pd.DataFrame]:
        """
        对整个洪水事件进行插值（内存优化版本）

        Args:
            event_name: 洪水事件名称
            memory_efficient: 是否使用内存优化模式

        Returns:
            所有站点的插值结果字典
        """
        try:
            logger.info(f"开始处理洪水事件: {event_name}")

            # 记录内存使用情况
            if hasattr(self.data_loader, 'get_memory_usage'):
                memory_before = self.data_loader.get_memory_usage()
                logger.info(f"处理前内存使用: RSS={memory_before['rss']:.1f}MB, VMS={memory_before['vms']:.1f}MB")

            # 加载洪水事件数据
            try:
                station_data = self.data_loader.load_flood_event_data(event_name, memory_efficient=memory_efficient)
                if not station_data:
                    logger.warning(f"洪水事件{event_name}无可用数据")
                    return {}
                logger.info(f"成功加载{len(station_data)}个站点的数据")
            except Exception as e:
                logger.error(f"加载洪水事件{event_name}数据失败: {e}")
                return {}

            # 验证和清理数据
            try:
                station_data = self.data_loader.validate_station_data(station_data)
                logger.info(f"数据验证完成，有效站点数: {len(station_data)}")
            except Exception as e:
                logger.error(f"验证洪水事件{event_name}数据失败: {e}")
                return {}

            # 获取可用的验证站点
            try:
                available_stations = self.data_loader.get_available_stations_for_event(event_name)
                if not available_stations:
                    logger.warning(f"洪水事件{event_name}无可用验证站点")
                    return {}
                logger.info(f"洪水事件{event_name}有{len(available_stations)}个可用验证站点")
            except Exception as e:
                logger.error(f"获取洪水事件{event_name}可用站点失败: {e}")
                return {}

            # 对每个验证站点进行插值
            event_results = {}

            if memory_efficient and hasattr(self.config, 'MEMORY_EFFICIENT_MODE') and self.config.MEMORY_EFFICIENT_MODE:
                # 内存优化模式：分批处理站点
                batch_size = getattr(self.config, 'BATCH_SIZE', 10)
                clear_frequency = getattr(self.config, 'CLEAR_CACHE_FREQUENCY', 5)

                for i, station_id in enumerate(available_stations):
                    try:
                        logger.info(f"处理站点: {station_id} ({i+1}/{len(available_stations)})")

                        # 进行时间序列插值
                        station_results = self.interpolate_station_timeseries(station_id, station_data, memory_efficient=True)

                        if not station_results.empty:
                            event_results[station_id] = station_results

                            # 计算站点评估指标
                            metrics = self.evaluation_metrics.evaluate_station_performance(station_results)
                            logger.info(f"站点{station_id}评估指标: NSE={metrics.get('NSE', np.nan):.4f}, "
                                      f"MAE={metrics.get('MAE', np.nan):.4f}, "
                                      f"RMSE={metrics.get('RMSE', np.nan):.4f}")
                        else:
                            logger.warning(f"站点{station_id}插值结果为空")

                        # 定期清理内存
                        if (i + 1) % clear_frequency == 0:
                            gc.collect()
                            if hasattr(self.data_loader, 'get_memory_usage'):
                                memory_current = self.data_loader.get_memory_usage()
                                logger.debug(f"处理{i+1}个站点后内存使用: RSS={memory_current['rss']:.1f}MB")

                    except Exception as e:
                        logger.error(f"处理站点{station_id}失败: {e}")
                        continue
            else:
                # 原始模式
                for station_id in available_stations:
                    try:
                        logger.info(f"处理站点: {station_id}")

                        # 进行时间序列插值
                        station_results = self.interpolate_station_timeseries(station_id, station_data, memory_efficient=False)

                        if not station_results.empty:
                            event_results[station_id] = station_results

                            # 计算站点评估指标
                            metrics = self.evaluation_metrics.evaluate_station_performance(station_results)
                            logger.info(f"站点{station_id}评估指标: NSE={metrics.get('NSE', np.nan):.4f}, "
                                      f"MAE={metrics.get('MAE', np.nan):.4f}, "
                                      f"RMSE={metrics.get('RMSE', np.nan):.4f}")
                        else:
                            logger.warning(f"站点{station_id}插值结果为空")

                    except Exception as e:
                        logger.error(f"处理站点{station_id}失败: {e}")
                        continue

            # 清理站点数据以释放内存
            del station_data
            gc.collect()

            logger.info(f"洪水事件{event_name}处理完成，成功处理{len(event_results)}个站点")

            # 记录处理后内存使用情况
            if hasattr(self.data_loader, 'get_memory_usage'):
                memory_after = self.data_loader.get_memory_usage()
                logger.info(f"处理后内存使用: RSS={memory_after['rss']:.1f}MB, VMS={memory_after['vms']:.1f}MB")

            return event_results

        except Exception as e:
            logger.error(f"处理洪水事件{event_name}失败: {e}")
            return {}
    
    def batch_interpolate_events(self, event_names: List[str] = None) -> Dict[str, Dict[str, pd.DataFrame]]:
        """
        批量处理多个洪水事件
        
        Args:
            event_names: 洪水事件名称列表，如果为None则处理所有事件
            
        Returns:
            所有事件的插值结果
        """
        try:
            if event_names is None:
                event_names = self.config.get_flood_events()
            
            logger.info(f"开始批量处理{len(event_names)}个洪水事件")
            
            all_results = {}
            
            for event_name in event_names:
                try:
                    logger.info(f"处理洪水事件: {event_name}")
                    
                    # 处理单个事件
                    event_results = self.interpolate_flood_event(event_name)
                    
                    if event_results:
                        all_results[event_name] = event_results
                        logger.info(f"洪水事件{event_name}处理成功")
                    else:
                        logger.warning(f"洪水事件{event_name}处理失败或无结果")
                        
                except Exception as e:
                    logger.error(f"处理洪水事件{event_name}失败: {e}")
                    continue
            
            logger.info(f"批量处理完成，成功处理{len(all_results)}个洪水事件")
            
            return all_results

        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return {}

    def get_interpolation_summary(self, all_results: Dict[str, Dict[str, pd.DataFrame]]) -> Dict:
        """
        获取插值结果汇总

        Args:
            all_results: 所有事件的插值结果

        Returns:
            汇总统计信息
        """
        try:
            summary = {
                'total_events': len(all_results),
                'total_stations': 0,
                'total_interpolations': 0,
                'successful_interpolations': 0,
                'events_summary': {}
            }

            for event_name, event_results in all_results.items():
                event_summary = {
                    'stations_count': len(event_results),
                    'total_points': 0,
                    'successful_points': 0
                }

                for station_id, station_results in event_results.items():
                    total_points = len(station_results)
                    successful_points = station_results['predicted'].notna().sum()

                    event_summary['total_points'] += total_points
                    event_summary['successful_points'] += successful_points

                summary['events_summary'][event_name] = event_summary
                summary['total_stations'] += event_summary['stations_count']
                summary['total_interpolations'] += event_summary['total_points']
                summary['successful_interpolations'] += event_summary['successful_points']

            # 计算成功率
            if summary['total_interpolations'] > 0:
                summary['success_rate'] = summary['successful_interpolations'] / summary['total_interpolations']
            else:
                summary['success_rate'] = 0.0

            return summary

        except Exception as e:
            logger.error(f"生成插值汇总失败: {e}")
            return {}

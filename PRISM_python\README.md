# PRISM空间插值系统详细使用指南

## 🎯 系统简介

本系统实现了基于PRISM方法的降雨空间插值，主要功能包括：

1. **Delaunay三角网构建**：自动构建高质量的站点三角网络
2. **莫兰指数权重计算**：基于空间自相关性计算权重
3. **地形特征权重**：高程、坡度、坡向综合权重计算
4. **PRISM插值算法**：实现高精度的空间插值
5. **留一法验证**：自动验证插值精度
6. **评价指标计算**：计算MAE、RMSE、NSE、R²等指标
7. **栅格输出**：生成ASC格式的栅格文件
8. **批量处理**：支持多个洪水事件的批量处理
9. **参数优化**：自动优化插值参数以提高精度
10. **并行计算**：12核CPU并行处理支持

## 🖥️ 系统要求

### Python版本
- Python 3.7 或更高版本

### 依赖包
```bash
pip install -r requirements.txt
```

### 硬件要求
- **推荐配置**：12核CPU，16GB以上内存，SSD硬盘
- **最低配置**：4核CPU，8GB内存
- **存储空间**：至少10GB可用空间（批量处理需要更多）

### 📁 数据要求

#### 1. 文件夹结构
```
D:/pythondata/spatial_interpolation/
├── input_another/                  # 降雨数据根目录
│   ├── 2009-1/                    # 洪水事件文件夹
│   │   ├── 80606500.csv           # 站点降雨文件
│   │   ├── 80607800.csv
│   │   └── ...
│   ├── 2009-2/                    # 其他洪水事件
│   ├── 2015-3/
│   └── ...
├── stations.csv                   # 站点信息文件
├── terrain/90/                    # 地形数据文件夹
│   ├── dem.asc                    # 数字高程模型
│   ├── slope.asc                  # 坡度数据
│   └── aspect.asc                 # 坡向数据
└── output/PRISM/                  # 输出目录（自动创建）
    ├── 2009-1/                   # 对应的输出文件夹
    └── ...
```

#### 2. 数据格式要求

**站点信息文件 (stations.csv)**
```csv
站点,经度,纬度
80606500,110.606944,24.090833
80607800,110.513055,24.231666
80608000,110.445833,24.275000
...
```

**降雨数据文件 (如 80606500.csv)**
```csv
时间,雨量
2009-04-16 03:00:00,0.0
2009-04-16 04:00:00,0.0
2009-04-16 05:00:00,4.0
2009-04-16 06:00:00,8.5
...
```

**地形数据文件**
- `dem.asc`: 标准ASC格式的数字高程模型
- `slope.asc`: 标准ASC格式的坡度数据
- `aspect.asc`: 标准ASC格式的坡向数据

## 快速开始

### 方法一：直接运行（推荐新手）

1. 确保您的数据文件按以下结构组织：
```
D:/pythondata/spatial_interpolation/
├── input_another/2009-1/          # 点雨量数据文件夹
│   ├── 80606500.csv               # 站点雨量文件
│   ├── 80607800.csv
│   └── ...
├── stations.csv                   # 站点信息文件
├── terrain/90/                    # 地形数据文件夹
│   ├── dem.asc                    # 数字高程模型
│   ├── slope.asc                  # 坡度数据
│   └── aspect.asc                 # 坡向数据
└── output/PRISM/2009-1/           # 输出目录（自动创建）
```

2. 运行主程序：
```bash
python run_prism.py
```

3. 选择"使用默认配置运行"，系统会自动处理

### 方法二：交互式配置

1. 运行主程序：
```bash
python run_prism.py --setup
```

2. 根据提示输入您的文件路径和参数

### 方法三：使用配置文件

1. 创建配置文件：
```bash
python run_prism.py
```
选择"创建配置文件模板"

2. 编辑配置文件 `prism_config.json`

3. 使用配置文件运行：
```bash
python run_prism.py --config prism_config.json
```

### 方法四：批量处理
```bash
python run_prism.py --mode batch
```

## 详细使用说明

### 1. 数据准备

#### 文件夹结构
```
D:/pythondata/spatial_interpolation/
├── input_another/
│   ├── 2009-1/          # 降雨数据文件夹
│   │   ├── 80606500.csv # 站点降雨数据
│   │   ├── 80606600.csv
│   │   └── ...
│   ├── 2015-3/          # 其他降雨事件
│   └── ...
├── terrain/90/
│   ├── dem.asc          # 数字高程模型
│   ├── slope.asc        # 坡度数据
│   └── aspect.asc       # 坡向数据
├── stations.csv         # 站点信息
└── output/PRISM/        # 输出目录
```

#### 降雨数据格式
每个CSV文件代表一个站点，包含两列：
```csv
时间,雨量
2009-07-01 00:00:00,0.0
2009-07-01 01:00:00,2.5
2009-07-01 02:00:00,5.2
...
```

#### 站点信息格式
```csv
站点,经度,纬度
80606500,108.123,23.456
80606600,108.234,23.567
...
```

### 2. 配置参数

#### 主要路径配置
```json
{
    "input_dir": "D:/pythondata/spatial_interpolation/input_another/2009-1",
    "terrain_dir": "D:/pythondata/spatial_interpolation/terrain/90",
    "output_dir": "D:/pythondata/spatial_interpolation/output/PRISM/2009-1",
    "stations_file": "D:/pythondata/spatial_interpolation/stations.csv"
}
```

#### 插值参数
```json
{
    "neighbor_count": 3,           // 邻近站点数量
    "distance_power": 2.0,         // 距离权重指数
    "elevation_weight": 0.4,       // 高程权重系数
    "slope_weight": 0.3,           // 坡度权重系数
    "aspect_weight": 0.2,          // 坡向权重系数
    "moran_weight": 0.1,           // 莫兰指数权重系数
    "rainfall_threshold": 0.5      // 降雨阈值(mm)
}
```

#### 计算参数
```json
{
    "num_cores": 12,               // 并行核心数
    "batch_size": 20,              // 批处理大小
    "memory_efficient": true       // 内存优化
}
```

#### 输出控制
```json
{
    "output_raster": true,         // 是否输出栅格
    "output_delaunay_plot": true,  // 是否输出三角网图
    "output_evaluation": true      // 是否输出评价指标
}
```

### 3. 运行模式

#### 单文件夹处理
处理单个降雨事件的插值：
```bash
python run_prism.py --mode single
```

#### 批量处理
自动处理多个降雨事件：
```bash
python run_prism.py --mode batch
```

批量处理会自动发现input_another目录下的所有子文件夹，并逐一处理。

### 4. 输出结果

#### 文件结构
```
output/PRISM/2009-1/
├── points/              # 站点插值结果
│   ├── 80606500.csv
│   └── ...
├── rasters/             # 栅格插值结果
│   ├── rainfall_20090701_000000.asc
│   └── ...
├── plots/               # 可视化图表
│   ├── delaunay_triangulation.png
│   └── scatter_plot_*.png
└── evaluation/          # 评价指标
    ├── evaluation_report_*.txt
    ├── detailed_metrics_*.csv
    └── weights_*.csv
```

#### 评价指标说明
- **MAE**: 平均绝对误差，越小越好
- **RMSE**: 均方根误差，越小越好
- **NSE**: 纳什效率系数，>0.75表示很好，>0.5表示满意
- **R**: 相关系数，越接近1越好
- **R²**: 决定系数，越接近1越好

## 参数调整指南

### 新手用户
1. 只需修改配置文件中的路径设置
2. 其他参数使用默认值即可

### 高级用户
根据数据特点调整以下参数：

#### 地形权重调整
- 山区数据：增加elevation_weight
- 平原数据：减少elevation_weight，增加distance权重

#### 站点选择优化
- 站点密集：减少neighbor_count
- 站点稀疏：增加neighbor_count

#### 性能优化
- 大数据集：启用memory_efficient
- 多核CPU：增加num_cores

## 常见问题

### Q1: 程序运行很慢怎么办？
A: 
1. 增加num_cores参数使用更多CPU核心
2. 启用memory_efficient选项
3. 减少batch_size
4. 如果不需要栅格输出，设置output_raster为false

### Q2: NSE值为负数怎么办？
A: 
1. 检查数据质量，确保站点坐标正确
2. 调整地形权重系数
3. 增加邻近站点数量
4. 检查降雨数据是否有异常值

### Q3: 内存不足怎么办？
A: 
1. 启用memory_efficient选项
2. 减少batch_size
3. 分批处理数据
4. 关闭不必要的输出选项

### Q4: 找不到站点怎么办？
A: 
1. 检查stations.csv文件编码（建议使用UTF-8）
2. 确保站点名称与文件名一致
3. 检查坐标是否在地形数据范围内

## 技术支持

如有问题，请检查：
1. 日志文件：prism_interpolation.log
2. 数据格式是否正确
3. 路径设置是否正确
4. 依赖包是否完整安装

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现PRISM核心算法
- 支持Delaunay三角网站点选择
- 支持莫兰指数分析
- 支持并行计算
- 支持批量处理

## 许可证

本项目仅供学习和研究使用。

## 致谢

感谢以下文献的理论支持：
- 《珠江流域实时监测雨量数据融合方法应用研究》- 卢康明
- 《PRISM模型在复杂地形月降雨空间插值中的可行性研究》- 蒋育昊

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Kriging空间插值系统 - 主运行脚本
简单易用的Kriging插值系统入口

使用方法:
1. 直接运行: python run_kriging.py
2. 交互式设置: python run_kriging.py --setup
3. 批量处理: python run_kriging.py --mode batch
4. 指定配置: python run_kriging.py --config my_config.json

作者: AI助手
版本: 1.0
"""

import os
import sys
import json
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from easy_run import main, run_kriging_interpolation, interactive_setup, create_default_config_file


def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'numpy', 'pandas', 'scipy', 'matplotlib', 'scikit-learn', 
        'seaborn', 'rasterio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("错误: 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def show_welcome():
    """显示欢迎信息"""
    print("="*70)
    print("                    Kriging空间插值系统")
    print("              基于文献方法的降雨空间插值")
    print("="*70)
    print("功能特点:")
    print("  ✓ 基于Delaunay三角网的参证站选择")
    print("  ✓ 莫兰指数空间自相关权重")
    print("  ✓ 多种半变异函数模型支持")
    print("  ✓ 留一法交叉验证")
    print("  ✓ 24核并行计算支持")
    print("  ✓ 自动参数优化")
    print("  ✓ 批量处理支持")
    print("="*70)


def quick_start():
    """快速开始向导"""
    print("\n快速开始向导")
    print("-"*30)
    
    # 检查是否存在配置文件
    config_file = "kriging_config.json"
    
    if os.path.exists(config_file):
        print(f"发现配置文件: {config_file}")
        
        # 询问运行模式
        print("\n请选择运行模式:")
        print("1. 单文件夹处理")
        print("2. 批量处理")
        print("3. 重新配置")
        print("4. 退出")
        
        while True:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                print("\n开始单文件夹处理...")
                return run_kriging_interpolation(config_file, "single")
            elif choice == '2':
                print("\n开始批量处理...")
                return run_kriging_interpolation(config_file, "batch")
            elif choice == '3':
                print("\n启动交互式配置...")
                return interactive_setup()
            elif choice == '4':
                print("退出程序")
                return True
            else:
                print("无效选择，请重新输入")
    else:
        print(f"未发现配置文件，将创建默认配置: {config_file}")
        
        # 询问是否使用交互式设置
        use_interactive = input("是否使用交互式设置? (y/n) [y]: ").strip().lower()
        
        if use_interactive != 'n':
            return interactive_setup()
        else:
            # 创建默认配置文件
            create_default_config_file(config_file)
            print(f"\n默认配置文件已创建: {config_file}")
            print("请根据您的数据路径修改配置文件，然后重新运行程序")
            return True


def show_system_info():
    """显示系统信息"""
    try:
        import numpy as np
        import pandas as pd
        import scipy
        import matplotlib
        import sklearn
        
        print("\n系统环境信息:")
        print(f"  Python版本: {sys.version.split()[0]}")
        print(f"  NumPy版本: {np.__version__}")
        print(f"  Pandas版本: {pd.__version__}")
        print(f"  SciPy版本: {scipy.__version__}")
        print(f"  Matplotlib版本: {matplotlib.__version__}")
        print(f"  Scikit-learn版本: {sklearn.__version__}")
        
        # 检查可选包
        try:
            import rasterio
            print(f"  Rasterio版本: {rasterio.__version__}")
        except ImportError:
            print("  Rasterio: 未安装 (栅格输出功能将不可用)")
        
        try:
            import seaborn as sns
            print(f"  Seaborn版本: {sns.__version__}")
        except ImportError:
            print("  Seaborn: 未安装 (高级可视化功能将不可用)")
        
    except Exception as e:
        print(f"获取系统信息失败: {e}")


def main_entry():
    """主入口函数"""
    try:
        # 显示欢迎信息
        show_welcome()
        
        # 检查依赖
        if not check_dependencies():
            return False
        
        # 显示系统信息
        show_system_info()
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            # 有命令行参数，使用原始main函数
            main()
        else:
            # 无命令行参数，使用快速开始向导
            success = quick_start()
            if success:
                print("\n程序执行完成！")
            else:
                print("\n程序执行失败！")
                return False
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n用户中断程序执行")
        return False
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        return False


def show_usage_examples():
    """显示使用示例"""
    examples = """
使用示例:
=========

1. 快速开始（推荐新手）:
   python run_kriging.py

2. 交互式设置:
   python run_kriging.py --setup

3. 使用自定义配置文件:
   python run_kriging.py --config my_config.json

4. 批量处理模式:
   python run_kriging.py --mode batch

5. 显示详细帮助:
   python run_kriging.py --help-detail

配置文件示例:
============
{
    "input_dir": "D:/pythondata/spatial_interpolation/input_another/2009-1",
    "output_dir": "D:/pythondata/spatial_interpolation/output/Kriging/2009-1",
    "stations_file": "D:/pythondata/spatial_interpolation/stations.csv",
    "variogram_model": "spherical",
    "neighbor_count": 3,
    "num_cores": 24,
    "enable_moran_weighting": true,
    "enable_parameter_optimization": true
}

参数说明:
========
- variogram_model: 半变异函数模型 (spherical/exponential/gaussian/linear)
- neighbor_count: 邻近站点数量
- num_cores: 并行核心数
- enable_moran_weighting: 是否启用莫兰指数权重
- enable_parameter_optimization: 是否启用参数优化
    """
    print(examples)


if __name__ == "__main__":
    # 检查是否请求帮助
    if len(sys.argv) > 1 and ('--help' in sys.argv or '-h' in sys.argv):
        show_usage_examples()
        sys.exit(0)
    
    success = main_entry()
    sys.exit(0 if success else 1)

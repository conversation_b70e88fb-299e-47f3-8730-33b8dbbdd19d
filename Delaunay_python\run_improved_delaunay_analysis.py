#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行改进的Delaunay插值分析

新增功能：
1. NSE监控：当NSE < -10时自动切换为平等权重
2. 权重调整记录和报告
3. 改进效果统计分析

作者: 空间插值研究团队
日期: 2024年12月
版本: 2.0
"""

import sys
import os
from pathlib import Path
import logging
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入现有模块
try:
    from config import DelaunayConfig
    from data_loader import DelaunayDataLoader
    from evaluation_metrics import EvaluationMetrics
    from improved_delaunay_interpolator import ImprovedDelaunayInterpolator
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必要的模块文件存在")
    sys.exit(1)

class ImprovedDelaunayAnalysisRunner:
    """改进的Delaunay分析运行器"""
    
    def __init__(self):
        self.config = DelaunayConfig()
        self.data_loader = DelaunayDataLoader(self.config)
        self.evaluation_metrics = EvaluationMetrics(self.config)
        self.interpolator = ImprovedDelaunayInterpolator(self.config, self.data_loader, self.evaluation_metrics)
        
        # 设置日志
        self.setup_logging()
        
        # 创建输出目录
        self.output_dir = Path(self.config.OUTPUT_DIR)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.improved_output_dir = self.output_dir / 'improved_analysis'
        self.improved_output_dir.mkdir(parents=True, exist_ok=True)
        
    def setup_logging(self):
        """设置日志"""
        log_dir = Path(self.config.OUTPUT_DIR) / 'logs'
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / f'improved_delaunay_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("改进的Delaunay分析系统启动")
    
    def run_single_event_analysis(self, event_name: str) -> bool:
        """运行单个洪水事件的改进分析"""
        try:
            self.logger.info(f"开始分析洪水事件: {event_name}")
            
            # 使用改进的插值器进行分析
            event_results = self.interpolator.interpolate_flood_event_with_monitoring(
                event_name, memory_efficient=True)
            
            if not event_results:
                self.logger.warning(f"洪水事件{event_name}无插值结果")
                return False
            
            # 保存插值结果
            self.save_event_results(event_name, event_results)
            
            # 计算和保存评价指标
            self.calculate_and_save_metrics(event_name, event_results)
            
            self.logger.info(f"洪水事件{event_name}分析完成")
            return True
            
        except Exception as e:
            self.logger.error(f"分析洪水事件{event_name}失败: {e}")
            return False
    
    def save_event_results(self, event_name: str, event_results: dict):
        """保存事件插值结果"""
        try:
            event_dir = self.improved_output_dir / 'interpolation_results' / event_name
            event_dir.mkdir(parents=True, exist_ok=True)
            
            for station_id, results_df in event_results.items():
                if not results_df.empty:
                    output_file = event_dir / f'{station_id}_interpolation.csv'
                    results_df.to_csv(output_file, encoding='utf-8')
                    self.logger.debug(f"保存站点{station_id}插值结果: {output_file}")
            
            self.logger.info(f"事件{event_name}插值结果已保存到: {event_dir}")
            
        except Exception as e:
            self.logger.error(f"保存事件{event_name}插值结果失败: {e}")
    
    def calculate_and_save_metrics(self, event_name: str, event_results: dict):
        """计算和保存评价指标"""
        try:
            metrics_dir = self.improved_output_dir / 'metrics'
            metrics_dir.mkdir(parents=True, exist_ok=True)
            
            event_metrics = []
            
            for station_id, results_df in event_results.items():
                if results_df.empty:
                    continue
                
                # 计算评价指标
                metrics = self.evaluation_metrics.evaluate_station_performance(results_df)
                
                # 添加事件和站点信息
                metrics['事件名称'] = event_name
                metrics['站点代码'] = station_id
                
                # 检查是否使用了权重调整
                weight_methods = results_df['weight_method'].unique()
                metrics['使用平等权重'] = 'equal' in weight_methods or 'equal_fallback' in weight_methods
                
                event_metrics.append(metrics)
            
            if event_metrics:
                # 保存为CSV文件
                metrics_df = pd.DataFrame(event_metrics)
                metrics_file = metrics_dir / f'{event_name}_improved_metrics.csv'
                metrics_df.to_csv(metrics_file, index=False, encoding='utf-8')
                
                self.logger.info(f"事件{event_name}评价指标已保存: {metrics_file}")
            
        except Exception as e:
            self.logger.error(f"计算事件{event_name}评价指标失败: {e}")
    
    def run_all_events_analysis(self) -> bool:
        """运行所有洪水事件的改进分析"""
        try:
            self.logger.info("开始运行所有洪水事件的改进分析")
            
            # 获取所有可用的洪水事件
            available_events = self.data_loader.get_available_flood_events()
            if not available_events:
                self.logger.warning("未找到可用的洪水事件")
                return False
            
            self.logger.info(f"找到{len(available_events)}个洪水事件")
            
            # 分析每个事件
            successful_events = 0
            failed_events = []
            
            for i, event_name in enumerate(available_events):
                self.logger.info(f"处理事件 {i+1}/{len(available_events)}: {event_name}")
                
                try:
                    success = self.run_single_event_analysis(event_name)
                    if success:
                        successful_events += 1
                    else:
                        failed_events.append(event_name)
                        
                except Exception as e:
                    self.logger.error(f"处理事件{event_name}时发生异常: {e}")
                    failed_events.append(event_name)
            
            # 生成权重调整汇总报告
            self.interpolator.save_comprehensive_weight_adjustment_summary()
            
            # 生成分析汇总报告
            self.generate_analysis_summary(successful_events, failed_events, available_events)
            
            self.logger.info(f"所有事件分析完成: 成功{successful_events}个，失败{len(failed_events)}个")
            
            return successful_events > 0
            
        except Exception as e:
            self.logger.error(f"运行所有事件分析失败: {e}")
            return False
    
    def generate_analysis_summary(self, successful_count: int, failed_events: list, all_events: list):
        """生成分析汇总报告"""
        try:
            summary_file = self.improved_output_dir / 'analysis_summary.txt'
            
            # 获取权重调整统计
            weight_stats = self.interpolator.get_weight_adjustment_statistics()
            
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("="*80 + "\n")
                f.write("Delaunay插值系统改进分析汇总报告\n")
                f.write("Improved Delaunay Interpolation Analysis Summary Report\n")
                f.write("="*80 + "\n\n")
                
                f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"NSE监控阈值: {self.interpolator.nse_threshold}\n\n")
                
                # 基本统计
                f.write("基本统计 Basic Statistics:\n")
                f.write("-" * 40 + "\n")
                f.write(f"总事件数: {len(all_events)}\n")
                f.write(f"成功分析事件数: {successful_count}\n")
                f.write(f"失败事件数: {len(failed_events)}\n")
                f.write(f"成功率: {successful_count/len(all_events)*100:.2f}%\n\n")
                
                # 权重调整统计
                f.write("权重调整统计 Weight Adjustment Statistics:\n")
                f.write("-" * 40 + "\n")
                f.write(f"总调整次数: {weight_stats.get('total_adjustments', 0)}\n")
                f.write(f"调整站点数: {weight_stats.get('adjusted_stations_count', 0)}\n")
                
                if weight_stats.get('adjusted_stations'):
                    f.write(f"调整站点列表: {', '.join(weight_stats['adjusted_stations'])}\n")
                
                if weight_stats.get('nse_improvements'):
                    improvements = weight_stats['nse_improvements']
                    f.write(f"NSE改进统计:\n")
                    f.write(f"  平均改进: {improvements['mean']:.4f}\n")
                    f.write(f"  最大改进: {improvements['max']:.4f}\n")
                    f.write(f"  最小改进: {improvements['min']:.4f}\n")
                    f.write(f"  改进次数: {improvements['count']}\n")
                
                f.write("\n")
                
                # 失败事件列表
                if failed_events:
                    f.write("失败事件列表 Failed Events:\n")
                    f.write("-" * 40 + "\n")
                    for i, event in enumerate(failed_events, 1):
                        f.write(f"{i}. {event}\n")
                    f.write("\n")
                
                # 成功事件列表
                successful_events = [event for event in all_events if event not in failed_events]
                if successful_events:
                    f.write("成功事件列表 Successful Events:\n")
                    f.write("-" * 40 + "\n")
                    for i, event in enumerate(successful_events, 1):
                        f.write(f"{i}. {event}\n")
            
            self.logger.info(f"分析汇总报告已保存: {summary_file}")
            
        except Exception as e:
            self.logger.error(f"生成分析汇总报告失败: {e}")
    
    def print_analysis_summary(self):
        """打印分析汇总信息"""
        try:
            weight_stats = self.interpolator.get_weight_adjustment_statistics()
            
            print("\n" + "="*80)
            print("Delaunay插值系统改进分析完成")
            print("="*80)
            print(f"NSE监控阈值: {self.interpolator.nse_threshold}")
            print(f"权重调整次数: {weight_stats.get('total_adjustments', 0)}")
            print(f"调整站点数: {weight_stats.get('adjusted_stations_count', 0)}")
            
            if weight_stats.get('adjusted_stations'):
                print(f"调整站点: {', '.join(weight_stats['adjusted_stations'])}")
            
            if weight_stats.get('nse_improvements'):
                improvements = weight_stats['nse_improvements']
                print(f"平均NSE改进: {improvements['mean']:.4f}")
                print(f"最大NSE改进: {improvements['max']:.4f}")
            
            print(f"\n📁 结果保存在: {self.improved_output_dir}")
            print("📊 包含文件:")
            print("  - interpolation_results/: 插值结果")
            print("  - metrics/: 评价指标")
            print("  - weight_adjustments/: 权重调整报告")
            print("  - analysis_summary.txt: 分析汇总")
            print("="*80)
            
        except Exception as e:
            self.logger.error(f"打印分析汇总失败: {e}")


def main():
    """主程序"""
    try:
        print("="*80)
        print("Delaunay插值系统改进分析")
        print("Improved Delaunay Interpolation Analysis")
        print("="*80)
        print("新增功能:")
        print("✅ NSE监控：当NSE < -10时自动切换为平等权重")
        print("✅ 权重调整记录和详细报告")
        print("✅ 改进效果统计分析")
        print("✅ 内存优化处理")
        print("="*80)
        
        # 创建分析运行器
        runner = ImprovedDelaunayAnalysisRunner()
        
        # 询问用户是否继续
        response = input("\n是否开始改进分析? (y/n): ").strip().lower()
        if response not in ['y', 'yes', '是']:
            print("分析已取消")
            return 0
        
        # 运行分析
        success = runner.run_all_events_analysis()
        
        if success:
            runner.print_analysis_summary()
            print("\n✅ 改进分析完成!")
            return 0
        else:
            print("\n❌ 改进分析失败!")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n分析被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

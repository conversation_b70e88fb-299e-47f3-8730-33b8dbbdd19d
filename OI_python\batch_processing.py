# -*- coding: utf-8 -*-
"""
批量处理模块

负责处理多个洪水事件文件夹的批量插值
"""

import os
import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Optional
from datetime import datetime

from config import Config
from oi_main import OIInterpolation
from evaluation_metrics import save_evaluation_results

class BatchProcessor:
    """
    批量处理器类
    
    用于处理多个洪水事件文件夹的批量插值
    """
    
    def __init__(self, config: Config):
        """
        初始化批量处理器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.batch_results = []
        
        # 验证批量处理配置
        if not config.batch_processing:
            raise ValueError("配置中未启用批量处理模式")
        
        if not os.path.exists(config.batch_root_dir):
            raise ValueError(f"批量处理根目录不存在: {config.batch_root_dir}")
    
    def find_event_folders(self) -> List[str]:
        """
        查找所有洪水事件文件夹
        
        Returns:
            List[str]: 事件文件夹名称列表
        """
        try:
            all_folders = []
            
            # 遍历根目录下的所有文件夹
            for item in os.listdir(self.config.batch_root_dir):
                item_path = os.path.join(self.config.batch_root_dir, item)
                if os.path.isdir(item_path):
                    # 检查文件夹中是否包含CSV文件
                    csv_files = [f for f in os.listdir(item_path) if f.endswith('.csv')]
                    if csv_files:
                        all_folders.append(item)
            
            # 如果指定了目标文件夹，只处理指定的文件夹
            if self.config.target_folders:
                target_folders = [folder for folder in all_folders if folder in self.config.target_folders]
                if not target_folders:
                    logging.warning("指定的目标文件夹中没有找到有效的事件文件夹")
                return target_folders
            
            # 排序文件夹名称
            all_folders.sort()
            
            logging.info(f"找到 {len(all_folders)} 个事件文件夹: {all_folders}")
            return all_folders
        
        except Exception as e:
            logging.error(f"查找事件文件夹时出错: {e}")
            return []
    
    def process_single_event(self, event_folder: str) -> Dict:
        """
        处理单个洪水事件
        
        Args:
            event_folder: 事件文件夹名称
        
        Returns:
            Dict: 处理结果字典
        """
        try:
            logging.info(f"开始处理事件: {event_folder}")
            
            # 创建事件专用的配置
            event_config = Config(**self.config.to_dict())
            event_config.input_dir = os.path.join(self.config.batch_root_dir, event_folder)
            event_config.output_dir = os.path.join(self.config.batch_output_root, event_folder)
            
            # 确保输出目录存在
            os.makedirs(event_config.output_dir, exist_ok=True)
            
            # 验证输入目录
            if not os.path.exists(event_config.input_dir):
                raise ValueError(f"事件输入目录不存在: {event_config.input_dir}")
            
            # 检查是否有CSV文件
            csv_files = [f for f in os.listdir(event_config.input_dir) if f.endswith('.csv')]
            if not csv_files:
                raise ValueError(f"事件目录中没有找到CSV文件: {event_config.input_dir}")
            
            # 创建OI插值对象并运行
            oi_processor = OIInterpolation(event_config)
            success = oi_processor.run_complete_workflow()
            
            if success:
                # 提取评价指标
                overall_metrics = oi_processor.results.get('overall_metrics', {})
                
                result = {
                    'event_folder': event_folder,
                    'success': True,
                    'input_dir': event_config.input_dir,
                    'output_dir': event_config.output_dir,
                    'num_stations': len(oi_processor.stations) if oi_processor.stations is not None else 0,
                    'num_time_steps': len(oi_processor.rainfall_data) if oi_processor.rainfall_data is not None else 0,
                    'mae': overall_metrics.get('mae', None),
                    'rmse': overall_metrics.get('rmse', None),
                    'r2': overall_metrics.get('r2', None),
                    'nse': overall_metrics.get('nse', None),
                    'cc': overall_metrics.get('cc', None),
                    'bias': overall_metrics.get('bias', None),
                    'processing_time': oi_processor.results.get('processing_time', None)
                }
                
                logging.info(f"事件 {event_folder} 处理成功")
                logging.info(f"  - MAE: {result.get('mae', 'N/A')}")
                logging.info(f"  - RMSE: {result.get('rmse', 'N/A')}")
                logging.info(f"  - NSE: {result.get('nse', 'N/A')}")
            else:
                result = {
                    'event_folder': event_folder,
                    'success': False,
                    'error': 'Processing workflow failed',
                    'input_dir': event_config.input_dir,
                    'output_dir': event_config.output_dir
                }
                logging.error(f"事件 {event_folder} 处理失败")
            
            return result
        
        except Exception as e:
            error_msg = f"处理事件 {event_folder} 时出错: {e}"
            logging.error(error_msg)
            
            return {
                'event_folder': event_folder,
                'success': False,
                'error': str(e),
                'input_dir': os.path.join(self.config.batch_root_dir, event_folder),
                'output_dir': os.path.join(self.config.batch_output_root, event_folder)
            }
    
    def run_batch_processing(self) -> bool:
        """
        运行批量处理
        
        Returns:
            bool: 是否成功完成批量处理
        """
        try:
            logging.info("开始批量处理...")
            
            # 查找所有事件文件夹
            event_folders = self.find_event_folders()
            
            if not event_folders:
                logging.error("没有找到可处理的事件文件夹")
                return False
            
            logging.info(f"将处理 {len(event_folders)} 个事件文件夹")
            
            # 处理每个事件
            for i, event_folder in enumerate(event_folders, 1):
                print(f"\n{'='*60}")
                print(f"处理事件 {i}/{len(event_folders)}: {event_folder}")
                print(f"{'='*60}")
                
                result = self.process_single_event(event_folder)
                self.batch_results.append(result)
                
                # 显示进度
                successful = sum(1 for r in self.batch_results if r['success'])
                failed = len(self.batch_results) - successful
                print(f"当前进度: {i}/{len(event_folders)}, 成功: {successful}, 失败: {failed}")
            
            # 保存批量处理结果
            self._save_batch_results()
            
            # 生成批量处理摘要
            self._generate_batch_summary()
            
            logging.info("批量处理完成")
            return True
        
        except Exception as e:
            logging.error(f"批量处理失败: {e}")
            return False
    
    def _save_batch_results(self):
        """
        保存批量处理结果
        """
        try:
            # 创建结果DataFrame
            results_df = pd.DataFrame(self.batch_results)
            
            # 保存到CSV文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_file = os.path.join(self.config.batch_output_root, f"batch_results_{timestamp}.csv")
            results_df.to_csv(results_file, index=False, encoding='utf-8')
            
            logging.info(f"批量处理结果已保存到: {results_file}")
            
            # 保存到JSON文件（包含更多详细信息）
            import json
            json_file = os.path.join(self.config.batch_output_root, f"batch_results_{timestamp}.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.batch_results, f, ensure_ascii=False, indent=2, default=str)
            
            logging.info(f"详细批量处理结果已保存到: {json_file}")
            
        except Exception as e:
            logging.error(f"保存批量处理结果时出错: {e}")
    
    def _generate_batch_summary(self):
        """
        生成批量处理摘要
        """
        try:
            successful_results = [r for r in self.batch_results if r['success']]
            failed_results = [r for r in self.batch_results if not r['success']]
            
            print(f"\n{'='*60}")
            print("批量处理摘要")
            print(f"{'='*60}")
            print(f"总事件数: {len(self.batch_results)}")
            print(f"成功处理: {len(successful_results)}")
            print(f"处理失败: {len(failed_results)}")
            
            if successful_results:
                # 计算统计指标
                mae_values = [r['mae'] for r in successful_results if r.get('mae') is not None]
                rmse_values = [r['rmse'] for r in successful_results if r.get('rmse') is not None]
                nse_values = [r['nse'] for r in successful_results if r.get('nse') is not None]
                r2_values = [r['r2'] for r in successful_results if r.get('r2') is not None]
                
                print(f"\n评价指标统计 (基于 {len(successful_results)} 个成功事件):")
                if mae_values:
                    print(f"MAE - 平均: {np.mean(mae_values):.4f}, 范围: {np.min(mae_values):.4f} - {np.max(mae_values):.4f}")
                if rmse_values:
                    print(f"RMSE - 平均: {np.mean(rmse_values):.4f}, 范围: {np.min(rmse_values):.4f} - {np.max(rmse_values):.4f}")
                if nse_values:
                    print(f"NSE - 平均: {np.mean(nse_values):.4f}, 范围: {np.min(nse_values):.4f} - {np.max(nse_values):.4f}")
                if r2_values:
                    print(f"R² - 平均: {np.mean(r2_values):.4f}, 范围: {np.min(r2_values):.4f} - {np.max(r2_values):.4f}")
            
            if failed_results:
                print(f"\n失败的事件:")
                for result in failed_results:
                    print(f"  - {result['event_folder']}: {result.get('error', 'Unknown error')}")
            
            print(f"{'='*60}")
            
        except Exception as e:
            logging.error(f"生成批量处理摘要时出错: {e}")

def run_batch_processing(config: Config) -> bool:
    """
    运行批量处理的便捷函数
    
    Args:
        config: 配置对象
    
    Returns:
        bool: 是否成功完成批量处理
    """
    try:
        processor = BatchProcessor(config)
        return processor.run_batch_processing()
    except Exception as e:
        logging.error(f"批量处理初始化失败: {e}")
        return False

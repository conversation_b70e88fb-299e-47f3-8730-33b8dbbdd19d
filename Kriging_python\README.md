# 🎯 Kriging空间插值系统

基于文献方法的降雨空间插值Python实现，专门为您的数据结构和需求设计。

## 🚀 系统特点

### 核心技术
- ✅ **Delaunay三角网参证站选择** - 基于文献《珠江流域实时监测雨量数据融合方法应用研究》
- ✅ **莫兰指数空间自相关权重** - 基于《地面降雨资料时空特征分析及质量控制方法研究》
- ✅ **多种半变异函数模型** - 球状、指数、高斯、线性模型支持
- ✅ **留一法交叉验证** - 严格的插值精度验证
- ✅ **自动参数优化** - 智能优化插值参数
- ✅ **24核并行计算** - 高效处理大数据集

### 验证评价
- ✅ **多指标评价体系** - NSE、RMSE、MAE、R²、相关系数
- ✅ **可视化验证** - 散点图、残差图、栅格图
- ✅ **批量处理报告** - 自动生成汇总分析报告

### 输出功能
- ✅ **栅格输出** - ASC格式栅格文件
- ✅ **批量处理** - 多个洪水事件自动处理
- ✅ **详细日志** - 完整的处理过程记录

## 📁 系统结构

```
Kriging_python/                     # 完整系统目录
├── 核心模块
│   ├── __init__.py                  # 模块初始化
│   ├── config.py                    # 配置管理
│   ├── data_processing.py           # 数据加载处理
│   ├── delaunay_triangulation.py   # Delaunay三角网
│   ├── moran_index.py              # 莫兰指数计算
│   ├── kriging_core.py             # Kriging核心算法
│   ├── evaluation_metrics.py       # 评价指标
│   ├── raster_processing.py        # 栅格处理
│   ├── parallel_processing.py      # 并行计算
│   ├── parameter_optimization.py   # 参数优化
│   └── batch_processing.py         # 批量处理
├── 运行文件
│   ├── run_kriging.py              # 主运行脚本 ⭐
│   ├── easy_run.py                 # 简易运行接口
│   ├── kriging_config.json         # 默认配置文件 ⭐
│   └── requirements.txt            # 依赖包列表
└── 文档
    └── README.md                   # 本文档
```

## 🚀 立即开始使用

### 第一步：进入系统目录
```bash
cd Kriging_python
```

### 第二步：安装依赖包
```bash
pip install -r requirements.txt
```

### 第三步：运行系统
```bash
python run_kriging.py
```

## 🎛️ 三种运行方式

### 1. 快速开始（推荐新手）
```bash
python run_kriging.py
```
- 程序会自动检查配置文件
- 如果没有配置文件，会创建默认配置
- 按提示选择运行模式

### 2. 命令行参数运行
```bash
# 单文件夹处理
python run_kriging.py --mode single

# 批量处理
python run_kriging.py --mode batch

# 交互式设置
python run_kriging.py --setup

# 指定配置文件
python run_kriging.py --config my_config.json
```

### 3. 直接调用模块
```python
from kriging_main import KrigingInterpolation
from config import Config

config = Config()
kriging = KrigingInterpolation(config)
results = kriging.run_complete_workflow()
```

## ⚙️ 配置文件说明

### 默认配置文件位置
- `kriging_config.json` - 系统自动创建的默认配置

### 主要参数说明

#### 路径配置
```json
{
    "input_dir": "D:/pythondata/spatial_interpolation/input_another/2009-1",
    "output_dir": "D:/pythondata/spatial_interpolation/output/Kriging/2009-1",
    "stations_file": "D:/pythondata/spatial_interpolation/stations.csv"
}
```

#### Kriging参数
```json
{
    "variogram_model": "spherical",     // 半变异函数模型
    "neighbor_count": 3,                // 邻近站点数量
    "kriging_type": "ordinary",         // Kriging类型
    "enable_parameter_optimization": true  // 是否启用参数优化
}
```

#### 权重配置
```json
{
    "enable_moran_weighting": true,     // 是否启用莫兰指数权重
    "moran_weight": 0.3,               // 莫兰指数权重系数
    "distance_weight": 0.7             // 距离权重系数
}
```

#### 计算参数
```json
{
    "num_cores": 24,                   // 并行核心数
    "batch_size": 50,                  // 批处理大小
    "memory_efficient": true           // 内存优化
}
```

## 📊 结果文件说明

### 单文件夹处理输出
```
output/Kriging/2009-1/
├── evaluation/                      # 评价结果
│   ├── evaluation_metrics.csv      # 评价指标
│   └── optimization_history.csv    # 优化历史
├── points/                          # 点数据结果
│   └── leave_one_out_results.csv   # 留一法结果
├── plots/                           # 图形文件
│   ├── kriging_scatter_plot.png     # 散点图
│   ├── kriging_residual_plot.png    # 残差图
│   ├── delaunay_triangulation.png   # 三角网图
│   └── kriging_raster.png          # 栅格图
├── rasters/                         # 栅格文件
│   └── kriging_interpolation.asc    # 插值栅格
└── kriging_interpolation.log        # 日志文件
```

### 批量处理输出
```
output/Kriging/
├── 2009-1/                         # 各文件夹结果
├── 2015-3/
├── ...
└── batch_summary/                   # 汇总结果
    ├── batch_detailed_results_*.csv # 详细结果
    ├── batch_summary_*.csv         # 汇总统计
    └── batch_comparison_*.png      # 对比图
```

## 🔍 评价指标解释

- **NSE** (纳什效率系数): 接近1最好，>0.75为优秀，>0.5为可接受
- **RMSE** (均方根误差): 越小越好，表示预测精度
- **MAE** (平均绝对误差): 越小越好，表示平均误差
- **R²** (决定系数): 接近1最好，表示相关性
- **CORR** (相关系数): 接近1最好，表示线性相关

## 🛠️ 参数调整指南

### 新手推荐设置
```json
{
    "variogram_model": "spherical",
    "neighbor_count": 3,
    "enable_moran_weighting": true,
    "enable_parameter_optimization": true,
    "num_cores": 12
}
```

### 高精度设置
```json
{
    "variogram_model": "spherical",
    "neighbor_count": 5,
    "enable_moran_weighting": true,
    "enable_parameter_optimization": true,
    "optimization_iterations": 10,
    "num_cores": 24
}
```

### 快速处理设置
```json
{
    "variogram_model": "spherical",
    "neighbor_count": 3,
    "enable_moran_weighting": false,
    "enable_parameter_optimization": false,
    "output_raster": false,
    "num_cores": 24
}
```

## ⚠️ 常见问题解决

### 1. 程序无法启动
```bash
# 检查Python环境
python --version

# 安装依赖包
pip install numpy pandas scipy matplotlib scikit-learn seaborn
```

### 2. 数据文件找不到
- 检查文件路径是否正确
- 确保CSV文件包含中文列名"时间"和"雨量"
- 确保stations.csv包含"站点"、"经度"、"纬度"列

### 3. 内存不足
- 减少并行核数
- 选择不保存栅格文件
- 启用内存优化选项

### 4. 处理速度慢
- 增加并行核数
- 禁用参数优化
- 减少邻近站点数量

### 5. 结果不理想
- 启用参数优化功能
- 尝试不同的半变异函数模型
- 检查站点分布是否合理

## 🎯 使用建议

### 新手入门流程
1. **第一步**: 运行 `python run_kriging.py` 验证系统
2. **第二步**: 使用交互式设置配置参数
3. **第三步**: 熟悉后使用批量处理功能

### 参数设置建议
- **初学者**: 使用默认参数即可
- **进阶用户**: 可尝试启用参数优化
- **大数据处理**: 禁用栅格输出，启用并行处理

### 性能优化建议
- **小数据集** (<100个时间点): 使用单线程，启用所有功能
- **中等数据集** (100-1000个时间点): 使用12-24核并行
- **大数据集** (>1000个时间点): 使用最大核数，禁用栅格输出

## 📞 技术支持

1. **查看日志**: 检查output目录中的.log文件
2. **检查配置**: 确认kriging_config.json中的路径设置
3. **验证数据**: 确保输入数据格式正确
4. **性能调优**: 根据硬件配置调整并行参数

## 🎉 成功标志

当您看到以下信息时，说明处理成功：
- ✅ 数据加载成功
- ✅ Delaunay三角网构建完成
- ✅ Kriging插值处理完成
- ✅ 评价指标计算完成
- ✅ 结果文件已保存

恭喜您！现在可以查看output目录中的结果文件了。

---

**记住**: 如果遇到任何问题，最简单的方法是运行 `python run_kriging.py`，按照提示进行操作！

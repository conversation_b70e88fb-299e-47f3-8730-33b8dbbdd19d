# -*- coding: utf-8 -*-
"""
测试版莫兰指数分析系统
只分析前3个洪水事件进行快速验证

作者：空间插值系统
日期：2024年
"""

import pandas as pd
import numpy as np
import math
import os
import glob
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入主分析类
from batch_molan_analysis import BatchMoranAnalysis

class TestBatchMoranAnalysis(BatchMoranAnalysis):
    """测试版批量莫兰指数分析类"""
    
    def get_flood_events(self):
        """获取前3个洪水事件进行测试"""
        flood_dirs = []
        for item in os.listdir(self.input_dir):
            item_path = os.path.join(self.input_dir, item)
            if os.path.isdir(item_path) and item != 'rains_每场一表格':
                flood_dirs.append(item)
        
        flood_dirs.sort()  # 按时间顺序排序
        test_events = flood_dirs[:3]  # 只取前3个事件进行测试
        print(f"测试模式：只分析前 {len(test_events)} 个洪水事件: {test_events}")
        return test_events
    
    def calculate_moran_index_with_significance(self, ref_station, target_station, station_list, rain_data, n_permutations=49):
        """
        快速版莫兰指数计算（减少置换次数）
        """
        # 调用父类方法，但使用更少的置换次数
        return super().calculate_moran_index_with_significance(
            ref_station, target_station, station_list, rain_data, n_permutations
        )


def main():
    """测试主函数"""
    print("测试版莫兰指数分析系统（包含显著性检验）")
    print("=" * 60)
    print("注意：这是测试版本，只分析前3个洪水事件")
    
    # 文件路径配置
    excel_file = "../水晏泰森.xlsx"
    stations_file = "../stations.csv"
    input_dir = "../input_another"
    output_dir = "../output/molan_test"

    # 站点剔除配置
    exclude_stations = False

    try:
        # 创建测试分析实例
        analyzer = TestBatchMoranAnalysis(excel_file, stations_file, input_dir, output_dir, exclude_stations)
        
        # 显示配置信息
        if exclude_stations:
            print("\n【站点剔除配置】")
            print("整体组剔除站点:", analyzer.excluded_stations['整体'])
            print("大化组剔除站点:", analyzer.excluded_stations['大化'])
            print("太平组剔除站点:", analyzer.excluded_stations['太平'])
            print("水晏组剔除站点:", analyzer.excluded_stations['水晏'])
        
        # 显示分组信息
        print("\n【站点分组信息】")
        for group_name, stations in analyzer.grouped_stations.items():
            print(f"{group_name}组: {len(stations)}个站点")
        
        # 运行测试分析
        results = analyzer.run_batch_analysis()
        
        print("\n" + "=" * 60)
        print("测试分析完成！")
        print(f"共分析了 {len(analyzer.flood_events)} 个洪水事件")
        print(f"生成了 {len(results)} 条分析记录")
        print(f"结果已保存到: {output_dir}")
        
        # 显示部分结果示例
        if results:
            print("\n【结果示例】")
            sample_result = results[0]
            print(f"洪水事件: {sample_result['flood_event']}")
            print(f"分组: {sample_result['group']}")
            print(f"参考站点: {sample_result['reference_station']}")
            print(f"最佳站点: {sample_result['top_1_station']}")
            print(f"莫兰指数: {sample_result['top_1_moran']:.4f}")
            print(f"p值: {sample_result['top_1_p_value']:.3f}")
            print(f"显著性: {sample_result['top_1_significance']}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

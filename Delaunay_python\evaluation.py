#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay三角剖分插值系统评价指标计算模块

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class InterpolationEvaluator:
    """插值结果评价器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_nse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算Nash-Sutcliffe效率系数 (NSE)
        
        NSE = 1 - Σ(Oi - Pi)² / Σ(Oi - Ō)²
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            NSE值，范围(-∞, 1]，1为完美匹配
        """
        try:
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if np.sum(mask) < 2:
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            # 计算观测值均值
            obs_mean = np.mean(obs_clean)
            
            # 计算NSE
            numerator = np.sum((obs_clean - pred_clean) ** 2)
            denominator = np.sum((obs_clean - obs_mean) ** 2)
            
            if denominator == 0:
                return np.nan
            
            nse = 1 - (numerator / denominator)
            return float(nse)
            
        except Exception as e:
            self.logger.error(f"计算NSE时出错: {e}")
            return np.nan
    
    def calculate_rmse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算均方根误差 (RMSE)
        
        RMSE = √(Σ(Oi - Pi)² / n)
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            RMSE值，越小越好
        """
        try:
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if np.sum(mask) < 1:
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            # 计算RMSE
            mse = np.mean((obs_clean - pred_clean) ** 2)
            rmse = np.sqrt(mse)
            
            return float(rmse)
            
        except Exception as e:
            self.logger.error(f"计算RMSE时出错: {e}")
            return np.nan
    
    def calculate_mae(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算平均绝对误差 (MAE)
        
        MAE = Σ|Oi - Pi| / n
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            MAE值，越小越好
        """
        try:
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if np.sum(mask) < 1:
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            # 计算MAE
            mae = np.mean(np.abs(obs_clean - pred_clean))
            
            return float(mae)
            
        except Exception as e:
            self.logger.error(f"计算MAE时出错: {e}")
            return np.nan
    
    def calculate_correlation(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算皮尔逊相关系数
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            相关系数，范围[-1, 1]
        """
        try:
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if np.sum(mask) < 2:
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            # 计算相关系数
            correlation, _ = stats.pearsonr(obs_clean, pred_clean)
            
            return float(correlation)
            
        except Exception as e:
            self.logger.error(f"计算相关系数时出错: {e}")
            return np.nan
    
    def calculate_bias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算偏差 (Bias)
        
        Bias = Σ(Pi - Oi) / n
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            偏差值，正值表示高估，负值表示低估
        """
        try:
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if np.sum(mask) < 1:
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            # 计算偏差
            bias = np.mean(pred_clean - obs_clean)
            
            return float(bias)
            
        except Exception as e:
            self.logger.error(f"计算偏差时出错: {e}")
            return np.nan
    
    def calculate_relative_bias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算相对偏差 (Relative Bias)
        
        RBias = Σ(Pi - Oi) / Σ(Oi) × 100%
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            相对偏差百分比
        """
        try:
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if np.sum(mask) < 1:
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            # 计算相对偏差
            obs_sum = np.sum(obs_clean)
            if obs_sum == 0:
                return np.nan
            
            relative_bias = (np.sum(pred_clean - obs_clean) / obs_sum) * 100
            
            return float(relative_bias)
            
        except Exception as e:
            self.logger.error(f"计算相对偏差时出错: {e}")
            return np.nan
    
    def calculate_r_squared(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算决定系数 (R²)
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            R²值，范围[0, 1]
        """
        try:
            correlation = self.calculate_correlation(observed, predicted)
            if np.isnan(correlation):
                return np.nan
            
            r_squared = correlation ** 2
            return float(r_squared)
            
        except Exception as e:
            self.logger.error(f"计算R²时出错: {e}")
            return np.nan
    
    def evaluate_comprehensive(self, observed: np.ndarray, predicted: np.ndarray) -> Dict[str, float]:
        """
        综合评价插值结果
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            包含所有评价指标的字典
        """
        metrics = {
            'NSE': self.calculate_nse(observed, predicted),
            'RMSE': self.calculate_rmse(observed, predicted),
            'MAE': self.calculate_mae(observed, predicted),
            'Correlation': self.calculate_correlation(observed, predicted),
            'R_squared': self.calculate_r_squared(observed, predicted),
            'Bias': self.calculate_bias(observed, predicted),
            'Relative_Bias': self.calculate_relative_bias(observed, predicted),
            'Sample_Count': int(np.sum(~(np.isnan(observed) | np.isnan(predicted))))
        }
        
        return metrics
    
    def classify_performance(self, nse: float) -> str:
        """
        根据NSE值分类插值性能
        
        Args:
            nse: NSE值
            
        Returns:
            性能等级字符串
        """
        if np.isnan(nse):
            return "无效"
        elif nse > 0.75:
            return "优秀"
        elif nse > 0.65:
            return "良好"
        elif nse > 0.50:
            return "满意"
        elif nse > 0.20:
            return "不满意"
        else:
            return "不可接受"
    
    def generate_evaluation_summary(self, metrics_list: List[Dict[str, float]]) -> Dict[str, any]:
        """
        生成评价结果汇总
        
        Args:
            metrics_list: 评价指标列表
            
        Returns:
            汇总统计结果
        """
        if not metrics_list:
            return {}
        
        # 提取所有指标
        all_metrics = {}
        for metric_name in metrics_list[0].keys():
            values = [m[metric_name] for m in metrics_list if not np.isnan(m[metric_name])]
            if values:
                all_metrics[metric_name] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'median': np.median(values),
                    'count': len(values)
                }
        
        # NSE性能分类统计
        nse_values = [m['NSE'] for m in metrics_list if not np.isnan(m['NSE'])]
        performance_counts = {}
        for nse in nse_values:
            performance = self.classify_performance(nse)
            performance_counts[performance] = performance_counts.get(performance, 0) + 1
        
        summary = {
            'total_evaluations': len(metrics_list),
            'valid_evaluations': len(nse_values),
            'metrics_statistics': all_metrics,
            'performance_distribution': performance_counts
        }
        
        return summary

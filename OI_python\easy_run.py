# -*- coding: utf-8 -*-
"""
简单运行模块

为新手提供简单易用的运行接口
"""

import os
import sys
from typing import Optional

from config import Config
from oi_main import OIInterpolation
from batch_processing import run_batch_processing

def run_oi_interpolation(config_file: Optional[str] = None, 
                        input_dir: Optional[str] = None,
                        output_dir: Optional[str] = None,
                        stations_file: Optional[str] = None,
                        mask_file: Optional[str] = None,
                        batch_mode: bool = False,
                        debug_mode: bool = False) -> bool:
    """
    运行OI插值的简单接口
    
    Args:
        config_file: 配置文件路径（可选）
        input_dir: 输入目录路径（可选）
        output_dir: 输出目录路径（可选）
        stations_file: 站点文件路径（可选）
        mask_file: 掩膜文件路径（可选）
        batch_mode: 是否启用批量处理模式
        debug_mode: 是否启用调试模式
    
    Returns:
        bool: 是否成功完成处理
    """
    try:
        # 加载配置
        if config_file and os.path.exists(config_file):
            print(f"从配置文件加载配置: {config_file}")
            config = Config.load_from_file(config_file)
        else:
            print("使用默认配置")
            config = Config()
        
        # 覆盖配置参数（如果提供了）
        if input_dir:
            config.input_dir = input_dir
        if output_dir:
            config.output_dir = output_dir
        if stations_file:
            config.stations_file = stations_file
        if mask_file:
            config.mask_file = mask_file
        if batch_mode:
            config.batch_processing = True
        if debug_mode:
            config.debug_mode = True
        
        # 验证配置
        if not config.validate():
            print("配置验证失败，请检查配置参数")
            return False
        
        print("配置验证通过，开始处理...")
        
        # 根据模式运行处理
        if config.batch_processing:
            print("运行批量处理模式...")
            success = run_batch_processing(config)
        else:
            print("运行单事件处理模式...")
            oi_processor = OIInterpolation(config)
            success = oi_processor.run_complete_workflow()
        
        if success:
            print("处理成功完成！")
            print(f"结果保存在: {config.output_dir}")
        else:
            print("处理失败，请检查日志文件")
        
        return success
    
    except Exception as e:
        print(f"运行时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def interactive_run():
    """
    交互式运行模式
    """
    print("="*60)
    print("OI插值系统 - 交互式运行模式")
    print("="*60)
    
    try:
        # 选择运行模式
        print("\n请选择运行模式:")
        print("1. 单事件处理")
        print("2. 批量处理")
        print("3. 调试模式（单事件，少量数据）")
        
        while True:
            choice = input("\n请输入选择 (1/2/3): ").strip()
            if choice in ['1', '2', '3']:
                break
            print("无效选择，请重新输入")
        
        # 创建配置
        config = Config()
        
        if choice == '1':
            # 单事件处理
            config.batch_processing = False
            config.debug_mode = False
            
            # 获取输入参数
            input_dir = input(f"\n输入数据目录 (默认: {config.input_dir}): ").strip()
            if input_dir:
                config.input_dir = input_dir
            
            output_dir = input(f"输出目录 (默认: {config.output_dir}): ").strip()
            if output_dir:
                config.output_dir = output_dir
        
        elif choice == '2':
            # 批量处理
            config.batch_processing = True
            config.debug_mode = False
            
            batch_root = input(f"\n批量处理根目录 (默认: {config.batch_root_dir}): ").strip()
            if batch_root:
                config.batch_root_dir = batch_root
            
            batch_output = input(f"批量输出根目录 (默认: {config.batch_output_root}): ").strip()
            if batch_output:
                config.batch_output_root = batch_output
        
        elif choice == '3':
            # 调试模式
            config.batch_processing = False
            config.debug_mode = True
            config.debug_time_steps = 5
            
            input_dir = input(f"\n输入数据目录 (默认: {config.input_dir}): ").strip()
            if input_dir:
                config.input_dir = input_dir
            
            output_dir = input(f"输出目录 (默认: {config.output_dir}): ").strip()
            if output_dir:
                config.output_dir = output_dir
        
        # 获取其他参数
        stations_file = input(f"\n站点文件路径 (默认: {config.stations_file}): ").strip()
        if stations_file:
            config.stations_file = stations_file
        
        mask_file = input(f"掩膜文件路径 (默认: {config.mask_file}): ").strip()
        if mask_file:
            config.mask_file = mask_file
        
        # 询问是否生成栅格输出
        raster_choice = input(f"\n是否生成栅格输出? (y/n, 默认: {'y' if config.generate_raster else 'n'}): ").strip().lower()
        if raster_choice in ['y', 'yes']:
            config.generate_raster = True
        elif raster_choice in ['n', 'no']:
            config.generate_raster = False
        
        # 询问并行进程数
        processes = input(f"\n并行进程数 (默认: {config.num_processes}): ").strip()
        if processes.isdigit():
            config.num_processes = int(processes)
        
        print("\n配置完成，开始处理...")
        
        # 运行处理
        success = run_oi_interpolation(config=config)
        
        if success:
            print("\n处理成功完成！")
        else:
            print("\n处理失败，请检查错误信息")
        
        return success
    
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
        return False
    except Exception as e:
        print(f"\n交互式运行出错: {e}")
        return False

def create_sample_config(output_path: str = "oi_config.json"):
    """
    创建示例配置文件
    
    Args:
        output_path: 输出配置文件路径
    """
    try:
        config = Config()
        config.save_to_file(output_path)
        print(f"示例配置文件已创建: {output_path}")
        print("您可以编辑此文件来修改配置参数")
        return True
    except Exception as e:
        print(f"创建配置文件失败: {e}")
        return False

def main():
    """
    主函数 - 命令行入口
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='OI插值系统')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--input', type=str, help='输入数据目录')
    parser.add_argument('--output', type=str, help='输出目录')
    parser.add_argument('--stations', type=str, help='站点文件路径')
    parser.add_argument('--mask', type=str, help='掩膜文件路径')
    parser.add_argument('--batch', action='store_true', help='启用批量处理模式')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--interactive', action='store_true', help='交互式运行模式')
    parser.add_argument('--create-config', type=str, help='创建示例配置文件')
    
    args = parser.parse_args()
    
    # 创建配置文件
    if args.create_config:
        return create_sample_config(args.create_config)
    
    # 交互式模式
    if args.interactive:
        return interactive_run()
    
    # 命令行模式
    success = run_oi_interpolation(
        config_file=args.config,
        input_dir=args.input,
        output_dir=args.output,
        stations_file=args.stations,
        mask_file=args.mask,
        batch_mode=args.batch,
        debug_mode=args.debug
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

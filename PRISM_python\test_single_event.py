"""
测试单个洪水事件的PRISM插值
"""

import os
import sys
import logging

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from config import Config
from prism_main import PRISMInterpolation

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_single_event(event_name="2009-1"):
    """测试单个洪水事件"""
    print("="*60)
    print(f"PRISM插值测试 - {event_name}")
    print("="*60)
    
    try:
        # 创建配置
        config = Config(
            input_dir=f"D:/pythondata/spatial_interpolation/input_another/{event_name}",
            terrain_dir="D:/pythondata/spatial_interpolation/terrain/90",
            output_dir=f"D:/pythondata/spatial_interpolation/output/PRISM/{event_name}",
            stations_file="D:/pythondata/spatial_interpolation/stations.csv",
            num_cores=12,
            memory_efficient=True,
            output_raster=True,
            output_delaunay_plot=True,
            output_evaluation=True,
            verbose_logging=True
        )
        
        print(f"配置信息:")
        print(f"  输入目录: {config.input_dir}")
        print(f"  输出目录: {config.output_dir}")
        print(f"  并行核心数: {config.num_cores}")
        print("-"*60)
        
        # 检查输入数据
        if not os.path.exists(config.input_dir):
            print(f"❌ 输入目录不存在: {config.input_dir}")
            return False
        
        csv_files = [f for f in os.listdir(config.input_dir) if f.endswith('.csv')]
        print(f"发现 {len(csv_files)} 个CSV文件")
        
        if len(csv_files) == 0:
            print("❌ 输入目录中没有CSV文件")
            return False
        
        # 运行PRISM插值
        print("开始PRISM插值处理...")
        prism = PRISMInterpolation(config)
        evaluation_results = prism.run_complete_workflow()
        
        if evaluation_results:
            print("✅ PRISM插值处理成功完成！")
            
            # 显示评价结果
            if 'overall_metrics' in evaluation_results:
                metrics = evaluation_results['overall_metrics']
                print(f"\n评价指标:")
                print(f"  NSE: {metrics.get('NSE', 0):.4f}")
                print(f"  R²:  {metrics.get('R2', 0):.4f}")
                print(f"  RMSE: {metrics.get('RMSE', 0):.2f} mm")
                print(f"  MAE: {metrics.get('MAE', 0):.2f} mm")
                print(f"  样本数: {metrics.get('Count', 0)}")
                
                # 评价结果
                nse = metrics.get('NSE', 0)
                if nse > 0.75:
                    print(f"  结果评价: 优秀 ✅")
                elif nse > 0.5:
                    print(f"  结果评价: 良好 ⚠️")
                else:
                    print(f"  结果评价: 需要优化 ❌")
            
            print(f"\n结果已保存到: {config.output_dir}")
            return True
        else:
            print("❌ PRISM插值处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        logger.error(f"处理失败: {e}", exc_info=True)
        return False


def main():
    """主函数"""
    print("PRISM单事件测试脚本")
    print("此脚本将测试单个洪水事件的PRISM插值处理")
    print()
    
    # 可以修改这里来测试不同的事件
    test_events = ["2009-1", "2015-3", "2020-1"]
    
    for event in test_events:
        print(f"\n正在测试事件: {event}")
        success = test_single_event(event)
        
        if success:
            print(f"✅ 事件 {event} 测试成功")
        else:
            print(f"❌ 事件 {event} 测试失败")
        
        print("-"*60)


if __name__ == "__main__":
    main()

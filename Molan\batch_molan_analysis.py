# -*- coding: utf-8 -*-
"""
批量莫兰指数分析系统（包含显著性检验）
基于input_another目录中的洪水事件数据和stations.csv进行分析

作者：空间插值系统
日期：2024年
"""

import pandas as pd
import numpy as np
import math
import os
import glob
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class BatchMoranAnalysis:
    """批量莫兰指数分析类"""
    
    def __init__(self, excel_file, stations_file, input_dir, output_dir, exclude_stations=False):
        """
        初始化批量莫兰指数分析系统

        参数:
        excel_file: 水晏泰森.xlsx文件路径
        stations_file: 站点位置信息文件路径
        input_dir: 输入目录路径（包含各洪水事件文件夹）
        output_dir: 输出目录路径
        exclude_stations: 是否剔除指定站点（True=剔除，False=保留所有站点）
        """
        self.excel_file = excel_file
        self.stations_file = stations_file
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.exclude_stations = exclude_stations

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 需要剔除的站点配置
        self.excluded_stations = {
            '整体': ['80606500', '80607500', '805g2300'],
            '大化': ['80607800', '80608500', '80628800'],
            '太平': ['80607500', '80609500', '80633100'],
            '水晏': ['80609500', '80633500', '80633800']
        }

        # 加载基础数据
        self.load_base_data()

        # 获取所有洪水事件
        self.flood_events = self.get_flood_events()
        
    def load_base_data(self):
        """加载基础数据"""
        print("正在加载基础数据...")

        # 加载Excel文件获取站点分组信息
        self.excel_df = pd.read_excel(self.excel_file)
        print(f"加载了Excel文件，包含 {len(self.excel_df)} 行数据")

        # 加载站点位置数据
        self.stations_df = pd.read_csv(self.stations_file)
        print(f"加载了 {len(self.stations_df)} 个站点位置信息")

        # 创建站点分组
        self.create_station_groups()
        
    def create_station_groups(self):
        """根据水晏泰森.xlsx文件创建站点分组"""
        self.grouped_stations = {}

        # 获取所有站点（从stations.csv）
        all_stations = [str(row['站点']).lower() for _, row in self.stations_df.iterrows()]

        # 站点分组定义（基于Excel文件的行号，注意Python索引从0开始）
        group_definitions = {
            '大化': {'start': 0, 'end': 16},    # 第2-18行 (Excel行号)，Python索引0-16
            '太平': {'start': 17, 'end': 30},   # 第19-32行 (Excel行号)，Python索引17-30
            '水晏': {'start': 31, 'end': 36}    # 第33-38行 (Excel行号)，Python索引31-36
        }

        # 根据Excel文件的行号创建分组
        for group_name, indices in group_definitions.items():
            start_idx = indices['start']
            end_idx = indices['end']

            # 从Excel文件中获取该范围内的站点
            group_stations = []
            for i in range(start_idx, min(end_idx + 1, len(self.excel_df))):
                if i < len(self.excel_df):
                    # 获取PSTCD列的站点ID，并清理空格和转换为小写
                    station_id = str(self.excel_df.iloc[i]['PSTCD']).strip().lower()
                    # 确保站点存在于stations.csv中
                    if station_id in all_stations:
                        group_stations.append(station_id)
                    else:
                        print(f"警告：站点 {station_id} 在Excel中但不在stations.csv中")

            # 如果启用了站点剔除功能，则剔除指定站点
            if self.exclude_stations and group_name in self.excluded_stations:
                excluded_list = self.excluded_stations[group_name]
                original_count = len(group_stations)
                group_stations = [s for s in group_stations if s not in excluded_list]
                excluded_count = original_count - len(group_stations)
                print(f"{group_name}组原有 {original_count} 个站点，剔除 {excluded_count} 个站点，剩余 {len(group_stations)} 个站点")
                if excluded_count > 0:
                    actually_excluded = [s for s in excluded_list if s in [str(self.excel_df.iloc[i]['PSTCD']).strip().lower()
                                                                          for i in range(start_idx, min(end_idx + 1, len(self.excel_df)))]]
                    print(f"  实际剔除的站点: {actually_excluded}")
            else:
                print(f"{group_name}组包含 {len(group_stations)} 个站点")
                print(f"  站点列表: {group_stations}")

            self.grouped_stations[group_name] = group_stations

        # 整体组：所有站点减去剔除的站点
        if self.exclude_stations and '整体' in self.excluded_stations:
            excluded_list = self.excluded_stations['整体']
            all_valid_stations = [s for s in all_stations if s not in excluded_list]
            print(f"整体组原有 {len(all_stations)} 个站点，剔除 {len(excluded_list)} 个站点，剩余 {len(all_valid_stations)} 个站点")
        else:
            all_valid_stations = all_stations
            print(f"整体组包含 {len(all_valid_stations)} 个站点")

        self.grouped_stations['整体'] = all_valid_stations
    
    def get_flood_events(self):
        """获取所有洪水事件文件夹"""
        flood_dirs = []
        for item in os.listdir(self.input_dir):
            item_path = os.path.join(self.input_dir, item)
            if os.path.isdir(item_path) and item != 'rains_每场一表格':
                flood_dirs.append(item)
        
        flood_dirs.sort()  # 按时间顺序排序
        print(f"发现 {len(flood_dirs)} 个洪水事件: {flood_dirs[:5]}...")
        return flood_dirs
    
    def load_flood_data(self, flood_event):
        """
        加载单个洪水事件的数据
        
        参数:
        flood_event: 洪水事件名称（如'2015-1'）
        
        返回:
        合并后的降雨数据DataFrame
        """
        flood_dir = os.path.join(self.input_dir, flood_event)
        csv_files = glob.glob(os.path.join(flood_dir, "*.csv"))
        
        if not csv_files:
            return None
        
        # 读取第一个文件获取时间列
        first_file = csv_files[0]
        first_df = pd.read_csv(first_file)
        merged_df = first_df[['时间']].copy()
        
        # 逐个读取站点数据并合并
        for csv_file in csv_files:
            station_id = os.path.basename(csv_file).replace('.csv', '').lower()
            try:
                station_df = pd.read_csv(csv_file)
                if '雨量' in station_df.columns:
                    merged_df[station_id] = station_df['雨量']
            except Exception as e:
                print(f"读取文件 {csv_file} 时出错: {e}")
                continue
        
        return merged_df
    
    def haversine_distance(self, lon1, lat1, lon2, lat2):
        """
        计算两点间的球面距离（公里）
        使用Haversine公式
        """
        # 转换为弧度
        lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])
        
        # Haversine公式
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371  # 地球半径（公里）
        
        return c * r
    
    def calculate_spatial_weights(self, station_list, method='inverse_distance'):
        """
        计算空间权重矩阵
        
        参数:
        station_list: 站点列表
        method: 权重计算方法
        
        返回:
        权重矩阵
        """
        n_stations = len(station_list)
        weights = np.zeros((n_stations, n_stations))
        
        # 创建站点位置字典
        station_coords = {}
        for _, row in self.stations_df.iterrows():
            station_id = str(row['站点']).lower()
            if station_id in station_list:
                station_coords[station_id] = (row['经度'], row['纬度'])
        
        # 计算权重矩阵
        for i, station_i in enumerate(station_list):
            if station_i not in station_coords:
                continue
                
            lon_i, lat_i = station_coords[station_i]
            
            for j, station_j in enumerate(station_list):
                if i == j or station_j not in station_coords:
                    weights[i, j] = 0
                    continue
                    
                lon_j, lat_j = station_coords[station_j]
                distance = self.haversine_distance(lon_i, lat_i, lon_j, lat_j)
                
                if method == 'inverse_distance':
                    # 反距离权重
                    weights[i, j] = 1.0 / (distance + 1e-10)  # 避免除零
                elif method == 'binary':
                    # 二元权重（距离阈值）
                    threshold = 50  # 50公里阈值
                    weights[i, j] = 1.0 if distance <= threshold else 0.0
        
        # 行标准化
        row_sums = weights.sum(axis=1)
        for i in range(n_stations):
            if row_sums[i] > 0:
                weights[i, :] = weights[i, :] / row_sums[i]
                
        return weights

    def calculate_moran_index_with_significance(self, ref_station, target_station, station_list, rain_data, n_permutations=199):
        """
        计算两个站点间的莫兰指数及其显著性检验

        参数:
        ref_station: 参考站点
        target_station: 目标站点
        station_list: 站点列表
        rain_data: 降雨数据DataFrame
        n_permutations: 置换检验次数

        返回:
        字典包含: moran_i, p_value, z_score, significance_level
        """
        try:
            # 检查站点是否存在于降雨数据中
            if ref_station not in rain_data.columns or target_station not in rain_data.columns:
                return {'moran_i': 0.0, 'p_value': 1.0, 'z_score': 0.0, 'significance_level': 'NS'}

            # 获取降雨数据
            ref_data = rain_data[ref_station].values
            target_data = rain_data[target_station].values

            # 移除缺失值
            valid_mask = ~(np.isnan(ref_data) | np.isnan(target_data))
            if not np.any(valid_mask):
                return {'moran_i': 0.0, 'p_value': 1.0, 'z_score': 0.0, 'significance_level': 'NS'}

            ref_valid = ref_data[valid_mask]
            target_valid = target_data[valid_mask]

            if len(ref_valid) < 10:  # 需要足够的数据点
                return {'moran_i': 0.0, 'p_value': 1.0, 'z_score': 0.0, 'significance_level': 'NS'}

            # 标准化数据
            ref_mean = np.mean(ref_valid)
            target_mean = np.mean(target_valid)
            ref_std = np.std(ref_valid)
            target_std = np.std(target_valid)

            if ref_std == 0 or target_std == 0:
                return {'moran_i': 0.0, 'p_value': 1.0, 'z_score': 0.0, 'significance_level': 'NS'}

            ref_normalized = (ref_valid - ref_mean) / ref_std
            target_normalized = (target_valid - target_mean) / target_std

            # 计算空间权重
            weights = self.calculate_spatial_weights(station_list)

            # 获取站点在列表中的索引
            try:
                ref_idx = station_list.index(ref_station)
                target_idx = station_list.index(target_station)
            except ValueError:
                return {'moran_i': 0.0, 'p_value': 1.0, 'z_score': 0.0, 'significance_level': 'NS'}

            # 计算观测的莫兰指数
            if ref_idx < len(weights) and target_idx < len(weights[0]):
                weight = weights[ref_idx, target_idx]
                observed_moran = weight * np.mean(ref_normalized * target_normalized)
            else:
                return {'moran_i': 0.0, 'p_value': 1.0, 'z_score': 0.0, 'significance_level': 'NS'}

            # 置换检验计算显著性
            permuted_morans = []
            for _ in range(n_permutations):
                # 随机置换目标站点数据
                permuted_target = np.random.permutation(target_normalized)
                permuted_moran = weight * np.mean(ref_normalized * permuted_target)
                permuted_morans.append(permuted_moran)

            permuted_morans = np.array(permuted_morans)

            # 计算p值（双尾检验）
            if observed_moran >= 0:
                p_value = (np.sum(permuted_morans >= observed_moran) + 1) / (n_permutations + 1)
            else:
                p_value = (np.sum(permuted_morans <= observed_moran) + 1) / (n_permutations + 1)

            # 计算z分数
            expected_moran = np.mean(permuted_morans)
            std_moran = np.std(permuted_morans)
            if std_moran > 0:
                z_score = (observed_moran - expected_moran) / std_moran
            else:
                z_score = 0.0

            # 确定显著性水平
            if p_value < 0.001:
                significance_level = '***'
            elif p_value < 0.01:
                significance_level = '**'
            elif p_value < 0.05:
                significance_level = '*'
            elif p_value < 0.1:
                significance_level = '.'
            else:
                significance_level = 'NS'

            return {
                'moran_i': observed_moran,
                'p_value': p_value,
                'z_score': z_score,
                'significance_level': significance_level
            }

        except Exception as e:
            print(f"计算莫兰指数时出错 ({ref_station} -> {target_station}): {e}")
            return {'moran_i': 0.0, 'p_value': 1.0, 'z_score': 0.0, 'significance_level': 'NS'}

    def analyze_flood_event(self, flood_event):
        """
        分析单个洪水事件的莫兰指数及显著性

        参数:
        flood_event: 洪水事件名称

        返回:
        分析结果列表
        """
        print(f"\n正在分析洪水事件: {flood_event}")

        # 加载洪水事件数据
        rain_data = self.load_flood_data(flood_event)
        if rain_data is None:
            print(f"无法加载洪水事件 {flood_event} 的数据")
            return []

        # 获取可用站点列表
        available_stations = [col for col in rain_data.columns if col != '时间']
        print(f"可用站点数量: {len(available_stations)}")

        results = []

        # 为每个分组进行分析
        for group_name in ['整体', '大化', '太平', '水晏']:
            # 获取该组的站点，并筛选出在当前洪水事件中可用的站点
            group_stations = self.grouped_stations.get(group_name, [])
            station_list = [s for s in group_stations if s in available_stations]

            if len(station_list) < 2:
                print(f"跳过 {group_name} 组：可用站点数量不足 ({len(station_list)})")
                continue

            print(f"  分析 {group_name} 组，包含 {len(station_list)} 个站点")

            # 对每个站点进行分析
            for ref_station in station_list:
                station_correlations = []

                for target_station in station_list:
                    if ref_station == target_station:
                        continue

                    # 计算莫兰指数及显著性检验
                    result = self.calculate_moran_index_with_significance(
                        ref_station, target_station, station_list, rain_data, n_permutations=99
                    )

                    station_correlations.append({
                        'target_station': target_station,
                        'moran_index': result['moran_i'],
                        'p_value': result['p_value'],
                        'z_score': result['z_score'],
                        'significance_level': result['significance_level']
                    })

                # 按莫兰指数排序，选择前3个
                station_correlations.sort(key=lambda x: x['moran_index'], reverse=True)
                top_3 = station_correlations[:3]

                # 记录结果
                result = {
                    'flood_event': flood_event,
                    'group': group_name,
                    'reference_station': ref_station,
                    'top_1_station': top_3[0]['target_station'] if len(top_3) > 0 else '',
                    'top_1_moran': top_3[0]['moran_index'] if len(top_3) > 0 else 0.0,
                    'top_1_p_value': top_3[0]['p_value'] if len(top_3) > 0 else 1.0,
                    'top_1_z_score': top_3[0]['z_score'] if len(top_3) > 0 else 0.0,
                    'top_1_significance': top_3[0]['significance_level'] if len(top_3) > 0 else 'NS',
                    'top_2_station': top_3[1]['target_station'] if len(top_3) > 1 else '',
                    'top_2_moran': top_3[1]['moran_index'] if len(top_3) > 1 else 0.0,
                    'top_2_p_value': top_3[1]['p_value'] if len(top_3) > 1 else 1.0,
                    'top_2_z_score': top_3[1]['z_score'] if len(top_3) > 1 else 0.0,
                    'top_2_significance': top_3[1]['significance_level'] if len(top_3) > 1 else 'NS',
                    'top_3_station': top_3[2]['target_station'] if len(top_3) > 2 else '',
                    'top_3_moran': top_3[2]['moran_index'] if len(top_3) > 2 else 0.0,
                    'top_3_p_value': top_3[2]['p_value'] if len(top_3) > 2 else 1.0,
                    'top_3_z_score': top_3[2]['z_score'] if len(top_3) > 2 else 0.0,
                    'top_3_significance': top_3[2]['significance_level'] if len(top_3) > 2 else 'NS',
                    'avg_moran': np.mean([item['moran_index'] for item in top_3]) if top_3 else 0.0,
                    'max_moran': max([item['moran_index'] for item in top_3]) if top_3 else 0.0,
                    'significant_count': sum([1 for item in top_3 if item['significance_level'] in ['*', '**', '***']])
                }

                results.append(result)

        return results

    def run_batch_analysis(self):
        """
        运行批量莫兰指数分析（包含显著性检验）
        """
        print("=" * 80)
        print("开始批量莫兰指数分析（包含显著性检验）")
        print("=" * 80)

        all_results = []

        # 对每个洪水事件进行分析
        for i, flood_event in enumerate(self.flood_events, 1):
            print(f"\n进度: {i}/{len(self.flood_events)} - {flood_event}")

            try:
                event_results = self.analyze_flood_event(flood_event)
                all_results.extend(event_results)
            except Exception as e:
                print(f"分析洪水事件 {flood_event} 时出错: {e}")
                continue

        # 保存结果
        self.save_results(all_results)

        # 生成汇总统计
        self.generate_summary_statistics(all_results)

        # 生成显著性检验汇总表
        self.generate_significance_summary(all_results)

        return all_results

    def save_results(self, results):
        """
        保存分析结果到文件

        参数:
        results: 分析结果列表
        """
        print("\n正在保存分析结果...")

        # 创建详细结果DataFrame
        df_results = pd.DataFrame(results)

        # 保存详细结果
        detailed_file = os.path.join(self.output_dir, '莫兰指数分析详细结果.csv')
        df_results.to_csv(detailed_file, index=False, encoding='utf-8-sig')
        print(f"详细结果已保存到: {detailed_file}")

        # 按洪水事件分别保存
        for flood_event in self.flood_events:
            event_results = [r for r in results if r['flood_event'] == flood_event]
            if event_results:
                event_df = pd.DataFrame(event_results)
                event_file = os.path.join(self.output_dir, f'{flood_event}_莫兰指数分析.csv')
                event_df.to_csv(event_file, index=False, encoding='utf-8-sig')

        # 按分组保存汇总结果
        for group_name in ['整体', '大化', '太平', '水晏']:
            group_results = [r for r in results if r['group'] == group_name]
            if group_results:
                group_df = pd.DataFrame(group_results)
                group_file = os.path.join(self.output_dir, f'{group_name}_所有洪水事件莫兰指数.csv')
                group_df.to_csv(group_file, index=False, encoding='utf-8-sig')
                print(f"{group_name}组汇总结果已保存到: {group_file}")

    def generate_significance_summary(self, results):
        """
        生成显著性检验汇总表

        参数:
        results: 分析结果列表
        """
        print("\n正在生成显著性检验汇总表...")

        significance_summary = []

        # 按分组统计显著性
        for group_name in ['整体', '大化', '太平', '水晏']:
            group_results = [r for r in results if r['group'] == group_name]
            if not group_results:
                continue

            # 统计各显著性水平的数量
            total_pairs = len(group_results) * 3  # 每个参考站点有3个目标站点

            significance_counts = {
                '***': 0, '**': 0, '*': 0, '.': 0, 'NS': 0
            }

            for result in group_results:
                for level in ['top_1_significance', 'top_2_significance', 'top_3_significance']:
                    sig_level = result.get(level, 'NS')
                    if sig_level in significance_counts:
                        significance_counts[sig_level] += 1
                    else:
                        significance_counts['NS'] += 1

            # 计算百分比
            percentages = {k: (v / total_pairs * 100) if total_pairs > 0 else 0
                          for k, v in significance_counts.items()}

            summary_row = {
                '分组': group_name,
                '总站点对数': total_pairs,
                '极显著(p<0.001)数量': significance_counts['***'],
                '极显著(p<0.001)百分比': f"{percentages['***']:.1f}%",
                '高显著(p<0.01)数量': significance_counts['**'],
                '高显著(p<0.01)百分比': f"{percentages['**']:.1f}%",
                '显著(p<0.05)数量': significance_counts['*'],
                '显著(p<0.05)百分比': f"{percentages['*']:.1f}%",
                '边际显著(p<0.1)数量': significance_counts['.'],
                '边际显著(p<0.1)百分比': f"{percentages['.']:.1f}%",
                '不显著数量': significance_counts['NS'],
                '不显著百分比': f"{percentages['NS']:.1f}%",
                '显著站点对总数': significance_counts['***'] + significance_counts['**'] + significance_counts['*'],
                '显著站点对百分比': f"{(percentages['***'] + percentages['**'] + percentages['*']):.1f}%"
            }

            significance_summary.append(summary_row)

        # 保存显著性汇总表
        df_significance = pd.DataFrame(significance_summary)
        significance_file = os.path.join(self.output_dir, '莫兰指数显著性检验汇总表.csv')
        df_significance.to_csv(significance_file, index=False, encoding='utf-8-sig')
        print(f"显著性检验汇总表已保存到: {significance_file}")

        # 打印显著性汇总
        print("\n" + "=" * 80)
        print("显著性检验汇总")
        print("=" * 80)
        print("显著性水平说明：")
        print("*** : p < 0.001 (极显著)")
        print("**  : p < 0.01  (高显著)")
        print("*   : p < 0.05  (显著)")
        print(".   : p < 0.1   (边际显著)")
        print("NS  : p >= 0.1  (不显著)")
        print("-" * 80)

        for summary in significance_summary:
            print(f"\n【{summary['分组']}组】")
            print(f"总站点对数: {summary['总站点对数']}")
            print(f"极显著(***): {summary['极显著(p<0.001)数量']} ({summary['极显著(p<0.001)百分比']})")
            print(f"高显著(**) : {summary['高显著(p<0.01)数量']} ({summary['高显著(p<0.01)百分比']})")
            print(f"显著(*)   : {summary['显著(p<0.05)数量']} ({summary['显著(p<0.05)百分比']})")
            print(f"边际显著(.): {summary['边际显著(p<0.1)数量']} ({summary['边际显著(p<0.1)百分比']})")
            print(f"不显著(NS): {summary['不显著数量']} ({summary['不显著百分比']})")
            print(f"显著合计   : {summary['显著站点对总数']} ({summary['显著站点对百分比']})")

    def generate_summary_statistics(self, results):
        """
        生成汇总统计报告

        参数:
        results: 分析结果列表
        """
        print("\n正在生成汇总统计报告...")

        report_lines = []
        report_lines.append("=" * 100)
        report_lines.append("批量莫兰指数分析汇总统计报告（包含显著性检验）")
        report_lines.append("=" * 100)
        report_lines.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"总洪水事件数: {len(self.flood_events)}")
        report_lines.append(f"总分析记录数: {len(results)}")

        if self.exclude_stations:
            report_lines.append("\n【站点剔除配置】")
            for group, excluded in self.excluded_stations.items():
                report_lines.append(f"{group}组剔除站点: {excluded}")

        report_lines.append("")

        # 按分组统计
        for group_name in ['整体', '大化', '太平', '水晏']:
            group_results = [r for r in results if r['group'] == group_name]
            if not group_results:
                continue

            report_lines.append(f"【{group_name}组统计】")
            report_lines.append(f"  分析记录数: {len(group_results)}")

            # 计算莫兰指数统计
            all_moran_values = []
            significant_moran_values = []

            for result in group_results:
                for i in range(1, 4):
                    moran_key = f'top_{i}_moran'
                    sig_key = f'top_{i}_significance'
                    if moran_key in result:
                        all_moran_values.append(result[moran_key])
                        if result.get(sig_key, 'NS') in ['*', '**', '***']:
                            significant_moran_values.append(result[moran_key])

            if all_moran_values:
                report_lines.append(f"  莫兰指数统计:")
                report_lines.append(f"    所有值平均: {np.mean(all_moran_values):.4f}")
                report_lines.append(f"    所有值最大: {np.max(all_moran_values):.4f}")
                report_lines.append(f"    所有值最小: {np.min(all_moran_values):.4f}")
                report_lines.append(f"    所有值标准差: {np.std(all_moran_values):.4f}")

                if significant_moran_values:
                    report_lines.append(f"  显著莫兰指数统计:")
                    report_lines.append(f"    显著值数量: {len(significant_moran_values)}")
                    report_lines.append(f"    显著值平均: {np.mean(significant_moran_values):.4f}")
                    report_lines.append(f"    显著值最大: {np.max(significant_moran_values):.4f}")

            # 找出莫兰指数最高的站点对
            if group_results:
                best_result = max(group_results, key=lambda x: x.get('top_1_moran', 0))
                report_lines.append(f"  最强相关性:")
                report_lines.append(f"    洪水事件: {best_result['flood_event']}")
                report_lines.append(f"    站点对: {best_result['reference_station']} -> {best_result['top_1_station']}")
                report_lines.append(f"    莫兰指数: {best_result['top_1_moran']:.4f}")
                report_lines.append(f"    显著性: {best_result.get('top_1_significance', 'NS')} (p={best_result.get('top_1_p_value', 1.0):.3f})")

            report_lines.append("")

        # 保存报告
        report_file = os.path.join(self.output_dir, '莫兰指数分析汇总报告.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print(f"汇总统计报告已保存到: {report_file}")


def main():
    """主函数"""
    print("批量莫兰指数分析与站点筛选系统（包含显著性检验）")
    print("=" * 80)

    # 文件路径配置
    excel_file = "../水晏泰森.xlsx"
    stations_file = "../stations.csv"
    input_dir = "../input_another"
    output_dir = "../output/molan"

    # 站点剔除配置
    # 设置为 True 表示剔除指定站点，False 表示保留所有站点
    exclude_stations = False

    try:
        # 创建分析实例
        analyzer = BatchMoranAnalysis(excel_file, stations_file, input_dir, output_dir, exclude_stations)

        # 显示配置信息
        if exclude_stations:
            print("\n【站点剔除配置】")
            print("整体组剔除站点:", analyzer.excluded_stations['整体'])
            print("大化组剔除站点:", analyzer.excluded_stations['大化'])
            print("太平组剔除站点:", analyzer.excluded_stations['太平'])
            print("水晏组剔除站点:", analyzer.excluded_stations['水晏'])
        else:
            print("\n【配置】保留所有站点，不进行剔除")

        # 运行批量分析
        results = analyzer.run_batch_analysis()

        print("\n" + "=" * 80)
        print("批量分析完成！")
        print(f"共分析了 {len(analyzer.flood_events)} 个洪水事件")
        print(f"生成了 {len(results)} 条分析记录")
        print(f"结果已保存到: {output_dir}")
        print("\n输出文件包括：")
        print("1. 莫兰指数分析详细结果.csv - 包含所有莫兰指数和显著性检验结果")
        print("2. 莫兰指数显著性检验汇总表.csv - 各组显著性统计汇总")
        print("3. 莫兰指数分析汇总报告.txt - 详细统计报告")
        print("4. 各洪水事件和分组的详细结果文件")

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

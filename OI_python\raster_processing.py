# -*- coding: utf-8 -*-
"""
栅格处理模块

负责生成ASC格式的栅格输出和面雨量计算
"""

import numpy as np
import pandas as pd
import os
from typing import Dict, Tuple, List
import logging
from scipy.interpolate import griddata
from oi_core import optimal_interpolation_basic

def safe_filename(filename: str) -> str:
    """
    生成安全的文件名，替换不允许的字符
    
    Args:
        filename: 原始文件名（如时间戳）
    
    Returns:
        str: 安全的文件名
    """
    import re
    # 替换常见的不允许字符
    safe_name = re.sub(r'[\\/*?:"<>|]', "_", str(filename))
    # 替换冒号和空格
    safe_name = safe_name.replace(":", "-").replace(" ", "_")
    return safe_name

def create_interpolation_grid(mask_array: np.ndarray, header: Dict) -> Tuple[np.ndarray, np.ndarray]:
    """
    创建插值网格
    
    Args:
        mask_array: 掩膜数组
        header: ASC文件头信息
    
    Returns:
        Tuple[np.ndarray, np.ndarray]: (经度网格, 纬度网格)
    """
    try:
        # 从头信息中提取参数
        ncols = int(header['ncols'])
        nrows = int(header['nrows'])
        xllcorner = header['xllcorner']
        yllcorner = header['yllcorner']
        cellsize = header['cellsize']
        
        # 创建经纬度网格
        x = np.linspace(xllcorner, xllcorner + ncols * cellsize, ncols)
        y = np.linspace(yllcorner, yllcorner + nrows * cellsize, nrows)
        
        # 注意：ASC文件中的行是从北到南排列的，所以需要翻转y轴
        y = y[::-1]
        
        lon_grid, lat_grid = np.meshgrid(x, y)
        
        return lon_grid, lat_grid
    
    except Exception as e:
        logging.error(f"创建插值网格时出错: {e}")
        raise

def interpolate_to_grid(stations: pd.DataFrame, rainfall_data: pd.DataFrame, 
                       time_index: pd.Timestamp, mask_array: np.ndarray, 
                       header: Dict, cov_matrix: np.ndarray, 
                       method: str = 'oi') -> np.ndarray:
    """
    将点雨量插值到栅格
    
    Args:
        stations: 站点信息DataFrame
        rainfall_data: 降雨数据DataFrame
        time_index: 当前时间索引
        mask_array: 掩膜数组
        header: ASC文件头信息
        cov_matrix: 协方差矩阵
        method: 插值方法 ('oi', 'idw', 'linear', 'cubic')
    
    Returns:
        np.ndarray: 插值后的栅格数组
    """
    try:
        # 创建插值网格
        lon_grid, lat_grid = create_interpolation_grid(mask_array, header)
        
        # 获取当前时间的降雨数据
        current_rainfall = rainfall_data.loc[time_index]
        
        # 提取有效的站点数据
        valid_stations = []
        valid_values = []
        
        for _, station_row in stations.iterrows():
            station_id = station_row['station_id']
            if station_id in current_rainfall and not pd.isna(current_rainfall[station_id]):
                valid_stations.append([station_row['longitude'], station_row['latitude']])
                valid_values.append(current_rainfall[station_id])
        
        if not valid_stations:
            logging.warning(f"时间 {time_index} 没有有效的站点数据")
            return np.full_like(mask_array, header.get('nodata_value', -9999), dtype=float)
        
        valid_stations = np.array(valid_stations)
        valid_values = np.array(valid_values)
        
        # 创建输出数组
        nodata_value = header.get('nodata_value', -9999)
        result_array = np.full_like(mask_array, nodata_value, dtype=float)
        
        # 只对掩膜内的点进行插值
        valid_mask = mask_array != nodata_value
        
        if not np.any(valid_mask):
            logging.warning("掩膜中没有有效的栅格点")
            return result_array
        
        # 获取需要插值的点坐标
        valid_lons = lon_grid[valid_mask]
        valid_lats = lat_grid[valid_mask]
        target_points = np.column_stack([valid_lons, valid_lats])
        
        if method == 'oi':
            # 使用最优插值方法
            interpolated_values = []
            
            for target_point in target_points:
                # 对每个目标点进行最优插值
                interpolated_value = optimal_interpolation_basic(
                    target_point.tolist(), 
                    valid_stations.tolist(), 
                    valid_values.tolist(), 
                    cov_matrix, 
                    stations
                )
                interpolated_values.append(interpolated_value)
            
            interpolated_values = np.array(interpolated_values)
        
        else:
            # 使用scipy的griddata进行插值
            interpolated_values = griddata(
                valid_stations, valid_values, target_points, 
                method=method, fill_value=0.0
            )
            
            # 处理NaN值
            interpolated_values = np.nan_to_num(interpolated_values, nan=0.0)
        
        # 将插值结果填入结果数组
        result_array[valid_mask] = interpolated_values
        
        logging.debug(f"完成栅格插值，方法: {method}, 有效点数: {len(target_points)}")
        
        return result_array
    
    except Exception as e:
        logging.error(f"栅格插值时出错: {e}")
        # 返回填充了nodata值的数组
        nodata_value = header.get('nodata_value', -9999)
        return np.full_like(mask_array, nodata_value, dtype=float)

def save_raster_to_asc(raster_array: np.ndarray, header: Dict, 
                      output_path: str) -> bool:
    """
    将栅格数组保存为ASC文件
    
    Args:
        raster_array: 栅格数组
        header: ASC文件头信息
        output_path: 输出文件路径
    
    Returns:
        bool: 是否保存成功
    """
    try:
        with open(output_path, 'w') as f:
            # 写入头信息
            f.write(f"ncols         {int(header['ncols'])}\n")
            f.write(f"nrows         {int(header['nrows'])}\n")
            f.write(f"xllcorner     {header['xllcorner']}\n")
            f.write(f"yllcorner     {header['yllcorner']}\n")
            f.write(f"cellsize      {header['cellsize']}\n")
            f.write(f"NODATA_value  {header.get('nodata_value', -9999)}\n")
            
            # 写入数据
            for row in raster_array:
                row_str = ' '.join([f"{val:.6f}" if val != header.get('nodata_value', -9999) else str(int(header.get('nodata_value', -9999))) for val in row])
                f.write(row_str + '\n')
        
        logging.debug(f"栅格文件已保存到: {output_path}")
        return True
    
    except Exception as e:
        logging.error(f"保存ASC文件时出错: {e}")
        return False

def calculate_areal_rainfall(raster_array: np.ndarray, header: Dict) -> float:
    """
    计算流域面雨量
    
    Args:
        raster_array: 栅格数组
        header: ASC文件头信息
    
    Returns:
        float: 流域面雨量 (mm)
    """
    try:
        nodata_value = header.get('nodata_value', -9999)
        
        # 提取有效数据
        valid_data = raster_array[raster_array != nodata_value]
        
        if len(valid_data) == 0:
            return 0.0
        
        # 计算面雨量（简单平均）
        areal_rainfall = np.mean(valid_data)
        
        return float(areal_rainfall)
    
    except Exception as e:
        logging.error(f"计算面雨量时出错: {e}")
        return 0.0

def process_raster_output(stations: pd.DataFrame, rainfall_data: pd.DataFrame,
                         mask_array: np.ndarray, header: Dict, 
                         cov_matrix: np.ndarray, output_dir: str,
                         time_indices: List[pd.Timestamp] = None,
                         generate_areal_rainfall: bool = True) -> Dict:
    """
    处理栅格输出
    
    Args:
        stations: 站点信息DataFrame
        rainfall_data: 降雨数据DataFrame
        mask_array: 掩膜数组
        header: ASC文件头信息
        cov_matrix: 协方差矩阵
        output_dir: 输出目录
        time_indices: 要处理的时间索引列表（None表示处理所有时间）
        generate_areal_rainfall: 是否生成面雨量数据
    
    Returns:
        Dict: 处理结果统计
    """
    try:
        # 创建栅格输出目录
        raster_dir = os.path.join(output_dir, "raster_output")
        os.makedirs(raster_dir, exist_ok=True)
        
        # 如果没有指定时间索引，使用所有时间
        if time_indices is None:
            time_indices = rainfall_data.index.tolist()
        
        # 统计信息
        stats = {
            'total_time_steps': len(time_indices),
            'successful_rasters': 0,
            'failed_rasters': 0,
            'areal_rainfall_data': []
        }
        
        logging.info(f"开始生成栅格输出，共 {len(time_indices)} 个时间步")
        
        for i, time_index in enumerate(time_indices):
            try:
                # 生成安全的文件名
                safe_time = safe_filename(str(time_index))
                raster_filename = f"rainfall_{safe_time}.asc"
                raster_path = os.path.join(raster_dir, raster_filename)
                
                # 进行栅格插值
                raster_array = interpolate_to_grid(
                    stations, rainfall_data, time_index, 
                    mask_array, header, cov_matrix, method='oi'
                )
                
                # 保存栅格文件
                if save_raster_to_asc(raster_array, header, raster_path):
                    stats['successful_rasters'] += 1
                    
                    # 计算面雨量
                    if generate_areal_rainfall:
                        areal_rainfall = calculate_areal_rainfall(raster_array, header)
                        stats['areal_rainfall_data'].append({
                            'time': time_index,
                            'areal_rainfall': areal_rainfall
                        })
                else:
                    stats['failed_rasters'] += 1
                
                # 进度报告
                if (i + 1) % 10 == 0 or i == len(time_indices) - 1:
                    logging.info(f"栅格生成进度: {i + 1}/{len(time_indices)}")
            
            except Exception as e:
                logging.error(f"处理时间步 {time_index} 的栅格时出错: {e}")
                stats['failed_rasters'] += 1
                continue
        
        # 保存面雨量数据
        if generate_areal_rainfall and stats['areal_rainfall_data']:
            areal_df = pd.DataFrame(stats['areal_rainfall_data'])
            areal_path = os.path.join(output_dir, "areal_rainfall.csv")
            areal_df.to_csv(areal_path, index=False, encoding='utf-8')
            logging.info(f"面雨量数据已保存到: {areal_path}")
        
        logging.info(f"栅格输出完成，成功: {stats['successful_rasters']}, 失败: {stats['failed_rasters']}")
        
        return stats
    
    except Exception as e:
        logging.error(f"处理栅格输出时出错: {e}")
        return {'error': str(e)}

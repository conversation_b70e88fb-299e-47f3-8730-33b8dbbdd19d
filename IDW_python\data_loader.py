#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据加载模块
负责加载Delaunay分析结果和洪水事件数据

作者: 空间插值系统
日期: 2024年
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class DataLoader:
    """数据加载器类"""
    
    def __init__(self, config):
        self.config = config
        self.delaunay_data = None
        self.station_coords = {}
        self.load_delaunay_data()
    
    def load_delaunay_data(self):
        """加载Delaunay三角网分析结果"""
        try:
            logger.info(f"加载Delaunay分析数据: {self.config.delaunay_file}")
            
            # 读取CSV文件
            self.delaunay_data = pd.read_csv(self.config.delaunay_file, encoding='utf-8')
            
            # 验证必要列是否存在
            required_columns = [
                '验证站点代码', '验证站点名称', '验证站点经度', '验证站点纬度',
                '包围站点1代码', '包围站点1名称', '包围站点2代码', '包围站点2名称', 
                '包围站点3代码', '包围站点3名称', '权重1', '权重2', '权重3'
            ]
            
            missing_columns = [col for col in required_columns if col not in self.delaunay_data.columns]
            if missing_columns:
                raise ValueError(f"Delaunay数据缺少必要列: {missing_columns}")
            
            # 构建站点坐标字典
            self._build_station_coordinates()
            
            logger.info(f"成功加载{len(self.delaunay_data)}个验证站点的Delaunay分析结果")
            
        except Exception as e:
            logger.error(f"加载Delaunay数据失败: {e}")
            raise
    
    def _build_station_coordinates(self):
        """构建站点坐标字典"""
        # 从验证站点获取坐标
        for _, row in self.delaunay_data.iterrows():
            station_id = str(row['验证站点代码'])
            self.station_coords[station_id] = {
                'name': row['验证站点名称'],
                'longitude': row['验证站点经度'],
                'latitude': row['验证站点纬度']
            }
        
        # 从包围站点获取坐标（需要从其他验证站点中查找）
        for _, row in self.delaunay_data.iterrows():
            for i in range(1, 4):
                station_id = str(row[f'包围站点{i}代码'])
                if station_id not in self.station_coords:
                    # 尝试从验证站点中查找该站点的坐标
                    coord_row = self.delaunay_data[
                        self.delaunay_data['验证站点代码'].astype(str) == station_id
                    ]
                    if not coord_row.empty:
                        self.station_coords[station_id] = {
                            'name': coord_row.iloc[0]['验证站点名称'],
                            'longitude': coord_row.iloc[0]['验证站点经度'],
                            'latitude': coord_row.iloc[0]['验证站点纬度']
                        }
                    else:
                        logger.warning(f"未找到站点{station_id}的坐标信息")
    
    def get_surrounding_stations(self, target_station_id: str) -> Optional[Dict]:
        """获取目标站点的包围站点信息"""
        target_station_id = str(target_station_id)
        
        # 查找目标站点的Delaunay信息
        station_row = self.delaunay_data[
            self.delaunay_data['验证站点代码'].astype(str) == target_station_id
        ]
        
        if station_row.empty:
            logger.warning(f"未找到站点{target_station_id}的Delaunay分析结果")
            return None
        
        row = station_row.iloc[0]
        
        # 构建包围站点信息
        surrounding_info = {
            'target_station': {
                'id': target_station_id,
                'name': row['验证站点名称'],
                'longitude': row['验证站点经度'],
                'latitude': row['验证站点纬度']
            },
            'surrounding_stations': []
        }
        
        # 添加包围站点信息
        for i in range(1, 4):
            station_id = str(row[f'包围站点{i}代码'])
            station_name = row[f'包围站点{i}名称']
            weight = row[f'权重{i}']
            
            # 获取站点坐标
            if station_id in self.station_coords:
                coord_info = self.station_coords[station_id]
                surrounding_info['surrounding_stations'].append({
                    'id': station_id,
                    'name': station_name,
                    'longitude': coord_info['longitude'],
                    'latitude': coord_info['latitude'],
                    'weight': weight
                })
            else:
                logger.warning(f"包围站点{station_id}缺少坐标信息")
        
        return surrounding_info
    
    def load_flood_event_data(self, event_name: str) -> Dict[str, pd.DataFrame]:
        """加载指定洪水事件的所有站点数据"""
        event_dir = self.config.input_dir / event_name
        if not event_dir.exists():
            raise FileNotFoundError(f"洪水事件目录不存在: {event_dir}")
        
        station_data = {}
        
        # 遍历事件目录中的所有CSV文件
        for csv_file in event_dir.glob("*.csv"):
            station_id = csv_file.stem  # 文件名作为站点ID
            
            try:
                # 读取站点数据
                df = pd.read_csv(csv_file, encoding='utf-8')
                
                # 验证数据格式
                if '时间' not in df.columns or '雨量' not in df.columns:
                    logger.warning(f"站点{station_id}数据格式不正确，跳过")
                    continue
                
                # 转换时间列
                df['时间'] = pd.to_datetime(df['时间'])
                
                # 确保雨量为数值类型
                df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce')
                
                # 按时间排序
                df = df.sort_values('时间').reset_index(drop=True)
                
                station_data[station_id] = df
                
            except Exception as e:
                logger.error(f"加载站点{station_id}数据失败: {e}")
                continue
        
        logger.info(f"成功加载洪水事件{event_name}的{len(station_data)}个站点数据")
        return station_data
    
    def get_station_rainfall_at_time(self, station_data: Dict[str, pd.DataFrame], 
                                   station_id: str, target_time: datetime) -> Optional[float]:
        """获取指定站点在指定时间的降雨量"""
        if station_id not in station_data:
            return None
        
        df = station_data[station_id]
        
        # 查找最接近的时间点
        time_diff = abs(df['时间'] - target_time)
        closest_idx = time_diff.idxmin()
        
        # 检查时间差是否在合理范围内（1小时）
        if time_diff.iloc[closest_idx].total_seconds() <= 3600:
            rainfall = df.iloc[closest_idx]['雨量']
            return rainfall if not pd.isna(rainfall) else None
        
        return None
    
    def get_all_timestamps(self, station_data: Dict[str, pd.DataFrame]) -> List[datetime]:
        """获取所有站点数据的时间戳并集"""
        all_timestamps = set()
        
        for df in station_data.values():
            all_timestamps.update(df['时间'].tolist())
        
        return sorted(list(all_timestamps))
    
    def get_available_stations(self) -> List[str]:
        """获取所有可用的验证站点列表"""
        return self.delaunay_data['验证站点代码'].astype(str).tolist()
    
    def get_station_info(self, station_id: str) -> Optional[Dict]:
        """获取站点基本信息"""
        station_id = str(station_id)
        
        if station_id in self.station_coords:
            return self.station_coords[station_id]
        
        return None

if __name__ == "__main__":
    # 测试数据加载器
    from idw_config import config
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建数据加载器
    loader = DataLoader(config)
    
    # 测试获取包围站点信息
    test_station = "80606500"
    surrounding_info = loader.get_surrounding_stations(test_station)
    if surrounding_info:
        print(f"站点{test_station}的包围站点信息:")
        print(f"目标站点: {surrounding_info['target_station']}")
        print("包围站点:")
        for station in surrounding_info['surrounding_stations']:
            print(f"  {station}")
    
    # 测试加载洪水事件数据
    flood_events = config.get_flood_events()
    if flood_events:
        test_event = flood_events[0]
        print(f"\n测试加载洪水事件: {test_event}")
        event_data = loader.load_flood_event_data(test_event)
        print(f"加载了{len(event_data)}个站点的数据")

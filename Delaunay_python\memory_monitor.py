#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存监控工具

用于监控和管理Delaunay插值系统的内存使用
"""

import psutil
import gc
import logging
import time
from typing import Dict, Optional
import threading

logger = logging.getLogger(__name__)

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, warning_threshold_mb: float = 1000, critical_threshold_mb: float = 2000):
        """
        初始化内存监控器
        
        Args:
            warning_threshold_mb: 警告阈值（MB）
            critical_threshold_mb: 严重阈值（MB）
        """
        self.warning_threshold = warning_threshold_mb
        self.critical_threshold = critical_threshold_mb
        self.process = psutil.Process()
        self.monitoring = False
        self.monitor_thread = None
        self.memory_history = []
        
    def get_memory_usage(self) -> Dict[str, float]:
        """获取当前内存使用情况"""
        try:
            memory_info = self.process.memory_info()
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
                'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
                'percent': self.process.memory_percent(),  # 内存使用百分比
                'available_mb': psutil.virtual_memory().available / 1024 / 1024  # 可用内存
            }
        except Exception as e:
            logger.error(f"获取内存使用情况失败: {e}")
            return {'rss_mb': 0, 'vms_mb': 0, 'percent': 0, 'available_mb': 0}
    
    def check_memory_status(self) -> str:
        """检查内存状态"""
        memory_usage = self.get_memory_usage()
        rss_mb = memory_usage['rss_mb']
        
        if rss_mb > self.critical_threshold:
            return 'critical'
        elif rss_mb > self.warning_threshold:
            return 'warning'
        else:
            return 'normal'
    
    def force_garbage_collection(self) -> Dict[str, float]:
        """强制垃圾回收"""
        memory_before = self.get_memory_usage()
        
        # 执行垃圾回收
        collected = gc.collect()
        
        memory_after = self.get_memory_usage()
        
        freed_mb = memory_before['rss_mb'] - memory_after['rss_mb']
        
        logger.info(f"垃圾回收完成: 回收{collected}个对象, 释放{freed_mb:.1f}MB内存")
        
        return {
            'before_mb': memory_before['rss_mb'],
            'after_mb': memory_after['rss_mb'],
            'freed_mb': freed_mb,
            'collected_objects': collected
        }
    
    def start_monitoring(self, interval_seconds: float = 30):
        """开始内存监控"""
        if self.monitoring:
            logger.warning("内存监控已在运行")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval_seconds,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        logger.info(f"内存监控已启动，监控间隔: {interval_seconds}秒")
    
    def stop_monitoring(self):
        """停止内存监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("内存监控已停止")
    
    def _monitor_loop(self, interval_seconds: float):
        """监控循环"""
        while self.monitoring:
            try:
                memory_usage = self.get_memory_usage()
                status = self.check_memory_status()
                
                # 记录内存历史
                self.memory_history.append({
                    'timestamp': time.time(),
                    'memory_usage': memory_usage,
                    'status': status
                })
                
                # 保持历史记录在合理范围内
                if len(self.memory_history) > 1000:
                    self.memory_history = self.memory_history[-500:]
                
                # 根据状态采取行动
                if status == 'critical':
                    logger.warning(f"内存使用严重: {memory_usage['rss_mb']:.1f}MB, "
                                 f"可用内存: {memory_usage['available_mb']:.1f}MB")
                    self.force_garbage_collection()
                elif status == 'warning':
                    logger.info(f"内存使用警告: {memory_usage['rss_mb']:.1f}MB")
                
                time.sleep(interval_seconds)
                
            except Exception as e:
                logger.error(f"内存监控循环错误: {e}")
                time.sleep(interval_seconds)
    
    def get_memory_summary(self) -> Dict:
        """获取内存使用汇总"""
        if not self.memory_history:
            return {}
        
        rss_values = [entry['memory_usage']['rss_mb'] for entry in self.memory_history]
        
        return {
            'current_mb': self.get_memory_usage()['rss_mb'],
            'max_mb': max(rss_values),
            'min_mb': min(rss_values),
            'avg_mb': sum(rss_values) / len(rss_values),
            'warning_count': sum(1 for entry in self.memory_history if entry['status'] == 'warning'),
            'critical_count': sum(1 for entry in self.memory_history if entry['status'] == 'critical'),
            'total_samples': len(self.memory_history)
        }
    
    def optimize_memory(self) -> Dict[str, float]:
        """内存优化"""
        logger.info("开始内存优化...")
        
        memory_before = self.get_memory_usage()
        
        # 1. 强制垃圾回收
        gc_result = self.force_garbage_collection()
        
        # 2. 清理缓存（如果可能）
        try:
            import sys
            if hasattr(sys, '_clear_type_cache'):
                sys._clear_type_cache()
        except Exception as e:
            logger.debug(f"清理类型缓存失败: {e}")
        
        memory_after = self.get_memory_usage()
        total_freed = memory_before['rss_mb'] - memory_after['rss_mb']
        
        logger.info(f"内存优化完成，总共释放: {total_freed:.1f}MB")
        
        return {
            'before_mb': memory_before['rss_mb'],
            'after_mb': memory_after['rss_mb'],
            'total_freed_mb': total_freed,
            'gc_freed_mb': gc_result['freed_mb']
        }
    
    def is_memory_available(self, required_mb: float) -> bool:
        """检查是否有足够的可用内存"""
        memory_usage = self.get_memory_usage()
        available_mb = memory_usage['available_mb']
        
        return available_mb > required_mb
    
    def wait_for_memory(self, required_mb: float, timeout_seconds: float = 300, 
                       check_interval: float = 10) -> bool:
        """等待足够的内存可用"""
        start_time = time.time()
        
        while time.time() - start_time < timeout_seconds:
            if self.is_memory_available(required_mb):
                return True
            
            logger.info(f"等待{required_mb:.1f}MB内存可用...")
            self.optimize_memory()
            time.sleep(check_interval)
        
        logger.warning(f"等待内存超时: 需要{required_mb:.1f}MB")
        return False

# 全局内存监控器实例
_global_monitor: Optional[MemoryMonitor] = None

def get_memory_monitor() -> MemoryMonitor:
    """获取全局内存监控器实例"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = MemoryMonitor()
    return _global_monitor

def start_global_monitoring(interval_seconds: float = 30):
    """启动全局内存监控"""
    monitor = get_memory_monitor()
    monitor.start_monitoring(interval_seconds)

def stop_global_monitoring():
    """停止全局内存监控"""
    global _global_monitor
    if _global_monitor:
        _global_monitor.stop_monitoring()

def optimize_global_memory() -> Dict[str, float]:
    """优化全局内存"""
    monitor = get_memory_monitor()
    return monitor.optimize_memory()

def check_memory_and_optimize(threshold_mb: float = 1000) -> bool:
    """检查内存并在需要时优化"""
    monitor = get_memory_monitor()
    memory_usage = monitor.get_memory_usage()
    
    if memory_usage['rss_mb'] > threshold_mb:
        logger.info(f"内存使用超过阈值({threshold_mb}MB)，开始优化...")
        monitor.optimize_memory()
        return True
    
    return False

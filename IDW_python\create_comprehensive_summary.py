#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IDW插值系统综合汇总报告生成器
基于所有处理结果生成综合分析报告

作者: 空间插值系统
日期: 2024年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging
from datetime import datetime
import warnings

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def load_all_evaluation_results(base_dir):
    """加载所有评估结果"""
    logger = logging.getLogger(__name__)
    
    evaluation_dir = Path(base_dir) / "evaluation_metrics"
    
    if not evaluation_dir.exists():
        logger.error(f"评估结果目录不存在: {evaluation_dir}")
        return None, None
    
    all_detailed_metrics = []
    all_summary_metrics = []
    
    # 遍历所有事件目录
    for event_dir in evaluation_dir.iterdir():
        if event_dir.is_dir():
            event_name = event_dir.name
            
            # 读取详细指标
            detailed_file = event_dir / f"{event_name}_detailed_metrics.csv"
            if detailed_file.exists():
                try:
                    df = pd.read_csv(detailed_file, index_col=0)
                    df['Event'] = event_name
                    all_detailed_metrics.append(df)
                except Exception as e:
                    logger.warning(f"读取{detailed_file}失败: {e}")
            
            # 读取汇总指标
            summary_file = event_dir / f"{event_name}_summary_metrics.csv"
            if summary_file.exists():
                try:
                    df = pd.read_csv(summary_file)
                    df['Event'] = event_name
                    all_summary_metrics.append(df)
                except Exception as e:
                    logger.warning(f"读取{summary_file}失败: {e}")
    
    # 合并数据
    detailed_df = pd.concat(all_detailed_metrics, ignore_index=True) if all_detailed_metrics else None
    summary_df = pd.concat(all_summary_metrics, ignore_index=True) if all_summary_metrics else None
    
    logger.info(f"成功加载{len(all_detailed_metrics)}个事件的详细指标")
    logger.info(f"成功加载{len(all_summary_metrics)}个事件的汇总指标")
    
    return detailed_df, summary_df

def create_overall_performance_summary(detailed_df, output_dir):
    """创建整体性能汇总图"""
    logger = logging.getLogger(__name__)
    
    if detailed_df is None or detailed_df.empty:
        logger.warning("没有详细指标数据，跳过整体性能汇总")
        return None
    
    # 创建图形
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    # 主要指标
    metrics = ['MAE', 'RMSE', 'NSE', 'R2', 'BIAS', 'KGE']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    
    for i, metric in enumerate(metrics):
        ax = axes[i]
        
        if metric in detailed_df.columns:
            # 过滤有效数据
            valid_data = detailed_df[metric].dropna()
            
            if len(valid_data) > 0:
                # 绘制直方图
                ax.hist(valid_data, bins=30, alpha=0.7, color=colors[i], 
                       edgecolor='black', linewidth=0.5)
                
                # 添加统计线
                mean_val = valid_data.mean()
                median_val = valid_data.median()
                
                ax.axvline(mean_val, color='red', linestyle='--', linewidth=2, 
                          label=f'均值: {mean_val:.3f}')
                ax.axvline(median_val, color='orange', linestyle='--', linewidth=2, 
                          label=f'中位数: {median_val:.3f}')
                
                # 设置标题和标签
                ax.set_title(f'{metric}分布 (N={len(valid_data)})', 
                           fontsize=14, fontweight='bold')
                ax.set_xlabel(metric, fontsize=12)
                ax.set_ylabel('频数', fontsize=12)
                ax.legend()
                ax.grid(True, alpha=0.3)
                
                # 添加统计信息
                stats_text = f'标准差: {valid_data.std():.3f}\n最小值: {valid_data.min():.3f}\n最大值: {valid_data.max():.3f}'
                ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                       verticalalignment='top', fontsize=10,
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            else:
                ax.text(0.5, 0.5, f'无有效{metric}数据', ha='center', va='center', 
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'{metric}分布', fontsize=14, fontweight='bold')
        else:
            ax.text(0.5, 0.5, f'{metric}列不存在', ha='center', va='center', 
                   transform=ax.transAxes, fontsize=14)
            ax.set_title(f'{metric}分布', fontsize=14, fontweight='bold')
    
    # 设置总标题
    fig.suptitle('IDW插值系统整体性能指标分布', fontsize=16, fontweight='bold')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    output_file = output_dir / "IDW_overall_performance_summary.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close(fig)
    
    logger.info(f"整体性能汇总图已保存: {output_file}")
    return output_file

def create_event_comparison_chart(detailed_df, output_dir):
    """创建事件对比图表"""
    logger = logging.getLogger(__name__)
    
    if detailed_df is None or detailed_df.empty:
        logger.warning("没有详细指标数据，跳过事件对比图")
        return None
    
    # 按事件计算平均指标
    event_metrics = detailed_df.groupby('Event')[['MAE', 'RMSE', 'NSE', 'R2']].mean()
    
    if event_metrics.empty:
        logger.warning("没有有效的事件指标数据")
        return None
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    metrics = ['MAE', 'RMSE', 'NSE', 'R2']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    for i, metric in enumerate(metrics):
        ax = axes[i]
        
        if metric in event_metrics.columns:
            # 过滤有效数据
            valid_data = event_metrics[metric].dropna()
            
            if len(valid_data) > 0:
                # 排序
                valid_data = valid_data.sort_values(ascending=(metric in ['MAE', 'RMSE']))
                
                # 绘制柱状图
                bars = ax.bar(range(len(valid_data)), valid_data.values, 
                             color=colors[i], alpha=0.8, edgecolor='black')
                
                # 设置x轴标签
                ax.set_xticks(range(len(valid_data)))
                ax.set_xticklabels(valid_data.index, rotation=45, ha='right')
                
                # 在柱子上添加数值
                for j, bar in enumerate(bars):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{height:.3f}', ha='center', va='bottom', fontsize=8)
                
                # 设置标题和标签
                ax.set_title(f'各洪水事件{metric}对比', fontsize=14, fontweight='bold')
                ax.set_ylabel(metric, fontsize=12)
                ax.grid(True, alpha=0.3, axis='y')
                
                # 添加平均线
                mean_val = valid_data.mean()
                ax.axhline(mean_val, color='red', linestyle='--', linewidth=2, 
                          label=f'平均值: {mean_val:.3f}')
                ax.legend()
            else:
                ax.text(0.5, 0.5, f'无有效{metric}数据', ha='center', va='center', 
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'各洪水事件{metric}对比', fontsize=14, fontweight='bold')
        else:
            ax.text(0.5, 0.5, f'{metric}列不存在', ha='center', va='center', 
                   transform=ax.transAxes, fontsize=14)
            ax.set_title(f'各洪水事件{metric}对比', fontsize=14, fontweight='bold')
    
    # 设置总标题
    fig.suptitle('IDW插值系统各洪水事件性能对比', fontsize=16, fontweight='bold')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    output_file = output_dir / "IDW_event_comparison.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close(fig)
    
    logger.info(f"事件对比图已保存: {output_file}")
    return output_file

def create_performance_classification_summary(detailed_df, output_dir):
    """创建性能分类汇总"""
    logger = logging.getLogger(__name__)
    
    if detailed_df is None or detailed_df.empty:
        logger.warning("没有详细指标数据，跳过性能分类汇总")
        return None
    
    # 定义性能分类函数
    def classify_performance(nse, r2):
        if pd.isna(nse) or pd.isna(r2):
            return "无效"
        if nse > 0.75 and r2 > 0.75:
            return "优秀"
        elif nse > 0.65 and r2 > 0.65:
            return "良好"
        elif nse > 0.50 and r2 > 0.50:
            return "满意"
        elif nse > 0.30 and r2 > 0.30:
            return "可接受"
        else:
            return "不满意"
    
    # 应用分类
    detailed_df['Performance'] = detailed_df.apply(
        lambda row: classify_performance(row.get('NSE'), row.get('R2')), axis=1
    )
    
    # 统计各性能等级
    performance_counts = detailed_df['Performance'].value_counts()
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 饼图
    colors = ['#2E8B57', '#32CD32', '#FFD700', '#FFA500', '#FF6347', '#808080']
    wedges, texts, autotexts = ax1.pie(performance_counts.values, 
                                      labels=performance_counts.index,
                                      autopct='%1.1f%%',
                                      colors=colors[:len(performance_counts)],
                                      startangle=90)
    
    ax1.set_title('IDW插值系统性能分类分布', fontsize=14, fontweight='bold')
    
    # 柱状图
    bars = ax2.bar(performance_counts.index, performance_counts.values, 
                  color=colors[:len(performance_counts)], alpha=0.8, edgecolor='black')
    
    # 在柱子上添加数值标签
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{int(height)}', ha='center', va='bottom', fontsize=12)
    
    ax2.set_title('各性能等级站点数量', fontsize=14, fontweight='bold')
    ax2.set_xlabel('性能等级', fontsize=12)
    ax2.set_ylabel('站点数量', fontsize=12)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 旋转x轴标签
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    output_file = output_dir / "IDW_performance_classification_summary.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close(fig)
    
    logger.info(f"性能分类汇总图已保存: {output_file}")
    return output_file

def create_comprehensive_statistics_table(detailed_df, output_dir):
    """创建综合统计表"""
    logger = logging.getLogger(__name__)
    
    if detailed_df is None or detailed_df.empty:
        logger.warning("没有详细指标数据，跳过统计表创建")
        return None
    
    # 计算综合统计
    metrics = ['MAE', 'RMSE', 'NSE', 'R2', 'BIAS', 'KGE']
    stats_data = []
    
    for metric in metrics:
        if metric in detailed_df.columns:
            valid_data = detailed_df[metric].dropna()
            if len(valid_data) > 0:
                stats_data.append({
                    '指标': metric,
                    '样本数': len(valid_data),
                    '均值': valid_data.mean(),
                    '中位数': valid_data.median(),
                    '标准差': valid_data.std(),
                    '最小值': valid_data.min(),
                    '最大值': valid_data.max(),
                    '25%分位数': valid_data.quantile(0.25),
                    '75%分位数': valid_data.quantile(0.75)
                })
    
    if not stats_data:
        logger.warning("没有有效的统计数据")
        return None
    
    # 创建DataFrame
    stats_df = pd.DataFrame(stats_data)
    
    # 保存CSV文件
    csv_file = output_dir / "IDW_comprehensive_statistics.csv"
    stats_df.to_csv(csv_file, index=False, encoding='utf-8')
    
    logger.info(f"综合统计表已保存: {csv_file}")
    return csv_file

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("开始创建IDW插值系统综合汇总报告")
    
    # 设置路径
    base_dir = Path("../output/IDW")
    output_dir = base_dir / "comprehensive_summary"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 加载所有评估结果
        logger.info("加载评估结果数据...")
        detailed_df, summary_df = load_all_evaluation_results(base_dir)
        
        if detailed_df is None:
            logger.error("无法加载评估结果数据")
            return
        
        logger.info(f"成功加载{len(detailed_df)}条详细指标记录")
        
        # 创建各种汇总图表
        logger.info("创建整体性能汇总图...")
        create_overall_performance_summary(detailed_df, output_dir)
        
        logger.info("创建事件对比图...")
        create_event_comparison_chart(detailed_df, output_dir)
        
        logger.info("创建性能分类汇总图...")
        create_performance_classification_summary(detailed_df, output_dir)
        
        logger.info("创建综合统计表...")
        create_comprehensive_statistics_table(detailed_df, output_dir)
        
        # 保存原始数据
        if detailed_df is not None:
            detailed_file = output_dir / "all_detailed_metrics.csv"
            detailed_df.to_csv(detailed_file, index=False, encoding='utf-8')
            logger.info(f"所有详细指标已保存: {detailed_file}")
        
        if summary_df is not None:
            summary_file = output_dir / "all_summary_metrics.csv"
            summary_df.to_csv(summary_file, index=False, encoding='utf-8')
            logger.info(f"所有汇总指标已保存: {summary_file}")
        
        logger.info("IDW插值系统综合汇总报告创建完成")
        logger.info(f"所有结果保存在: {output_dir}")
        
    except Exception as e:
        logger.error(f"创建综合汇总报告时发生错误: {e}")
        raise

if __name__ == "__main__":
    main()

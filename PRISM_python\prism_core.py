"""
PRISM插值核心算法模块
实现PRISM方法的核心插值算法
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional
from moran_index import MoranIndex

logger = logging.getLogger(__name__)


class PRISMCore:
    """PRISM插值核心算法类"""
    
    def __init__(self, config):
        """初始化PRISM核心算法"""
        self.config = config
        self.moran_calculator = MoranIndex(config)
    
    def calculate_distance_weight(self, test_point: np.ndarray, 
                                neighbor_points: np.ndarray) -> np.ndarray:
        """计算基于距离的权重"""
        try:
            # 计算距离
            distances = np.array([
                np.linalg.norm(test_point - neighbor_point) 
                for neighbor_point in neighbor_points
            ])
            
            # 处理距离为0的情况
            zero_distance_mask = distances < 1e-10
            if np.any(zero_distance_mask):
                weights = np.zeros_like(distances)
                weights[zero_distance_mask] = 1.0
                return weights / np.sum(weights)
            
            # 计算反距离权重
            weights = 1.0 / np.power(distances, self.config.distance_power)
            
            # 归一化
            return weights / np.sum(weights)
            
        except Exception as e:
            logger.error(f"计算距离权重失败: {e}")
            # 返回均等权重
            return np.ones(len(neighbor_points)) / len(neighbor_points)
    
    def calculate_elevation_weight(self, test_elevation: float, 
                                 neighbor_elevations: np.ndarray) -> np.ndarray:
        """计算基于高程的权重"""
        try:
            # 计算高程差异
            elev_diffs = np.abs(neighbor_elevations - test_elevation)
            
            # 如果所有高程相同，返回均等权重
            if np.all(elev_diffs == 0):
                return np.ones(len(neighbor_elevations)) / len(neighbor_elevations)
            
            # 计算相似性权重（高程差异越小，权重越大）
            max_diff = np.max(elev_diffs)
            if max_diff > 0:
                weights = 1.0 - (elev_diffs / max_diff)
            else:
                weights = np.ones_like(elev_diffs)
            
            # 确保权重为正
            weights = np.maximum(weights, 0.1)
            
            # 归一化
            return weights / np.sum(weights)
            
        except Exception as e:
            logger.error(f"计算高程权重失败: {e}")
            return np.ones(len(neighbor_elevations)) / len(neighbor_elevations)
    
    def calculate_slope_weight(self, test_slope: float, 
                             neighbor_slopes: np.ndarray) -> np.ndarray:
        """计算基于坡度的权重"""
        try:
            # 计算坡度差异
            slope_diffs = np.abs(neighbor_slopes - test_slope)
            
            # 如果所有坡度相同，返回均等权重
            if np.all(slope_diffs == 0):
                return np.ones(len(neighbor_slopes)) / len(neighbor_slopes)
            
            # 计算相似性权重
            max_diff = np.max(slope_diffs)
            if max_diff > 0:
                weights = 1.0 - (slope_diffs / max_diff)
            else:
                weights = np.ones_like(slope_diffs)
            
            # 确保权重为正
            weights = np.maximum(weights, 0.1)
            
            # 归一化
            return weights / np.sum(weights)
            
        except Exception as e:
            logger.error(f"计算坡度权重失败: {e}")
            return np.ones(len(neighbor_slopes)) / len(neighbor_slopes)
    
    def calculate_aspect_weight(self, test_aspect: float, 
                              neighbor_aspects: np.ndarray) -> np.ndarray:
        """计算基于坡向的权重"""
        try:
            # 将角度转换为弧度
            test_aspect_rad = np.deg2rad(test_aspect)
            neighbor_aspects_rad = np.deg2rad(neighbor_aspects)
            
            # 计算余弦相似度（考虑坡向的循环性）
            cos_similarities = np.cos(neighbor_aspects_rad - test_aspect_rad)
            
            # 将相似度从[-1, 1]映射到[0, 1]
            weights = (cos_similarities + 1) / 2
            
            # 确保权重为正
            weights = np.maximum(weights, 0.1)
            
            # 归一化
            return weights / np.sum(weights)
            
        except Exception as e:
            logger.error(f"计算坡向权重失败: {e}")
            return np.ones(len(neighbor_aspects)) / len(neighbor_aspects)
    
    def calculate_moran_weight(self, test_moran: float, 
                             neighbor_morans: np.ndarray) -> np.ndarray:
        """计算基于莫兰指数的权重"""
        try:
            if len(neighbor_morans) == 0:
                return np.array([])
            
            # 计算莫兰指数差异
            moran_diffs = np.abs(neighbor_morans - test_moran)
            
            # 如果所有莫兰指数相同，返回均等权重
            if np.all(moran_diffs == 0):
                return np.ones(len(neighbor_morans)) / len(neighbor_morans)
            
            # 计算相似性权重
            max_diff = np.max(moran_diffs)
            if max_diff > 0:
                weights = 1.0 - (moran_diffs / max_diff)
            else:
                weights = np.ones_like(moran_diffs)
            
            # 确保权重为正
            weights = np.maximum(weights, 0.1)
            
            # 归一化
            return weights / np.sum(weights)
            
        except Exception as e:
            logger.error(f"计算莫兰指数权重失败: {e}")
            return np.ones(len(neighbor_morans)) / len(neighbor_morans)
    
    def calculate_rainfall_enhancement_factor(self, neighbor_rainfall: np.ndarray) -> np.ndarray:
        """计算降雨增强因子"""
        try:
            # 检查是否有明显降雨
            has_significant_rain = np.any(neighbor_rainfall > self.config.rainfall_threshold)
            
            if not has_significant_rain:
                # 无明显降雨，返回均等因子
                return np.ones(len(neighbor_rainfall))
            
            # 有明显降雨，增强有降雨站点的权重
            max_rainfall = np.max(neighbor_rainfall)
            if max_rainfall > 0:
                # 归一化降雨值作为增强因子
                enhancement_factor = neighbor_rainfall / max_rainfall
                # 为无降雨站点设置最小增强因子
                enhancement_factor = np.maximum(enhancement_factor, 0.3)
            else:
                enhancement_factor = np.ones(len(neighbor_rainfall))
            
            return enhancement_factor
            
        except Exception as e:
            logger.error(f"计算降雨增强因子失败: {e}")
            return np.ones(len(neighbor_rainfall))
    
    def calculate_prism_weights(self, test_station_info: pd.Series, 
                              neighbor_stations_info: pd.DataFrame,
                              neighbor_rainfall: Optional[np.ndarray] = None) -> np.ndarray:
        """计算PRISM综合权重"""
        try:
            n_neighbors = len(neighbor_stations_info)
            if n_neighbors == 0:
                return np.array([])
            
            # 提取测试点信息
            test_point = np.array([test_station_info['经度'], test_station_info['纬度']])
            test_elevation = test_station_info['高程']
            test_slope = test_station_info['坡度']
            test_aspect = test_station_info['坡向']
            test_moran = test_station_info.get('莫兰指数', 0)
            
            # 提取邻居信息
            neighbor_points = neighbor_stations_info[['经度', '纬度']].values
            neighbor_elevations = neighbor_stations_info['高程'].values
            neighbor_slopes = neighbor_stations_info['坡度'].values
            neighbor_aspects = neighbor_stations_info['坡向'].values
            neighbor_morans = neighbor_stations_info.get('莫兰指数', pd.Series([0] * n_neighbors)).values
            
            # 计算各种权重
            distance_weights = self.calculate_distance_weight(test_point, neighbor_points)
            elevation_weights = self.calculate_elevation_weight(test_elevation, neighbor_elevations)
            slope_weights = self.calculate_slope_weight(test_slope, neighbor_slopes)
            aspect_weights = self.calculate_aspect_weight(test_aspect, neighbor_aspects)
            moran_weights = self.calculate_moran_weight(test_moran, neighbor_morans)
            
            # 综合权重计算
            combined_weights = (
                distance_weights * (1 - self.config.elevation_weight - self.config.slope_weight - 
                                  self.config.aspect_weight - self.config.moran_weight) +
                elevation_weights * self.config.elevation_weight +
                slope_weights * self.config.slope_weight +
                aspect_weights * self.config.aspect_weight +
                moran_weights * self.config.moran_weight
            )
            
            # 如果有降雨数据，应用降雨增强因子
            if neighbor_rainfall is not None:
                enhancement_factor = self.calculate_rainfall_enhancement_factor(neighbor_rainfall)
                combined_weights = combined_weights * enhancement_factor
            
            # 最终归一化
            if np.sum(combined_weights) > 0:
                final_weights = combined_weights / np.sum(combined_weights)
            else:
                final_weights = np.ones(n_neighbors) / n_neighbors
            
            return final_weights
            
        except Exception as e:
            logger.error(f"计算PRISM权重失败: {e}")
            # 返回均等权重作为备选
            return np.ones(len(neighbor_stations_info)) / len(neighbor_stations_info)
    
    def interpolate_point(self, test_station_idx: int, neighbor_indices: List[int],
                         stations_with_terrain: pd.DataFrame, 
                         rainfall_at_time: Dict[str, float]) -> Tuple[float, Dict]:
        """对单个站点进行PRISM插值"""
        try:
            # 获取测试站点信息
            test_station_info = stations_with_terrain.iloc[test_station_idx]
            test_station_name = test_station_info['站点']
            
            # 获取邻居站点信息
            neighbor_stations_info = stations_with_terrain.iloc[neighbor_indices]
            neighbor_names = neighbor_stations_info['站点'].values
            
            # 获取邻居站点的降雨量
            neighbor_rainfall = np.array([
                rainfall_at_time.get(name, 0.0) for name in neighbor_names
            ])
            
            # 检查是否为全零降雨
            if np.all(neighbor_rainfall == 0):
                return 0.0, {
                    'method': '零降雨',
                    'neighbor_stations': neighbor_names.tolist(),
                    'neighbor_rainfall': neighbor_rainfall.tolist(),
                    'weights': np.ones(len(neighbor_names)).tolist()
                }
            
            # 计算PRISM权重
            weights = self.calculate_prism_weights(
                test_station_info, neighbor_stations_info, neighbor_rainfall
            )
            
            # 执行插值
            interpolated_value = np.sum(neighbor_rainfall * weights)
            
            # 确保结果非负
            interpolated_value = max(0.0, interpolated_value)
            
            # 返回结果和详细信息
            interpolation_info = {
                'method': 'PRISM',
                'neighbor_stations': neighbor_names.tolist(),
                'neighbor_rainfall': neighbor_rainfall.tolist(),
                'weights': weights.tolist(),
                'interpolated_value': interpolated_value
            }
            
            return interpolated_value, interpolation_info
            
        except Exception as e:
            logger.error(f"PRISM插值失败 (station_idx={test_station_idx}): {e}")
            # 返回简单平均值作为备选
            neighbor_stations_info = stations_with_terrain.iloc[neighbor_indices]
            neighbor_names = neighbor_stations_info['站点'].values
            neighbor_rainfall = np.array([
                rainfall_at_time.get(name, 0.0) for name in neighbor_names
            ])
            
            fallback_value = np.mean(neighbor_rainfall) if len(neighbor_rainfall) > 0 else 0.0
            
            return fallback_value, {
                'method': '简单平均',
                'neighbor_stations': neighbor_names.tolist(),
                'neighbor_rainfall': neighbor_rainfall.tolist(),
                'weights': (np.ones(len(neighbor_names)) / len(neighbor_names)).tolist()
            }
    
    def interpolate_for_grid_point(self, grid_point_info: Dict, 
                                 neighbor_indices: List[int],
                                 stations_with_terrain: pd.DataFrame,
                                 rainfall_at_time: Dict[str, float]) -> float:
        """为栅格点进行PRISM插值"""
        try:
            # 创建虚拟测试站点信息
            test_station_info = pd.Series({
                '经度': grid_point_info['x'],
                '纬度': grid_point_info['y'],
                '高程': grid_point_info['elevation'],
                '坡度': grid_point_info['slope'],
                '坡向': grid_point_info['aspect'],
                '莫兰指数': 0  # 栅格点的莫兰指数设为0
            })
            
            # 获取邻居站点信息
            neighbor_stations_info = stations_with_terrain.iloc[neighbor_indices]
            neighbor_names = neighbor_stations_info['站点'].values
            
            # 获取邻居站点的降雨量
            neighbor_rainfall = np.array([
                rainfall_at_time.get(name, 0.0) for name in neighbor_names
            ])
            
            # 如果所有邻居都没有降雨，返回0
            if np.all(neighbor_rainfall == 0):
                return 0.0
            
            # 计算PRISM权重
            weights = self.calculate_prism_weights(
                test_station_info, neighbor_stations_info, neighbor_rainfall
            )
            
            # 执行插值
            interpolated_value = np.sum(neighbor_rainfall * weights)
            
            # 确保结果非负
            return max(0.0, interpolated_value)
            
        except Exception as e:
            logger.error(f"栅格点PRISM插值失败: {e}")
            # 返回简单平均值作为备选
            neighbor_stations_info = stations_with_terrain.iloc[neighbor_indices]
            neighbor_names = neighbor_stations_info['站点'].values
            neighbor_rainfall = np.array([
                rainfall_at_time.get(name, 0.0) for name in neighbor_names
            ])
            
            return np.mean(neighbor_rainfall) if len(neighbor_rainfall) > 0 else 0.0

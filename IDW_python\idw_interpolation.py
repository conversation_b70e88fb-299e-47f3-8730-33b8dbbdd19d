#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IDW插值核心算法模块
基于Delaunay三角网分析结果的反距离权重插值

作者: 空间插值系统
日期: 2024年
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime
import math

logger = logging.getLogger(__name__)

class IDWInterpolator:
    """IDW插值器类"""
    
    def __init__(self, config, data_loader):
        self.config = config
        self.data_loader = data_loader
        self.interpolation_results = {}
    
    def calculate_distance(self, lon1: float, lat1: float, lon2: float, lat2: float) -> float:
        """计算两点间的地理距离（单位：公里）"""
        # 使用Haversine公式计算球面距离
        R = 6371.0  # 地球半径（公里）
        
        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # 计算差值
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        # Haversine公式
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c
        
        return distance
    
    def calculate_idw_weights(self, target_coords: Tuple[float, float], 
                            station_coords: List[Tuple[float, float]], 
                            power: float = 2.0) -> List[float]:
        """计算IDW权重"""
        target_lon, target_lat = target_coords
        weights = []
        distances = []
        
        # 计算距离
        for station_lon, station_lat in station_coords:
            distance = self.calculate_distance(target_lon, target_lat, station_lon, station_lat)
            distances.append(distance)
        
        # 检查是否有距离为0的点（完全重合）
        min_distance = min(distances)
        if min_distance < 0.001:  # 距离小于1米认为重合
            # 如果有重合点，该点权重为1，其他为0
            weights = [1.0 if d < 0.001 else 0.0 for d in distances]
        else:
            # 计算IDW权重
            inverse_distances = [1.0 / (d ** power) for d in distances]
            total_inverse = sum(inverse_distances)
            weights = [inv_d / total_inverse for inv_d in inverse_distances]
        
        return weights
    
    def interpolate_single_point(self, target_station_id: str, station_data: Dict[str, pd.DataFrame], 
                               target_time: datetime, use_delaunay_weights: bool = True) -> Optional[float]:
        """对单个站点单个时刻进行插值"""
        # 获取目标站点的包围站点信息
        surrounding_info = self.data_loader.get_surrounding_stations(target_station_id)
        if not surrounding_info:
            logger.warning(f"无法获取站点{target_station_id}的包围站点信息")
            return None
        
        # 收集包围站点的降雨数据
        station_values = []
        station_coords = []
        station_weights = []
        
        for station_info in surrounding_info['surrounding_stations']:
            station_id = station_info['id']
            
            # 获取该站点在目标时间的降雨量
            rainfall = self.data_loader.get_station_rainfall_at_time(
                station_data, station_id, target_time
            )
            
            if rainfall is not None:
                station_values.append(rainfall)
                station_coords.append((station_info['longitude'], station_info['latitude']))
                if use_delaunay_weights:
                    station_weights.append(station_info['weight'])
        
        # 检查是否有足够的数据
        if len(station_values) < self.config.min_stations:
            logger.debug(f"站点{target_station_id}在时间{target_time}的有效包围站点数不足")
            return None
        
        # 计算插值结果
        if use_delaunay_weights and len(station_weights) == len(station_values):
            # 使用Delaunay权重
            # 归一化权重
            total_weight = sum(station_weights)
            if total_weight > 0:
                normalized_weights = [w / total_weight for w in station_weights]
                interpolated_value = sum(v * w for v, w in zip(station_values, normalized_weights))
            else:
                return None
        else:
            # 使用传统IDW权重
            target_coords = (
                surrounding_info['target_station']['longitude'],
                surrounding_info['target_station']['latitude']
            )
            idw_weights = self.calculate_idw_weights(
                target_coords, station_coords, self.config.idw_power
            )
            interpolated_value = sum(v * w for v, w in zip(station_values, idw_weights))
        
        return max(0.0, interpolated_value)  # 确保降雨量非负
    
    def interpolate_station_timeseries(self, target_station_id: str, 
                                     station_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """对单个站点的整个时间序列进行插值"""
        logger.info(f"开始对站点{target_station_id}进行时间序列插值")
        
        # 获取所有时间戳
        all_timestamps = self.data_loader.get_all_timestamps(station_data)
        
        # 准备结果DataFrame
        results = []
        
        for timestamp in all_timestamps:
            # 进行插值
            interpolated_value = self.interpolate_single_point(
                target_station_id, station_data, timestamp, 
                self.config.use_delaunay_weights
            )
            
            # 获取观测值（如果存在）
            observed_value = self.data_loader.get_station_rainfall_at_time(
                station_data, target_station_id, timestamp
            )
            
            results.append({
                '时间': timestamp,
                '观测值': observed_value,
                '插值': interpolated_value,
                '站点代码': target_station_id
            })
        
        result_df = pd.DataFrame(results)
        
        # 过滤掉插值失败的记录
        valid_results = result_df.dropna(subset=['插值'])
        
        logger.info(f"站点{target_station_id}插值完成，有效记录数: {len(valid_results)}/{len(results)}")
        
        return result_df
    
    def interpolate_flood_event(self, event_name: str) -> Dict[str, pd.DataFrame]:
        """对整个洪水事件进行插值"""
        logger.info(f"开始处理洪水事件: {event_name}")
        
        # 加载洪水事件数据
        station_data = self.data_loader.load_flood_event_data(event_name)
        
        # 获取所有可用的验证站点
        available_stations = self.data_loader.get_available_stations()
        
        # 过滤出在当前事件中有数据的站点
        event_stations = [station_id for station_id in available_stations 
                         if station_id in station_data]
        
        logger.info(f"洪水事件{event_name}中有{len(event_stations)}个验证站点有数据")
        
        # 对每个站点进行插值
        event_results = {}
        
        for station_id in event_stations:
            try:
                result_df = self.interpolate_station_timeseries(station_id, station_data)
                event_results[station_id] = result_df
                
            except Exception as e:
                logger.error(f"站点{station_id}插值失败: {e}")
                continue
        
        # 保存结果
        self.interpolation_results[event_name] = event_results
        
        logger.info(f"洪水事件{event_name}插值完成，成功处理{len(event_results)}个站点")
        
        return event_results
    
    def save_interpolation_results(self, event_name: str, results: Dict[str, pd.DataFrame]):
        """保存插值结果"""
        output_dir = self.config.output_dir / "interpolation_results" / event_name
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存每个站点的结果
        for station_id, result_df in results.items():
            output_file = output_dir / f"{station_id}_interpolation.csv"
            result_df.to_csv(output_file, index=False, encoding='utf-8')
        
        # 保存汇总结果
        if results:
            # 合并所有站点的结果
            all_results = []
            for station_id, result_df in results.items():
                station_df = result_df.copy()
                station_df['站点代码'] = station_id
                all_results.append(station_df)
            
            combined_df = pd.concat(all_results, ignore_index=True)
            summary_file = output_dir / f"{event_name}_interpolation_summary.csv"
            combined_df.to_csv(summary_file, index=False, encoding='utf-8')
            
            logger.info(f"插值结果已保存到: {output_dir}")
    
    def get_interpolation_summary(self, event_name: str) -> Dict:
        """获取插值结果汇总统计"""
        if event_name not in self.interpolation_results:
            return {}
        
        results = self.interpolation_results[event_name]
        summary = {
            'event_name': event_name,
            'total_stations': len(results),
            'station_summaries': {}
        }
        
        for station_id, result_df in results.items():
            valid_data = result_df.dropna(subset=['观测值', '插值'])
            
            station_summary = {
                'total_records': len(result_df),
                'valid_records': len(valid_data),
                'success_rate': len(valid_data) / len(result_df) if len(result_df) > 0 else 0,
                'mean_observed': valid_data['观测值'].mean() if len(valid_data) > 0 else None,
                'mean_interpolated': valid_data['插值'].mean() if len(valid_data) > 0 else None
            }
            
            summary['station_summaries'][station_id] = station_summary
        
        return summary

if __name__ == "__main__":
    # 测试IDW插值器
    from idw_config import config
    from data_loader import DataLoader
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建数据加载器和插值器
    loader = DataLoader(config)
    interpolator = IDWInterpolator(config, loader)
    
    # 测试单个洪水事件的插值
    flood_events = config.get_flood_events()
    if flood_events:
        test_event = flood_events[0]
        print(f"测试洪水事件: {test_event}")
        
        results = interpolator.interpolate_flood_event(test_event)
        summary = interpolator.get_interpolation_summary(test_event)
        
        print(f"插值结果汇总: {summary}")
        
        # 保存结果
        interpolator.save_interpolation_results(test_event, results)

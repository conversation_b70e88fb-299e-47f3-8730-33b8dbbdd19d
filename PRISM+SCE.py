# -*- coding: utf-8 -*-
"""
PRISM空间插值方法实现
用于水文学点雨量转面雨量研究
包含点雨量插值和面雨量ASC栅格生成
"""

# 导入必要的库
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay
from scipy.interpolate import griddata
from multiprocessing import Pool, cpu_count
import rasterio
import time
import glob
import warnings
import logging
from tqdm import tqdm
import gc  # 用于垃圾回收，提高内存利用效率
from datetime import datetime
from sceua import SCEUA
# 忽略不必要的警告
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("prism_interpolation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()


def read_asc_file(file_path):
    """
    读取ASC格式的栅格文件
    
    参数:
        file_path: ASC文件路径
    
    返回:
        data: 栅格数据
        transform: 坐标转换信息
        nodata: 无数据值
    """
    try:
        # 使用rasterio打开ASC文件
        with rasterio.open(file_path) as src:
            data = src.read(1)  # 读取第一个波段
            transform = src.transform  # 获取坐标转换信息
            nodata = src.nodata  # 获取无数据值
        return data, transform, nodata
    except Exception as e:
        logger.error(f"读取ASC文件 {file_path} 失败: {e}")
        raise


def get_value_at_point(data, transform, x, y, nodata):
    """
    从栅格数据中获取特定坐标点的值
    
    参数:
        data: 栅格数据
        transform: 坐标转换信息
        x, y: 坐标点
        nodata: 无数据值
    
    返回:
        栅格点的值，如果越界或为nodata则返回np.nan
    """
    try:
        # 将地理坐标(x,y)转换为栅格坐标(row,col)
        row, col = rasterio.transform.rowcol(transform, x, y)
        
        # 检查是否越界
        if row < 0 or row >= data.shape[0] or col < 0 or col >= data.shape[1]:
            return np.nan
            
        # 获取值并检查是否为无数据值
        value = data[row, col]
        if value == nodata:
            return np.nan
        return value
    except Exception as e:
        logger.warning(f"获取栅格点值失败 (x={x}, y={y}): {e}")
        return np.nan
def find_nearest_valid_value(data, transform, x, y, nodata, search_radius=5):
    """
    寻找最近的有效栅格值
    
    参数:
        data: 栅格数据
        transform: 坐标转换信息
        x, y: 原始坐标点
        nodata: 无数据值
        search_radius: 搜索半径(栅格单位)
    
    返回:
        最近的有效值，如果找不到则返回np.nan
    """
    # 将地理坐标转换为栅格坐标
    center_row, center_col = rasterio.transform.rowcol(transform, x, y)
    
    # 存储有效值和距离
    valid_values = []
    distances = []
    
    # 在指定半径内搜索有效值
    for r in range(max(0, center_row - search_radius), min(data.shape[0], center_row + search_radius + 1)):
        for c in range(max(0, center_col - search_radius), min(data.shape[1], center_col + search_radius + 1)):
            # 跳过中心点(原始无效点)
            if r == center_row and c == center_col:
                continue
                
            value = data[r, c]
            if value != nodata:
                # 计算与中心点的距离
                distance = np.sqrt((r - center_row)**2 + (c - center_col)**2)
                valid_values.append(value)
                distances.append(distance)
    
    # 如果找到有效值，返回最近的
    if valid_values:
        nearest_idx = np.argmin(distances)
        return valid_values[nearest_idx]
    
    return np.nan

def extract_terrain_features(stations_df, dem_path, slope_path, aspect_path):
    """
    从地形栅格数据中提取站点的高程、坡度和坡向信息
    
    参数:
        stations_df: 包含站点信息的DataFrame
        dem_path: 高程数据文件路径
        slope_path: 坡度数据文件路径
        aspect_path: 坡向数据文件路径
    
    返回:
        更新后的站点DataFrame，包含高程、坡度和坡向信息
    """
    logger.info("正在读取地形数据...")
    # 读取地形栅格数据
    dem_data, dem_transform, dem_nodata = read_asc_file(dem_path)
    slope_data, slope_transform, slope_nodata = read_asc_file(slope_path)
    aspect_data, aspect_transform, aspect_nodata = read_asc_file(aspect_path)
    
    logger.info("正在提取站点地形特征...")
    # 创建结果DataFrame的副本，避免修改原始数据
    result_df = stations_df.copy()
    
    # 提取每个站点的高程特征
    result_df['高程'] = result_df.apply(
        lambda row: get_value_at_point(dem_data, dem_transform, row['经度'], row['纬度'], dem_nodata), 
        axis=1
    )
    
    # 提取每个站点的坡度特征
    result_df['坡度'] = result_df.apply(
        lambda row: get_value_at_point(slope_data, slope_transform, row['经度'], row['纬度'], slope_nodata), 
        axis=1
    )
    
    # 提取每个站点的坡向特征
    result_df['坡向'] = result_df.apply(
        lambda row: get_value_at_point(aspect_data, aspect_transform, row['经度'], row['纬度'], aspect_nodata), 
        axis=1
    )
    
    # 检查是否有缺失的地形特征，如果有则使用最近有效栅格的值填充
    for feature, data, transform, nodata in zip(
        ['高程', '坡度', '坡向'],
        [dem_data, slope_data, aspect_data],
        [dem_transform, slope_transform, aspect_transform],
        [dem_nodata, slope_nodata, aspect_nodata]
    ):
        missing_indices = result_df[feature].isna()
        missing_count = missing_indices.sum()
    
        if missing_count > 0:
            for idx in result_df[missing_indices].index:
                x = result_df.loc[idx, '经度']
                y = result_df.loc[idx, '纬度']
                nearest_value = find_nearest_valid_value(data, transform, x, y, nodata)

                # 如果找不到最近的有效值，才使用平均值
                if np.isnan(nearest_value):
                    nearest_value = result_df[feature].mean()
                    logger.warning(f"站点 {result_df.loc[idx, '站点']} 的{feature}数据缺失，且附近无有效值，已用平均值{nearest_value:.2f}填充")
                else:
                    logger.warning(f"站点 {result_df.loc[idx, '站点']} 的{feature}数据缺失，已用最近栅格值{nearest_value:.2f}填充")
                
                result_df.loc[idx, feature] = nearest_value
    # 释放内存
    del dem_data, slope_data, aspect_data
    gc.collect()
    
    return result_df, dem_transform, dem_nodata


def build_delaunay_triangulation(stations_df):
    """
    构建Delaunay三角网，用于确定相邻站点
    
    参数:
        stations_df: 包含站点经纬度的DataFrame
    
    返回:
        tri: Delaunay三角剖分对象
        points: 用于三角剖分的点集
    """
    # 提取站点坐标作为点集
    points = stations_df[['经度', '纬度']].values
    
    # 构建Delaunay三角网
    tri = Delaunay(points)
    
    return tri, points


def find_neighbors(tri, points, station_idx, max_neighbors=10):
    """
    找到站点的邻近站点
    
    参数:
        tri: Delaunay三角剖分对象
        points: 用于三角剖分的点集
        station_idx: 目标站点的索引
        max_neighbors: 最大邻居数量
    
    返回:
        邻近站点的索引列表
    """
    try:
        # 找到包含当前站点的所有三角形
        mask = np.any(tri.simplices == station_idx, axis=1)
        triangles = tri.simplices[mask]
        
        # 提取所有相邻点的索引
        neighbors = np.unique(triangles.flatten())
        
        # 移除自身
        neighbors = neighbors[neighbors != station_idx]
        
        # 如果邻居太多，选择最近的几个
        if len(neighbors) > max_neighbors:
            target_point = points[station_idx]
            distances = np.sqrt(np.sum((points[neighbors] - target_point)**2, axis=1))
            closest_indices = np.argsort(distances)[:max_neighbors]
            neighbors = neighbors[closest_indices]
        
        return neighbors
    except Exception as e:
        logger.warning(f"查找邻近站点失败 (station_idx={station_idx}): {e}")
        # 如果三角剖分方法失败，使用最近邻点
        distances = np.sqrt(np.sum((points - points[station_idx])**2, axis=1))
        # 排除自身
        distances[station_idx] = np.inf
        closest_indices = np.argsort(distances)[:max_neighbors]
        return closest_indices


ELEV_COEFF = 0.1889021437764377
SLOPE_COEFF = 0.19825393034840816
ASPECT_COEFF = 0.02644106455385166
DISTANCE_DECAY = 1.5032703974410062

def calculate_terrain_weight(station_elev, target_elev, station_slope, target_slope, station_aspect, target_aspect):
    """
    更新后的地形权重计算函数，使用全局优化参数
    """
    # 高程差异权重
    elev_diff = abs(station_elev - target_elev)
    elev_weight = 1 / (1 + ELEV_COEFF * elev_diff)

    # 坡度差异权重
    slope_diff = abs(station_slope - target_slope)
    slope_weight = 1 / (1 + SLOPE_COEFF * slope_diff)

    # 坡向差异权重 (考虑坡向的循环性)
    aspect_diff = min(abs(station_aspect - target_aspect), 360 - abs(station_aspect - target_aspect))
    aspect_weight = 1 / (1 + ASPECT_COEFF * aspect_diff)

    # 综合权重
    terrain_weight = elev_weight * 0.5027427048216744 + slope_weight * 0.11576211167994278 + aspect_weight * 0.38832446672417

    return terrain_weight

def calculate_distance_weight(station_x, station_y, target_x, target_y):
    """
    更新后的距离权重计算函数
    """
    distance = np.sqrt((station_x - target_x)**2 + (station_y - target_y)**2)
    if distance < 1e-10:  # 避免除以零
        return 1.0

    # 使用优化后的距离衰减系数
    return 1 / (distance ** DISTANCE_DECAY)

def calculate_spatial_autocorrelation_weight(station_rainfall, neighbor_rainfall_values):
    """
    计算基于空间自相关性的权重
    
    参数:
        station_rainfall: 目标站点的降雨量
        neighbor_rainfall_values: 邻近站点的降雨量
    
    返回:
        空间自相关性权重
    """
    # 如果没有足够的数据计算相关性，返回默认权重
    if len(neighbor_rainfall_values) < 2:
        return 1.0
    
    try:
        # 计算当前站点雨量与邻近站点雨量的相关性
        # 这里使用简单的相似度度量：1 - |差值|/最大值
        diffs = [abs(station_rainfall - value) / (max(abs(station_rainfall), abs(value)) + 0.1) 
                for value in neighbor_rainfall_values]
        correlation = 1 - np.mean(diffs)
        
        # 避免负权重，限制在[0.1, 1]范围内
        correlation = max(0.1, min(1, correlation))
        
        return correlation
    except Exception as e:
        logger.warning(f"计算空间自相关性权重失败: {e}")
        return 1.0  # 出错时返回默认权重


def prism_interpolation(target_station, neighbor_stations, rain_data, stations_terrain, target_actual_value=None):
    """
    对目标站点进行PRISM插值
    
    参数:
        target_station: 目标站点名称
        neighbor_stations: 邻近站点列表
        rain_data: 所有站点的降雨数据字典
        stations_terrain: 包含站点地形特征的DataFrame
        target_actual_value: 目标站点的实际观测值（可选，用于留一法验证）
    
    返回:
        插值结果
    """
    try:
        # 获取目标站点信息
        target_info = stations_terrain[stations_terrain['站点'] == target_station]
        if target_info.empty:
            logger.warning(f"找不到站点 {target_station} 的信息")
            return np.nan
        
        target_info = target_info.iloc[0]
        target_x, target_y = target_info['经度'], target_info['纬度']
        target_elev = target_info['高程']
        target_slope = target_info['坡度']
        target_aspect = target_info['坡向']
        
        weights = []
        rain_values = []
        all_neighbor_values = []  # 用于计算空间自相关性
        
        # 找出有效的邻近站点（有降雨数据的站点）
        valid_neighbors = [n for n in neighbor_stations 
                          if n in rain_data and n != target_station and n in stations_terrain['站点'].values]
        
        if not valid_neighbors:
            logger.warning(f"站点 {target_station} 没有有效的邻近站点")
            return np.nan
        
        # 收集所有邻近站点的降雨值，用于计算空间自相关性
        for neighbor in valid_neighbors:
            all_neighbor_values.append(rain_data[neighbor])
        
        # 如果提供了目标站点的实际值，用于计算空间自相关性权重
        # 否则，在留一法中，我们不使用目标站点的实际值进行插值
        if target_actual_value is not None:
            autocorrelation_weight = calculate_spatial_autocorrelation_weight(
                target_actual_value, all_neighbor_values
            )
        else:
            autocorrelation_weight = 1.0
        
        # 计算每个邻近站点的权重
        for neighbor in valid_neighbors:
            neighbor_info = stations_terrain[stations_terrain['站点'] == neighbor]
            if neighbor_info.empty:
                continue
            
            neighbor_info = neighbor_info.iloc[0]
            neighbor_x, neighbor_y = neighbor_info['经度'], neighbor_info['纬度']
            neighbor_elev = neighbor_info['高程']
            neighbor_slope = neighbor_info['坡度']
            neighbor_aspect = neighbor_info['坡向']
            
            # 计算地形相似性权重
            terrain_weight = calculate_terrain_weight(
                neighbor_elev, target_elev,
                neighbor_slope, target_slope,
                neighbor_aspect, target_aspect
            )
            
            # 计算距离权重
            distance_weight = calculate_distance_weight(neighbor_x, neighbor_y, target_x, target_y)
            
            # 综合权重
            total_weight = terrain_weight * distance_weight * autocorrelation_weight
            
            weights.append(total_weight)
            rain_values.append(rain_data[neighbor])
        
        # 归一化权重
        if sum(weights) > 0:
            weights = np.array(weights) / sum(weights)
            
            # 使用权重插值
            interpolated_value = np.sum(np.array(rain_values) * weights)
        else:
            # 如果所有权重都为0，则使用邻近站点的平均值
            interpolated_value = np.mean(rain_values) if rain_values else np.nan
        
        return interpolated_value
    
    except Exception as e:
        logger.error(f"PRISM插值计算失败 (站点={target_station}): {e}")
        return np.nan



def process_time_step(args):
    """
    处理单个时间步的函数（用于并行计算）
    
    参数:
        args: (time_step, rainfall_data, stations_terrain, tri, points)
    
    返回:
        (time_step, {station: interpolated_value, ...})
    """
    try:
        time_step, rainfall_data, stations_terrain, tri, points = args
        
        # 获取当前时间步有观测数据的站点
        current_data = {}
        for station, data in rainfall_data.items():
            if time_step in data and not np.isnan(data[time_step]):
                current_data[station] = data[time_step]
        
        # 如果站点数量太少，无法进行插值
        if len(current_data) < 3:
            return time_step, {}
        
        result = {}
        validation_pairs = []  # 仅收集实际值和预测值对
        # 对每个站点进行留一法交叉验证
        for target_station in current_data.keys():
            # 目标站点在stations_terrain中的索引
            target_idx = stations_terrain[stations_terrain['站点'] == target_station].index
            if len(target_idx) == 0:
                continue
            target_idx = target_idx[0]
            
            # 找到邻近站点
            neighbor_indices = find_neighbors(tri, points, target_idx)
            neighbor_stations = stations_terrain.iloc[neighbor_indices]['站点'].tolist()
            
            # 创建不包含目标站点的数据集（留一法）
            leave_one_out_data = current_data.copy()
            actual_value = leave_one_out_data.pop(target_station, None)
            
            # 只有当有足够的邻近站点时才进行插值
            if len(leave_one_out_data) >= 3:
                # 使用PRISM方法插值
                interpolated_value = prism_interpolation(
                    target_station, neighbor_stations, leave_one_out_data, stations_terrain, actual_value
                )
                
                result[target_station] = interpolated_value
                #    只收集实际值和预测值对
                validation_pairs.append((actual_value, interpolated_value))
        return time_step, result, validation_pairs
    
    except Exception as e:
        logger.error(f"处理时间步失败 (time_step={time_step}): {e}")
        return time_step, {}



def load_rainfall_data(input_dir, stations_list):
    """
    加载所有站点的降雨数据
    
    参数:
        input_dir: 降雨数据文件目录
        stations_list: 站点列表
    
    返回:
        {station: {time: rainfall, ...}, ...}
    """
    rainfall_data = {}
    
    # 获取所有雨量文件
    rainfall_files = glob.glob(os.path.join(input_dir, "*.csv"))
    logger.info(f"找到 {len(rainfall_files)} 个降雨数据文件")
    
    # 跟踪成功加载的文件数
    success_count = 0
    
    for file in tqdm(rainfall_files, desc="加载降雨数据"):
        try:
            station_name = os.path.splitext(os.path.basename(file))[0]
            
            # 检查站点是否在我们的站点列表中
            if station_name not in stations_list:
                continue
            
            # 尝试不同的编码格式读取文件
            try:
                df = pd.read_csv(file, encoding='utf-8')
            except:
                try:
                    df = pd.read_csv(file, encoding='gbk')
                except Exception as e:
                    logger.warning(f"无法读取文件 {file}: {e}")
                    continue
            
            # 确保列名正确
            if '时间' not in df.columns or '雨量' not in df.columns:
                logger.warning(f"文件 {file} 缺少必要的列 ('时间', '雨量')")
                continue
            
            # 设置时间为索引
            df['时间'] = pd.to_datetime(df['时间'])
            station_data = {}
            
            # 将数据转换为字典格式，提高后续访问效率
            for _, row in df.iterrows():
                if not pd.isna(row['雨量']):  # 跳过缺失值
                    station_data[row['时间']] = row['雨量']
            
            rainfall_data[station_name] = station_data
            success_count += 1
            
            # 释放内存
            del df
        except Exception as e:
            logger.error(f"处理文件 {file} 时出错: {e}")
    
    logger.info(f"成功加载了 {success_count} 个站点的降雨数据")
    
    # 强制垃圾回收
    gc.collect()
    
    return rainfall_data

def objective_function(params):
    """
    目标函数：计算给定参数下的插值误差
    参数:
        params: 待优化的参数列表
               [elev_coefficient, slope_coefficient, aspect_coefficient, distance_decay, max_neighbors]
    返回:
        误差指标（如RMSE）
    """
    elev_coeff, slope_coeff, aspect_coeff, distance_decay, max_neighbors = params

    # 确保三个系数的和为1
    if not np.isclose(elev_coeff + slope_coeff + aspect_coeff, 1.0):
        # 如果不满足条件，返回一个很大的值，表示这个解不可接受
        return float('inf')

    # 更新全局参数
    global ELEV_COEFF, SLOPE_COEFF, ASPECT_COEFF, DISTANCE_DECAY, MAX_NEIGHBORS
    ELEV_COEFF = elev_coeff
    SLOPE_COEFF = slope_coeff
    ASPECT_COEFF = aspect_coeff
    DISTANCE_DECAY = distance_decay
    MAX_NEIGHBORS = int(max_neighbors)  # 邻近站点数必须是整数

    # 运行插值并计算误差
    # 需要在主流程中捕获验证误差，例如 RMSE
    # 假设 validation_rmse 是捕获的验证误差
    current_rmse = validation_rmse
    logger.debug(f"当前参数: {params}, 当前RMSE: {current_rmse}")

    return current_rmse
optimization_params = {
    'elev_coefficient': (0.5, 0.8),  # 高程权重系数范围
    'slope_coefficient': (0.01, 0.3), # 坡度权重系数范围
    'aspect_coefficient': (0.001, 0.3), # 坡向权重系数范围
    'distance_decay': (1.0, 3.0), # 距离衰减系数范围
    'max_neighbors': (3, 10) # 最大邻近站点数范围
}

def prism_interpolation_pipeline(input_dir, terrain_dir, output_dir, stations_file):
    """
    PRISM空间插值主流程
    
    参数:
        input_dir: 输入点雨量数据目录
        terrain_dir: 地形数据目录
        output_dir: 输出插值结果目录
        stations_file: 站点信息文件
    """
    
    start_time = time.time()
    logger.info("开始PRISM空间插值...")
    
    # 创建输出目录
    point_output_dir = os.path.join(output_dir, "points")
    grid_output_dir = os.path.join(output_dir, "grids")
    os.makedirs(point_output_dir, exist_ok=True)
    os.makedirs(grid_output_dir, exist_ok=True)
    
    # 读取站点信息
    logger.info("读取站点信息...")
    try:
        try:
            stations_df = pd.read_csv(stations_file, encoding='utf-8')
        except:
            stations_df = pd.read_csv(stations_file, encoding='gbk')
        
        # 确保列名正确
        if '站点' not in stations_df.columns or '经度' not in stations_df.columns or '纬度' not in stations_df.columns:
            logger.error("站点文件缺少必要的列 ('站点', '经度', '纬度')")
            return
        
        logger.info(f"成功读取 {len(stations_df)} 个站点信息")
    except Exception as e:
        logger.error(f"无法读取站点文件: {e}")
        return
    
    # 检查地形数据文件是否存在
    dem_path = os.path.join(terrain_dir, 'dem.asc')
    slope_path = os.path.join(terrain_dir, 'slope.asc')
    aspect_path = os.path.join(terrain_dir, 'aspect.asc')
    
    if not all(os.path.exists(p) for p in [dem_path, slope_path, aspect_path]):
        logger.error("缺少必要的地形数据文件")
        return
    
    # 提取地形特征
    try:
        stations_terrain, dem_transform, dem_nodata = extract_terrain_features(
            stations_df, dem_path, slope_path, aspect_path
        )
        
        # 保存带有地形特征的站点信息（作为中转表，方便调试）
        terrain_features_path = os.path.join(output_dir, 'station_terrain_features.csv')
        stations_terrain.to_csv(terrain_features_path, index=False, encoding='utf-8')
        logger.info(f"已保存站点地形特征到: {terrain_features_path}")
    except Exception as e:
        logger.error(f"提取地形特征失败: {e}")
        return
    
    # 构建Delaunay三角网
    logger.info("构建Delaunay三角网...")
    try:
        tri, points = build_delaunay_triangulation(stations_terrain)
    except Exception as e:
        logger.error(f"构建Delaunay三角网失败: {e}")
        return
    
    # 读取所有雨量数据
    try:
        rainfall_data = load_rainfall_data(input_dir, stations_terrain['站点'].tolist())
        
        if not rainfall_data:
            logger.error("没有找到有效的降雨数据")
            return
        
        logger.info(f"成功加载了 {len(rainfall_data)} 个站点的降雨数据")
    except Exception as e:
        logger.error(f"读取降雨数据失败: {e}")
        return
    
    # 获取所有时间步
    all_time_steps = set()
    for data in rainfall_data.values():
        all_time_steps.update(data.keys())
    all_time_steps = sorted(all_time_steps)
    
    logger.info(f"共有 {len(all_time_steps)} 个时间步需要处理")
    
    # 准备点插值的参数
    point_process_args = [
        (time_step, rainfall_data, stations_terrain, tri, points)
        for time_step in all_time_steps
    ]
    
    # 使用多进程并行计算 - 点插值
    logger.info(f"开始并行处理点插值，使用 {min(cpu_count(), 23)} 个核心...")
    try:
        num_cores = min(cpu_count(), 12)  # 使用最多23个核心
        with Pool(num_cores) as pool:
            point_results = list(tqdm(
                pool.imap(process_time_step, point_process_args),
                total=len(point_process_args),
                desc="点插值进度"
            ))
    except Exception as e:
        logger.error(f"点插值并行处理失败: {e}")
        return
    
    # 收集点插值结果和验证对
    logger.info("整理点插值结果...")
    station_results = {}
    all_observed = []
    all_predicted = []
    
    for time_step, step_results, validation_pairs in point_results:
        # 收集站点结果
        for station, value in step_results.items():
            if station not in station_results:
                station_results[station] = {}
            station_results[station][time_step] = value
        
        # 收集验证对
        for actual, predicted in validation_pairs:
            all_observed.append(actual)
            all_predicted.append(predicted)
     # 计算评价指标
    logger.info("计算整体评价指标...")
    
    # 移除缺失值对
    valid_indices = ~(np.isnan(all_observed) | np.isnan(all_predicted))
    obs = np.array(all_observed)[valid_indices]
    pred = np.array(all_predicted)[valid_indices]

    if len(obs) >= 2:
       # 计算MAE (Mean Absolute Error)
        mae = np.mean(np.abs(obs - pred))
        # 计算RMSE (Root Mean Square Error)
        rmse = np.sqrt(np.mean((obs - pred) ** 2))
        # 将 RMSE 存入全局变量
        global validation_rmse
        validation_rmse = rmse
        
        # 计算CC (Correlation Coefficient)
        cc = np.corrcoef(obs, pred)[0, 1]
        
        # 计算NSE (Nash-Sutcliffe Efficiency)
        if np.var(obs) == 0:
            nse = np.nan  # 避免除以零
        else:
            nse = 1 - np.sum((obs - pred) ** 2) / np.sum((obs - np.mean(obs)) ** 2)
        
        # 打印评价指标
        print("\n" + "="*60)
        print("PRISM插值整体评价指标:")
        print("="*60)
        print(f"样本数: {len(obs)}")
        print(f"MAE: {mae:.4f} mm")
        print(f"RMSE: {rmse:.4f} mm")
        print(f"CC: {cc:.4f}")
        print(f"NSE: {nse:.4f}")
        print("="*60)
        print("指标说明:")
        print("  MAE - 平均绝对误差，越小越好")
        print("  RMSE - 均方根误差，越小越好")
        print("  CC - 相关系数，越接近1越好")
        print("  NSE - 纳什效率系数，>0.75表示模型效果很好")
        print("="*60 + "\n")
        
        # 同时记录到日志
        logger.info(f"整体评价指标: 样本数={len(obs)}, MAE={mae:.4f}, RMSE={rmse:.4f}, CC={cc:.4f}, NSE={nse:.4f}")
        
        # 保存评价指标到文件
        metrics_path = os.path.join(output_dir, datetime.now().strftime('%Y%m%d_%H%M%S') + ".txt")
        with open(metrics_path, 'w') as f:
            f.write("PRISM插值整体评价指标:\n")
            f.write(f"样本数: {len(obs)}\n")
            f.write(f"MAE: {mae:.4f} mm\n")
            f.write(f"RMSE: {rmse:.4f} mm\n")
            f.write(f"CC: {cc:.4f}\n")
            f.write(f"NSE: {nse:.4f}\n")
            f.write(f"max_neighbors = 10\n")
        # 绘制简单的散点图
        try:
            plt.figure(figsize=(10, 10))
            plt.scatter(obs, pred, alpha=0.5, s=10)
            
            # 添加1:1线
            max_val = max(max(obs), max(pred))
            min_val = min(min(obs), min(pred))
            plt.plot([min_val, max_val], [min_val, max_val], 'r--')
            
            plt.xlabel('Observed Rainfall (mm)')
            plt.ylabel('Predicted Rainfall (mm)')
            plt.title(f'PRISM Validation\nMAE: {mae:.2f}, RMSE: {rmse:.2f}, CC: {cc:.2f}, NSE: {nse:.2f}')
            plt.grid(True)
            plt.axis('equal')
            plt.tight_layout()
            
            scatter_path = os.path.join(output_dir, 'validation_scatter.png')
            plt.savefig(scatter_path, dpi=300)
            plt.close()
            logger.info(f"验证散点图已保存到: {scatter_path}")
        except Exception as e:
            logger.warning(f"生成验证散点图失败: {e}")
    else:
        logger.warning("有效验证对数量不足，无法计算评价指标")
    
    # 保存点插值结果
    logger.info("保存点插值结果...")
    
    # 保存点插值结果
    logger.info("保存点插值结果...")
    for station, data in tqdm(station_results.items(), desc="保存点插值结果"):
        if data:
            # 将字典转换为DataFrame
            result_df = pd.DataFrame(
                [(time_step, value) for time_step, value in data.items()],
                columns=['时间', '雨量']
            )
            result_df.sort_values('时间', inplace=True)
            
            # 创建安全的文件名
            safe_filename = f"{station}.csv"
            output_path = os.path.join(point_output_dir, safe_filename)
            
            # 保存为CSV
            result_df.to_csv(output_path, index=False, encoding='utf-8')
    
    # 清理内存，为栅格插值做准备
    del point_results
    gc.collect()
    
    


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='PRISM空间插值')
    parser.add_argument('--input', type=str, default='D:/pythondata/spatial_interpolation/input/2021-2/点雨量')
    parser.add_argument('--terrain', type=str, default='D:/pythondata/spatial_interpolation/terrain/90',
                        help='地形数据目录')
    parser.add_argument('--output', type=str, default='D:/pythondata/spatial_interpolation/output/PRISM/2021-2',
                        help='输出插值结果目录')
    parser.add_argument('--stations', type=str, default='D:/pythondata/spatial_interpolation/stations.csv',
                        help='站点信息文件')
    parser.add_argument('--debug', action='store_true',
                        help='启用调试模式，打印更多日志')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.debug:
        logger.setLevel(logging.DEBUG)
    
    print("=" * 50)
    print("PRISM空间插值程序")
    print("=" * 50)
    print(f"输入点雨量数据目录: {args.input}")
    print(f"地形数据目录: {args.terrain}")
    print(f"输出插值结果目录: {args.output}")
    print(f"站点信息文件: {args.stations}")
    print("=" * 50)
    
    # 运行主流程
    try:
        prism_interpolation_pipeline(args.input, args.terrain, args.output, args.stations)
    except Exception as e:
        logger.error(f"程序执行出错: {e}", exc_info=True)
    # 定义SCE-UA参数
    scea_params = {
        'n_complexes': 4,       # 复杂度数量
        'points_per_complex': 5,# 每个复杂度的点数
        'total_generations': 50,# 总代数
        'p_random': 0.2         # 随机抽样概率
    }

    # 执行SCE-UA优化
    optimizer = SCEUA(
        objective_function,
        param_ranges=list(optimization_params.values()),
        **scea_params
    )

    best_params, best_value = optimizer.run()

    logger.info(f"最佳参数: {best_params}, 最小RMSE: {best_value}")

    # 使用最佳参数重新运行插值
    ELEV_COEFF, SLOPE_COEFF, ASPECT_COEFF, DISTANCE_DECAY, MAX_NEIGHBORS = best_params
    MAX_NEIGHBORS = int(MAX_NEIGHBORS)
    prism_interpolation_pipeline(args.input, args.terrain, args.output, args.stations)

if __name__ == "__main__":
    main()

    
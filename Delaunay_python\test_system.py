#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay插值系统测试脚本

用于验证系统各组件是否正常工作
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
import logging

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from config import DelaunayConfig
from data_loader import DelaunayDataLoader
from evaluation_metrics import EvaluationMetrics
from delaunay_interpolator import DelaunayInterpolator

def test_config():
    """测试配置模块"""
    print("测试配置模块...")
    
    try:
        config = DelaunayConfig()
        print(f"✓ 配置初始化成功")
        print(f"  - 输入目录: {config.INPUT_DIR}")
        print(f"  - 输出目录: {config.OUTPUT_DIR}")
        print(f"  - Delaunay分析文件: {config.DELAUNAY_ANALYSIS_FILE}")
        
        # 验证配置
        errors = config.validate_config()
        if errors:
            print("✗ 配置验证失败:")
            for error in errors:
                print(f"    - {error}")
            return False
        else:
            print("✓ 配置验证通过")
        
        # 获取洪水事件
        events = config.get_flood_events()
        print(f"✓ 发现{len(events)}个洪水事件")
        if events:
            print(f"  - 示例事件: {events[:3]}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_data_loader():
    """测试数据加载器"""
    print("\n测试数据加载器...")
    
    try:
        config = DelaunayConfig()
        data_loader = DelaunayDataLoader(config)
        
        # 测试加载Delaunay分析
        print("  测试Delaunay分析加载...")
        delaunay_df = data_loader.load_delaunay_analysis()
        print(f"✓ 成功加载{len(delaunay_df)}个站点的Delaunay分析")
        
        # 显示示例站点信息
        if len(delaunay_df) > 0:
            sample_station = delaunay_df.iloc[0]['验证站点代码']
            station_info = data_loader.get_station_info(str(sample_station))
            if station_info:
                print(f"  - 示例站点{sample_station}: {station_info['name']}")
                print(f"    包围站点数: {len(station_info['surrounding_stations'])}")
        
        # 测试加载洪水事件数据
        events = config.get_flood_events()
        if events:
            test_event = events[0]
            print(f"  测试洪水事件数据加载: {test_event}")
            
            try:
                event_data = data_loader.load_flood_event_data(test_event)
                print(f"✓ 成功加载{len(event_data)}个站点的数据")
                
                # 显示数据统计
                if event_data:
                    sample_station_id = list(event_data.keys())[0]
                    sample_data = event_data[sample_station_id]
                    print(f"  - 示例站点{sample_station_id}: {len(sample_data)}个时间点")
                    
                    # 获取公共时间范围
                    start_time, end_time = data_loader.get_common_time_range(event_data)
                    if start_time and end_time:
                        print(f"  - 公共时间范围: {start_time} 到 {end_time}")
                
            except Exception as e:
                print(f"✗ 洪水事件数据加载失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据加载器测试失败: {e}")
        return False

def test_evaluation_metrics():
    """测试评估指标计算"""
    print("\n测试评估指标计算...")
    
    try:
        config = DelaunayConfig()
        evaluator = EvaluationMetrics(config)
        
        # 生成测试数据
        np.random.seed(42)
        observed = np.random.rand(100) * 10
        predicted = observed + np.random.normal(0, 1, 100)  # 添加噪声
        
        # 测试各种指标
        mae = evaluator.calculate_mae(observed, predicted)
        rmse = evaluator.calculate_rmse(observed, predicted)
        nse = evaluator.calculate_nse(observed, predicted)
        r = evaluator.calculate_correlation(observed, predicted)
        
        print(f"✓ MAE计算成功: {mae:.4f}")
        print(f"✓ RMSE计算成功: {rmse:.4f}")
        print(f"✓ NSE计算成功: {nse:.4f}")
        print(f"✓ 相关系数计算成功: {r:.4f}")
        
        # 测试综合指标计算
        all_metrics = evaluator.calculate_all_metrics(observed, predicted)
        print(f"✓ 综合指标计算成功，包含{len(all_metrics)}个指标")
        
        return True
        
    except Exception as e:
        print(f"✗ 评估指标测试失败: {e}")
        return False

def test_interpolator():
    """测试插值器"""
    print("\n测试插值器...")
    
    try:
        config = DelaunayConfig()
        data_loader = DelaunayDataLoader(config)
        evaluator = EvaluationMetrics(config)
        interpolator = DelaunayInterpolator(config, data_loader, evaluator)
        
        # 加载必要数据
        delaunay_df = data_loader.load_delaunay_analysis()
        events = config.get_flood_events()
        
        if not events:
            print("✗ 无可用洪水事件进行测试")
            return False
        
        # 选择第一个事件进行测试
        test_event = events[0]
        print(f"  使用事件{test_event}进行测试...")
        
        # 加载事件数据
        event_data = data_loader.load_flood_event_data(test_event)
        if not event_data:
            print("✗ 无可用事件数据")
            return False
        
        # 获取可用验证站点
        available_stations = data_loader.get_available_stations_for_event(test_event)
        if not available_stations:
            print("✗ 无可用验证站点")
            return False
        
        # 选择第一个站点进行单点插值测试
        test_station = available_stations[0]
        print(f"  测试站点{test_station}的插值...")
        
        # 获取公共时间范围
        start_time, end_time = data_loader.get_common_time_range(event_data)
        if not start_time:
            print("✗ 无法确定时间范围")
            return False
        
        # 测试单个时间点插值
        test_time = start_time
        result = interpolator.interpolate_station_at_time(test_station, test_time, event_data)
        
        if result['success']:
            print(f"✓ 单点插值成功")
            print(f"  - 插值结果: {result['interpolated_value']:.4f}")
            print(f"  - 实际值: {result['actual_value']:.4f}")
            print(f"  - 使用包围站点数: {len(result['surrounding_stations'])}")
        else:
            print(f"✗ 单点插值失败: {result.get('error', 'Unknown error')}")
            return False
        
        # 测试时间序列插值（仅测试前10个时间点）
        print(f"  测试时间序列插值...")
        station_results = interpolator.interpolate_station_timeseries(test_station, event_data)
        
        if not station_results.empty:
            valid_count = station_results['predicted'].notna().sum()
            total_count = len(station_results)
            print(f"✓ 时间序列插值成功: {valid_count}/{total_count}个时间点")
            
            # 计算简单统计
            if valid_count > 0:
                valid_data = station_results.dropna(subset=['observed', 'predicted'])
                if not valid_data.empty:
                    mae = np.mean(np.abs(valid_data['error']))
                    print(f"  - 平均绝对误差: {mae:.4f}")
        else:
            print("✗ 时间序列插值失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 插值器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Delaunay插值系统测试")
    print("=" * 50)
    
    # 设置简单日志
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        ("配置模块", test_config),
        ("数据加载器", test_data_loader),
        ("评估指标", test_evaluation_metrics),
        ("插值器", test_interpolator)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name}测试通过")
            else:
                print(f"✗ {test_name}测试失败")
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过，系统可以正常运行")
        return 0
    else:
        print("✗ 部分测试失败，请检查系统配置")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

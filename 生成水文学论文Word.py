#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成水文学专业论文Word文档
"""

import os
from docx import Document
from docx.shared import Inches, Pt, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
import re

def create_hydrology_paper():
    """创建水文学论文Word文档"""
    
    # 创建新文档
    doc = Document()
    
    # 设置页面边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)
        section.left_margin = Cm(3.17)
        section.right_margin = Cm(3.17)
    
    # 设置文档标题
    title = doc.add_heading('珠江流域空间降雨插值方法对比研究', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_run = title.runs[0]
    title_run.font.name = '宋体'
    title_run.font.size = Pt(18)
    title_run.font.bold = True
    
    # 添加作者信息
    author_para = doc.add_paragraph()
    author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    author_run = author_para.add_run('XXX¹  导师姓名²')
    author_run.font.name = '宋体'
    author_run.font.size = Pt(12)
    
    # 添加单位信息
    affiliation_para = doc.add_paragraph()
    affiliation_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    affiliation_run = affiliation_para.add_run('(1. XXX大学 水利工程学院，城市 邮编；2. XXX大学 水利工程学院，城市 邮编)')
    affiliation_run.font.name = '宋体'
    affiliation_run.font.size = Pt(10)
    
    doc.add_paragraph()  # 空行
    
    return doc

def add_markdown_to_word(doc, markdown_file):
    """将Markdown内容转换为Word格式"""
    
    if not os.path.exists(markdown_file):
        print(f"文件不存在: {markdown_file}")
        return
    
    with open(markdown_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
        
        # 处理一级标题
        if line.startswith('# '):
            title_text = line[2:].strip()
            heading = doc.add_heading(title_text, level=1)
            heading_run = heading.runs[0]
            heading_run.font.name = '宋体'
            heading_run.font.size = Pt(16)
            heading_run.font.bold = True
            
        # 处理二级标题
        elif line.startswith('## '):
            title_text = line[3:].strip()
            heading = doc.add_heading(title_text, level=2)
            heading_run = heading.runs[0]
            heading_run.font.name = '宋体'
            heading_run.font.size = Pt(14)
            heading_run.font.bold = True
            
        # 处理三级标题
        elif line.startswith('### '):
            title_text = line[4:].strip()
            heading = doc.add_heading(title_text, level=3)
            heading_run = heading.runs[0]
            heading_run.font.name = '宋体'
            heading_run.font.size = Pt(12)
            heading_run.font.bold = True
            
        # 处理四级标题
        elif line.startswith('#### '):
            title_text = line[5:].strip()
            para = doc.add_paragraph()
            run = para.add_run(title_text)
            run.font.name = '宋体'
            run.font.size = Pt(12)
            run.font.bold = True
            
        # 处理数学公式（简化处理）
        elif '$$' in line:
            formula_text = line.replace('$$', '').strip()
            para = doc.add_paragraph()
            para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run = para.add_run(formula_text)
            run.font.name = 'Times New Roman'
            run.font.size = Pt(11)
            run.italic = True
            
        # 处理表格标记
        elif line.startswith('|') and line.endswith('|'):
            # 简化的表格处理
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            if cells and not all('---' in cell for cell in cells):
                para = doc.add_paragraph()
                run = para.add_run(' | '.join(cells))
                run.font.name = '宋体'
                run.font.size = Pt(10)
                
        # 处理普通段落
        else:
            if line and not line.startswith('**') and not line.startswith('*'):
                para = doc.add_paragraph()
                
                # 处理粗体文本
                if '**' in line:
                    parts = line.split('**')
                    for i, part in enumerate(parts):
                        if i % 2 == 0:
                            run = para.add_run(part)
                        else:
                            run = para.add_run(part)
                            run.bold = True
                        run.font.name = '宋体'
                        run.font.size = Pt(12)
                else:
                    run = para.add_run(line)
                    run.font.name = '宋体'
                    run.font.size = Pt(12)
                
                # 设置段落格式
                para.paragraph_format.first_line_indent = Cm(0.74)  # 首行缩进2字符
                para.paragraph_format.line_spacing = 1.5  # 1.5倍行距

def create_complete_hydrology_paper():
    """创建完整的水文学论文"""
    
    print("正在生成水文学专业论文...")
    
    # 创建文档
    doc = create_hydrology_paper()
    
    # 添加论文内容
    add_markdown_to_word(doc, '珠江流域空间降雨插值方法对比研究_水文学论文.md')
    
    # 保存文档
    output_file = "珠江流域空间降雨插值方法对比研究_水文学论文.docx"
    doc.save(output_file)
    
    print(f"水文学论文已生成: {output_file}")
    return output_file

def create_defense_materials():
    """创建答辩材料"""
    
    print("正在生成水文学专业答辩材料...")
    
    # 创建答辩问题文档
    doc = Document()
    
    # 设置标题
    title = doc.add_heading('珠江流域空间降雨插值方法研究 - 水文学专业答辩问题与回答', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 添加内容
    add_markdown_to_word(doc, '水文学专业答辩问题与回答.md')
    
    # 保存文档
    defense_file = "水文学专业答辩问题与回答.docx"
    doc.save(defense_file)
    
    print(f"答辩材料已生成: {defense_file}")
    return defense_file

def create_presentation_outline():
    """创建答辩演示大纲"""
    
    outline_content = """# 珠江流域空间降雨插值方法研究 - 答辩演示大纲

## 第一部分：研究背景与意义 (3分钟)

### 1.1 研究背景
- 降雨空间分布在水文学中的重要性
- 珠江流域水文气象特征
- 空间插值方法的必要性

### 1.2 研究意义
- 理论意义：完善降雨空间插值理论
- 实践意义：服务洪水预报和水资源管理
- 科学意义：为复杂地形区域提供方法指导

## 第二部分：研究内容与方法 (8分钟)

### 2.1 研究区域与数据
- 珠江流域基本特征
- 40个雨量站13年观测数据
- 48个典型洪水事件

### 2.2 四种插值方法
- **最优插值法(OI)**：贝叶斯统计理论，协方差建模
- **PRISM方法**：地形权重融合，物理机制明确
- **克里金插值**：地统计学理论，半变异函数建模
- **反距离加权(IDW)**：距离衰减原理，计算简单

### 2.3 评价指标体系
- Nash-Sutcliffe效率系数(NSE)
- 均方根误差(RMSE)
- 平均绝对误差(MAE)
- 相关系数(R)

## 第三部分：结果分析与讨论 (6分钟)

### 3.1 插值精度对比
- PRISM > Kriging > OI > IDW
- PRISM方法NSE达到0.724
- 各方法适用性分析

### 3.2 地形因子影响
- 高程影响权重70.4%
- 坡度影响权重21.8%
- 坡向影响权重7.8%

### 3.3 敏感性分析
- 地形复杂度敏感性
- 站点密度敏感性
- 数据质量要求差异

## 第四部分：结论与展望 (3分钟)

### 4.1 主要结论
- PRISM方法最适用于珠江流域
- 地形是影响降雨分布的主导因子
- 不同方法具有不同的适用条件

### 4.2 创新点
- 建立了统一的对比评价体系
- 量化了地形因子的影响权重
- 提出了方法选择指导原则

### 4.3 应用前景
- 洪水预报精度提升
- 水资源评估支撑
- 气候变化研究服务

### 4.4 研究展望
- 时空建模发展
- 多源数据融合
- 机器学习应用
- 实时业务化

---

## 答辞要点

### 开场白
尊敬的各位专家老师，大家好！我是XXX，今天汇报的题目是《珠江流域空间降雨插值方法对比研究》。

### 结束语
以上是我的研究工作汇报，不足之处请各位专家老师批评指正，谢谢大家！

### 回答问题策略
1. 认真听题，理解问题核心
2. 从水文学角度组织回答
3. 结合具体数据和实例
4. 承认不足，提出改进思路
"""
    
    with open('水文学专业答辞大纲.md', 'w', encoding='utf-8') as f:
        f.write(outline_content)
    
    print("✅ 水文学专业答辞大纲已生成")

def main():
    """主函数"""
    print("="*60)
    print("🌊 水文学专业论文与答辩材料生成器")
    print("="*60)
    
    try:
        # 检查依赖
        from docx import Document
        print("✅ python-docx 包已安装")
    except ImportError:
        print("❌ 需要安装 python-docx 包")
        print("请运行: pip install python-docx")
        return
    
    success_count = 0
    total_tasks = 3
    
    # 1. 生成水文学论文
    try:
        paper_file = create_complete_hydrology_paper()
        success_count += 1
    except Exception as e:
        print(f"❌ 生成论文失败: {e}")
    
    # 2. 生成答辩材料
    try:
        defense_file = create_defense_materials()
        success_count += 1
    except Exception as e:
        print(f"❌ 生成答辩材料失败: {e}")
    
    # 3. 生成答辞大纲
    try:
        create_presentation_outline()
        success_count += 1
    except Exception as e:
        print(f"❌ 生成答辞大纲失败: {e}")
    
    print()
    print("="*60)
    print(f"📊 任务完成情况: {success_count}/{total_tasks}")
    
    if success_count == total_tasks:
        print("🎉 所有水文学专业材料生成完成！")
        print()
        print("📁 生成的文件：")
        files = [
            "珠江流域空间降雨插值方法对比研究_水文学论文.md",
            "珠江流域空间降雨插值方法对比研究_水文学论文.docx",
            "水文学专业答辩问题与回答.md",
            "水文学专业答辩问题与回答.docx",
            "水文学专业答辞大纲.md"
        ]
        
        for i, file in enumerate(files, 1):
            if os.path.exists(file):
                print(f"  {i}. ✅ {file}")
            else:
                print(f"  {i}. ❌ {file}")
        
        print()
        print("💡 水文学专业特色：")
        print("1. 突出水文学理论基础和物理机制")
        print("2. 强调降雨插值的水文学意义")
        print("3. 结合珠江流域水文特征分析")
        print("4. 注重实际应用和工程价值")
        print("5. 采用标准学术论文格式")
        
    else:
        print("⚠️  部分任务未完成，请检查错误信息")
    
    print("="*60)

if __name__ == "__main__":
    main()

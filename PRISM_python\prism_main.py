"""
PRISM插值主程序
整合所有模块，实现完整的PRISM插值流程
"""

import os
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional
import gc

from config import Config
from data_processing import DataProcessor
from delaunay_triangulation import DelaunayTriangulation
from moran_index import MoranIndex
from prism_core import PRISMCore
from evaluation_metrics import EvaluationMetrics
from raster_processing import RasterProcessor
from parallel_processing import ParallelProcessor

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("prism_interpolation.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class PRISMInterpolation:
    """PRISM插值主类"""
    
    def __init__(self, config: Config):
        """初始化PRISM插值系统"""
        self.config = config
        
        # 初始化各个模块
        self.data_processor = DataProcessor(config)
        self.delaunay_tri = DelaunayTriangulation(config)
        self.moran_calculator = MoranIndex(config)
        self.prism_core = PRISMCore(config)
        self.evaluator = EvaluationMetrics(config)
        self.raster_processor = RasterProcessor(config)
        self.parallel_processor = ParallelProcessor(config)
        
        # 数据存储
        self.stations_df = None
        self.stations_with_terrain = None
        self.rainfall_data = None
        self.triangulation_info = None
        self.interpolation_results = None
        
        logger.info("PRISM插值系统初始化完成")
    
    def load_data(self):
        """加载所有必要数据"""
        logger.info("开始加载数据...")
        
        try:
            # 1. 加载站点信息
            self.stations_df = self.data_processor.load_stations()
            
            # 2. 加载地形数据
            terrain_data = self.data_processor.load_terrain_data()
            
            # 3. 提取站点地形特征
            self.stations_with_terrain = self.data_processor.extract_terrain_features(
                self.stations_df
            )
            
            # 4. 加载降雨数据
            station_names = self.stations_with_terrain['站点'].tolist()
            self.rainfall_data = self.data_processor.load_rainfall_data(station_names)
            
            # 5. 为栅格处理器加载地形数据
            if self.config.output_raster:
                self.raster_processor.load_terrain_rasters(terrain_data)
            
            logger.info("数据加载完成")
            
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            raise
    
    def build_spatial_structure(self):
        """构建空间结构（Delaunay三角网）"""
        logger.info("构建空间结构...")
        
        try:
            # 构建Delaunay三角网
            self.triangulation_info = self.delaunay_tri.build_triangulation(
                self.stations_with_terrain
            )
            
            # 输出三角网可视化
            if self.config.output_delaunay_plot:
                output_dirs = self.config.get_output_dirs()
                plot_path = os.path.join(output_dirs['plots'], 'delaunay_triangulation.png')
                self.delaunay_tri.plot_triangulation(
                    output_path=plot_path, show_weights=True
                )
            
            # 打印三角网统计信息
            tri_info = self.delaunay_tri.get_triangulation_info()
            logger.info(f"三角网统计: 总三角形数={tri_info.get('total_triangles', 0)}, "
                       f"高质量三角形数={tri_info.get('quality_triangles', 0)}")
            
        except Exception as e:
            logger.error(f"构建空间结构失败: {e}")
            raise
    
    def run_interpolation(self):
        """执行插值计算"""
        logger.info("开始执行插值计算...")
        
        try:
            # 获取所有时间点
            time_points = self.data_processor.get_time_series()
            logger.info(f"共有 {len(time_points)} 个时间点需要处理")
            
            # 执行并行插值
            self.interpolation_results = self.parallel_processor.parallel_interpolation(
                time_points, self.stations_with_terrain, self.rainfall_data,
                self.delaunay_tri, self.prism_core, self.moran_calculator
            )
            
            logger.info("插值计算完成")
            
        except Exception as e:
            logger.error(f"插值计算失败: {e}")
            raise
    
    def evaluate_results(self):
        """评价插值结果"""
        logger.info("开始评价插值结果...")
        
        try:
            if not self.interpolation_results:
                logger.warning("没有插值结果可供评价")
                return
            
            # 收集所有观测值和预测值
            observed_series = []
            predicted_series = []
            time_points = []
            
            for time_point, result in self.interpolation_results.items():
                if 'observed_values' in result and 'predicted_values' in result:
                    observed_series.append(result['observed_values'])
                    predicted_series.append(result['predicted_values'])
                    time_points.append(time_point)
            
            if not observed_series:
                logger.warning("没有有效的评价数据")
                return
            
            # 计算评价指标
            evaluation_results = self.evaluator.evaluate_time_series(
                observed_series, predicted_series, time_points
            )
            
            # 输出评价结果
            output_dirs = self.config.get_output_dirs()
            
            if self.config.output_evaluation:
                # 创建评价报告
                self.evaluator.create_evaluation_report(
                    evaluation_results, output_dirs['evaluation']
                )
                
                # 保存详细指标
                self.evaluator.save_detailed_metrics(
                    evaluation_results, output_dirs['evaluation']
                )
                
                # 创建散点图
                all_observed = np.concatenate(observed_series)
                all_predicted = np.concatenate(predicted_series)
                self.evaluator.create_scatter_plot(
                    all_observed, all_predicted, output_dirs['plots']
                )
            
            # 打印摘要指标
            self.evaluator.print_summary_metrics(evaluation_results)
            
            return evaluation_results
            
        except Exception as e:
            logger.error(f"评价插值结果失败: {e}")
            return None
    
    def save_interpolation_results(self):
        """保存插值结果"""
        logger.info("保存插值结果...")
        
        try:
            if not self.interpolation_results:
                logger.warning("没有插值结果可保存")
                return
            
            output_dirs = self.config.get_output_dirs()
            
            # 为每个站点创建插值结果文件
            station_names = self.stations_with_terrain['站点'].tolist()
            
            for i, station_name in enumerate(station_names):
                # 收集该站点的所有插值结果
                time_data = []
                rainfall_data = []
                
                for time_point, result in sorted(self.interpolation_results.items()):
                    if ('predicted_values' in result and 
                        i < len(result['predicted_values'])):
                        time_data.append(time_point)
                        rainfall_data.append(result['predicted_values'][i])
                
                if time_data:
                    # 创建DataFrame
                    station_df = pd.DataFrame({
                        '时间': time_data,
                        '雨量': rainfall_data
                    })
                    
                    # 保存为CSV文件
                    safe_station_name = station_name.replace('/', '_').replace('\\', '_')
                    output_file = os.path.join(output_dirs['points'], f"{safe_station_name}.csv")
                    station_df.to_csv(output_file, index=False, encoding='utf-8')
            
            logger.info(f"插值结果已保存到: {output_dirs['points']}")
            
        except Exception as e:
            logger.error(f"保存插值结果失败: {e}")
    
    def save_weight_information(self):
        """保存权重信息"""
        if not self.config.output_weight_info or not self.interpolation_results:
            return
        
        try:
            logger.info("保存权重信息...")
            
            output_dirs = self.config.get_output_dirs()
            
            # 收集权重信息
            weight_records = []
            
            for time_point, result in self.interpolation_results.items():
                if 'interpolation_details' not in result:
                    continue
                
                station_names = self.stations_with_terrain['站点'].tolist()
                details = result['interpolation_details']
                
                for i, (station_name, detail) in enumerate(zip(station_names, details)):
                    if isinstance(detail, dict):
                        record = {
                            '时间点': time_point,
                            '测试站点': station_name,
                            '插值方法': detail.get('method', '未知')
                        }
                        
                        # 添加邻居站点和权重信息
                        neighbor_stations = detail.get('neighbor_stations', [])
                        weights = detail.get('weights', [])
                        
                        for j, (neighbor, weight) in enumerate(zip(neighbor_stations, weights)):
                            record[f'邻居站点{j+1}'] = neighbor
                            record[f'权重{j+1}'] = weight
                        
                        weight_records.append(record)
            
            if weight_records:
                # 创建DataFrame并保存
                weight_df = pd.DataFrame(weight_records)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                weight_file = os.path.join(output_dirs['evaluation'], f"weights_{timestamp}.csv")
                weight_df.to_csv(weight_file, index=False, encoding='utf-8')
                
                logger.info(f"权重信息已保存到: {weight_file}")
            
        except Exception as e:
            logger.error(f"保存权重信息失败: {e}")
    
    def process_raster_output(self):
        """处理栅格输出"""
        if not self.config.output_raster or not self.interpolation_results:
            return
        
        try:
            logger.info("处理栅格输出...")
            
            time_points = sorted(self.interpolation_results.keys())
            output_dirs = self.config.get_output_dirs()
            
            self.raster_processor.process_time_series_rasters(
                time_points, self.interpolation_results,
                self.prism_core, self.delaunay_tri,
                self.stations_with_terrain, output_dirs['main']
            )
            
        except Exception as e:
            logger.error(f"处理栅格输出失败: {e}")
    
    def run_complete_workflow(self):
        """运行完整的PRISM插值工作流程"""
        start_time = time.time()
        
        try:
            logger.info("="*60)
            logger.info("开始PRISM插值完整工作流程")
            logger.info("="*60)
            
            # 1. 加载数据
            self.load_data()
            
            # 2. 构建空间结构
            self.build_spatial_structure()
            
            # 3. 执行插值
            self.run_interpolation()
            
            # 4. 评价结果
            evaluation_results = self.evaluate_results()
            
            # 5. 保存结果
            self.save_interpolation_results()
            self.save_weight_information()
            
            # 6. 处理栅格输出
            self.process_raster_output()
            
            # 7. 清理内存
            self.cleanup()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            logger.info("="*60)
            logger.info(f"PRISM插值工作流程完成！总耗时: {total_time:.2f} 秒")
            logger.info(f"结果保存在: {self.config.output_dir}")
            logger.info("="*60)
            
            return evaluation_results
            
        except Exception as e:
            logger.error(f"PRISM插值工作流程失败: {e}")
            raise
    
    def cleanup(self):
        """清理内存"""
        if self.config.memory_efficient:
            self.data_processor.cleanup()
            self.raster_processor.cleanup()
            gc.collect()
            logger.info("内存清理完成")

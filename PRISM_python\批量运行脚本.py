"""
PRISM批量运行脚本 - 运行所有洪水事件并优化表现较差的场次
"""

import os
import sys
import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from config import Config
from batch_processing import BatchProcessor
from prism_main import PRISMInterpolation

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("prism_batch_run.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def run_all_floods():
    """运行所有洪水批次"""
    print("="*80)
    print("PRISM空间插值系统 - 批量运行所有洪水事件")
    print("="*80)
    
    try:
        # 创建批量处理配置
        config = Config(
            enable_batch_processing=True,
            batch_input_root="D:/pythondata/spatial_interpolation/input_another",
            batch_output_root="D:/pythondata/spatial_interpolation/output/PRISM",
            stations_file="D:/pythondata/spatial_interpolation/stations.csv",
            terrain_dir="D:/pythondata/spatial_interpolation/terrain/90",
            num_cores=12,
            memory_efficient=True,
            output_raster=True,  # 批量处理时关闭栅格输出以节省时间
            output_delaunay_plot=True,
            output_evaluation=True,
            verbose_logging=True
        )
        
        print(f"配置信息:")
        print(f"  输入根目录: {config.batch_input_root}")
        print(f"  输出根目录: {config.batch_output_root}")
        print(f"  并行核心数: {config.num_cores}")
        print("-"*80)
        
        # 执行批量处理
        print("开始批量处理所有洪水事件...")
        batch_processor = BatchProcessor(config)
        batch_results = batch_processor.run_batch_processing()
        
        if not batch_results:
            print("❌ 批量处理失败")
            return
        
        print(f"✅ 批量处理完成，处理了 {len(batch_results)} 个事件")
        
        # 分析结果
        analyze_results(batch_results)
        
        # 优化表现较差的事件
        optimize_poor_events(batch_results, config)
        
        print("="*80)
        print("批量处理完成！")
        print(f"详细结果请查看: {config.batch_output_root}")
        print("="*80)
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        print(f"❌ 程序执行失败: {e}")


def analyze_results(batch_results):
    """分析批量处理结果"""
    print("\n" + "="*50)
    print("批量处理结果分析")
    print("="*50)
    
    successful_events = []
    failed_events = []
    nse_values = []
    
    for event_name, result in batch_results.items():
        if isinstance(result, dict):
            if result.get('status') == 'success':
                successful_events.append(event_name)
                nse = result.get('overall_nse', 0)
                nse_values.append((event_name, nse))
            else:
                failed_events.append(event_name)
    
    print(f"总事件数: {len(batch_results)}")
    print(f"成功处理: {len(successful_events)}")
    print(f"处理失败: {len(failed_events)}")
    print(f"成功率: {len(successful_events)/len(batch_results)*100:.1f}%")
    
    if failed_events:
        print(f"\n失败事件: {', '.join(failed_events[:5])}")
        if len(failed_events) > 5:
            print(f"... 还有 {len(failed_events) - 5} 个")
    
    if nse_values:
        nse_only = [nse for _, nse in nse_values]
        print(f"\nNSE统计:")
        print(f"  平均NSE: {np.mean(nse_only):.4f}")
        print(f"  最大NSE: {np.max(nse_only):.4f}")
        print(f"  最小NSE: {np.min(nse_only):.4f}")
        
        # 分类统计
        excellent = sum(1 for nse in nse_only if nse > 0.75)
        good = sum(1 for nse in nse_only if 0.5 <= nse <= 0.75)
        poor = sum(1 for nse in nse_only if nse < 0.5)
        
        print(f"  优秀 (NSE > 0.75): {excellent} ({excellent/len(nse_only)*100:.1f}%)")
        print(f"  良好 (0.5 ≤ NSE ≤ 0.75): {good} ({good/len(nse_only)*100:.1f}%)")
        print(f"  较差 (NSE < 0.5): {poor} ({poor/len(nse_only)*100:.1f}%)")
        
        # 显示表现较差的事件
        poor_events = [(name, nse) for name, nse in nse_values if nse < 0.5]
        if poor_events:
            print(f"\n表现较差的事件 (NSE < 0.5):")
            for name, nse in sorted(poor_events, key=lambda x: x[1])[:10]:
                print(f"  {name}: NSE = {nse:.4f}")
            if len(poor_events) > 10:
                print(f"  ... 还有 {len(poor_events) - 10} 个")


def optimize_poor_events(batch_results, base_config):
    """优化表现较差的事件"""
    print("\n" + "="*50)
    print("优化表现较差的事件")
    print("="*50)
    
    # 找出NSE < 0.5的事件
    poor_events = []
    for event_name, result in batch_results.items():
        if isinstance(result, dict) and result.get('status') == 'success':
            nse = result.get('overall_nse', 0)
            if nse < 0.5:
                poor_events.append((event_name, nse))
    
    if not poor_events:
        print("没有需要优化的事件（所有事件NSE >= 0.5）")
        return
    
    print(f"发现 {len(poor_events)} 个需要优化的事件")
    
    optimization_results = {}
    
    for event_name, original_nse in poor_events[:5]:  # 只优化前5个最差的事件
        print(f"\n正在优化事件: {event_name} (原始NSE: {original_nse:.4f})")
        
        try:
            # 尝试不同的参数组合
            param_sets = [
                # 参数组合1：增加邻站数，减少地形权重
                {
                    'neighbor_count': 5,
                    'elevation_weight': 0.2,
                    'slope_weight': 0.2,
                    'aspect_weight': 0.2,
                    'moran_weight': 0.4
                },
                # 参数组合2：更平滑的插值
                {
                    'neighbor_count': 4,
                    'distance_power': 1.5,
                    'elevation_weight': 0.3,
                    'slope_weight': 0.3,
                    'aspect_weight': 0.2,
                    'moran_weight': 0.2
                },
                # 参数组合3：强调空间相关性
                {
                    'neighbor_count': 4,
                    'elevation_weight': 0.1,
                    'slope_weight': 0.1,
                    'aspect_weight': 0.1,
                    'moran_weight': 0.7
                }
            ]
            
            best_nse = original_nse
            best_params = None
            
            for i, params in enumerate(param_sets):
                print(f"  尝试参数组合 {i+1}/3...")
                
                # 创建优化配置
                opt_config = Config(
                    input_dir=os.path.join(base_config.batch_input_root, event_name),
                    terrain_dir=base_config.terrain_dir,
                    output_dir=os.path.join(base_config.batch_output_root, f"{event_name}_opt"),
                    stations_file=base_config.stations_file,
                    num_cores=base_config.num_cores,
                    memory_efficient=True,
                    output_raster=False,
                    output_delaunay_plot=False,
                    output_evaluation=True,
                    verbose_logging=False
                )
                
                # 应用参数
                for key, value in params.items():
                    setattr(opt_config, key, value)
                
                # 运行优化
                prism = PRISMInterpolation(opt_config)
                eval_results = prism.run_complete_workflow()
                
                if eval_results and 'overall_metrics' in eval_results:
                    nse = eval_results['overall_metrics'].get('NSE', -999)
                    print(f"    NSE: {nse:.4f}")
                    
                    if nse > best_nse:
                        best_nse = nse
                        best_params = params.copy()
                        print(f"    ✅ 找到更好的配置！")
                
                # 清理临时文件
                import shutil
                if os.path.exists(opt_config.output_dir):
                    shutil.rmtree(opt_config.output_dir)
            
            if best_params:
                improvement = best_nse - original_nse
                print(f"  优化完成: NSE从 {original_nse:.4f} 提升到 {best_nse:.4f} (提升 {improvement:.4f})")
                
                optimization_results[event_name] = {
                    'original_nse': original_nse,
                    'optimized_nse': best_nse,
                    'improvement': improvement,
                    'best_params': best_params
                }
                
                # 使用最佳参数重新运行并保存结果
                final_config = Config(
                    input_dir=os.path.join(base_config.batch_input_root, event_name),
                    terrain_dir=base_config.terrain_dir,
                    output_dir=os.path.join(base_config.batch_output_root, f"{event_name}_optimized"),
                    stations_file=base_config.stations_file,
                    num_cores=base_config.num_cores,
                    memory_efficient=True,
                    output_raster=True,  # 最终结果输出栅格
                    output_delaunay_plot=True,
                    output_evaluation=True
                )
                
                for key, value in best_params.items():
                    setattr(final_config, key, value)
                
                print(f"  正在生成优化后的最终结果...")
                prism_final = PRISMInterpolation(final_config)
                prism_final.run_complete_workflow()
                
            else:
                print(f"  优化失败，未找到更好的参数")
                optimization_results[event_name] = {
                    'original_nse': original_nse,
                    'optimization_failed': True
                }
                
        except Exception as e:
            print(f"  优化事件 {event_name} 时出错: {e}")
            optimization_results[event_name] = {
                'original_nse': original_nse,
                'optimization_failed': True,
                'error': str(e)
            }
    
    # 保存优化结果
    if optimization_results:
        save_optimization_results(optimization_results, base_config.batch_output_root)


def save_optimization_results(optimization_results, output_root):
    """保存优化结果"""
    try:
        summary_dir = os.path.join(output_root, "optimization_summary")
        os.makedirs(summary_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON结果
        json_file = os.path.join(summary_dir, f"optimization_results_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(optimization_results, f, indent=4, ensure_ascii=False)
        
        # 创建CSV汇总
        summary_data = []
        for event_name, result in optimization_results.items():
            summary_data.append({
                'Event': event_name,
                'Original_NSE': result.get('original_nse', 0),
                'Optimized_NSE': result.get('optimized_nse', 0),
                'Improvement': result.get('improvement', 0),
                'Success': not result.get('optimization_failed', False),
                'Best_Params': str(result.get('best_params', {}))
            })
        
        summary_df = pd.DataFrame(summary_data)
        csv_file = os.path.join(summary_dir, f"optimization_summary_{timestamp}.csv")
        summary_df.to_csv(csv_file, index=False, encoding='utf-8')
        
        print(f"\n优化结果已保存到: {summary_dir}")
        
    except Exception as e:
        logger.error(f"保存优化结果失败: {e}")


def main():
    """主函数"""
    print("PRISM批量运行脚本")
    print("此脚本将：")
    print("1. 批量处理所有洪水事件")
    print("2. 分析处理结果")
    print("3. 自动优化表现较差的事件（NSE < 0.5）")
    print("4. 生成优化后的最终结果")
    print()
    
    confirm = input("是否继续？(y/n): ").strip().lower()
    if confirm != 'y':
        print("已取消运行")
        return
    
    run_all_floods()


if __name__ == "__main__":
    main()

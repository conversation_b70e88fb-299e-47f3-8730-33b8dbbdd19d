import pandas as pd
import numpy as np

# 假设pivot_df是之前处理后的数据表
df = pd.read_csv('D:/pythondata/spatial_interpolation/output/OI/201702/processed_data.csv')  # 如果已保存到文件，可以先读取

# 提取时间索引作为单独列
time_index = df['time_index']

# 获取所有站点ID
station_cols = df.columns[1:]  # 假设第一个列是时间索引，其余列是站点数据
station_ids = [col.split('_')[1] for col in station_cols]

# 提取每个站点的实际值和插值
actual_values = []
interpolated_values = []
for col in station_cols:
    if col.startswith('actual_value_'):
        actual_values.append(df[col].values)
    elif col.startswith('interpolated_value_'):
        interpolated_values.append(df[col].values)

# 确保站点数量与实际值和插值数量匹配
station_ids = station_ids[:len(actual_values)]

# 计算每个站点的 MAE、RMSE、NSE
mae_list = []
rmse_list = []
nse_list = []

for i in range(len(station_ids)):
    actual = actual_values[i]
    interpolated = interpolated_values[i]
    
    # MAE
    mae = np.mean(np.abs(actual - interpolated))
    
    # RMSE
    rmse = np.sqrt(np.mean((actual - interpolated)**2))
    
    # Nash-Sutcliffe Efficiency (NSE)
    mean_actual = np.mean(actual)
    numerator = np.sum((actual - interpolated)**2)
    denominator = np.sum((actual - mean_actual)**2)
    nse = 1 - (numerator / denominator) if denominator != 0 else np.nan
    
    mae_list.append(mae)
    rmse_list.append(rmse)
    nse_list.append(nse)

# 创建指标数据框
metrics_df = pd.DataFrame({
    'station_id': station_ids,
    'MAE': mae_list,
    'RMSE': rmse_list,
    'NSE': nse_list
})

# 打印结果
print(metrics_df[['station_id', 'MAE', 'RMSE', 'NSE']].to_string(index=False))

# 保存结果到CSV
metrics_df.to_csv('D:/pythondata/spatial_interpolation/output/OI/201702/station_metrics.csv', index=False)

print("站点指标已计算完成并已保存到 station_metrics.csv")
# -*- coding: utf-8 -*-
"""
PRISM空间插值方法实现
此程序用于将点雨量数据通过PRISM方法插值为面雨量
作者：水文专业教授
日期：2025-03-04
"""

# 导入所需的库
import os
import sys
import numpy as np
import pandas as pd
from osgeo import gdal
import matplotlib.pyplot as plt
from datetime import datetime
import glob
import re
import random
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from scipy.spatial.distance import cdist
from multiprocessing import Pool, cpu_count
import time
import warnings
import logging

# 忽略警告
warnings.filterwarnings("ignore")

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PRISMInterpolation:
    """PRISM空间插值方法的实现类"""
    
    def __init__(self, input_dir, terrain_dir, output_dir, stations_file, watershed_info, n_cores=23,G=0.01):
        """
        初始化PRISM插值类
        
        参数:
            input_dir (str): 输入数据目录，包含点雨量CSV文件
            terrain_dir (str): 地形数据目录，包含DEM、坡度和坡向数据
            output_dir (str): 输出目录
            stations_file (str): 站点信息文件路径
            watershed_info (dict): 流域信息
            n_cores (int): 并行计算的核心数
        """
        self.input_dir = input_dir
        self.terrain_dir = terrain_dir
        self.output_dir = output_dir
        self.stations_file = stations_file
        self.watershed_info = watershed_info
        self.n_cores = min(n_cores, cpu_count())
        self.G = G 
        # 创建输出目录（如果不存在）
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logger.info(f"创建输出目录: {output_dir}")
        
        # 加载地形数据
        self.dem, self.dem_geotransform, self.dem_proj = self._load_raster(os.path.join(terrain_dir, "dem.asc"))
        self.slope, _, _ = self._load_raster(os.path.join(terrain_dir, "slope.asc"))
        self.aspect, _, _ = self._load_raster(os.path.join(terrain_dir, "aspect.asc"))
        
        # 流域掩码（用于最终裁剪）
        self.watershed_mask = self._create_watershed_mask()
        
        # 加载站点信息并提取地形数据
        self.stations_data = self._load_stations()
        
        # 获取所有降雨量文件列表
        self.rainfall_files = self._get_rainfall_files()
        
        # 预计算所有栅格单元的坐标
        self.grid_coords = self._calculate_grid_coordinates()
        
        # 设置输出栅格信息
        self.out_geotransform = self.dem_geotransform
        self.out_proj = self.dem_proj
        
        logger.info("PRISM插值初始化完成")
    
    def _load_raster(self, raster_path):
        """
        加载栅格数据
        
        参数:
            raster_path (str): 栅格文件路径
            
        返回:
            tuple: (栅格数据数组, 地理变换参数, 投影信息)
        """
        try:
            # 打开栅格文件
            ds = gdal.Open(raster_path)
            if ds is None:
                raise ValueError(f"无法打开栅格文件: {raster_path}")
            
            # 读取地理变换和投影信息
            geotransform = ds.GetGeoTransform()
            projection = ds.GetProjection()
            
            # 读取栅格数据为NumPy数组
            band = ds.GetRasterBand(1)
            data = band.ReadAsArray()
            
            # 获取NoData值
            nodata = band.GetNoDataValue()
            
            # 关闭数据集
            ds = None
            
            # 将NoData值替换为NaN
            if nodata is not None:
                data = np.where(data == nodata, np.nan, data)
                
            logger.info(f"成功加载栅格文件: {raster_path}")
            return data, geotransform, projection
            
        except Exception as e:
            logger.error(f"加载栅格文件时出错: {raster_path}, 错误: {str(e)}")
            sys.exit(1)
    
    def _create_watershed_mask(self):
        """
        创建流域掩码
        
        返回:
            numpy.ndarray: 流域掩码数组
        """
        # 使用DEM创建流域掩码（非NaN区域为流域区域）
        mask = ~np.isnan(self.dem)
        logger.info("创建流域掩码完成")
        return mask
    
    def _load_stations(self):
        """
        加载站点数据并提取地形信息
        
        返回:
            pandas.DataFrame: 带有地形信息的站点数据
        """
        try:
            # 读取站点数据
            stations = pd.read_csv(self.stations_file, encoding='utf-8')
            logger.info(f"站点数据加载成功，共 {len(stations)} 个站点")
            
            # 确保站点数据包含必要的列
            required_columns = ['站点', '经度', '纬度']
            for col in required_columns:
                if col not in stations.columns:
                    raise ValueError(f"站点数据缺少必要的列: {col}")
            
            # 为每个站点提取DEM、坡度和坡向值
            stations['高程'] = np.nan
            stations['坡度'] = np.nan
            stations['坡向'] = np.nan
            
            for idx, station in stations.iterrows():
                # 计算栅格索引
                x_index, y_index = self._lonlat_to_index(station['经度'], station['纬度'])
                
                # 提取地形数据（如果索引有效）
                if 0 <= y_index < self.dem.shape[0] and 0 <= x_index < self.dem.shape[1]:
                    stations.at[idx, '高程'] = self.dem[y_index, x_index]
                    stations.at[idx, '坡度'] = self.slope[y_index, x_index]
                    stations.at[idx, '坡向'] = self.aspect[y_index, x_index]
            
            # 检查是否所有站点都有有效的地形数据
            invalid_stations = stations[stations['高程'].isna()]['站点'].tolist()
            if invalid_stations:
                logger.warning(f"以下站点缺少地形数据（可能位于研究区外）: {invalid_stations}")
                
            # 转换站点ID为字符串类型
            stations['站点'] = stations['站点'].astype(str)
            
            return stations
            
        except Exception as e:
            logger.error(f"加载站点数据时出错: {str(e)}")
            sys.exit(1)

    def _calculate_triangle_area(self, a, b, c):
        """
    计算三角形面积（海伦公式）
    
    参数:
        a, b, c (float): 三角形的三条边长
        
    返回:
        float: 三角形面积
    """
        s = (a + b + c) / 2
        area = np.sqrt(s * (s - a) * (s - b) * (s - c))
        return area
    
    def _select_reference_stations(self, station_coords, grid_coords):
        """
    根据三角几何关系选择参证站
    
    参数:
        station_coords (numpy.ndarray): 所有站点的坐标 [x, y]
        grid_coords (numpy.ndarray): 待估点的坐标 [x, y]
        
    返回:
        list: 选择的参证站索引
    """
        # 计算所有站点与待估点的距离
        distances = cdist(grid_coords.reshape(1, -1), station_coords)
    
     # 选择距离最近的2个站点
        nearest_indices = np.argsort(distances)[0][:2]
        nearest_stations = station_coords[nearest_indices]
    
    # 计算三角形面积比例
        selected_indices = list(nearest_indices)
        for i in range(len(station_coords)):
            if i not in nearest_indices:
                # 计算三角形ABC的面积
                a = np.linalg.norm(station_coords[nearest_indices[0]] - station_coords[nearest_indices[1]])
                b = np.linalg.norm(station_coords[nearest_indices[0]] - station_coords[i])
                c = np.linalg.norm(station_coords[nearest_indices[1]] - station_coords[i])
                area_abc = self._calculate_triangle_area(a, b, c)
            
                # 计算三角形ABM、ACM、BCM的面积
                d1 = np.linalg.norm(station_coords[nearest_indices[0]] - grid_coords)
                d2 = np.linalg.norm(station_coords[nearest_indices[1]] - grid_coords)
                d3 = np.linalg.norm(station_coords[i] - grid_coords)
                area_abm = self._calculate_triangle_area(a, d1, d2)
                area_acm = self._calculate_triangle_area(b, d1, d3)
                area_bcm = self._calculate_triangle_area(c, d2, d3)
            
                # 计算面积比例
                area_ratio = area_abc / (area_abm + area_acm + area_bcm)
            
                # 如果面积比例接近1，则选择该站点
                if np.isclose(area_ratio, 1, rtol=0.1):
                    selected_indices.append(i)
                    break
    
        # 如果没有找到满足条件的第三个站点，则只使用最近的2个站点
        if len(selected_indices) < 3:
            selected_indices = nearest_indices
    
        return selected_indices

    def _get_rainfall_files(self):
        """
        获取所有降雨量CSV文件的路径
        
        返回:
            list: 降雨量文件路径列表
        """
        # 获取输入目录中的所有CSV文件
        csv_files = glob.glob(os.path.join(self.input_dir, "*.csv"))
        
        # 过滤掉不是站点的CSV文件
        station_ids = set(self.stations_data['站点'].astype(str).tolist())
        rainfall_files = []
        
        for csv_file in csv_files:
            filename = os.path.basename(csv_file).split('.')[0]
            if filename in station_ids:
                rainfall_files.append(csv_file)
        
        logger.info(f"找到 {len(rainfall_files)} 个有效的降雨量文件")
        return rainfall_files
    
    def _lonlat_to_index(self, lon, lat):
        """
        将经纬度坐标转换为栅格索引
        
        参数:
            lon (float): 经度
            lat (float): 纬度
            
        返回:
            tuple: (x索引, y索引)
        """
        # 从地理变换参数中提取信息
        x_origin = self.dem_geotransform[0]
        y_origin = self.dem_geotransform[3]
        pixel_width = self.dem_geotransform[1]
        pixel_height = self.dem_geotransform[5]
        
        # 计算索引
        x_index = int((lon - x_origin) / pixel_width)
        y_index = int((lat - y_origin) / pixel_height)
        
        return x_index, y_index
    
    def _index_to_lonlat(self, x_index, y_index):
        """
        将栅格索引转换为经纬度坐标
        
        参数:
            x_index (int): x索引
            y_index (int): y索引
            
        返回:
            tuple: (经度, 纬度)
        """
        # 从地理变换参数中提取信息
        x_origin = self.dem_geotransform[0]
        y_origin = self.dem_geotransform[3]
        pixel_width = self.dem_geotransform[1]
        pixel_height = self.dem_geotransform[5]
        
        # 计算经纬度（使用像素中心）
        lon = x_origin + (x_index + 0.5) * pixel_width
        lat = y_origin + (y_index + 0.5) * pixel_height
        
        return lon, lat
    
    def _calculate_grid_coordinates(self):
        """
        预计算所有栅格单元的坐标
        
        返回:
            numpy.ndarray: 栅格坐标数组 [x, y]
        """
        # 获取栅格尺寸
        rows, cols = self.dem.shape
        
        # 创建坐标数组
        grid_coords = np.zeros((rows * cols, 2))
        
        # 填充坐标
        idx = 0
        for y in range(rows):
            for x in range(cols):
                if not np.isnan(self.dem[y, x]):  # 只考虑有效的DEM单元
                    lon, lat = self._index_to_lonlat(x, y)
                    grid_coords[idx] = [lon, lat]
                    idx += 1
        
        # 只保留有效坐标
        grid_coords = grid_coords[:idx]
        
        logger.info(f"计算了 {len(grid_coords)} 个有效栅格单元的坐标")
        return grid_coords
    
    def _calculate_terrain_similarity(self, station_dem, station_slope, station_aspect, grid_dem, grid_slope, grid_aspect):
        """
        计算地形相似性权重
        
        参数:
            station_dem (float): 站点高程
            station_slope (float): 站点坡度
            station_aspect (float): 站点坡向
            grid_dem (numpy.ndarray): 栅格高程
            grid_slope (numpy.ndarray): 栅格坡度
            grid_aspect (numpy.ndarray): 栅格坡向
            
        返回:
            numpy.ndarray: 地形相似性权重
        """
        # 计算高程相似性（归一化）
        dem_max_diff = 1602  # 假设最大高程差为1573米
        G = 0.01  # 从外部参数传入
        dem_similarity = 1 - np.abs((grid_dem - station_dem)* G) / dem_max_diff
        dem_similarity = np.clip(dem_similarity, 0.01, 1)
        
        # 计算坡度相似性（归一化）
        slope_max_diff = 81.1917  # 假设最大坡度差为67.6254度
        slope_similarity = 1 - np.abs(grid_slope - station_slope) / slope_max_diff
        slope_similarity = np.clip(slope_similarity, 0.01, 1)
        
        # 计算坡向相似性（考虑环形数据的特性）
        aspect_diff = np.minimum(np.abs(grid_aspect - station_aspect), 360 - np.abs(grid_aspect - station_aspect))
        aspect_similarity = 1 - aspect_diff / 180
        aspect_similarity = np.clip(aspect_similarity, 0.01, 1)
        
        # 综合地形相似性（分层计算）
        # 首先按高程带分组
        elevation_bands = np.array([
            (np.abs(grid_dem - station_dem) < 100),  # 高程相差小于100米
            (np.abs(grid_dem - station_dem) < 300) & (np.abs(grid_dem - station_dem) >= 100),  # 高程相差100-300米
            (np.abs(grid_dem - station_dem) < 600) & (np.abs(grid_dem - station_dem) >= 300),  # 高程相差300-600米
            (np.abs(grid_dem - station_dem) >= 600)  # 高程相差大于600米
        ], dtype=float)
        
        # 为每个高程带分配权重因子
        elevation_weights = np.array([0.95, 0.7, 0.5, 0.3])
        
        # 在每个高程带内部计算地形相似性
        terrain_similarity = np.zeros_like(grid_dem)
        
        for i in range(len(elevation_bands)):
            band_mask = elevation_bands[i]
            if np.any(band_mask):
                # 在高程带内部，综合考虑坡度和坡向的相似性
                band_similarity = slope_similarity * 0.4 + aspect_similarity * 0.6
                terrain_similarity += band_mask * band_similarity * elevation_weights[i]
        
        # 确保权重介于0.1和1之间
        terrain_similarity = np.clip(terrain_similarity, 0.1, 1.0)
        
        return terrain_similarity
    
    def _calculate_spatial_weights(self, station_coords, grid_coords, terrain_similarity):
        """
        计算空间权重
        
        参数:
            station_coords (numpy.ndarray): 站点坐标 [x, y]
            grid_coords (numpy.ndarray): 栅格坐标 [x, y]
            terrain_similarity (numpy.ndarray): 地形相似性权重
            
        返回:
            numpy.ndarray: 空间权重
        """
        # 计算距离矩阵
        distances = cdist(grid_coords, station_coords)
        
        # 设置距离权重（使用IDW方法）
        # 参数p控制距离衰减的速率，p越大表示距离影响越显著
        p = 1.8
        distance_weights = 1.0 / (distances ** p + 1e-10)  # 避免除以零
        
        # 计算空间自相关权重
        # 这里使用简化的空间自相关模型，实际应用中可能需要更复杂的模型
        spatial_autocorr = np.exp(-distances / 30000)  # 假设空间自相关范围为50km
        
        # 组合地形相似性权重、距离权重和空间自相关权重
        # 展开地形相似性以匹配距离矩阵的形状
        terrain_similarity_expanded = np.repeat(terrain_similarity[:, np.newaxis], station_coords.shape[0], axis=1)
        
        # 计算最终权重（地形相似性 × 距离权重 × 空间自相关）
        weights = terrain_similarity_expanded * distance_weights * spatial_autocorr
        
        # 归一化权重
        row_sums = weights.sum(axis=1, keepdims=True)
        weights = weights / (row_sums + 1e-10)  # 避免除以零
        
        return weights
    
    def _prism_interpolation(self, rainfall_data, train_stations_indices, time_step):
            """
            执行单个时间步的PRISM插值
            
            参数:
                rainfall_data (dict): 时间步的降雨量数据
                train_stations_indices (list): 用于训练的站点索引
                time_step (str): 时间步
                
            返回:
                tuple: (插值结果数组, 验证结果字典)
            """
            # 定义高程带
            elevation_bands = [
                (-100, 100),  # 低海拔
                (100, 400),  # 中低海拔
                (400, 800),  # 中海拔
                (800, 1200),  # 中高海拔
                (1200, 1650)  # 高海拔
            ]
            
            # 获取训练站点信息
            train_stations = self.stations_data.iloc[train_stations_indices].copy()
            
            # 添加当前时间步的降雨量
            train_stations['降雨量'] = np.nan
            for idx, station in train_stations.iterrows():
                station_id = station['站点']
                if station_id in rainfall_data:
                    train_stations.at[idx, '降雨量'] = rainfall_data[station_id]
            
            # 删除没有降雨量数据的站点
            valid_stations = train_stations.dropna(subset=['降雨量', '高程', '坡度', '坡向']).copy()
            
            if len(valid_stations) < 3:
                logger.warning(f"时间步 {time_step} 的有效站点数量不足（{len(valid_stations)}个），无法进行插值")
                return None, {}
            
            # 获取站点坐标
            station_coords = valid_stations[['经度', '纬度']].values
            
            # 创建输出栅格
            result = np.full_like(self.dem, np.nan)
            
            # 遍历每个高程带进行插值
            for band_min, band_max in elevation_bands:
                # 筛选当前高程带的栅格和站点
                band_mask = (self.dem >= band_min) & (self.dem < band_max)
                band_stations = valid_stations[(valid_stations['高程'] >= band_min) & (valid_stations['高程'] < band_max)]
            
                # 如果当前高程带没有站点，尝试使用邻近高程带的站点
                if len(band_stations) < 2:
                    # 扩展高程带范围，寻找邻近高程带的站点
                    extended_band_min = max(band_min - 300, -300)  # 扩展500米
                    extended_band_max = min(band_max + 300, 10000)  # 扩展500米
                    band_stations = valid_stations[(valid_stations['高程'] >= extended_band_min) & (valid_stations['高程'] < extended_band_max)]
                    logger.warning(f"高程带 {band_min}-{band_max} 的站点数据不足，使用扩展高程带 {extended_band_min}-{extended_band_max} 的站点进行插值")
            
                if not np.any(band_mask):
                    # 如果高程带内没有栅格，跳过此高程带
                    logger.warning(f"高程带 {band_min}-{band_max} 没有栅格，跳过此高程带")
                    continue
                
                # 获取高程带内的栅格坐标
                y_indices, x_indices = np.where(band_mask)
                band_dem = self.dem[band_mask]
                band_slope = self.slope[band_mask]
                band_aspect = self.aspect[band_mask]
                
                # 收集高程带内栅格的坐标
                band_grid_coords = np.zeros((len(y_indices), 2))
                for i in range(len(y_indices)):
                    lon, lat = self._index_to_lonlat(x_indices[i], y_indices[i])
                    band_grid_coords[i] = [lon, lat]
                
                # 获取高程带内站点的地形和降雨量数据
                band_station_coords = band_stations[['经度', '纬度']].values
                band_station_dem = band_stations['高程'].values
                band_station_slope = band_stations['坡度'].values
                band_station_aspect = band_stations['坡向'].values
                band_station_rainfall = band_stations['降雨量'].values
                
                # 对高程带内每个栅格计算降雨量
                for i in range(len(y_indices)):
                    y, x = y_indices[i], x_indices[i]
                    grid_dem = band_dem[i]
                    grid_slope = band_slope[i]
                    grid_aspect = band_aspect[i]
                    
                    # 选择参证站
                    selected_indices = self._select_reference_stations(band_station_coords, band_grid_coords[i])
                    
                    # 获取选择的参证站数据
                    selected_station_coords = band_station_coords[selected_indices]
                    selected_station_dem = band_station_dem[selected_indices]
                    selected_station_slope = band_station_slope[selected_indices]
                    selected_station_aspect = band_station_aspect[selected_indices]
                    selected_station_rainfall = band_station_rainfall[selected_indices]
                    
                    # 计算地形相似性
                    terrain_similarities = np.zeros(len(selected_indices))
                    for j in range(len(selected_indices)):
                        station_dem = selected_station_dem[j]
                        station_slope = selected_station_slope[j]
                        station_aspect = selected_station_aspect[j]
                        
                        # 计算高程相似性
                        dem_similarity = 1 - min(abs(grid_dem - station_dem) / 2000, 1)
                        
                        # 计算坡度相似性
                        slope_similarity = 1 - min(abs(grid_slope - station_slope) / 90, 1)
                        
                        # 计算坡向相似性
                        aspect_diff = min(abs(grid_aspect - station_aspect), 360 - abs(grid_aspect - station_aspect))
                        aspect_similarity = 1 - aspect_diff / 180
                        
                        # 综合地形相似性
                        terrain_similarities[j] = 0.5 * dem_similarity + 0.3 * slope_similarity + 0.2 * aspect_similarity
                    
                    # 计算距离权重
                    distances = np.sqrt(
                        (selected_station_coords[:, 0] - band_grid_coords[i, 0])**2 + 
                        (selected_station_coords[:, 1] - band_grid_coords[i, 1])**2
                    )
                    
                    # 计算IDW权重
                    p = 1.8  # 距离幂
                    idw_weights = 1.0 / (distances**p + 1e-10)
                    
                    # 计算空间自相关权重
                    spatial_weights = np.exp(-distances / 0.1)  # 假设0.1度范围内具有显著空间自相关
                    
                    # 计算综合权重
                    combined_weights = terrain_similarities * idw_weights * spatial_weights
                    combined_weights = combined_weights / combined_weights.sum()  # 归一化
                    
                    if np.all(selected_station_rainfall == 0):
                        result[y, x] = 0.0
                    else:
                        result[y, x] = np.sum(combined_weights * selected_station_rainfall)
            
            # 裁剪结果到流域范围
            result = np.where(self.watershed_mask, result, np.nan)
            
            # 构建验证结果
            validation_results = {}
            if len(train_stations_indices) < len(self.stations_data):
                # 有验证站点
                validation_indices = [i for i in range(len(self.stations_data)) if i not in train_stations_indices]
                validation_stations = self.stations_data.iloc[validation_indices].copy()
                
                for idx, station in validation_stations.iterrows():
                    station_id = station['站点']
                    if station_id in rainfall_data:
                        # 获取站点坐标和实际降雨量
                        lon, lat = station['经度'], station['纬度']
                        actual_rainfall = rainfall_data[station_id]
                        
                        # 获取插值降雨量
                        x_idx, y_idx = self._lonlat_to_index(lon, lat)
                        if 0 <= y_idx < result.shape[0] and 0 <= x_idx < result.shape[1]:
                            interpolated_rainfall = result[y_idx, x_idx]
                            
                            if not np.isnan(interpolated_rainfall):
                                validation_results[station_id] = {
                                    '实际降雨量': actual_rainfall,
                                    '插值降雨量': interpolated_rainfall,
                                    '误差': interpolated_rainfall - actual_rainfall
                                }
            
            return result, validation_results
    
    def _save_raster(self, data, output_path):
        """
        保存栅格数据到ASC文件
        
        参数:
            data (numpy.ndarray): 栅格数据数组
            output_path (str): 输出文件路径
        """
        try:
            # 获取栅格尺寸
            rows, cols = data.shape
            
            # 创建GDAL驱动
            driver = gdal.GetDriverByName('AAIGrid')
            if driver is None:
                raise RuntimeError("GDAL AAIGrid driver not found.")
           
             # 创建内存数据集
            mem_driver = gdal.GetDriverByName('MEM')
            mem_ds = mem_driver.Create('', cols, rows, 1, gdal.GDT_Float32)
            mem_ds.SetGeoTransform(self.out_geotransform)
            mem_ds.SetProjection(self.out_proj)
        
            # 写入数据
            mem_band = mem_ds.GetRasterBand(1)
            mem_band.SetNoDataValue(-9999)
            data_copy = data.copy()
            data_copy[np.isnan(data_copy)] = -9999
            mem_band.WriteArray(data_copy)
        
            # 使用 CreateCopy() 方法保存到文件
            out_ds = driver.CreateCopy(output_path, mem_ds)
            if out_ds is None:
                raise RuntimeError(f"Failed to create dataset: {output_path}")
        
            # 关闭数据集
            mem_ds = None
            out_ds = None
        
            logger.info(f"栅格数据成功保存到: {output_path}")
        
        except Exception as e:
            logger.error(f"保存栅格数据时出错: {str(e)}")
            # 设置地理变换和投影
            out_ds.SetGeoTransform(self.out_geotransform)
            out_ds.SetProjection(self.out_proj)
            
            # 设置NoData值
            out_band = out_ds.GetRasterBand(1)
            out_band.SetNoDataValue(-9999)
            
            # 将NaN替换为NoData值
            data_copy = data.copy()
            data_copy[np.isnan(data_copy)] = -9999
            
            # 写入数据
            out_band.WriteArray(data_copy)
            
            # 刷新缓存
            out_band.FlushCache()
            
            # 关闭数据集
            out_ds = None
            
            logger.info(f"栅格数据成功保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"保存栅格数据时出错: {str(e)}")
    
    def _process_time_step(self, args):
        """
        处理单个时间步（用于并行）
        
        参数:
            args (tuple): (时间步, 降雨量数据, 训练站点索引)
            
        返回:
            tuple: (时间步, 插值结果, 验证结果)
        """
        time_step, rainfall_data, train_indices = args
        
        # 执行PRISM插值
        interpolated_data, validation_results = self._prism_interpolation(rainfall_data, train_indices, time_step)
        
        return time_step, interpolated_data, validation_results
    
    def run_interpolation(self, output_validation=True, cross_validation=True, test_ratio=0.2):
        """
        运行PRISM插值
        
        参数:
            output_validation (bool): 是否输出验证结果
            cross_validation (bool): 是否进行交叉验证
            test_ratio (float): 测试站点比例
            
        返回:
            pandas.DataFrame: 验证结果
        """
        # 读取所有站点的降雨量数据
        all_station_data = {}
        time_steps = set()
        
        # 对每个站点逐个读取数据
        for station_file in self.rainfall_files:
            station_id = os.path.basename(station_file).split('.')[0]
            
            try:
                df = pd.read_csv(station_file, encoding='utf-8')
                if '时间' not in df.columns or '雨量' not in df.columns:
                    logger.warning(f"站点 {station_id} 的数据文件缺少必要的列")
                    continue
                
                # 转换为字典格式：{时间步: 雨量}
                station_data = dict(zip(df['时间'], df['雨量']))
                all_station_data[station_id] = station_data
                
                # 收集所有时间步
                time_steps.update(station_data.keys())
                
            except Exception as e:
                logger.error(f"读取站点 {station_id} 的数据时出错: {str(e)}")
        
        # 将时间步排序
        time_steps = sorted(list(time_steps))
        logger.info(f"共有 {len(time_steps)} 个时间步需要处理")
        
        if not time_steps:
            logger.error("没有找到有效的时间步，无法进行插值")
            return None
        
        # 分割训练和测试站点（用于交叉验证）
        if cross_validation:
            # 获取所有站点的索引
            station_indices = list(range(len(self.stations_data)))
            
            # 随机打乱
            random.shuffle(station_indices)
            
            # 分割训练和测试集
            test_size = max(1, int(len(station_indices) * test_ratio))
            train_indices = station_indices[:-test_size]
            
            logger.info(f"交叉验证: 使用 {len(train_indices)} 个站点进行插值，{test_size} 个站点进行验证")
        else:
            # 使用所有站点
            train_indices = list(range(len(self.stations_data)))
            logger.info(f"使用所有 {len(train_indices)} 个站点进行插值")
        
        # 准备并行处理的参数
        parallel_args = []
        for time_step in time_steps:
            # 收集特定时间步的所有站点降雨量
            rainfall_data = {}
            for station_id, data in all_station_data.items():
                if time_step in data:
                    rainfall_data[station_id] = data[time_step]
            
            parallel_args.append((time_step, rainfall_data, train_indices))
        
        # 初始化验证结果存储
        all_validation_results = []
        
        # 使用多进程并行处理
        start_time = time.time()
        
        try:
            with Pool(self.n_cores) as pool:
                # 使用tqdm来显示进度
                for time_step, interpolated_data, validation_results in pool.imap_unordered(self._process_time_step, parallel_args):
                    # 跳过无效的插值结果
                    if interpolated_data is None:
                        continue
                    
                    # 为时间步生成干净的文件名
                    clean_timestamp = re.sub(r'[:\\/ ]', '_', time_step)
                    output_path = os.path.join(self.output_dir, f"rainfall_{clean_timestamp}.asc")
                    
                    # 保存插值结果
                    self._save_raster(interpolated_data, output_path)
                    
                    # 收集验证结果
                    for station_id, result in validation_results.items():
                        all_validation_results.append({
                            '时间': time_step,
                            '站点': station_id,
                            '实际降雨量': result['实际降雨量'],
                            '插值降雨量': result['插值降雨量'],
                            '误差': result['误差']
                        })
        except Exception as e:
            logger.error(f"并行处理时出错: {str(e)}")
        
        end_time = time.time()
        logger.info(f"插值完成，耗时: {end_time - start_time:.2f} 秒")
        
        # 如果有验证结果并且需要输出
        if all_validation_results and output_validation:
            validation_df = pd.DataFrame(all_validation_results)
            
            # 计算统计指标
            mse = mean_squared_error(validation_df['实际降雨量'], validation_df['插值降雨量'])
            rmse = np.sqrt(mse)
            mae = mean_absolute_error(validation_df['实际降雨量'], validation_df['插值降雨量'])
            r2 = r2_score(validation_df['实际降雨量'], validation_df['插值降雨量'])
            
            # 将统计指标添加到DataFrame
            validation_stats = pd.DataFrame([{
                'MSE': mse,
                'RMSE': rmse,
                'MAE': mae,
                'R2': r2
            }])
            
            # 保存验证结果
            validation_output_path = os.path.join(self.output_dir, "validation_results.csv")
            validation_df.to_csv(validation_output_path, index=False, encoding='utf-8')
            
            # 保存统计指标
            stats_output_path = os.path.join(self.output_dir, "validation_statistics.csv")
            validation_stats.to_csv(stats_output_path, index=False, encoding='utf-8')
            
            logger.info(f"验证结果已保存到 {validation_output_path}")
            logger.info(f"验证统计已保存到 {stats_output_path}")
            logger.info(f"验证统计: MSE={mse:.4f}, RMSE={rmse:.4f}, MAE={mae:.4f}, R2={r2:.4f}")
            
            # 可视化验证结果
            self._visualize_validation(validation_df)
            
            return validation_df
        
        return None
    
    def _visualize_validation(self, validation_df):
        """
        可视化验证结果
        
        参数:
            validation_df (pandas.DataFrame): 验证结果数据框
        """
        try:
            # 创建散点图
            plt.figure(figsize=(10, 8))
            plt.scatter(validation_df['实际降雨量'], validation_df['插值降雨量'], alpha=0.6)
            
            # 添加1:1线
            min_val = min(validation_df['实际降雨量'].min(), validation_df['插值降雨量'].min())
            max_val = max(validation_df['实际降雨量'].max(), validation_df['插值降雨量'].max())
            plt.plot([min_val, max_val], [min_val, max_val], 'r--')
            
            # 添加标题和标签
            plt.title('PRISM插值验证: 实际降雨量 vs 插值降雨量')
            plt.xlabel('实际降雨量')
            plt.ylabel('插值降雨量')
            plt.grid(True)
            
            # 添加统计信息
            mse = mean_squared_error(validation_df['实际降雨量'], validation_df['插值降雨量'])
            rmse = np.sqrt(mse)
            mae = mean_absolute_error(validation_df['实际降雨量'], validation_df['插值降雨量'])
            r2 = r2_score(validation_df['实际降雨量'], validation_df['插值降雨量'])
            
            stats_text = f'RMSE: {rmse:.4f}\nMAE: {mae:.4f}\nR²: {r2:.4f}'
            plt.annotate(stats_text, xy=(0.05, 0.95), xycoords='axes fraction',
                        bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
            
            # 保存图像
            output_path = os.path.join(self.output_dir, "validation_plot.png")
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"验证结果图已保存到 {output_path}")
            
        except Exception as e:
            logger.error(f"可视化验证结果时出错: {str(e)}")


def main():
    """主函数"""
    # 用户可配置参数
    input_dir = 'D:\\pythondata\\spatial_interpolation\\input\\2020_4'
    terrain_dir = 'D:\\pythondata\\spatial_interpolation\\terrain'
    output_dir = 'D:\\pythondata\\spatial_interpolation\\output\\PRISM\\2020_4'
    stations_file = 'D:\\pythondata\\spatial_interpolation\\stations.csv'
    
    # 流域信息
    watershed_info = {
        'ncols': 2259,
        'nrows': 2718,
        'xllcorner': 110.229416879641,
        'yllcorner': 23.617071511454,
        'cellsize': 0.000294943097,
        'NODATA_value': -9999
    }
    
    # 并行核心数
    n_cores = 23
    
    # 创建PRISM插值实例
    prism = PRISMInterpolation(
        input_dir=input_dir,
        terrain_dir=terrain_dir,
        output_dir=output_dir,
        stations_file=stations_file,
        watershed_info=watershed_info,
        n_cores=n_cores,
        G=0.01
    )
    
    # 运行插值
    validation_results = prism.run_interpolation(
        output_validation=True,  # 输出验证结果
        cross_validation=True,   # 进行交叉验证
        test_ratio=0.2           # 20%的站点用于测试
    )
    
    logger.info("PRISM插值处理完成")


if __name__ == "__main__":
    main()
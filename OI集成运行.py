import os
import subprocess
import re
import pandas as pd
import glob
import time
import sys

def run_oi_batch_processing():
    """
    批处理函数，运行多个文件夹上的OI_不含栅格.py并记录评估指标
    """
    # 基础目录
    base_input_dir = "D:/pythondata/spatial_interpolation/input_another"
    stations_file = "D:/pythondata/spatial_interpolation/stations.csv"
    mask_file = "D:/pythondata/spatial_interpolation/terrain/90/mask.asc"
    base_output_dir = "D:/pythondata/spatial_interpolation/output/OI"
    
    # 确保输出基础目录存在
    os.makedirs(base_output_dir, exist_ok=True)
    
    # 寻找所有年份-季度文件夹
    input_folders = []
    for item in os.listdir(base_input_dir):
        item_path = os.path.join(base_input_dir, item)
        if os.path.isdir(item_path) and re.match(r"\d{4}-[1-4]", item):
            input_folders.append(item)
    
    if not input_folders:
        print(f"在 {base_input_dir} 中没有找到符合条件的文件夹")
        return
    
    # 排序文件夹
    input_folders.sort()
    print(f"找到 {len(input_folders)} 个待处理文件夹: {', '.join(input_folders)}")
    
    # 准备结果数据框
    results = []
    
    # 处理每个文件夹
    for folder in input_folders:
        print(f"\n处理文件夹: {folder}")
        try:
            input_folder_path = os.path.join(base_input_dir, folder)
            output_folder_path = os.path.join(base_output_dir, folder)
            
            # 运行OI_不含栅格.py并获取指标
            mae, rmse, cc, nse = run_oi_and_get_metrics(
                input_folder_path, 
                output_folder_path,
                stations_file,
                mask_file
            )
            
            # 添加到结果列表
            results.append({
                "Folder": folder,
                "MAE": mae,
                "RMSE": rmse,
                "CC": cc,
                "NSE": nse
            })
            
            print(f"{folder} 的指标: MAE={mae}, RMSE={rmse}, CC={cc}, NSE={nse}")
        except Exception as e:
            print(f"处理 {folder} 时出错: {e}")
            results.append({
                "Folder": folder,
                "MAE": None,
                "RMSE": None,
                "CC": None,
                "NSE": None,
                "Error": str(e)
            })
    
    # 创建数据框并保存到CSV
    results_df = pd.DataFrame(results)
    results_path = os.path.join(base_output_dir, "oi_metrics_summary.csv")
    results_df.to_csv(results_path, index=False, encoding='utf-8-sig')  # 使用带BOM的UTF-8编码，便于Excel打开
    
    print(f"\n结果已保存到: {results_path}")
    
    # 打印结果摘要
    print("\n结果摘要:")
    print(results_df.to_string())

def run_oi_and_get_metrics(input_folder, output_folder, stations_file, mask_file):
    """
    运行OI_不含栅格.py并提取评估指标
    
    参数:
    input_folder: 输入文件夹路径
    output_folder: 输出文件夹路径
    stations_file: 站点文件路径
    mask_file: 掩膜文件路径
    
    返回:
    mae, rmse, cc, nse: 提取的评估指标
    """
    # 创建输出文件夹（如果不存在）
    os.makedirs(output_folder, exist_ok=True)
    
    # 构建命令
    cmd = [
        sys.executable,  # 使用当前Python解释器
        "OI_不含栅格.py",
        "--input_dir", input_folder,
        "--stations_file", stations_file,
        "--mask_file", mask_file,
        "--output_dir", output_folder,
        "--num_processes", "20",
        "--batch_size", "100"
    ]
    
    # 运行命令并捕获输出
    print(f"运行 OI_不含栅格.py，处理文件夹 {os.path.basename(input_folder)}...")
    
    # 创建日志文件以捕获输出
    log_file_path = os.path.join(output_folder, "run_log.txt")
    start_time = time.time()
    
    with open(log_file_path, 'w', encoding='utf-8') as log_file:
        process = subprocess.run(cmd, stdout=log_file, stderr=subprocess.STDOUT, text=True)
    
    elapsed_time = time.time() - start_time
    print(f"OI_不含栅格.py 运行完成，耗时: {elapsed_time:.2f} 秒")
    
    # **从日志中提取指标**
    metrics_pattern = r"总体评价指标 - MAE: ([\d.]+), RMSE: ([\d.]+), CC: ([\d.]+), NSE: ([\d.]+)"
    
    # 首先尝试从捕获的日志中提取指标
    with open(log_file_path, 'r', encoding='utf-8') as log_file:
        log_content = log_file.read()
        match = re.search(metrics_pattern, log_content)
        if match:
            mae = float(match.group(1))
            rmse = float(match.group(2))
            cc = float(match.group(3))
            nse = float(match.group(4))
            return mae, rmse, cc, nse
    
    # 如果没有找到指标，检查输出目录中的其他日志文件
    log_files = glob.glob(os.path.join(output_folder, "*.log"))
    for log_file in log_files:
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                log_content = f.read()
                match = re.search(metrics_pattern, log_content)
                if match:
                    mae = float(match.group(1))
                    rmse = float(match.group(2))
                    cc = float(match.group(3))
                    nse = float(match.group(4))
                    return mae, rmse, cc, nse
        except Exception as e:
            print(f"读取日志文件 {log_file} 出错: {e}")
    
    # 如果仍未找到指标，尝试读取 validation_results.csv
    validation_file = os.path.join(output_folder, "validation_results.csv")
    if os.path.exists(validation_file):
        try:
            # 从验证结果计算指标
            df = pd.read_csv(validation_file)
            if 'actual_value' in df.columns and 'interpolated_value' in df.columns:
                df['error'] = df['actual_value'] - df['interpolated_value']
                mae = df['error'].abs().mean()
                rmse = (df['error'] ** 2).mean() ** 0.5
                
                # 计算NSE
                mean_actual = df['actual_value'].mean()
                if df['actual_value'].std() > 0:
                    numerator = ((df['actual_value'] - df['interpolated_value']) ** 2).sum()
                    denominator = ((df['actual_value'] - mean_actual) ** 2).sum()
                    nse = 1 - (numerator / denominator) if denominator != 0 else None
                    
                    # 计算CC (相关系数)
                    if df['interpolated_value'].std() > 0:
                        cc = df['actual_value'].corr(df['interpolated_value'])
                    else:
                        cc = None
                    
                    return mae, rmse, cc, nse
        except Exception as e:
            print(f"处理验证文件出错: {e}")
    
    raise Exception(f"无法提取 {input_folder} 的评估指标")

if __name__ == "__main__":
    try:
        run_oi_batch_processing()
    except Exception as e:
        print(f"程序运行出错: {e}")

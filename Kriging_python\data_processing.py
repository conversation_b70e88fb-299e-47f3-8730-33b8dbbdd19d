"""
Kriging插值系统数据处理模块
负责数据加载、预处理和验证
"""

import os
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
import rasterio
from rasterio.transform import from_bounds
import gc

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理器"""
    
    def __init__(self, config):
        """初始化数据处理器"""
        self.config = config
        self.stations_df = None
        self.rainfall_files = []
        self.terrain_data = {}
        
    def load_stations_data(self) -> pd.DataFrame:
        """加载站点数据"""
        try:
            logger.info(f"加载站点数据: {self.config.stations_file}")
            
            # 读取站点文件
            stations_df = pd.read_csv(self.config.stations_file, encoding='utf-8')
            
            # 验证必要列
            required_columns = ['站点', '经度', '纬度']
            missing_columns = [col for col in required_columns if col not in stations_df.columns]
            
            if missing_columns:
                raise ValueError(f"站点文件缺少必要列: {missing_columns}")
            
            # 数据清理
            stations_df = stations_df.dropna(subset=required_columns)
            
            # 数据类型转换
            stations_df['经度'] = pd.to_numeric(stations_df['经度'], errors='coerce')
            stations_df['纬度'] = pd.to_numeric(stations_df['纬度'], errors='coerce')
            
            # 移除无效坐标
            stations_df = stations_df.dropna(subset=['经度', '纬度'])
            
            # 坐标范围检查
            lon_range = (stations_df['经度'].min(), stations_df['经度'].max())
            lat_range = (stations_df['纬度'].min(), stations_df['纬度'].max())
            
            logger.info(f"站点数量: {len(stations_df)}")
            logger.info(f"经度范围: {lon_range[0]:.4f} ~ {lon_range[1]:.4f}")
            logger.info(f"纬度范围: {lat_range[0]:.4f} ~ {lat_range[1]:.4f}")
            
            self.stations_df = stations_df
            return stations_df
            
        except Exception as e:
            logger.error(f"加载站点数据失败: {e}")
            raise
    
    def load_rainfall_data(self) -> Dict[str, pd.DataFrame]:
        """加载降雨数据"""
        try:
            logger.info(f"加载降雨数据: {self.config.input_dir}")
            
            if not os.path.exists(self.config.input_dir):
                raise FileNotFoundError(f"输入目录不存在: {self.config.input_dir}")
            
            # 获取所有CSV文件
            csv_files = [f for f in os.listdir(self.config.input_dir) 
                        if f.endswith('.csv')]
            
            if not csv_files:
                raise FileNotFoundError(f"在 {self.config.input_dir} 中未找到CSV文件")
            
            rainfall_data = {}
            
            for csv_file in csv_files:
                file_path = os.path.join(self.config.input_dir, csv_file)
                station_name = os.path.splitext(csv_file)[0]
                
                try:
                    # 读取降雨数据
                    df = pd.read_csv(file_path, encoding='utf-8')
                    
                    # 验证必要列
                    if '时间' not in df.columns or '雨量' not in df.columns:
                        logger.warning(f"文件 {csv_file} 缺少必要列，跳过")
                        continue
                    
                    # 数据清理
                    df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce')
                    df = df.dropna(subset=['雨量'])
                    
                    # 确保雨量非负
                    df['雨量'] = df['雨量'].clip(lower=0)
                    
                    rainfall_data[station_name] = df
                    
                except Exception as e:
                    logger.warning(f"读取文件 {csv_file} 失败: {e}")
                    continue
            
            if not rainfall_data:
                raise ValueError("没有成功加载任何降雨数据文件")
            
            logger.info(f"成功加载 {len(rainfall_data)} 个站点的降雨数据")
            
            # 统计信息
            total_records = sum(len(df) for df in rainfall_data.values())
            logger.info(f"总降雨记录数: {total_records}")
            
            return rainfall_data
            
        except Exception as e:
            logger.error(f"加载降雨数据失败: {e}")
            raise
    
    def load_terrain_data(self) -> Dict[str, np.ndarray]:
        """加载地形数据（可选）"""
        if not self.config.enable_terrain_enhancement:
            logger.info("地形增强功能未启用，跳过地形数据加载")
            return {}
        
        try:
            logger.info("加载地形数据...")
            
            terrain_files = self.config.get_terrain_files()
            terrain_data = {}
            
            for name, file_path in terrain_files.items():
                if not os.path.exists(file_path):
                    logger.warning(f"地形文件不存在: {file_path}")
                    continue
                
                try:
                    with rasterio.open(file_path) as src:
                        data = src.read(1)
                        transform = src.transform
                        nodata = src.nodata
                        
                        # 处理无效值
                        if nodata is not None:
                            data = np.where(data == nodata, np.nan, data)
                        
                        terrain_data[name] = {
                            'data': data,
                            'transform': transform,
                            'nodata': nodata
                        }
                        
                        logger.info(f"加载地形数据 {name}: {data.shape}")
                        
                except Exception as e:
                    logger.warning(f"读取地形文件 {file_path} 失败: {e}")
                    continue
            
            if terrain_data:
                logger.info(f"成功加载 {len(terrain_data)} 个地形数据文件")
            else:
                logger.warning("未成功加载任何地形数据，将禁用地形增强功能")
                self.config.enable_terrain_enhancement = False
            
            self.terrain_data = terrain_data
            return terrain_data
            
        except Exception as e:
            logger.error(f"加载地形数据失败: {e}")
            self.config.enable_terrain_enhancement = False
            return {}
    
    def extract_terrain_features_for_stations(self, stations_df: pd.DataFrame) -> pd.DataFrame:
        """为站点提取地形特征"""
        if not self.config.enable_terrain_enhancement or not self.terrain_data:
            return stations_df
        
        try:
            logger.info("为站点提取地形特征...")
            
            stations_with_terrain = stations_df.copy()
            
            for terrain_name, terrain_info in self.terrain_data.items():
                data = terrain_info['data']
                transform = terrain_info['transform']
                
                terrain_values = []
                
                for _, station in stations_df.iterrows():
                    lon, lat = station['经度'], station['纬度']
                    
                    # 将地理坐标转换为像素坐标
                    col, row = ~transform * (lon, lat)
                    col, row = int(col), int(row)
                    
                    # 检查坐标是否在范围内
                    if (0 <= row < data.shape[0] and 0 <= col < data.shape[1]):
                        value = data[row, col]
                        if not np.isnan(value):
                            terrain_values.append(value)
                        else:
                            terrain_values.append(0.0)
                    else:
                        terrain_values.append(0.0)
                
                stations_with_terrain[terrain_name] = terrain_values
            
            logger.info(f"成功为 {len(stations_df)} 个站点提取地形特征")
            return stations_with_terrain
            
        except Exception as e:
            logger.error(f"提取地形特征失败: {e}")
            return stations_df
    
    def validate_data_consistency(self, stations_df: pd.DataFrame, 
                                rainfall_data: Dict[str, pd.DataFrame]) -> Tuple[pd.DataFrame, Dict[str, pd.DataFrame]]:
        """验证数据一致性"""
        try:
            logger.info("验证数据一致性...")
            
            # 获取站点列表
            station_names = set(stations_df['站点'].astype(str))
            rainfall_stations = set(rainfall_data.keys())
            
            # 找到匹配的站点
            matched_stations = station_names.intersection(rainfall_stations)
            
            if not matched_stations:
                raise ValueError("站点数据和降雨数据没有匹配的站点")
            
            # 过滤数据
            filtered_stations = stations_df[stations_df['站点'].astype(str).isin(matched_stations)].copy()
            filtered_rainfall = {k: v for k, v in rainfall_data.items() if k in matched_stations}
            
            logger.info(f"匹配的站点数量: {len(matched_stations)}")
            logger.info(f"未匹配的站点数量: {len(station_names) - len(matched_stations)}")
            
            if len(station_names) - len(matched_stations) > 0:
                unmatched = station_names - matched_stations
                logger.warning(f"未匹配的站点: {list(unmatched)[:10]}...")  # 只显示前10个
            
            return filtered_stations, filtered_rainfall
            
        except Exception as e:
            logger.error(f"数据一致性验证失败: {e}")
            raise
    
    def get_time_series_info(self, rainfall_data: Dict[str, pd.DataFrame]) -> Dict:
        """获取时间序列信息"""
        try:
            if not rainfall_data:
                return {}
            
            # 获取第一个站点的时间序列作为参考
            first_station = list(rainfall_data.keys())[0]
            first_df = rainfall_data[first_station]
            
            time_info = {
                'total_records': len(first_df),
                'time_column': '时间',
                'rainfall_column': '雨量',
                'sample_times': first_df['时间'].head(5).tolist() if len(first_df) > 0 else []
            }
            
            logger.info(f"时间序列信息: {time_info['total_records']} 个时间点")
            
            return time_info
            
        except Exception as e:
            logger.error(f"获取时间序列信息失败: {e}")
            return {}
    
    def cleanup(self):
        """清理内存"""
        try:
            if hasattr(self, 'terrain_data'):
                self.terrain_data.clear()
            
            gc.collect()
            logger.info("数据处理器内存清理完成")
            
        except Exception as e:
            logger.warning(f"内存清理失败: {e}")

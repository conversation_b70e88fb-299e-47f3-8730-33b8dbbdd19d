#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay三角剖分插值系统工具函数

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

def setup_logging(log_file: Path, log_level: str = 'INFO', console_output: bool = True):
    """设置日志系统"""
    # 创建日志目录
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 配置根日志器
    logging.basicConfig(
        level=level,
        format=log_format,
        handlers=[]
    )
    
    # 添加文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(level)
    file_handler.setFormatter(logging.Formatter(log_format))
    logging.getLogger().addHandler(file_handler)
    
    # 添加控制台处理器
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(console_handler)
    
    return logging.getLogger(__name__)

def load_delaunay_summary(file_path: Path) -> pd.DataFrame:
    """加载Delaunay分析汇总结果"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        logging.info(f"成功加载Delaunay分析结果，共{len(df)}个验证站点")
        return df
    except Exception as e:
        logging.error(f"加载Delaunay分析结果失败: {e}")
        raise

def load_station_data(file_path: Path) -> pd.DataFrame:
    """加载单个站点的降雨数据"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        # 确保时间列格式正确
        if '时间' in df.columns:
            df['时间'] = pd.to_datetime(df['时间'])
        # 确保降雨量列为数值型
        if '雨量' in df.columns:
            df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce').fillna(0.0)
        return df
    except Exception as e:
        logging.error(f"加载站点数据失败 {file_path}: {e}")
        return pd.DataFrame()

def load_flood_event_data(event_dir: Path, station_codes: List[str]) -> Dict[str, pd.DataFrame]:
    """加载洪水事件的所有相关站点数据"""
    station_data = {}
    
    for station_code in station_codes:
        file_path = event_dir / f"{station_code}.csv"
        if file_path.exists():
            data = load_station_data(file_path)
            if not data.empty:
                station_data[station_code] = data
        else:
            logging.warning(f"站点文件不存在: {file_path}")
    
    logging.info(f"成功加载{len(station_data)}个站点的数据")
    return station_data

def get_station_rainfall_at_time(station_data: Dict[str, pd.DataFrame], 
                                station_code: str, 
                                timestamp: pd.Timestamp) -> float:
    """获取指定站点在指定时间的降雨量"""
    if station_code not in station_data:
        return np.nan
    
    data = station_data[station_code]
    if data.empty:
        return np.nan
    
    # 查找最接近的时间点
    time_diff = abs(data['时间'] - timestamp)
    closest_idx = time_diff.idxmin()
    
    # 检查时间差是否在合理范围内（1小时）
    if time_diff.iloc[closest_idx] <= pd.Timedelta(hours=1):
        return data.loc[closest_idx, '雨量']
    else:
        return np.nan

def normalize_weights(weights: List[float]) -> List[float]:
    """归一化权重"""
    total_weight = sum(weights)
    if total_weight > 0:
        return [w / total_weight for w in weights]
    else:
        # 如果权重和为0，返回等权重
        n = len(weights)
        return [1.0 / n] * n if n > 0 else []

def calculate_weighted_interpolation(values: List[float], weights: List[float]) -> float:
    """计算加权插值结果"""
    if len(values) != len(weights):
        raise ValueError("值和权重的数量必须相等")
    
    if not values or not weights:
        return 0.0
    
    # 过滤掉NaN值
    valid_pairs = [(v, w) for v, w in zip(values, weights) if not np.isnan(v)]
    
    if not valid_pairs:
        return 0.0
    
    valid_values, valid_weights = zip(*valid_pairs)
    
    # 归一化权重
    normalized_weights = normalize_weights(list(valid_weights))
    
    # 计算加权平均
    result = sum(v * w for v, w in zip(valid_values, normalized_weights))
    
    # 确保结果非负
    return max(0.0, result)

def get_surrounding_stations_info(delaunay_summary: pd.DataFrame, 
                                target_station: str) -> Optional[Dict[str, Any]]:
    """获取目标站点的包围站点信息"""
    station_row = delaunay_summary[delaunay_summary['验证站点代码'] == target_station]
    
    if station_row.empty:
        logging.warning(f"未找到站点 {target_station} 的Delaunay分析结果")
        return None
    
    row = station_row.iloc[0]
    
    return {
        'target_station': {
            'code': row['验证站点代码'],
            'name': row['验证站点名称'],
            'longitude': row['验证站点经度'],
            'latitude': row['验证站点纬度']
        },
        'surrounding_stations': [
            {
                'code': row['包围站点1代码'],
                'name': row['包围站点1名称'],
                'weight': row['权重1']
            },
            {
                'code': row['包围站点2代码'],
                'name': row['包围站点2名称'],
                'weight': row['权重2']
            },
            {
                'code': row['包围站点3代码'],
                'name': row['包围站点3名称'],
                'weight': row['权重3']
            }
        ]
    }

def validate_data_quality(data: pd.DataFrame, station_code: str) -> Dict[str, Any]:
    """验证数据质量"""
    quality_report = {
        'station_code': station_code,
        'total_records': len(data),
        'missing_values': 0,
        'negative_values': 0,
        'extreme_values': 0,
        'data_range': {'min': 0, 'max': 0, 'mean': 0},
        'quality_score': 1.0,
        'issues': []
    }
    
    if data.empty:
        quality_report['quality_score'] = 0.0
        quality_report['issues'].append('数据为空')
        return quality_report
    
    rainfall_data = data['雨量']
    
    # 检查缺失值
    missing_count = rainfall_data.isna().sum()
    quality_report['missing_values'] = missing_count
    if missing_count > 0:
        quality_report['issues'].append(f'存在{missing_count}个缺失值')
    
    # 检查负值
    negative_count = (rainfall_data < 0).sum()
    quality_report['negative_values'] = negative_count
    if negative_count > 0:
        quality_report['issues'].append(f'存在{negative_count}个负值')
    
    # 检查极值（大于500mm/h的降雨）
    extreme_count = (rainfall_data > 500).sum()
    quality_report['extreme_values'] = extreme_count
    if extreme_count > 0:
        quality_report['issues'].append(f'存在{extreme_count}个极值（>500mm/h）')
    
    # 计算数据范围
    valid_data = rainfall_data.dropna()
    if not valid_data.empty:
        quality_report['data_range'] = {
            'min': float(valid_data.min()),
            'max': float(valid_data.max()),
            'mean': float(valid_data.mean())
        }
    
    # 计算质量分数
    total_issues = missing_count + negative_count + extreme_count
    if len(data) > 0:
        quality_report['quality_score'] = max(0.0, 1.0 - (total_issues / len(data)))
    
    return quality_report

def format_number(value: float, decimal_places: int = 4) -> str:
    """格式化数字显示"""
    if np.isnan(value):
        return 'NaN'
    return f"{value:.{decimal_places}f}"

def create_progress_bar(total: int, description: str = "处理进度"):
    """创建进度条（简单版本）"""
    def update_progress(current: int):
        percent = (current / total) * 100
        bar_length = 50
        filled_length = int(bar_length * current // total)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        print(f'\r{description}: |{bar}| {percent:.1f}% ({current}/{total})', end='', flush=True)
        if current == total:
            print()  # 换行
    
    return update_progress

def save_dataframe_to_multiple_formats(df: pd.DataFrame, 
                                     base_path: Path, 
                                     formats: List[str] = ['csv', 'excel'],
                                     decimal_places: int = 4):
    """将DataFrame保存为多种格式"""
    base_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 格式化数值列
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    for col in numeric_columns:
        df[col] = df[col].round(decimal_places)
    
    saved_files = []
    
    for format_type in formats:
        if format_type.lower() == 'csv':
            file_path = base_path.with_suffix('.csv')
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            saved_files.append(file_path)
        
        elif format_type.lower() in ['excel', 'xlsx']:
            file_path = base_path.with_suffix('.xlsx')
            df.to_excel(file_path, index=False, engine='openpyxl')
            saved_files.append(file_path)
    
    return saved_files

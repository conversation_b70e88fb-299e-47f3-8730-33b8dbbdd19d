#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Kriging插值系统批量运行脚本
自动处理所有洪水场次并生成汇总报告
"""

import os
import sys
import time
import logging
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from config import Config
from batch_processing import BatchProcessor
from parameter_optimization import ParameterOptimizer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kriging_batch_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def analyze_results(batch_results):
    """分析批量处理结果"""
    try:
        print("\n" + "="*80)
        print("                    批量处理结果分析")
        print("="*80)
        
        if not batch_results:
            print("❌ 没有批量处理结果可分析")
            return
        
        # 统计信息
        total_folders = len(batch_results)
        successful_folders = len([r for r in batch_results.values() if r])
        
        print(f"总处理文件夹数: {total_folders}")
        print(f"成功处理数: {successful_folders}")
        print(f"成功率: {successful_folders/total_folders:.1%}")
        
        # 性能统计
        nse_values = []
        rmse_values = []
        r2_values = []
        processing_times = []
        
        print(f"\n详细结果:")
        print(f"{'文件夹':<15} {'NSE':<8} {'RMSE':<8} {'R²':<8} {'时间(秒)':<10} {'状态'}")
        print("-" * 70)
        
        for folder_name, result in batch_results.items():
            if result:
                nse = result.get('NSE', 0)
                rmse = result.get('RMSE', 0)
                r2 = result.get('R2', 0)
                proc_time = result.get('processing_time', 0)
                
                nse_values.append(nse)
                rmse_values.append(rmse)
                r2_values.append(r2)
                processing_times.append(proc_time)
                
                # 性能等级
                if nse > 0.75:
                    status = "优秀"
                elif nse > 0.65:
                    status = "良好"
                elif nse > 0.5:
                    status = "可接受"
                else:
                    status = "较差"
                
                print(f"{folder_name:<15} {nse:<8.4f} {rmse:<8.4f} {r2:<8.4f} {proc_time:<10.1f} {status}")
            else:
                print(f"{folder_name:<15} {'失败':<8} {'失败':<8} {'失败':<8} {'失败':<10} 失败")
        
        if nse_values:
            print(f"\n性能统计:")
            print(f"  NSE: 平均={sum(nse_values)/len(nse_values):.4f}, 最小={min(nse_values):.4f}, 最大={max(nse_values):.4f}")
            print(f"  RMSE: 平均={sum(rmse_values)/len(rmse_values):.4f}, 最小={min(rmse_values):.4f}, 最大={max(rmse_values):.4f}")
            print(f"  R²: 平均={sum(r2_values)/len(r2_values):.4f}, 最小={min(r2_values):.4f}, 最大={max(r2_values):.4f}")
            print(f"  处理时间: 总计={sum(processing_times):.1f}秒, 平均={sum(processing_times)/len(processing_times):.1f}秒")
            
            # 性能等级统计
            excellent = len([nse for nse in nse_values if nse > 0.75])
            good = len([nse for nse in nse_values if 0.65 < nse <= 0.75])
            acceptable = len([nse for nse in nse_values if 0.5 < nse <= 0.65])
            poor = len([nse for nse in nse_values if nse <= 0.5])
            
            print(f"\n性能等级分布:")
            print(f"  优秀 (NSE>0.75): {excellent} ({excellent/len(nse_values):.1%})")
            print(f"  良好 (0.65<NSE≤0.75): {good} ({good/len(nse_values):.1%})")
            print(f"  可接受 (0.5<NSE≤0.65): {acceptable} ({acceptable/len(nse_values):.1%})")
            print(f"  较差 (NSE≤0.5): {poor} ({poor/len(nse_values):.1%})")
        
        print("="*80)
        
    except Exception as e:
        logger.error(f"结果分析失败: {e}")


def optimize_poor_events(batch_results, config):
    """优化表现较差的事件"""
    try:
        print("\n" + "="*80)
        print("                    优化表现较差的事件")
        print("="*80)
        
        # 找出NSE < 0.75的事件
        poor_events = []
        for folder_name, result in batch_results.items():
            if result and result.get('NSE', 0) < config.nse_threshold:
                poor_events.append((folder_name, result.get('NSE', 0)))
        
        if not poor_events:
            print("✅ 所有事件的NSE都达到了阈值要求，无需优化")
            return
        
        print(f"发现 {len(poor_events)} 个需要优化的事件:")
        for folder_name, nse in poor_events:
            print(f"  {folder_name}: NSE={nse:.4f}")
        
        # 询问是否进行优化
        optimize = input(f"\n是否对这些事件进行参数优化? (y/n) [n]: ").strip().lower()
        
        if optimize == 'y':
            print("开始参数优化...")
            
            for folder_name, original_nse in poor_events:
                try:
                    print(f"\n优化事件: {folder_name}")
                    
                    # 创建该事件的配置
                    event_config = Config(**config.to_dict())
                    event_config.update_paths_for_batch(folder_name)
                    event_config.enable_parameter_optimization = True
                    event_config.optimization_iterations = 10
                    
                    # 运行参数优化
                    optimizer = ParameterOptimizer(event_config)
                    optimization_result = optimizer.optimize_parameters()
                    
                    if optimization_result:
                        optimized_nse = optimization_result.get('score', 0)
                        improvement = optimized_nse - original_nse
                        
                        print(f"  原始NSE: {original_nse:.4f}")
                        print(f"  优化NSE: {optimized_nse:.4f}")
                        print(f"  改进幅度: {improvement:.4f}")
                        
                        if improvement > 0.05:  # 显著改进
                            print(f"  ✅ {folder_name} 优化成功")
                        else:
                            print(f"  ⚠️  {folder_name} 优化效果有限")
                    else:
                        print(f"  ❌ {folder_name} 优化失败")
                        
                except Exception as e:
                    print(f"  ❌ {folder_name} 优化出错: {e}")
                    continue
        else:
            print("跳过参数优化")
        
        print("="*80)
        
    except Exception as e:
        logger.error(f"优化较差事件失败: {e}")


def main():
    """主函数"""
    try:
        print("="*80)
        print("                Kriging插值系统批量处理")
        print("              处理所有洪水场次并生成汇总报告")
        print("="*80)
        
        start_time = time.time()
        
        # 创建配置
        config = Config()
        config.enable_batch_processing = True
        config.num_cores = 24  # 使用24核并行
        config.memory_efficient = True
        config.output_raster = True  # 输出栅格文件
        config.enable_parameter_optimization = False  # 初始不启用优化，加快速度
        
        print(f"配置信息:")
        print(f"  输入根目录: {config.batch_input_root}")
        print(f"  输出根目录: {config.batch_output_root}")
        print(f"  并行核心数: {config.num_cores}")
        print(f"  参数优化: {'启用' if config.enable_parameter_optimization else '禁用'}")
        print("-"*80)
        
        # 执行批量处理
        print("开始批量处理所有洪水事件...")
        batch_processor = BatchProcessor(config)
        batch_results = batch_processor.run_batch_processing()
        
        if not batch_results:
            print("❌ 批量处理失败")
            return
        
        print(f"✅ 批量处理完成，处理了 {len(batch_results)} 个事件")
        
        # 分析结果
        analyze_results(batch_results)
        
        # 优化表现较差的事件
        optimize_poor_events(batch_results, config)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print("="*80)
        print("                    批量处理完成")
        print("="*80)
        print(f"总耗时: {total_time:.1f} 秒 ({total_time/60:.1f} 分钟)")
        print(f"详细结果请查看: {config.batch_output_root}")
        print("="*80)
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        print(f"❌ 程序执行失败: {e}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断程序执行")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        logger.error(f"程序执行出错: {e}")

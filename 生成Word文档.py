#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
将研究方法总结和答辩问题转换为Word文档
"""

import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
import re

def create_word_document():
    """创建Word文档"""
    
    # 创建新文档
    doc = Document()
    
    # 设置文档标题
    title = doc.add_heading('空间降雨插值方法研究总结与论文答辩准备', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 添加作者信息
    author_para = doc.add_paragraph()
    author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    author_run = author_para.add_run('研究生论文研究方法总结')
    author_run.font.size = Pt(14)
    
    # 添加日期
    date_para = doc.add_paragraph()
    date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    date_run = date_para.add_run('2024年')
    date_run.font.size = Pt(12)
    
    # 添加分页符
    doc.add_page_break()
    
    return doc

def add_markdown_content_to_doc(doc, markdown_file, title_prefix=""):
    """将Markdown内容添加到Word文档"""
    
    if not os.path.exists(markdown_file):
        print(f"文件不存在: {markdown_file}")
        return
    
    with open(markdown_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 按行处理Markdown内容
    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # 处理标题
        if line.startswith('#'):
            level = len(line) - len(line.lstrip('#'))
            title_text = line.lstrip('#').strip()
            
            if level == 1:
                doc.add_heading(title_prefix + title_text, level=1)
            elif level == 2:
                doc.add_heading(title_text, level=2)
            elif level == 3:
                doc.add_heading(title_text, level=3)
            else:
                doc.add_heading(title_text, level=4)
                
        # 处理代码块
        elif line.startswith('```'):
            continue  # 跳过代码块标记
            
        # 处理表格（简化处理）
        elif '|' in line and line.count('|') >= 2:
            # 简单的表格处理
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            if cells and not all(cell == '' or '-' in cell for cell in cells):
                para = doc.add_paragraph()
                para.add_run(' | '.join(cells))
                
        # 处理列表项
        elif line.startswith('- ') or line.startswith('* '):
            text = line[2:].strip()
            para = doc.add_paragraph(text, style='List Bullet')
            
        elif re.match(r'^\d+\.', line):
            text = re.sub(r'^\d+\.\s*', '', line)
            para = doc.add_paragraph(text, style='List Number')
            
        # 处理普通段落
        else:
            if line:
                # 处理粗体文本
                if '**' in line:
                    para = doc.add_paragraph()
                    parts = line.split('**')
                    for i, part in enumerate(parts):
                        if i % 2 == 0:
                            para.add_run(part)
                        else:
                            run = para.add_run(part)
                            run.bold = True
                else:
                    doc.add_paragraph(line)

def create_comprehensive_document():
    """创建综合性的Word文档"""
    
    print("正在创建Word文档...")
    
    # 创建文档
    doc = create_word_document()
    
    # 添加目录页
    doc.add_heading('目录', level=1)
    toc_items = [
        "第一部分：研究方法总结",
        "1. 研究背景与目标",
        "2. 研究方法体系",
        "3. 评价指标体系", 
        "4. 技术创新点",
        "5. 应用效果分析",
        "6. 研究结论",
        "",
        "第二部分：论文答辩准备",
        "1. 研究方法相关问题",
        "2. 技术实现相关问题",
        "3. 评价指标相关问题",
        "4. 参数优化相关问题",
        "5. 结果分析相关问题",
        "6. 应用前景相关问题",
        "7. 答辩技巧提醒"
    ]
    
    for item in toc_items:
        if item:
            doc.add_paragraph(item, style='List Number' if item[0].isdigit() else 'Normal')
        else:
            doc.add_paragraph()
    
    doc.add_page_break()
    
    # 添加第一部分：研究方法总结
    doc.add_heading('第一部分：研究方法总结', level=1)
    add_markdown_content_to_doc(doc, '空间降雨插值方法研究总结.md')
    
    doc.add_page_break()
    
    # 添加第二部分：论文答辩准备
    doc.add_heading('第二部分：论文答辩准备', level=1)
    add_markdown_content_to_doc(doc, '论文答辩问题与回答.md')
    
    # 添加附录
    doc.add_page_break()
    doc.add_heading('附录：技术实现要点', level=1)
    
    # 添加技术要点总结
    tech_points = [
        "A. 各插值方法的核心代码结构",
        "B. 关键参数配置说明", 
        "C. 性能优化技术要点",
        "D. 常见问题及解决方案"
    ]
    
    for point in tech_points:
        doc.add_heading(point, level=2)
        doc.add_paragraph("（此部分可根据具体需要补充详细内容）")
    
    # 保存文档
    output_file = "空间降雨插值方法研究总结与答辩准备.docx"
    doc.save(output_file)
    
    print(f"Word文档已生成: {output_file}")
    return output_file

def add_technical_appendix(doc):
    """添加技术附录"""
    
    doc.add_page_break()
    doc.add_heading('技术附录', level=1)
    
    # A. 代码结构总结
    doc.add_heading('A. 各插值方法的代码结构', level=2)
    
    code_structure = """
    各插值方法都采用了模块化设计：
    
    1. 数据处理模块 (data_processing.py)
       - 站点数据加载
       - 降雨数据读取
       - 数据质量检查
       - 缺失值处理
    
    2. 空间结构模块 (delaunay_triangulation.py)
       - Delaunay三角网构建
       - 邻近站点选择
       - 空间关系分析
    
    3. 插值核心模块
       - OI: oi_core.py (最优插值算法)
       - PRISM: prism_core.py (地形权重计算)
       - Kriging: kriging_core.py (变异函数建模)
       - IDW: idw_interpolation.py (距离权重计算)
    
    4. 评价指标模块 (evaluation_metrics.py)
       - 统计指标计算
       - 水文指标计算
       - 结果可视化
    
    5. 并行处理模块 (parallel_processing.py)
       - 多核并行计算
       - 内存优化管理
       - 进度监控
    """
    
    doc.add_paragraph(code_structure)
    
    # B. 关键参数说明
    doc.add_heading('B. 关键参数配置说明', level=2)
    
    param_info = """
    各方法的关键参数及其影响：
    
    OI方法：
    - 协方差函数类型：影响空间相关性建模
    - 相关长度尺度：控制影响范围
    - 观测误差方差：考虑数据不确定性
    
    PRISM方法：
    - 地形权重系数：控制地形影响程度
    - 距离衰减指数：调节距离权重
    - 权重融合比例：平衡不同权重贡献
    
    Kriging方法：
    - 变异函数模型：spherical/exponential/gaussian
    - 块金效应(nugget)：短距离变异性
    - 变程(range)：空间相关距离
    
    IDW方法：
    - 幂次参数：控制距离权重衰减
    - 邻近站点数：影响插值精度
    - 最小距离阈值：避免数值不稳定
    """
    
    doc.add_paragraph(param_info)

if __name__ == "__main__":
    try:
        # 检查依赖包
        try:
            from docx import Document
            print("✅ python-docx 包已安装")
        except ImportError:
            print("❌ 需要安装 python-docx 包")
            print("请运行: pip install python-docx")
            exit(1)
        
        # 创建Word文档
        output_file = create_comprehensive_document()
        
        print("\n" + "="*60)
        print("📄 Word文档生成完成！")
        print(f"📁 文件位置: {os.path.abspath(output_file)}")
        print("="*60)
        
        # 提供使用建议
        print("\n💡 使用建议：")
        print("1. 打开Word文档后，可以进一步调整格式")
        print("2. 添加图表和公式以增强表达效果")
        print("3. 根据具体答辩要求调整内容结构")
        print("4. 建议打印一份纸质版本备用")
        
    except Exception as e:
        print(f"❌ 生成Word文档时出错: {e}")
        print("请检查文件权限和依赖包安装情况")

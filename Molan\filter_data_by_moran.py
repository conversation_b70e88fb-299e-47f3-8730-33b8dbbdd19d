# -*- coding: utf-8 -*-
"""
根据莫兰指数分析结果过滤数据
删除不显著的站点数据，保留显著相关的站点

作者：空间插值系统
日期：2024年
"""

import pandas as pd
import numpy as np
import os
import glob
import shutil
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class MoranDataFilter:
    """基于莫兰指数结果的数据过滤器"""

    def __init__(self, moran_results_file, input_dir, output_dir, filter_method='significance',
                 significance_threshold='*', keep_top_percent=80):
        """
        初始化数据过滤器

        参数:
        moran_results_file: 莫兰指数分析结果文件路径
        input_dir: 原始输入目录路径
        output_dir: 过滤后输出目录路径
        filter_method: 过滤方法 ('significance' 或 'ranking')
        significance_threshold: 显著性阈值 ('***', '**', '*', '.', 'NS')
        keep_top_percent: 当使用ranking方法时，保留的站点百分比
        """
        self.moran_results_file = moran_results_file
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.filter_method = filter_method
        self.significance_threshold = significance_threshold
        self.keep_top_percent = keep_top_percent

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 加载莫兰指数分析结果
        self.load_moran_results()

        # 注意：现在按每个洪水事件分别确定显著站点，不再全局确定
        
    def load_moran_results(self):
        """加载莫兰指数分析结果"""
        print("正在加载莫兰指数分析结果...")
        self.moran_df = pd.read_csv(self.moran_results_file, encoding='utf-8-sig')
        print(f"加载了 {len(self.moran_df)} 条莫兰指数分析记录")
        

    
    def get_significant_stations_for_event(self, flood_event):
        """
        获取特定洪水事件的显著站点列表

        参数:
        flood_event: 洪水事件名称

        返回:
        该洪水事件的显著站点集合
        """
        # 筛选该洪水事件的莫兰指数分析结果
        event_data = self.moran_df[self.moran_df['flood_event'] == flood_event]

        if len(event_data) == 0:
            print(f"  警告: 未找到洪水事件 {flood_event} 的莫兰指数分析结果")
            return set()

        # 收集该事件中所有站点
        all_stations = set()
        non_significant_stations = set()

        for _, row in event_data.iterrows():
            ref_station = row['reference_station'].lower()
            all_stations.add(ref_station)

            # 检查该站点是否有不显著的相关性
            has_non_significant = False

            # 检查前3个最相关的站点
            for i in range(1, 4):
                target_station = row.get(f'top_{i}_station', '')
                significance = row.get(f'top_{i}_significance', 'NS')

                if target_station:
                    all_stations.add(target_station.lower())

                    # 如果该站点对的相关性不显著，标记参考站点
                    if significance == 'NS':
                        has_non_significant = True

            # 如果该站点有不显著的相关性，将其加入不显著站点集合
            if has_non_significant:
                non_significant_stations.add(ref_station)

        # 显著站点 = 所有站点 - 不显著站点
        significant_stations = all_stations - non_significant_stations

        print(f"  该事件总站点数: {len(all_stations)}")
        print(f"  不显著站点数: {len(non_significant_stations)}")
        print(f"  显著站点数: {len(significant_stations)}")

        if non_significant_stations:
            print(f"  不显著站点: {sorted(list(non_significant_stations))}")

        return significant_stations

    def filter_flood_event(self, flood_event):
        """
        过滤单个洪水事件的数据

        参数:
        flood_event: 洪水事件名称

        返回:
        过滤结果统计
        """
        print(f"\n正在过滤洪水事件: {flood_event}")

        # 获取该洪水事件的显著站点
        significant_stations = self.get_significant_stations_for_event(flood_event)

        if len(significant_stations) == 0:
            print(f"  该洪水事件没有显著站点，跳过过滤")
            return None

        # 原始数据目录
        source_dir = os.path.join(self.input_dir, flood_event)
        if not os.path.exists(source_dir):
            print(f"  警告: 源目录不存在 {source_dir}")
            return None

        # 输出数据目录
        target_dir = os.path.join(self.output_dir, flood_event)

        # 如果目录存在，先删除再创建（确保清空）
        if os.path.exists(target_dir):
            shutil.rmtree(target_dir)
        os.makedirs(target_dir, exist_ok=True)

        # 获取所有CSV文件
        csv_files = glob.glob(os.path.join(source_dir, "*.csv"))

        copied_files = 0
        skipped_files = 0

        for csv_file in csv_files:
            # 提取站点ID
            station_id = os.path.basename(csv_file).replace('.csv', '').lower()

            if station_id in significant_stations:
                # 复制显著站点的文件
                target_file = os.path.join(target_dir, os.path.basename(csv_file))
                shutil.copy2(csv_file, target_file)
                copied_files += 1
            else:
                # 跳过不显著的站点
                skipped_files += 1

        print(f"  复制文件: {copied_files} 个")
        print(f"  跳过文件: {skipped_files} 个")

        return {
            'flood_event': flood_event,
            'copied_files': copied_files,
            'skipped_files': skipped_files,
            'total_files': copied_files + skipped_files,
            'significant_stations_count': len(significant_stations)
        }
    
    def run_batch_filter(self):
        """运行批量数据过滤"""
        print("=" * 80)
        print("开始基于莫兰指数的批量数据过滤")
        print("=" * 80)
        print(f"显著性阈值: {self.significance_threshold}")
        print(f"输入目录: {self.input_dir}")
        print(f"输出目录: {self.output_dir}")
        
        # 获取所有洪水事件
        flood_events = []
        for item in os.listdir(self.input_dir):
            item_path = os.path.join(self.input_dir, item)
            if os.path.isdir(item_path) and item != 'rains_每场一表格':
                flood_events.append(item)
        
        flood_events.sort()
        print(f"发现 {len(flood_events)} 个洪水事件")
        
        # 过滤结果统计
        filter_results = []
        
        # 对每个洪水事件进行过滤
        for i, flood_event in enumerate(flood_events, 1):
            print(f"\n进度: {i}/{len(flood_events)}")
            
            try:
                result = self.filter_flood_event(flood_event)
                if result:
                    filter_results.append(result)
            except Exception as e:
                print(f"过滤洪水事件 {flood_event} 时出错: {e}")
                continue
        
        # 保存过滤结果统计
        self.save_filter_results(filter_results)
        
        return filter_results
    
    def save_filter_results(self, filter_results):
        """保存过滤结果统计"""
        print("\n正在保存过滤结果统计...")
        
        # 创建统计DataFrame
        df_results = pd.DataFrame(filter_results)
        
        # 保存详细统计
        stats_file = os.path.join(self.output_dir, '数据过滤统计.csv')
        df_results.to_csv(stats_file, index=False, encoding='utf-8-sig')
        print(f"过滤统计已保存到: {stats_file}")
        
        # 生成汇总报告
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("基于莫兰指数的数据过滤汇总报告")
        report_lines.append("=" * 80)
        report_lines.append(f"过滤时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"显著性阈值: {self.significance_threshold}")
        report_lines.append(f"输入目录: {self.input_dir}")
        report_lines.append(f"输出目录: {self.output_dir}")
        report_lines.append("")
        
        # 站点过滤统计（按事件分别统计）
        report_lines.append("【站点过滤统计】")
        report_lines.append("注意：每个洪水事件的显著站点数量可能不同")
        if filter_results:
            avg_significant = np.mean([r['significant_stations_count'] for r in filter_results if 'significant_stations_count' in r])
            report_lines.append(f"平均显著站点数: {avg_significant:.1f}")
        report_lines.append("")
        
        # 文件过滤统计
        if filter_results:
            total_copied = sum([r['copied_files'] for r in filter_results])
            total_skipped = sum([r['skipped_files'] for r in filter_results])
            total_files = total_copied + total_skipped
            
            report_lines.append("【文件过滤统计】")
            report_lines.append(f"处理洪水事件数: {len(filter_results)}")
            report_lines.append(f"原始文件总数: {total_files}")
            report_lines.append(f"保留文件数: {total_copied}")
            report_lines.append(f"删除文件数: {total_skipped}")
            report_lines.append(f"文件保留比例: {total_copied/total_files*100:.1f}%")
            report_lines.append("")
            
            # 各洪水事件统计
            report_lines.append("【各洪水事件统计】")
            for result in filter_results:
                significant_count = result.get('significant_stations_count', '未知')
                report_lines.append(f"{result['flood_event']}: 保留{result['copied_files']}个, 删除{result['skipped_files']}个 (显著站点数: {significant_count})")

        report_lines.append("")
        report_lines.append("【说明】")
        report_lines.append("每个洪水事件根据其莫兰指数分析结果分别过滤")
        report_lines.append("不同洪水事件的显著站点可能不同")
        report_lines.append("只保留在该洪水事件中显著相关(p<0.1)的站点数据")
        
        # 保存报告
        report_file = os.path.join(self.output_dir, '数据过滤汇总报告.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print(f"过滤汇总报告已保存到: {report_file}")


def main():
    """主函数"""
    print("基于莫兰指数的数据过滤系统")
    print("=" * 50)
    
    # 文件路径配置
    moran_results_file = "../output/molan/莫兰指数分析详细结果.csv"
    input_dir = "../input_another"
    output_dir = "../input_Molan"
    
    # 过滤方法配置
    filter_method = 'significance'  # 'significance' 或 'ranking'

    # 显著性阈值配置（当filter_method='significance'时使用）
    # 这里的逻辑是：删除不显著（'NS'）的站点，保留显著相关的站点
    significance_threshold = '*'  # 保留显著相关的站点，删除不显著的

    # 排名过滤配置（当filter_method='ranking'时使用）
    keep_top_percent = 70  # 保留前70%的站点（基于莫兰指数排名）
    
    try:
        # 检查莫兰指数结果文件是否存在
        if not os.path.exists(moran_results_file):
            print(f"错误：找不到莫兰指数结果文件 {moran_results_file}")
            print("请先运行 batch_molan_analysis.py 生成分析结果")
            return
        
        # 创建数据过滤器
        filter_system = MoranDataFilter(
            moran_results_file,
            input_dir,
            output_dir,
            filter_method,
            significance_threshold,
            keep_top_percent
        )
        
        # 运行批量过滤
        results = filter_system.run_batch_filter()
        
        print("\n" + "=" * 80)
        print("数据过滤完成！")
        print(f"共处理了 {len(results)} 个洪水事件")
        print(f"过滤后的数据已保存到: {output_dir}")
        print(f"过滤方法: {filter_method}")
        if filter_method == 'significance':
            print(f"显著性阈值: {significance_threshold}")
        else:
            print(f"保留比例: {keep_top_percent}%")
        print("详细统计请查看输出目录中的报告文件")
        
    except Exception as e:
        print(f"数据过滤过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

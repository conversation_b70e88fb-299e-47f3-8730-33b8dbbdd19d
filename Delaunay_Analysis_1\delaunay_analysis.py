"""
Delaunay三角剖分分析工具
基于珠江流域实时监测雨量数据融合方法应用研究的Delaunay三角剖分方法
用于分析站点间的空间关系和权重计算

功能：
1. 构建Delaunay三角网
2. 可视化三角网（支持中文站点名称）
3. 分析每个站点的包围站点
4. 计算权重（基于距离的IDW权重）
5. 输出详细的分析结果
6. 集成水晏泰森.xlsx文件中的中文站点名称
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay
from scipy.spatial.distance import cdist
import logging
import os
from typing import Dict, List, Tuple, Optional
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DelaunayAnalyzer:
    """Delaunay三角剖分分析器

    基于珠江流域降雨数据融合研究方法，实现：
    - Delaunay三角网构建
    - 站点包围关系分析
    - 权重计算（IDW方法）
    - 可视化输出（支持中文站点名称）
    """

    def __init__(self, stations_file: str = 'stations.csv', excel_file: str = '水晏泰森.xlsx'):
        """
        初始化分析器

        参数:
            stations_file: 站点文件路径
            excel_file: 包含中文站点名称的Excel文件路径
        """
        self.stations_file = stations_file
        self.excel_file = excel_file
        self.stations_df = None
        self.triangulation = None
        self.points = None
        self.station_indices = {}
        self.analysis_results = {}
        self.station_names = {}  # 站点代码到中文名称的映射
        
    def load_station_names(self) -> Dict[str, str]:
        """
        从Excel文件加载站点名称映射

        返回:
            Dict[str, str]: 站点代码到中文名称的映射
        """
        try:
            if not os.path.exists(self.excel_file):
                logger.warning(f"Excel文件不存在: {self.excel_file}")
                return {}

            logger.info(f"加载站点名称映射: {self.excel_file}")

            # 读取Excel文件
            df = pd.read_excel(self.excel_file)

            # 检查必要列
            if 'PSTCD' not in df.columns or 'NAME' not in df.columns:
                logger.warning("Excel文件缺少PSTCD或NAME列")
                return {}

            # 创建站点代码到名称的映射（处理大小写问题）
            station_names = {}
            for _, row in df.iterrows():
                station_code = str(row['PSTCD']).strip()
                station_name = str(row['NAME']).strip()
                if pd.notna(station_code) and pd.notna(station_name):
                    # 同时存储大写和小写版本以处理大小写不匹配问题
                    station_names[station_code] = station_name
                    station_names[station_code.lower()] = station_name
                    station_names[station_code.upper()] = station_name

            logger.info(f"加载了{len(station_names)}个站点的名称映射")

            # 显示一些示例
            sample_items = list(station_names.items())[:5]
            for code, name in sample_items:
                logger.info(f"  {code} -> {name}")

            self.station_names = station_names
            return station_names

        except Exception as e:
            logger.error(f"加载站点名称映射失败: {e}")
            return {}

    def get_station_display_name(self, station_code: str) -> str:
        """
        获取站点显示名称

        参数:
            station_code: 站点代码

        返回:
            str: 站点名称，如果没有找到则返回原代码
        """
        return self.station_names.get(str(station_code), str(station_code))

    def load_stations(self) -> pd.DataFrame:
        """加载站点数据"""
        try:
            logger.info(f"加载站点数据: {self.stations_file}")

            # 读取站点数据
            self.stations_df = pd.read_csv(self.stations_file, encoding='utf-8')

            # 检查必要的列
            required_columns = ['站点', '经度', '纬度']
            for col in required_columns:
                if col not in self.stations_df.columns:
                    raise ValueError(f"站点文件缺少必要列: {col}")

            # 移除空行
            self.stations_df = self.stations_df.dropna(subset=['站点', '经度', '纬度'])

            # 加载站点名称映射
            self.load_station_names()

            logger.info(f"成功加载 {len(self.stations_df)} 个站点")

            # 显示站点信息（使用中文名称）
            logger.info("站点信息预览:")
            for idx, row in self.stations_df.head().iterrows():
                display_name = self.get_station_display_name(row['站点'])
                logger.info(f"  {row['站点']} ({display_name}): ({row['经度']:.6f}, {row['纬度']:.6f})")

            return self.stations_df

        except Exception as e:
            logger.error(f"加载站点数据失败: {e}")
            raise
    
    def build_triangulation(self) -> Delaunay:
        """构建Delaunay三角网"""
        try:
            if self.stations_df is None:
                self.load_stations()
            
            logger.info("构建Delaunay三角网...")
            
            # 提取站点坐标
            self.points = self.stations_df[['经度', '纬度']].values
            station_names = self.stations_df['站点'].values
            
            # 创建站点索引映射
            self.station_indices = {name: idx for idx, name in enumerate(station_names)}
            
            # 构建Delaunay三角网
            self.triangulation = Delaunay(self.points)
            
            # 验证三角网质量
            self._validate_triangulation()
            
            logger.info(f"成功构建Delaunay三角网:")
            logger.info(f"  站点数量: {len(self.points)}")
            logger.info(f"  三角形数量: {len(self.triangulation.simplices)}")
            
            return self.triangulation
            
        except Exception as e:
            logger.error(f"构建Delaunay三角网失败: {e}")
            raise
    
    def _validate_triangulation(self):
        """验证三角网质量"""
        try:
            # 计算三角形角度统计
            angles = []
            
            for simplex in self.triangulation.simplices:
                triangle_points = self.points[simplex]
                triangle_angles = self._calculate_triangle_angles(triangle_points)
                angles.extend(triangle_angles)
            
            angles = np.array(angles)
            
            logger.info("三角网质量统计:")
            logger.info(f"  最小角度: {np.min(angles):.2f}°")
            logger.info(f"  最大角度: {np.max(angles):.2f}°")
            logger.info(f"  平均角度: {np.mean(angles):.2f}°")
            logger.info(f"  角度标准差: {np.std(angles):.2f}°")
            
            # 检查是否有过小的角度
            small_angles = angles[angles < 10]
            if len(small_angles) > 0:
                logger.warning(f"存在 {len(small_angles)} 个小于10°的角度，可能影响插值质量")
            
        except Exception as e:
            logger.warning(f"三角网质量验证失败: {e}")
    
    def _calculate_triangle_angles(self, triangle_points: np.ndarray) -> List[float]:
        """计算三角形的三个角度"""
        try:
            p1, p2, p3 = triangle_points
            
            # 计算边长
            a = np.linalg.norm(p2 - p3)  # 对边a
            b = np.linalg.norm(p1 - p3)  # 对边b  
            c = np.linalg.norm(p1 - p2)  # 对边c
            
            # 使用余弦定理计算角度
            angle_A = np.arccos(np.clip((b**2 + c**2 - a**2) / (2*b*c), -1, 1))
            angle_B = np.arccos(np.clip((a**2 + c**2 - b**2) / (2*a*c), -1, 1))
            angle_C = np.arccos(np.clip((a**2 + b**2 - c**2) / (2*a*b), -1, 1))
            
            # 转换为度
            angles = [np.degrees(angle_A), np.degrees(angle_B), np.degrees(angle_C)]
            
            return angles
            
        except Exception as e:
            logger.warning(f"计算三角形角度失败: {e}")
            return [60.0, 60.0, 60.0]  # 返回默认值

    def analyze_station_surroundings(self, power: float = 2.0) -> Dict:
        """
        分析每个站点的包围关系和权重

        参数:
            power: IDW权重指数，默认为2.0

        返回:
            Dict: 包含每个站点分析结果的字典
        """
        try:
            if self.triangulation is None:
                self.build_triangulation()

            logger.info("开始分析站点包围关系...")

            self.analysis_results = {}
            stations = self.stations_df['站点'].values

            for i, target_station in enumerate(stations):
                logger.info(f"分析站点 {target_station} ({i+1}/{len(stations)})")

                # 获取目标站点坐标
                target_point = self.points[i]

                # 创建不包含目标站点的站点列表（留一法）
                remaining_indices = [j for j in range(len(stations)) if j != i]
                remaining_coords = self.points[remaining_indices]
                remaining_stations = stations[remaining_indices]

                if len(remaining_coords) < 3:
                    logger.warning(f"站点 {target_station} 周围站点不足3个，跳过分析")
                    continue

                # 找到包含目标站点的三角形
                triangle_info = self._find_containing_triangle(
                    target_point, remaining_coords, remaining_stations, power
                )

                if triangle_info:
                    self.analysis_results[target_station] = triangle_info

                    # 输出分析结果
                    logger.info(f"  包围站点: {triangle_info['surrounding_stations']}")
                    logger.info(f"  权重: {[f'{w:.4f}' for w in triangle_info['weights']]}")
                    logger.info(f"  距离: {[f'{d:.4f}' for d in triangle_info['distances']]}")

            logger.info(f"站点包围关系分析完成，成功分析 {len(self.analysis_results)} 个站点")

            return self.analysis_results

        except Exception as e:
            logger.error(f"分析站点包围关系失败: {e}")
            raise

    def _find_containing_triangle(self, target_point: np.ndarray,
                                remaining_coords: np.ndarray,
                                remaining_stations: np.ndarray,
                                power: float) -> Dict:
        """
        找到包含目标点的三角形并计算权重

        参数:
            target_point: 目标点坐标
            remaining_coords: 剩余站点坐标
            remaining_stations: 剩余站点名称
            power: IDW权重指数

        返回:
            Dict: 包含三角形信息和权重的字典
        """
        try:
            # 构建剩余站点的三角网
            tri = Delaunay(remaining_coords)

            # 找到包含目标点的三角形
            simplex_index = tri.find_simplex(target_point)

            if simplex_index == -1:
                # 目标点不在任何三角形内，找最近的三角形
                logger.debug(f"目标点不在三角网内，寻找最近三角形")
                simplex_index = self._find_nearest_triangle(target_point, tri, remaining_coords)

            # 获取三角形顶点
            triangle_vertices = tri.simplices[simplex_index]
            vertex_coords = remaining_coords[triangle_vertices]
            vertex_stations = remaining_stations[triangle_vertices]

            # 计算权重
            weights_info = self._calculate_idw_weights(target_point, vertex_coords, power)

            return {
                'target_station_coords': target_point.tolist(),
                'surrounding_stations': vertex_stations.tolist(),
                'surrounding_coords': vertex_coords.tolist(),
                'triangle_index': int(simplex_index),
                'distances': weights_info['distances'].tolist(),
                'weights': weights_info['weights'].tolist(),
                'power': power
            }

        except Exception as e:
            logger.error(f"寻找包含三角形失败: {e}")
            # 使用备选方案：最近的3个站点
            return self._fallback_nearest_stations(target_point, remaining_coords, remaining_stations, power)

    def _find_nearest_triangle(self, target_point: np.ndarray,
                             tri: Delaunay, coords: np.ndarray) -> int:
        """找到距离目标点最近的三角形"""
        min_distance = float('inf')
        nearest_triangle = 0

        for i, simplex in enumerate(tri.simplices):
            # 计算目标点到三角形重心的距离
            triangle_coords = coords[simplex]
            centroid = np.mean(triangle_coords, axis=0)
            dist = np.linalg.norm(target_point - centroid)

            if dist < min_distance:
                min_distance = dist
                nearest_triangle = i

        return nearest_triangle

    def _calculate_idw_weights(self, target_point: np.ndarray,
                             vertex_coords: np.ndarray, power: float) -> Dict:
        """
        计算IDW权重

        参数:
            target_point: 目标点坐标
            vertex_coords: 顶点坐标
            power: IDW权重指数

        返回:
            Dict: 包含距离和权重的字典
        """
        # 计算距离
        distances = cdist([target_point], vertex_coords)[0]

        # 避免除零错误
        min_distance = 1e-6
        distances = np.maximum(distances, min_distance)

        # IDW权重计算
        weights = 1.0 / (distances ** power)

        # 检查权重是否合理
        if np.any(np.isnan(weights)) or np.any(np.isinf(weights)):
            logger.warning(f"权重计算异常，使用等权重。距离: {distances}")
            weights = np.ones(len(distances)) / len(distances)
        else:
            # 归一化权重
            weight_sum = np.sum(weights)
            if weight_sum > 0:
                weights = weights / weight_sum
            else:
                weights = np.ones(len(distances)) / len(distances)

        return {
            'distances': distances,
            'weights': weights
        }

    def _fallback_nearest_stations(self, target_point: np.ndarray,
                                 coords: np.ndarray, stations: np.ndarray,
                                 power: float) -> Dict:
        """备选方案：使用最近的3个站点"""
        logger.warning("使用备选方案：最近的3个站点")

        # 计算所有站点到目标点的距离
        distances = cdist([target_point], coords)[0]

        # 找到最近的3个站点
        nearest_indices = np.argsort(distances)[:3]

        vertex_coords = coords[nearest_indices]
        vertex_stations = stations[nearest_indices]

        # 计算权重
        weights_info = self._calculate_idw_weights(target_point, vertex_coords, power)

        return {
            'target_station_coords': target_point.tolist(),
            'surrounding_stations': vertex_stations.tolist(),
            'surrounding_coords': vertex_coords.tolist(),
            'triangle_index': -1,  # 表示这是备选方案
            'distances': weights_info['distances'].tolist(),
            'weights': weights_info['weights'].tolist(),
            'power': power
        }

    def plot_delaunay_triangulation(self, output_dir: str = "output/Delaunay",
                                   show_station_names: bool = True,
                                   highlight_station: str = None) -> str:
        """
        绘制Delaunay三角网图（支持中文站点名称）

        参数:
            output_dir: 输出目录路径
            show_station_names: 是否显示站点名称
            highlight_station: 高亮显示的站点

        返回:
            str: 保存的文件路径
        """
        try:
            if self.triangulation is None:
                self.build_triangulation()

            logger.info("绘制Delaunay三角网图...")

            # 创建图形
            plt.figure(figsize=(18, 14))

            # 绘制三角网
            plt.triplot(self.points[:, 0], self.points[:, 1],
                       self.triangulation.simplices, 'b-', alpha=0.4, linewidth=0.8)

            # 绘制站点
            plt.scatter(self.points[:, 0], self.points[:, 1],
                       c='red', s=120, alpha=0.8, zorder=5,
                       edgecolors='black', linewidth=1.5, label='Rainfall Stations')

            # 添加站点标签（使用中文名称）
            if show_station_names:
                for idx, row in self.stations_df.iterrows():
                    # 获取中文站点名称
                    display_name = self.get_station_display_name(row['站点'])

                    # 智能调整标签位置以避免重叠
                    offset_x = 0.008 if idx % 2 == 0 else -0.008
                    offset_y = 0.008 if idx % 3 == 0 else -0.008

                    # 根据站点位置进一步调整偏移
                    if row['经度'] > 110.6:  # 右侧站点
                        offset_x = -0.012
                    elif row['经度'] < 110.4:  # 左侧站点
                        offset_x = 0.012

                    if row['纬度'] > 24.2:  # 上方站点
                        offset_y = -0.012
                    elif row['纬度'] < 23.9:  # 下方站点
                        offset_y = 0.012

                    plt.annotate(display_name,
                               (row['经度'], row['纬度']),
                               xytext=(row['经度'] + offset_x, row['纬度'] + offset_y),
                               fontsize=10, alpha=0.9, weight='bold',
                               bbox=dict(boxstyle='round,pad=0.3',
                                       facecolor='white', alpha=0.9,
                                       edgecolor='gray', linewidth=0.5),
                               ha='center', va='center')

            # 高亮特定站点
            if highlight_station and highlight_station in self.station_indices:
                idx = self.station_indices[highlight_station]
                point = self.points[idx]
                highlight_name = self.get_station_display_name(highlight_station)

                plt.scatter(point[0], point[1], c='yellow', s=200,
                          marker='*', edgecolors='black', linewidth=2,
                          zorder=6, label=f'Highlighted Station: {highlight_name}')

                # 如果有分析结果，显示包围关系
                if highlight_station in self.analysis_results:
                    result = self.analysis_results[highlight_station]
                    surrounding_coords = np.array(result['surrounding_coords'])

                    # 绘制包围站点
                    plt.scatter(surrounding_coords[:, 0], surrounding_coords[:, 1],
                              c='green', s=150, marker='s', alpha=0.7,
                              edgecolors='black', linewidth=1.5,
                              zorder=6, label='Surrounding Stations')

                    # 绘制连接线
                    for coord in surrounding_coords:
                        plt.plot([point[0], coord[0]], [point[1], coord[1]],
                               'g--', alpha=0.6, linewidth=2)

            # 设置图形属性（英文标签）
            plt.xlabel('Longitude (°)', fontsize=14, weight='bold')
            plt.ylabel('Latitude (°)', fontsize=14, weight='bold')
            plt.title('Delaunay Triangulation of Rainfall Stations in Pearl River Basin',
                     fontsize=16, weight='bold', pad=20)
            plt.grid(True, alpha=0.3)
            plt.legend(fontsize=12, loc='best')

            # 调整布局
            plt.tight_layout()

            # 保存图形
            output_path = os.path.join(output_dir, 'delaunay_triangulation_with_chinese_names.png')
            os.makedirs(output_dir, exist_ok=True)
            plt.savefig(output_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info(f"Delaunay三角网图已保存: {output_path}")

            return output_path

        except Exception as e:
            logger.error(f"绘制Delaunay三角网图失败: {e}")
            return ""

    def save_analysis_results(self, output_dir: str = "output/Delaunay") -> str:
        """
        保存分析结果到CSV文件（包含中文站点名称）

        参数:
            output_dir: 输出目录路径

        返回:
            str: 保存的文件路径
        """
        try:
            if not self.analysis_results:
                logger.warning("没有分析结果可保存，请先执行analyze_station_surroundings()")
                return ""

            logger.info("保存Delaunay分析结果...")

            # 准备数据
            results_data = []

            for target_station, result in self.analysis_results.items():
                # 获取目标站点信息
                target_coords = result['target_station_coords']
                surrounding_stations = result['surrounding_stations']
                distances = result['distances']
                weights = result['weights']

                # 获取中文名称
                target_name = self.get_station_display_name(target_station)

                # 为每个包围站点创建一行记录
                for i, (surr_station, distance, weight) in enumerate(zip(surrounding_stations, distances, weights)):
                    surr_name = self.get_station_display_name(surr_station)
                    results_data.append({
                        '验证站点代码': target_station,
                        '验证站点名称': target_name,
                        '验证站点经度': target_coords[0],
                        '验证站点纬度': target_coords[1],
                        '包围站点代码': surr_station,
                        '包围站点名称': surr_name,
                        '包围站点序号': i + 1,
                        '距离': distance,
                        '权重': weight,
                        'IDW指数': result['power'],
                        '三角形索引': result['triangle_index']
                    })

            # 创建DataFrame并保存
            df = pd.DataFrame(results_data)

            # 保存详细结果
            output_path = os.path.join(output_dir, 'delaunay_analysis_results_with_names.csv')
            os.makedirs(output_dir, exist_ok=True)
            df.to_csv(output_path, index=False, encoding='utf-8-sig')

            # 保存汇总结果
            summary_data = []
            for target_station, result in self.analysis_results.items():
                target_coords = result['target_station_coords']
                surrounding_stations = result['surrounding_stations']
                weights = result['weights']
                distances = result['distances']

                # 获取中文名称
                target_name = self.get_station_display_name(target_station)

                summary_data.append({
                    '验证站点代码': target_station,
                    '验证站点名称': target_name,
                    '验证站点经度': target_coords[0],
                    '验证站点纬度': target_coords[1],
                    '包围站点1代码': surrounding_stations[0] if len(surrounding_stations) > 0 else '',
                    '包围站点1名称': self.get_station_display_name(surrounding_stations[0]) if len(surrounding_stations) > 0 else '',
                    '包围站点2代码': surrounding_stations[1] if len(surrounding_stations) > 1 else '',
                    '包围站点2名称': self.get_station_display_name(surrounding_stations[1]) if len(surrounding_stations) > 1 else '',
                    '包围站点3代码': surrounding_stations[2] if len(surrounding_stations) > 2 else '',
                    '包围站点3名称': self.get_station_display_name(surrounding_stations[2]) if len(surrounding_stations) > 2 else '',
                    '权重1': weights[0] if len(weights) > 0 else 0,
                    '权重2': weights[1] if len(weights) > 1 else 0,
                    '权重3': weights[2] if len(weights) > 2 else 0,
                    '距离1': distances[0] if len(distances) > 0 else 0,
                    '距离2': distances[1] if len(distances) > 1 else 0,
                    '距离3': distances[2] if len(distances) > 2 else 0,
                    'IDW指数': result['power'],
                    '三角形索引': result['triangle_index']
                })

            summary_df = pd.DataFrame(summary_data)
            summary_path = os.path.join(output_dir, 'delaunay_analysis_summary_with_names.csv')
            summary_df.to_csv(summary_path, index=False, encoding='utf-8-sig')

            logger.info(f"详细分析结果已保存: {output_path}")
            logger.info(f"汇总分析结果已保存: {summary_path}")
            logger.info(f"共保存 {len(self.analysis_results)} 个站点的分析结果")

            return output_path

        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
            return ""

    def generate_report(self, output_dir: str = "output/Delaunay") -> str:
        """
        生成分析报告（包含中文站点名称）

        参数:
            output_dir: 输出目录路径

        返回:
            str: 报告文件路径
        """
        try:
            if not self.analysis_results:
                logger.warning("没有分析结果可生成报告，请先执行analyze_station_surroundings()")
                return ""

            logger.info("生成Delaunay分析报告...")

            report_lines = []
            report_lines.append("# 珠江流域雨量站点Delaunay三角剖分分析报告")
            report_lines.append("")
            report_lines.append("## 1. 基本信息")
            report_lines.append(f"- 分析站点数量: {len(self.stations_df)}")
            report_lines.append(f"- 三角形数量: {len(self.triangulation.simplices)}")
            report_lines.append(f"- 成功分析站点数量: {len(self.analysis_results)}")
            report_lines.append(f"- 站点名称映射数量: {len(self.station_names)}")
            report_lines.append("")

            report_lines.append("## 2. 站点包围关系分析")
            report_lines.append("")

            for target_station, result in self.analysis_results.items():
                target_name = self.get_station_display_name(target_station)
                report_lines.append(f"### 站点: {target_station} ({target_name})")
                report_lines.append(f"- 坐标: ({result['target_station_coords'][0]:.6f}, {result['target_station_coords'][1]:.6f})")

                # 包围站点信息（包含中文名称）
                surrounding_info = []
                for station in result['surrounding_stations']:
                    station_name = self.get_station_display_name(station)
                    surrounding_info.append(f"{station} ({station_name})")
                report_lines.append(f"- 包围站点: {', '.join(surrounding_info)}")

                report_lines.append("- 权重分配:")
                for i, (station, weight, distance) in enumerate(zip(result['surrounding_stations'],
                                                                   result['weights'],
                                                                   result['distances'])):
                    station_name = self.get_station_display_name(station)
                    report_lines.append(f"  - {station} ({station_name}): 权重={weight:.4f}, 距离={distance:.4f}")

                report_lines.append(f"- 三角形索引: {result['triangle_index']}")
                report_lines.append("")

            # 添加站点名称映射表
            if self.station_names:
                report_lines.append("## 3. 站点名称映射表")
                report_lines.append("")
                report_lines.append("| 站点代码 | 中文名称 |")
                report_lines.append("|---------|---------|")
                for code, name in sorted(self.station_names.items()):
                    report_lines.append(f"| {code} | {name} |")
                report_lines.append("")

            # 保存报告
            report_path = os.path.join(output_dir, 'delaunay_analysis_report_with_names.md')
            os.makedirs(output_dir, exist_ok=True)

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))

            logger.info(f"分析报告已保存: {report_path}")

            return report_path

        except Exception as e:
            logger.error(f"生成分析报告失败: {e}")
            return ""


def main():
    """主程序"""
    try:
        logger.info("开始Delaunay三角剖分分析...")

        # 创建分析器（包含Excel文件路径）
        analyzer = DelaunayAnalyzer('../stations.csv', '../水晏泰森.xlsx')

        # 加载站点数据
        stations_df = analyzer.load_stations()

        # 构建三角网
        triangulation = analyzer.build_triangulation()

        # 分析站点包围关系
        analysis_results = analyzer.analyze_station_surroundings(power=2.0)

        # 绘制三角网图（支持中文名称）
        plot_path = analyzer.plot_delaunay_triangulation(
            output_dir="output/Delaunay",
            show_station_names=True
        )

        # 保存分析结果（包含中文名称）
        results_path = analyzer.save_analysis_results(output_dir="output/Delaunay")

        # 生成报告（包含中文名称）
        report_path = analyzer.generate_report(output_dir="output/Delaunay")

        logger.info("Delaunay三角剖分分析完成！")
        logger.info("输出文件:")
        logger.info(f"  - 三角网图: {plot_path}")
        logger.info(f"  - 分析结果: {results_path}")
        logger.info(f"  - 分析报告: {report_path}")

        # 显示部分结果（包含中文名称）
        logger.info("\n=== 部分分析结果预览 ===")
        count = 0
        for station, result in analysis_results.items():
            if count >= 5:  # 只显示前5个
                break
            station_name = analyzer.get_station_display_name(station)
            logger.info(f"\n站点 {station} ({station_name}):")

            # 显示包围站点（包含中文名称）
            surrounding_info = []
            for surr_station in result['surrounding_stations']:
                surr_name = analyzer.get_station_display_name(surr_station)
                surrounding_info.append(f"{surr_station} ({surr_name})")
            logger.info(f"  包围站点: {surrounding_info}")
            logger.info(f"  权重: {[f'{w:.4f}' for w in result['weights']]}")
            count += 1

        if len(analysis_results) > 5:
            logger.info(f"\n... 还有 {len(analysis_results) - 5} 个站点的结果，详见输出文件")

        # 显示站点名称映射统计
        logger.info(f"\n=== 站点名称映射统计 ===")
        logger.info(f"总站点数: {len(stations_df)}")
        logger.info(f"有中文名称的站点数: {len(analyzer.station_names)}")
        logger.info(f"映射覆盖率: {len(analyzer.station_names)/len(stations_df)*100:.1f}%")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise


if __name__ == "__main__":
    main()

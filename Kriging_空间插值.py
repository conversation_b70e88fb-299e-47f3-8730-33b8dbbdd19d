# -*- coding: utf-8 -*-
"""
Kriging空间插值程序

本程序实现了基于Kriging方法的点雨量到面雨量的空间插值，
使用半变异函数模型捕捉空间相关性，并应用留一法进行验证。

Author: 水文学教授（基于OI方法改进）
Date: 2023-06-05
"""

# 导入必要的库
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay
from scipy.interpolate import LinearNDInterpolator
import multiprocessing as mp
from datetime import datetime
import time
import re
import logging
import glob
import argparse
from tqdm import tqdm
import sys
import traceback
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.ticker import MaxNLocator
import matplotlib.colors as mcolors
from pykrige.ok import OrdinaryKriging
from pykrige.uk import UniversalKriging
from functools import partial
import random

# 设置pandas显示选项，防止警告
pd.set_option('mode.chained_assignment', None)

# 设置随机种子，确保结果可重现
np.random.seed(42)

# 设置默认参数 - 根据您的文件夹结构
DEFAULT_INPUT_DIR = "D:/pythondata/spatial_interpolation/input_another/2015-3"
DEFAULT_OUTPUT_DIR = "D:/pythondata/spatial_interpolation/output/Kriging/2015-3"
DEFAULT_STATIONS_FILE = "D:/pythondata/spatial_interpolation/stations.csv"
DEFAULT_MASK_FILE = "D:/pythondata/spatial_interpolation/terrain/90/mask.asc"

def setup_logging(output_dir):
    """
    配置日志系统
    
    参数:
    output_dir: 输出目录
    
    返回:
    str: 日志文件路径
    """
    # 创建日志目录
    log_dir = os.path.join(output_dir, 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 生成日志文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(log_dir, f'interpolation_{timestamp}.log')
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info('日志系统已配置')
    return log_file

def read_stations(stations_file):
    """
    读取站点信息
    
    参数:
    stations_file: 站点文件路径
    
    返回:
    DataFrame: 包含站点信息的DataFrame
    """
    try:
        # 读取站点信息
        stations = pd.read_csv(stations_file, encoding='utf-8')
        
        # 确保必要的列存在
        required_columns = ['站点', '经度', '纬度']
        for col in required_columns:
            if col not in stations.columns:
                raise ValueError(f"站点文件缺少必要的列: {col}")
        
        logging.info(f"成功读取站点信息，共 {len(stations)} 个站点")
        return stations
    
    except Exception as e:
        logging.error(f"读取站点信息时出错: {e}")
        raise

def read_rainfall_data(input_dir, station_ids):
    """
    读取降雨数据
    
    参数:
    input_dir: 输入数据目录
    station_ids: 站点ID列表
    
    返回:
    DataFrame: 包含降雨数据的DataFrame，索引为时间，列为站点ID
    """
    try:
        # 查找所有CSV文件
        csv_files = glob.glob(os.path.join(input_dir, '*.csv'))
        
        if not csv_files:
            raise ValueError(f"在目录 {input_dir} 中没有找到CSV文件")
        
        # 读取所有CSV文件
        all_data = []
        
        for file in csv_files:
            try:
                # 从文件名中提取站点ID
                file_name = os.path.basename(file)
                station_id = os.path.splitext(file_name)[0]
                
                # 如果站点ID不在列表中，跳过
                if station_id not in station_ids:
                    continue
                
                # 读取CSV文件
                df = pd.read_csv(file)
                
                # 确保必要的列存在
                if '时间' not in df.columns or '雨量' not in df.columns:
                    logging.warning(f"文件 {file} 缺少必要的列: 时间 或 雨量")
                    continue
                
                # 重命名列
                df = df.rename(columns={'时间': 'time', '雨量': station_id})
                
                # 只保留需要的列
                df = df[['time', station_id]]
                
                # 将时间列转换为datetime
                df['time'] = pd.to_datetime(df['time'])
                
                # 添加到列表
                all_data.append(df)
            
            except Exception as e:
                logging.warning(f"读取文件 {file} 时出错: {e}")
        
        if not all_data:
            raise ValueError("没有读取到任何降雨数据")
        
        # 合并所有数据
        rainfall_data = all_data[0]
        for df in all_data[1:]:
            rainfall_data = pd.merge(rainfall_data, df, on='time', how='outer')
        
        # 将时间列设为索引
        rainfall_data = rainfall_data.set_index('time')
        
        # 按时间排序
        rainfall_data = rainfall_data.sort_index()
        
        logging.info(f"成功读取降雨数据，共 {len(rainfall_data)} 个时间点，{len(rainfall_data.columns)} 个站点")
        return rainfall_data
    
    except Exception as e:
        logging.error(f"读取降雨数据时出错: {e}")
        raise

def read_mask(mask_file):
    """
    读取掩膜文件
    
    参数:
    mask_file: 掩膜文件路径
    
    返回:
    tuple: (掩膜数组, 头信息)
    """
    try:
        # 读取ASC文件
        with open(mask_file, 'r') as f:
            lines = f.readlines()
        
        # 解析头信息
        header = {}
        data_start_line = 0
        
        for i, line in enumerate(lines):
            if line.strip().startswith('ncols'):
                header['ncols'] = float(line.strip().split()[1])
                data_start_line = i + 6
            elif line.strip().startswith('nrows'):
                header['nrows'] = float(line.strip().split()[1])
            elif line.strip().startswith('xllcorner'):
                header['xllcorner'] = float(line.strip().split()[1])
            elif line.strip().startswith('yllcorner'):
                header['yllcorner'] = float(line.strip().split()[1])
            elif line.strip().startswith('cellsize'):
                header['cellsize'] = float(line.strip().split()[1])
            elif line.strip().startswith('NODATA_value'):
                header['nodata_value'] = float(line.strip().split()[1])
        
        # 读取数据
        nrows = int(header['nrows'])
        ncols = int(header['ncols'])
        mask_array = np.zeros((nrows, ncols))
        
        for i in range(nrows):
            row_data = lines[data_start_line + i].strip().split()
            for j in range(ncols):
                mask_array[i, j] = float(row_data[j])
        
        logging.info(f"成功读取掩膜文件，大小: {nrows} x {ncols}")
        return mask_array, header
    
    except Exception as e:
        logging.error(f"读取掩膜文件时出错: {e}")
        raise

def create_delaunay_triangulation(stations):
    """
    创建Delaunay三角网
    
    参数:
    stations: 包含站点经纬度的DataFrame
    
    返回:
    Delaunay: Delaunay三角网对象
    """
    try:
        # 提取站点坐标
        points = stations[['经度', '纬度']].values
        
        # 创建Delaunay三角网
        tri = Delaunay(points)
        
        logging.info(f"成功创建Delaunay三角网，包含 {len(tri.simplices)} 个三角形")
        return tri
    
    except Exception as e:
        logging.error(f"创建Delaunay三角网时出错: {e}")
        raise

def fit_variogram_model(stations, rainfall_data, time_index, model='spherical'):
    """
    拟合半变异函数模型
    
    参数:
    stations: 包含站点经纬度的DataFrame
    rainfall_data: 降雨数据DataFrame
    time_index: 时间索引
    model: 半变异函数模型类型，可选 'spherical', 'exponential', 'gaussian', 'linear'
    
    返回:
    dict: 半变异函数模型参数
    """
    try:
        # 提取当前时间步的降雨数据
        current_rainfall = rainfall_data.loc[time_index]
        
        # 提取有效的站点数据
        valid_points = []
        valid_values = []
        
        for i, row in stations.iterrows():
            station_id = row['站点']
            if station_id in current_rainfall and not np.isnan(current_rainfall[station_id]):
                valid_points.append([row['经度'], row['纬度']])
                valid_values.append(current_rainfall[station_id])
        
        # 如果有效站点数量太少，返回默认参数
        if len(valid_points) < 5:
            logging.warning(f"时间步 {time_index} 的有效站点数量太少，使用默认半变异函数参数")
            return {'sill': 1.0, 'range': 50.0, 'nugget': 0.1, 'model': model}
        
        # 计算站点间距离矩阵
        n = len(valid_points)
        distances = np.zeros((n, n))
        for i in range(n):
            for j in range(i+1, n):
                dist = np.sqrt((valid_points[i][0] - valid_points[j][0])**2 + 
                              (valid_points[i][1] - valid_points[j][1])**2)
                distances[i, j] = distances[j, i] = dist
        
        # 计算站点间半变异函数值
        values = np.array(valid_values)
        variogram = np.zeros((n, n))
        for i in range(n):
            for j in range(i+1, n):
                variogram[i, j] = variogram[j, i] = 0.5 * (values[i] - values[j])**2
        
        # 将距离和半变异函数值展平为一维数组
        dist_flat = distances[np.triu_indices(n, k=1)]
        vario_flat = variogram[np.triu_indices(n, k=1)]
        
        # 按距离排序
        sort_idx = np.argsort(dist_flat)
        dist_sorted = dist_flat[sort_idx]
        vario_sorted = vario_flat[sort_idx]
        
        # 分组计算平均半变异函数值
        max_dist = np.max(dist_sorted)
        bin_width = max_dist / 10
        bins = np.arange(0, max_dist + bin_width, bin_width)
        bin_centers = (bins[:-1] + bins[1:]) / 2
        bin_means = np.zeros(len(bin_centers))
        
        for i in range(len(bin_centers)):
            mask = (dist_sorted >= bins[i]) & (dist_sorted < bins[i+1])
            if np.any(mask):
                bin_means[i] = np.mean(vario_sorted[mask])
            else:
                bin_means[i] = np.nan
        
        # 移除NaN值
        valid_mask = ~np.isnan(bin_means)
        bin_centers = bin_centers[valid_mask]
        bin_means = bin_means[valid_mask]
        
        # 如果没有足够的有效点，返回默认参数
        if len(bin_centers) < 3:
            logging.warning(f"时间步 {time_index} 的有效距离分组太少，使用默认半变异函数参数")
            return {'sill': 1.0, 'range': 50.0, 'nugget': 0.1, 'model': model}
        
        # 估计初始参数
        nugget = np.min(bin_means) * 0.1  # 块金值，取最小半变异函数值的10%
        sill = np.max(bin_means) - nugget  # 基台值，取最大半变异函数值减去块金值
        range_param = bin_centers[np.argmax(bin_means >= 0.95 * (sill + nugget))]  # 变程，取半变异函数值达到基台值95%的距离
        
        # 如果变程估计失败，使用最大距离的一半
        if np.isnan(range_param) or range_param <= 0:
            range_param = max_dist / 2
        
        # 返回拟合的参数
        return {'sill': sill, 'range': range_param, 'nugget': nugget, 'model': model}
    
    except Exception as e:
        logging.error(f"拟合半变异函数模型时出错: {e}")
        # 出错时返回默认参数
        return {'sill': 1.0, 'range': 50.0, 'nugget': 0.1, 'model': model}

def kriging_interpolation(target_point, observed_points, observed_values, variogram_params):
    """
    使用Kriging方法计算目标点的降雨量
    
    参数:
    target_point: 目标点坐标 [经度, 纬度]
    observed_points: 观测点坐标列表 [[lon1, lat1], [lon2, lat2], ...]
    observed_values: 观测点降雨量列表 [value1, value2, ...]
    variogram_params: 半变异函数模型参数
    
    返回:
    float: 插值后的降雨量
    """
    try:
        # 观测点数量
        n = len(observed_points)
        
        # 如果没有观测点，返回0
        if n == 0:
            return 0.0
        
        # 如果只有一个观测点，直接返回该观测点的值
        if n == 1:
            return observed_values[0]
        
        # 提取半变异函数模型参数
        model = variogram_params['model']
        sill = variogram_params['sill']
        range_param = variogram_params['range']
        nugget = variogram_params['nugget']
        
        # 构建克里金方程组矩阵
        # 矩阵大小为 (n+1) x (n+1)，其中n是观测点数量，额外的一行和一列用于拉格朗日乘子
        gamma_matrix = np.zeros((n+1, n+1))
        
        # 填充观测点之间的半变异函数值
        for i in range(n):
            for j in range(n):
                if i == j:
                    gamma_matrix[i, j] = 0
                else:
                    # 计算两点间距离
                    dist = np.sqrt((observed_points[i][0] - observed_points[j][0])**2 + 
                                  (observed_points[i][1] - observed_points[j][1])**2)
                    
                    # 根据模型计算半变异函数值
                    if model == 'spherical':
                        if dist <= range_param:
                            gamma_matrix[i, j] = nugget + sill * (1.5 * (dist / range_param) - 
                                                                0.5 * (dist / range_param)**3)
                        else:
                            gamma_matrix[i, j] = nugget + sill
                    elif model == 'exponential':
                        gamma_matrix[i, j] = nugget + sill * (1 - np.exp(-3 * dist / range_param))
                    elif model == 'gaussian':
                        gamma_matrix[i, j] = nugget + sill * (1 - np.exp(-3 * (dist / range_param)**2))
                    else:  # 默认线性模型
                        gamma_matrix[i, j] = nugget + sill * min(dist / range_param, 1.0)
        
        # 添加拉格朗日乘子约束
        for i in range(n):
            gamma_matrix[i, n] = 1
            gamma_matrix[n, i] = 1
        
        # 拉格朗日乘子对角线元素为0
        gamma_matrix[n, n] = 0
        
        # 构建目标点与观测点之间的半变异函数向量
        gamma_vector = np.zeros(n+1)
        
        # 填充目标点与观测点之间的半变异函数值
        for i in range(n):
            # 计算目标点与观测点的距离
            dist = np.sqrt((target_point[0] - observed_points[i][0])**2 + 
                          (target_point[1] - observed_points[i][1])**2)
            
            # 根据模型计算半变异函数值
            if model == 'spherical':
                if dist <= range_param:
                    gamma_vector[i] = nugget + sill * (1.5 * (dist / range_param) - 
                                                    0.5 * (dist / range_param)**3)
                else:
                    gamma_vector[i] = nugget + sill
            elif model == 'exponential':
                gamma_vector[i] = nugget + sill * (1 - np.exp(-3 * dist / range_param))
            elif model == 'gaussian':
                gamma_vector[i] = nugget + sill * (1 - np.exp(-3 * (dist / range_param)**2))
            else:  # 默认线性模型
                gamma_vector[i] = nugget + sill * min(dist / range_param, 1.0)
        
        # 添加拉格朗日乘子约束
        gamma_vector[n] = 1
        
        # 计算权重
        try:
            # 添加微小的对角线元素，增强数值稳定性
            gamma_matrix += np.eye(n+1) * 1e-5
            # 求解线性方程组，得到权重
            weights = np.linalg.solve(gamma_matrix, gamma_vector)[:n]
        except np.linalg.LinAlgError:
            # 如果矩阵奇异，使用伪逆
            logging.debug("使用伪逆计算权重")
            weights = np.linalg.lstsq(gamma_matrix, gamma_vector, rcond=None)[0][:n]
        
        # 计算插值结果: 观测值的加权和
        result = np.dot(weights, observed_values)
        
        # 确保结果非负（降雨量不能为负）
        result = max(0.0, result)
        
        return result
    
    except Exception as e:
        logging.error(f"Kriging插值计算时出错: {e}")
        # 出错时返回0，让程序继续运行
        return 0.0

def leave_one_out_validation(stations, rainfall_data, tri, time_index, variogram_params):
    """
    使用留一法验证插值精度
    
    参数:
    stations: 包含站点经纬度的DataFrame
    rainfall_data: 降雨数据DataFrame
    tri: Delaunay三角网对象
    time_index: 时间索引
    variogram_params: 半变异函数模型参数
    
    返回:
    DataFrame: 包含实际值和插值值的DataFrame
    """
    try:
        results = []
        
        # 提取当前时间步的降雨数据
        current_rainfall = rainfall_data.loc[time_index]
        
        # 对每个站点进行留一法验证
        for i, row in stations.iterrows():
            station_id = row['站点']
            
            # 跳过没有数据的站点
            if station_id not in current_rainfall or np.isnan(current_rainfall[station_id]):
                continue
            
            # 提取目标站点的坐标和实际降雨量
            target_point = [row['经度'], row['纬度']]
            actual_value = current_rainfall[station_id]
            
            # 找到相邻站点
            # 使用Delaunay三角网找到相邻站点
            neighbors = []
            for simplex in tri.simplices:
                if i in simplex:
                    neighbors.extend([idx for idx in simplex if idx != i])
            
            # 去重
            neighbors = list(set(neighbors))
            
            # 如果相邻站点太少，使用最近的10个站点
            if len(neighbors) < 5:
                # 计算所有站点到目标站点的距离
                distances = []
                for j, other_row in stations.iterrows():
                    if j != i:
                        other_id = other_row['站点']
                        if other_id in current_rainfall and not np.isnan(current_rainfall[other_id]):
                            dist = np.sqrt((target_point[0] - other_row['经度'])**2 + 
                                          (target_point[1] - other_row['纬度'])**2)
                            distances.append((j, dist))
                
                # 按距离排序
                distances.sort(key=lambda x: x[1])
                
                # 取最近的10个站点
                neighbors = [idx for idx, _ in distances[:10]]
            
            # 提取相邻站点的坐标和降雨量
            observed_points = []
            observed_values = []
            
            for neighbor_idx in neighbors:
                neighbor_row = stations.iloc[neighbor_idx]
                neighbor_id = neighbor_row['站点']
                
                if neighbor_id in current_rainfall and not np.isnan(current_rainfall[neighbor_id]):
                    observed_points.append([neighbor_row['经度'], neighbor_row['纬度']])
                    observed_values.append(current_rainfall[neighbor_id])
            
            # 如果没有足够的相邻站点，跳过
            if len(observed_points) < 3:
                continue
            
            # 使用Kriging方法插值
            interpolated_value = kriging_interpolation(
                target_point, observed_points, observed_values, variogram_params
            )
            
            # 添加结果
            results.append({
                '站点': station_id,
                'actual': actual_value,
                'predicted': interpolated_value
            })
        
        # 创建DataFrame
        results_df = pd.DataFrame(results)
        
        return results_df
    
    except Exception as e:
        logging.error(f"留一法验证时出错: {e}")
        return pd.DataFrame()

def process_time_point(args):
    """
    处理单个时间点的插值
    
    参数:
    args: 包含以下参数的元组:
        time_index: 时间索引
        stations: 包含站点经纬度的DataFrame
        rainfall_data: 降雨数据DataFrame
        tri: Delaunay三角网对象
        mask_array: 掩膜数组
        header: 掩膜文件头信息
        output_dir: 输出目录
        save_raster: 是否保存栅格文件
    
    返回:
    tuple: (时间索引, 评价指标, 实际值, 预测值)
    """
    try:
        # 解包参数
        time_index, stations, rainfall_data, tri, mask_array, header, output_dir, save_raster = args
        
        # 提取当前时间步的降雨数据
        current_rainfall = rainfall_data.loc[time_index]
        
        # 拟合半变异函数模型
        variogram_params = fit_variogram_model(stations, rainfall_data, time_index)
        
        # 留一法验证
        validation_results = leave_one_out_validation(stations, rainfall_data, tri, time_index, variogram_params)
        
        # 如果没有验证结果，返回空结果
        if validation_results.empty:
            logging.warning(f"时间步 {time_index} 没有验证结果")
            return time_index, {
                'RMSE': np.nan,
                'MAE': np.nan,
                'NSE': np.nan,
                'CORR': np.nan
            }, [], [], []
        
        # 计算评价指标
        actual = validation_results['actual'].values
        predicted = validation_results['predicted'].values
        
        rmse = np.sqrt(mean_squared_error(actual, predicted))
        mae = mean_absolute_error(actual, predicted)
        
        # 计算NSE (Nash-Sutcliffe Efficiency)
        mean_actual = np.mean(actual)
        nse = 1 - np.sum((predicted - actual) ** 2) / np.sum((actual - mean_actual) ** 2)
        
        # 计算相关系数
        corr = np.corrcoef(actual, predicted)[0, 1] if len(actual) > 1 else np.nan
        
        # 创建评价指标字典
        metrics = {
            'RMSE': rmse,
            'MAE': mae,
            'NSE': nse,
            'CORR': corr
        }
        
        # 如果需要保存栅格文件
        if save_raster:
            # 创建栅格目录
            raster_dir = os.path.join(output_dir, 'rasters')
            os.makedirs(raster_dir, exist_ok=True)
            
            # 生成栅格文件名
            time_str = time_index.strftime('%Y%m%d%H%M%S')
            raster_file = os.path.join(raster_dir, f'rainfall_{time_str}.asc')
            
            # 创建栅格
            create_kriging_raster(
                stations, current_rainfall, variogram_params, 
                mask_array, header, raster_file
            )
        
        logging.info(f"时间步 {time_index} 处理完成 - RMSE: {rmse:.4f}, MAE: {mae:.4f}, NSE: {nse:.4f}, CORR: {corr:.4f}")
        
        return time_index, metrics, actual, predicted, validation_results['站点'].values
    
    except Exception as e:
        logging.error(f"处理时间点 {time_index} 出错: {e}")
        return time_index, {
            'RMSE': np.nan,
            'MAE': np.nan,
            'NSE': np.nan,
            'CORR': np.nan
        }, [], [], []

def create_kriging_raster(stations, rainfall_data, variogram_params, mask_array, header, output_file):
    """
    创建Kriging插值栅格
    
    参数:
    stations: 包含站点经纬度的DataFrame
    rainfall_data: 当前时间步的降雨数据Series
    variogram_params: 半变异函数模型参数
    mask_array: 掩膜数组
    header: 掩膜文件头信息
    output_file: 输出文件路径
    
    返回:
    bool: 是否成功创建栅格
    """
    try:
        # 提取有效的站点数据
        valid_points = []
        valid_values = []
        
        for i, row in stations.iterrows():
            station_id = row['站点']
            if station_id in rainfall_data and not np.isnan(rainfall_data[station_id]):
                valid_points.append([row['经度'], row['纬度']])
                valid_values.append(rainfall_data[station_id])
        
        # 如果有效站点数量太少，返回False
        if len(valid_points) < 3:
            logging.warning(f"有效站点数量太少，无法创建栅格")
            return False
        
        # 提取栅格信息
        ncols = int(header['ncols'])
        nrows = int(header['nrows'])
        xllcorner = header['xllcorner']
        yllcorner = header['yllcorner']
        cellsize = header['cellsize']
        nodata_value = header.get('nodata_value', -9999)
        
        # 创建输出栅格
        rainfall_grid = np.full((nrows, ncols), nodata_value, dtype=np.float32)
        
        # 使用PyKrige进行Kriging插值
        try:
            # 转换为numpy数组
            valid_points = np.array(valid_points)
            valid_values = np.array(valid_values)
            
            # 创建Kriging对象
            if variogram_params['model'] == 'linear':
                # 线性模型使用普通Kriging
                ok = OrdinaryKriging(
                    valid_points[:, 0], valid_points[:, 1], valid_values,
                    variogram_model='linear',
                    verbose=False,
                    enable_plotting=False
                )
            else:
                # 其他模型使用普通Kriging
                ok = OrdinaryKriging(
                    valid_points[:, 0], valid_points[:, 1], valid_values,
                    variogram_model=variogram_params['model'],
                    variogram_parameters={
                        'sill': variogram_params['sill'],
                        'range': variogram_params['range'],
                        'nugget': variogram_params['nugget']
                    },
                    verbose=False,
                    enable_plotting=False
                )
            
            # 创建栅格坐标
            grid_x = np.arange(xllcorner, xllcorner + ncols * cellsize, cellsize) + cellsize / 2
            grid_y = np.arange(yllcorner, yllcorner + nrows * cellsize, cellsize) + cellsize / 2
            
            # 反转y坐标，使其从上到下
            grid_y = grid_y[::-1]
            
            # 执行Kriging插值
            z, ss = ok.execute('grid', grid_x, grid_y)
            
            # 将结果赋值给栅格
            for i in range(nrows):
                for j in range(ncols):
                    if mask_array[i, j] > 0:  # 只处理掩膜内的像素
                        rainfall_grid[i, j] = max(0, z[i, j])  # 确保降雨量非负
        
        except Exception as e:
            logging.warning(f"PyKrige插值失败，使用备用方法: {e}")
            
            # 备用方法：使用自定义Kriging插值
            for i in range(nrows):
                for j in range(ncols):
                    if mask_array[i, j] > 0:  # 只处理掩膜内的像素
                        # 计算像素中心的地理坐标
                        x = xllcorner + (j + 0.5) * cellsize
                        y = yllcorner + (nrows - i - 0.5) * cellsize
                        
                        # 使用Kriging插值
                        rainfall_grid[i, j] = kriging_interpolation(
                            [x, y], valid_points, valid_values, variogram_params
                        )
        
        # 写入ASC文件
        with open(output_file, 'w') as f:
            # 写入头信息
            f.write(f"ncols {ncols}\n")
            f.write(f"nrows {nrows}\n")
            f.write(f"xllcorner {xllcorner}\n")
            f.write(f"yllcorner {yllcorner}\n")
            f.write(f"cellsize {cellsize}\n")
            f.write(f"NODATA_value {nodata_value}\n")
            
            # 写入数据
            for i in range(nrows):
                row_str = ' '.join([str(rainfall_grid[i, j]) for j in range(ncols)])
                f.write(row_str + '\n')
        
        logging.info(f"成功创建栅格文件: {output_file}")
        return True
    
    except Exception as e:
        logging.error(f"创建栅格时出错: {e}")
        return False

def batch_process_time_points(time_points, stations, rainfall_data, tri, mask_array, header, output_dir, batch_size=100, save_raster=True):
    """
    批量处理时间点，以减少内存使用
    
    参数:
    time_points: 时间点列表
    stations: 包含站点经纬度的DataFrame
    rainfall_data: 降雨数据DataFrame
    tri: Delaunay三角网对象
    mask_array: 掩膜数组
    header: 掩膜文件头信息
    output_dir: 输出目录
    batch_size: 批处理大小
    save_raster: 是否保存栅格文件
    
    返回:
    dict: 处理结果字典
    """
    # 创建结果字典
    results = {}
    
    # 计算批次数
    num_batches = (len(time_points) + batch_size - 1) // batch_size
    
    # 获取CPU核心数
    cpu_count = min(mp.cpu_count(), 12)  # 使用最多12个核心
    logging.info(f"将使用 {cpu_count} 个CPU核心进行并行处理")
    
    # 处理每个批次
    for batch_idx in range(num_batches):
        # 计算当前批次的时间点
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(time_points))
        current_time_points = time_points[start_idx:end_idx]
        
        logging.info(f"处理批次 {batch_idx + 1}/{num_batches}, 包含 {len(current_time_points)} 个时间点")
        
        # 创建参数列表
        args_list = [
            (time_point, stations, rainfall_data, tri, mask_array, header, output_dir, save_raster)
            for time_point in current_time_points
        ]
        
        # 使用多进程处理
        with mp.Pool(cpu_count) as pool:
            batch_results = list(tqdm(
                pool.imap(process_time_point, args_list),
                total=len(args_list),
                desc=f"批次 {batch_idx + 1}/{num_batches}"
            ))
        
        # 处理结果
        for time_point, metrics, actual, predicted, station_ids in batch_results:
            results[time_point] = {
                'metrics': metrics,
                'actual': actual,
                'predicted': predicted,
                'station_ids': station_ids
            }
        
        # 清理内存
        del batch_results
        import gc
        gc.collect()
    
    return results

def create_evaluation_file(results, output_dir):
    """
    创建评价指标文件
    
    参数:
    results: 处理结果字典
    output_dir: 输出目录
    
    返回:
    str: 评价指标文件路径
    """
    try:
        # 提取评价指标
        metrics_data = []
        
        for time_point, result in sorted(results.items()):
            metrics = result['metrics']
            metrics_data.append({
                '时间': time_point,
                'RMSE': metrics['RMSE'],
                'MAE': metrics['MAE'],
                'NSE': metrics['NSE'],
                'CORR': metrics['CORR']
            })
        
        # 创建DataFrame
        metrics_df = pd.DataFrame(metrics_data)
        
        # 计算平均指标
        mean_metrics = {
            '时间': 'Average',
            'RMSE': metrics_df['RMSE'].mean(),
            'MAE': metrics_df['MAE'].mean(),
            'NSE': metrics_df['NSE'].mean(),
            'CORR': metrics_df['CORR'].mean()
        }
        
        # 添加平均指标
        metrics_df = pd.concat([metrics_df, pd.DataFrame([mean_metrics])], ignore_index=True)
        
        # 保存评价指标文件
        eval_dir = os.path.join(output_dir, 'evaluation')
        os.makedirs(eval_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        eval_file = os.path.join(eval_dir, f'metrics_{timestamp}.csv')
        
        metrics_df.to_csv(eval_file, index=False)
        
        logging.info(f"成功创建评价指标文件: {eval_file}")
        return eval_file
    
    except Exception as e:
        logging.error(f"创建评价指标文件时出错: {e}")
        return None

def create_scatter_plot(results, output_dir):
    """
    创建散点图，显示实际值和预测值的对比
    
    参数:
    results: 处理结果字典
    output_dir: 输出目录
    
    返回:
    str: 散点图文件路径
    """
    try:
        # 收集所有的实际值和预测值
        all_actual = []
        all_predicted = []
        
        for result in results.values():
            all_actual.extend(result['actual'])
            all_predicted.extend(result['predicted'])
        
        # 如果没有足够的数据，返回None
        if len(all_actual) < 10:
            logging.warning("没有足够的数据创建散点图")
            return None
        
        # 创建散点图
        plt.figure(figsize=(10, 8))
        plt.scatter(all_actual, all_predicted, alpha=0.5)
        
        # 添加对角线
        max_value = max(max(all_actual), max(all_predicted)) * 1.1
        plt.plot([0, max_value], [0, max_value], 'r--')
        
        # 添加标题和标签
        plt.title('Kriging插值验证: 实际值 vs 预测值')
        plt.xlabel('实际降雨量')
        plt.ylabel('预测降雨量')
        
        # 添加网格
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # 计算相关系数和RMSE
        corr = np.corrcoef(all_actual, all_predicted)[0, 1]
        rmse = np.sqrt(mean_squared_error(all_actual, all_predicted))
        
        # 添加统计信息
        plt.text(0.05, 0.95, f'相关系数: {corr:.4f}\nRMSE: {rmse:.4f}',
                 transform=plt.gca().transAxes, fontsize=12,
                 verticalalignment='top', bbox=dict(boxstyle='round', alpha=0.5))
        
        # 保存图像
        plot_dir = os.path.join(output_dir, 'plots')
        os.makedirs(plot_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        scatter_file = os.path.join(plot_dir, f'scatter_{timestamp}.png')
        
        plt.savefig(scatter_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        logging.info(f"成功创建散点图: {scatter_file}")
        return scatter_file
    
    except Exception as e:
        logging.error(f"创建散点图时出错: {e}")
        return None

def create_time_series_plot(results, output_dir):
    """
    创建时间序列图，显示评价指标随时间的变化
    
    参数:
    results: 处理结果字典
    output_dir: 输出目录
    
    返回:
    str: 时间序列图文件路径
    """
    try:
        # 提取时间和评价指标
        time_points = []
        rmse_values = []
        nse_values = []
        
        for time_point, result in sorted(results.items()):
            metrics = result['metrics']
            
            # 跳过无效值
            if np.isnan(metrics['RMSE']) or np.isnan(metrics['NSE']):
                continue
            
            time_points.append(time_point)
            rmse_values.append(metrics['RMSE'])
            nse_values.append(metrics['NSE'])
        
        # 如果没有足够的数据，返回None
        if len(time_points) < 2:
            logging.warning("没有足够的数据创建时间序列图")
            return None
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
        
        # 绘制RMSE
        ax1.plot(time_points, rmse_values, 'b-', marker='o', markersize=4)
        ax1.set_ylabel('RMSE')
        ax1.set_title('Kriging插值评价指标随时间的变化')
        ax1.grid(True, linestyle='--', alpha=0.7)
        
        # 绘制NSE
        ax2.plot(time_points, nse_values, 'g-', marker='o', markersize=4)
        ax2.set_ylabel('NSE')
        ax2.set_xlabel('时间')
        ax2.grid(True, linestyle='--', alpha=0.7)
        
        # 设置x轴格式
        fig.autofmt_xdate()
        
        # 保存图像
        plot_dir = os.path.join(output_dir, 'plots')
        os.makedirs(plot_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        time_series_file = os.path.join(plot_dir, f'time_series_{timestamp}.png')
        
        plt.savefig(time_series_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        logging.info(f"成功创建时间序列图: {time_series_file}")
        return time_series_file
    
    except Exception as e:
        logging.error(f"创建时间序列图时出错: {e}")
        return None

def create_variogram_plot(stations, rainfall_data, time_point, output_dir):
    """
    创建半变异函数图
    
    参数:
    stations: 包含站点经纬度的DataFrame
    rainfall_data: 降雨数据DataFrame
    time_point: 时间点
    output_dir: 输出目录
    
    返回:
    str: 半变异函数图文件路径
    """
    try:
        # 提取当前时间步的降雨数据
        current_rainfall = rainfall_data.loc[time_point]
        
        # 提取有效的站点数据
        valid_points = []
        valid_values = []
        
        for i, row in stations.iterrows():
            station_id = row['站点']
            if station_id in current_rainfall and not np.isnan(current_rainfall[station_id]):
                valid_points.append([row['经度'], row['纬度']])
                valid_values.append(current_rainfall[station_id])
        
        # 如果有效站点数量太少，返回None
        if len(valid_points) < 5:
            logging.warning(f"时间点 {time_point} 的有效站点数量太少，无法创建半变异函数图")
            return None
        
        # 计算站点间距离矩阵
        n = len(valid_points)
        distances = np.zeros((n, n))
        for i in range(n):
            for j in range(i+1, n):
                dist = np.sqrt((valid_points[i][0] - valid_points[j][0])**2 + 
                              (valid_points[i][1] - valid_points[j][1])**2)
                distances[i, j] = distances[j, i] = dist
        
        # 计算站点间半变异函数值
        values = np.array(valid_values)
        variogram = np.zeros((n, n))
        for i in range(n):
            for j in range(i+1, n):
                variogram[i, j] = variogram[j, i] = 0.5 * (values[i] - values[j])**2
        
        # 将距离和半变异函数值展平为一维数组
        dist_flat = distances[np.triu_indices(n, k=1)]
        vario_flat = variogram[np.triu_indices(n, k=1)]
        
        # 按距离排序
        sort_idx = np.argsort(dist_flat)
        dist_sorted = dist_flat[sort_idx]
        vario_sorted = vario_flat[sort_idx]
        
        # 分组计算平均半变异函数值
        max_dist = np.max(dist_sorted)
        bin_width = max_dist / 10
        bins = np.arange(0, max_dist + bin_width, bin_width)
        bin_centers = (bins[:-1] + bins[1:]) / 2
        bin_means = np.zeros(len(bin_centers))
        
        for i in range(len(bin_centers)):
            mask = (dist_sorted >= bins[i]) & (dist_sorted < bins[i+1])
            if np.any(mask):
                bin_means[i] = np.mean(vario_sorted[mask])
            else:
                bin_means[i] = np.nan
        
        # 移除NaN值
        valid_mask = ~np.isnan(bin_means)
        bin_centers = bin_centers[valid_mask]
        bin_means = bin_means[valid_mask]
        
        # 如果没有足够的有效点，返回None
        if len(bin_centers) < 3:
            logging.warning(f"时间点 {time_point} 的有效距离分组太少，无法创建半变异函数图")
            return None
        
        # 拟合半变异函数模型
        variogram_params = fit_variogram_model(stations, rainfall_data, time_point)
        
        # 创建半变异函数图
        plt.figure(figsize=(10, 8))
        
        # 绘制实验半变异函数点
        plt.scatter(bin_centers, bin_means, color='blue', label='实验半变异函数')
        
        # 绘制拟合的半变异函数模型
        x = np.linspace(0, max_dist, 100)
        y = np.zeros_like(x)
        
        model = variogram_params['model']
        sill = variogram_params['sill']
        range_param = variogram_params['range']
        nugget = variogram_params['nugget']
        
        for i in range(len(x)):
            if model == 'spherical':
                if x[i] <= range_param:
                    y[i] = nugget + sill * (1.5 * (x[i] / range_param) - 0.5 * (x[i] / range_param)**3)
                else:
                    y[i] = nugget + sill
            elif model == 'exponential':
                y[i] = nugget + sill * (1 - np.exp(-3 * x[i] / range_param))
            elif model == 'gaussian':
                y[i] = nugget + sill * (1 - np.exp(-3 * (x[i] / range_param)**2))
            else:  # 默认线性模型
                y[i] = nugget + sill * min(x[i] / range_param, 1.0)
        
        plt.plot(x, y, 'r-', label=f'拟合模型: {model}')
        
        # 添加标题和标签
        plt.title(f'半变异函数图 - {time_point}')
        plt.xlabel('距离')
        plt.ylabel('半变异函数值')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        
        # 添加模型参数
        plt.text(0.05, 0.95, f'模型: {model}\n基台值: {sill:.4f}\n变程: {range_param:.4f}\n块金值: {nugget:.4f}',
                 transform=plt.gca().transAxes, fontsize=12,
                 verticalalignment='top', bbox=dict(boxstyle='round', alpha=0.5))
        
        # 保存图像
        plot_dir = os.path.join(output_dir, 'plots')
        os.makedirs(plot_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        variogram_file = os.path.join(plot_dir, f'variogram_{timestamp}.png')
        
        plt.savefig(variogram_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        logging.info(f"成功创建半变异函数图: {variogram_file}")
        return variogram_file
    
    except Exception as e:
        logging.error(f"创建半变异函数图时出错: {e}")
        return None

def main():
    """
    主函数
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Kriging空间插值程序')
    parser.add_argument('--input', type=str, default=DEFAULT_INPUT_DIR, help='输入数据目录')
    parser.add_argument('--output', type=str, default=DEFAULT_OUTPUT_DIR, help='输出目录')
    parser.add_argument('--stations', type=str, default=DEFAULT_STATIONS_FILE, help='站点信息文件')
    parser.add_argument('--mask', type=str, default=DEFAULT_MASK_FILE, help='掩膜文件')
    parser.add_argument('--batch-size', type=int, default=100, help='批处理大小')
    parser.add_argument('--no-raster', action='store_true', help='不生成栅格文件')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 设置日志
    log_file = setup_logging(args.output)
    
    try:
        # 打印程序信息
        logging.info("=" * 50)
        logging.info("Kriging空间插值程序")
        logging.info("=" * 50)
        logging.info(f"输入数据目录: {args.input}")
        logging.info(f"输出目录: {args.output}")
        logging.info(f"站点信息文件: {args.stations}")
        logging.info(f"掩膜文件: {args.mask}")
        logging.info(f"批处理大小: {args.batch_size}")
        logging.info(f"生成栅格文件: {not args.no_raster}")
        logging.info("=" * 50)
        
        # 记录开始时间
        start_time = time.time()
        
        # 读取站点信息
        stations = read_stations(args.stations)
        
        # 读取降雨数据
        rainfall_data = read_rainfall_data(args.input, stations['站点'].values)
        
        # 读取掩膜文件
        mask_array, header = read_mask(args.mask)
        
        # 创建Delaunay三角网
        tri = create_delaunay_triangulation(stations)
        
        # 获取所有时间点
        time_points = rainfall_data.index.tolist()
        
        # 批量处理时间点
        results = batch_process_time_points(
            time_points, stations, rainfall_data, tri, mask_array, header, 
            args.output, args.batch_size, not args.no_raster
        )
        
        # 创建评价指标文件
        eval_file = create_evaluation_file(results, args.output)
        
        # 创建散点图
        scatter_file = create_scatter_plot(results, args.output)
        
        # 创建时间序列图
        time_series_file = create_time_series_plot(results, args.output)
        
        # 为第一个时间点创建半变异函数图
        if time_points:
            variogram_file = create_variogram_plot(stations, rainfall_data, time_points[0], args.output)
        
        # 计算总耗时
        end_time = time.time()
        total_time = end_time - start_time
        
        logging.info("=" * 50)
        logging.info(f"插值完成! 总耗时: {total_time:.2f} 秒")
        logging.info(f"评价指标文件: {eval_file}")
        logging.info(f"散点图: {scatter_file}")
        logging.info(f"时间序列图: {time_series_file}")
        if time_points:
            logging.info(f"半变异函数图: {variogram_file}")
        logging.info("=" * 50)
    
    except Exception as e:
        logging.error(f"程序执行出错: {e}")
        logging.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()


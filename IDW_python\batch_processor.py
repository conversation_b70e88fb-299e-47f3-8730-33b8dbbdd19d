#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批处理模块
批量处理所有洪水事件的IDW插值

作者: 空间插值系统
日期: 2024年
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
from typing import Dict, List, Optional
from datetime import datetime
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp

logger = logging.getLogger(__name__)

class BatchProcessor:
    """批处理器类"""
    
    def __init__(self, config, data_loader, interpolator, evaluator, visualizer, raster_output=None):
        self.config = config
        self.data_loader = data_loader
        self.interpolator = interpolator
        self.evaluator = evaluator
        self.visualizer = visualizer
        self.raster_output = raster_output
        
        self.processing_results = {}
        self.processing_summary = {}
    
    def process_single_event(self, event_name: str) -> Dict:
        """处理单个洪水事件"""
        logger.info(f"开始处理洪水事件: {event_name}")
        start_time = time.time()
        
        event_result = {
            'event_name': event_name,
            'start_time': datetime.now(),
            'success': False,
            'error_message': None,
            'processing_time': 0,
            'interpolation_results': {},
            'evaluation_metrics': {},
            'visualization_files': [],
            'raster_files': []
        }
        
        try:
            # 1. 执行插值
            logger.info(f"步骤1: 对事件{event_name}执行IDW插值")
            interpolation_results = self.interpolator.interpolate_flood_event(event_name)
            
            if not interpolation_results:
                raise ValueError(f"事件{event_name}插值失败，没有有效结果")
            
            event_result['interpolation_results'] = interpolation_results
            
            # 保存插值结果
            self.interpolator.save_interpolation_results(event_name, interpolation_results)
            
            # 2. 计算评估指标
            logger.info(f"步骤2: 计算事件{event_name}的评估指标")
            evaluation_metrics = self.evaluator.evaluate_event_results(
                interpolation_results, event_name
            )
            
            event_result['evaluation_metrics'] = evaluation_metrics
            
            # 保存评估结果
            self.evaluator.save_evaluation_results(event_name, evaluation_metrics)
            
            # 3. 生成可视化
            if self.config.enable_visualization:
                logger.info(f"步骤3: 为事件{event_name}生成可视化")
                visualization_files = self.visualizer.create_event_visualization_report(
                    event_name, interpolation_results, evaluation_metrics
                )
                event_result['visualization_files'] = visualization_files
            
            # 4. 生成栅格输出（如果启用）
            if self.config.enable_raster_output and self.raster_output:
                logger.info(f"步骤4: 为事件{event_name}生成栅格输出")
                
                # 重新加载站点数据（因为可能在不同进程中）
                station_data = self.data_loader.load_flood_event_data(event_name)
                
                # 生成栅格时间序列
                raster_files = self.raster_output.create_raster_timeseries(
                    event_name, station_data, self.interpolator
                )
                
                # 生成汇总栅格
                summary_raster = self.raster_output.create_summary_raster(
                    event_name, station_data, self.interpolator, 'total'
                )
                if summary_raster:
                    raster_files.append(summary_raster)
                
                event_result['raster_files'] = raster_files
            
            # 计算处理时间
            processing_time = time.time() - start_time
            event_result['processing_time'] = processing_time
            event_result['success'] = True
            event_result['end_time'] = datetime.now()
            
            logger.info(f"事件{event_name}处理完成，耗时: {processing_time:.2f}秒")
            
        except Exception as e:
            error_msg = f"处理事件{event_name}时出错: {str(e)}"
            logger.error(error_msg)
            event_result['error_message'] = error_msg
            event_result['processing_time'] = time.time() - start_time
            event_result['end_time'] = datetime.now()
        
        return event_result
    
    def process_all_events(self, event_list: Optional[List[str]] = None) -> Dict:
        """批量处理所有洪水事件"""
        logger.info("开始批量处理洪水事件")
        
        # 获取要处理的事件列表
        if event_list is None:
            event_list = self.config.get_flood_events()
        
        if not event_list:
            logger.warning("没有找到要处理的洪水事件")
            return {}
        
        logger.info(f"共找到{len(event_list)}个洪水事件待处理")
        
        batch_start_time = time.time()
        
        # 根据配置决定是否使用并行处理
        if self.config.enable_parallel and len(event_list) > 1:
            results = self._process_events_parallel(event_list)
        else:
            results = self._process_events_sequential(event_list)
        
        # 保存处理结果
        self.processing_results = results
        
        # 生成批处理汇总
        batch_summary = self._generate_batch_summary(results, batch_start_time)
        self.processing_summary = batch_summary
        
        # 保存批处理汇总
        self._save_batch_summary(batch_summary)
        
        logger.info(f"批量处理完成，总耗时: {batch_summary['total_processing_time']:.2f}秒")
        
        return results
    
    def _process_events_sequential(self, event_list: List[str]) -> Dict:
        """顺序处理事件"""
        logger.info("使用顺序处理模式")
        results = {}
        
        for i, event_name in enumerate(event_list, 1):
            logger.info(f"处理进度: {i}/{len(event_list)} - {event_name}")
            result = self.process_single_event(event_name)
            results[event_name] = result
        
        return results
    
    def _process_events_parallel(self, event_list: List[str]) -> Dict:
        """并行处理事件"""
        logger.info(f"使用并行处理模式，进程数: {self.config.n_cores}")
        
        # 注意：并行处理时需要确保每个进程都有独立的组件实例
        # 这里简化处理，实际使用时可能需要更复杂的进程间通信
        
        results = {}
        max_workers = min(self.config.n_cores, len(event_list))
        
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_event = {
                executor.submit(self._process_event_wrapper, event_name): event_name 
                for event_name in event_list
            }
            
            # 收集结果
            completed = 0
            for future in as_completed(future_to_event):
                event_name = future_to_event[future]
                completed += 1
                
                try:
                    result = future.result()
                    results[event_name] = result
                    logger.info(f"并行处理进度: {completed}/{len(event_list)} - {event_name} 完成")
                    
                except Exception as e:
                    logger.error(f"并行处理事件{event_name}失败: {e}")
                    results[event_name] = {
                        'event_name': event_name,
                        'success': False,
                        'error_message': str(e),
                        'processing_time': 0
                    }
        
        return results
    
    def _process_event_wrapper(self, event_name: str) -> Dict:
        """并行处理的包装函数"""
        # 在新进程中重新创建必要的组件
        from idw_config import config
        from data_loader import DataLoader
        from idw_interpolation import IDWInterpolator
        from evaluation_metrics import EvaluationMetrics
        from visualization import IDWVisualizer
        from raster_output import RasterOutput
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        
        # 创建组件
        loader = DataLoader(config)
        interpolator = IDWInterpolator(config, loader)
        evaluator = EvaluationMetrics(config)
        visualizer = IDWVisualizer(config)
        raster_output = RasterOutput(config, loader) if config.enable_raster_output else None
        
        # 创建临时批处理器
        temp_processor = BatchProcessor(
            config, loader, interpolator, evaluator, visualizer, raster_output
        )
        
        # 处理单个事件
        return temp_processor.process_single_event(event_name)
    
    def _generate_batch_summary(self, results: Dict, start_time: float) -> Dict:
        """生成批处理汇总"""
        total_time = time.time() - start_time
        
        # 统计成功和失败的事件
        successful_events = [name for name, result in results.items() if result.get('success', False)]
        failed_events = [name for name, result in results.items() if not result.get('success', False)]
        
        # 计算平均处理时间
        processing_times = [result.get('processing_time', 0) for result in results.values() 
                          if result.get('success', False)]
        avg_processing_time = np.mean(processing_times) if processing_times else 0
        
        # 收集评估指标统计
        all_metrics = []
        for result in results.values():
            if result.get('success', False) and result.get('evaluation_metrics'):
                for station_metrics in result['evaluation_metrics'].values():
                    all_metrics.append(station_metrics)
        
        # 计算整体指标统计
        overall_metrics = {}
        if all_metrics:
            metrics_df = pd.DataFrame(all_metrics)
            for metric in ['MAE', 'RMSE', 'NSE', 'R2']:
                if metric in metrics_df.columns:
                    valid_values = metrics_df[metric].dropna()
                    if len(valid_values) > 0:
                        overall_metrics[f'{metric}_mean'] = valid_values.mean()
                        overall_metrics[f'{metric}_std'] = valid_values.std()
                        overall_metrics[f'{metric}_median'] = valid_values.median()
        
        summary = {
            'batch_start_time': datetime.fromtimestamp(start_time),
            'batch_end_time': datetime.now(),
            'total_processing_time': total_time,
            'total_events': len(results),
            'successful_events': len(successful_events),
            'failed_events': len(failed_events),
            'success_rate': len(successful_events) / len(results) if results else 0,
            'average_processing_time': avg_processing_time,
            'successful_event_list': successful_events,
            'failed_event_list': failed_events,
            'overall_metrics': overall_metrics,
            'total_stations_processed': len(all_metrics),
            'processing_mode': 'parallel' if self.config.enable_parallel else 'sequential'
        }
        
        return summary
    
    def _save_batch_summary(self, summary: Dict):
        """保存批处理汇总"""
        output_dir = self.config.output_dir / "summary_reports"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存详细汇总
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = output_dir / f"batch_processing_summary_{timestamp}.json"
        
        # 转换datetime对象为字符串以便JSON序列化
        summary_for_json = summary.copy()
        summary_for_json['batch_start_time'] = summary['batch_start_time'].isoformat()
        summary_for_json['batch_end_time'] = summary['batch_end_time'].isoformat()
        
        import json
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_for_json, f, ensure_ascii=False, indent=2)
        
        # 保存简化的CSV汇总
        csv_summary = {
            '批处理开始时间': [summary['batch_start_time']],
            '批处理结束时间': [summary['batch_end_time']],
            '总处理时间(秒)': [summary['total_processing_time']],
            '总事件数': [summary['total_events']],
            '成功事件数': [summary['successful_events']],
            '失败事件数': [summary['failed_events']],
            '成功率': [summary['success_rate']],
            '平均处理时间(秒)': [summary['average_processing_time']],
            '总处理站点数': [summary['total_stations_processed']],
            '处理模式': [summary['processing_mode']]
        }
        
        csv_df = pd.DataFrame(csv_summary)
        csv_file = output_dir / f"batch_summary_{timestamp}.csv"
        csv_df.to_csv(csv_file, index=False, encoding='utf-8')
        
        logger.info(f"批处理汇总已保存: {summary_file}")
        logger.info(f"批处理汇总CSV已保存: {csv_file}")
    
    def get_processing_report(self) -> str:
        """获取处理报告文本"""
        if not self.processing_summary:
            return "尚未执行批处理"
        
        summary = self.processing_summary
        
        report = f"""
IDW插值批处理报告
{'='*50}

批处理信息:
  开始时间: {summary['batch_start_time']}
  结束时间: {summary['batch_end_time']}
  总耗时: {summary['total_processing_time']:.2f} 秒
  处理模式: {summary['processing_mode']}

事件处理统计:
  总事件数: {summary['total_events']}
  成功事件数: {summary['successful_events']}
  失败事件数: {summary['failed_events']}
  成功率: {summary['success_rate']:.1%}
  平均处理时间: {summary['average_processing_time']:.2f} 秒/事件

站点处理统计:
  总处理站点数: {summary['total_stations_processed']}

整体评估指标:"""
        
        if summary['overall_metrics']:
            for metric, value in summary['overall_metrics'].items():
                report += f"\n  {metric}: {value:.4f}"
        else:
            report += "\n  无可用评估指标"
        
        if summary['failed_events']:
            report += f"\n\n失败事件列表:\n"
            for event in summary['failed_events']:
                report += f"  - {event}\n"
        
        return report

if __name__ == "__main__":
    # 测试批处理器
    from idw_config import config
    from data_loader import DataLoader
    from idw_interpolation import IDWInterpolator
    from evaluation_metrics import EvaluationMetrics
    from visualization import IDWVisualizer
    from raster_output import RasterOutput
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建组件
    loader = DataLoader(config)
    interpolator = IDWInterpolator(config, loader)
    evaluator = EvaluationMetrics(config)
    visualizer = IDWVisualizer(config)
    raster_output = RasterOutput(config, loader) if config.enable_raster_output else None
    
    # 创建批处理器
    processor = BatchProcessor(
        config, loader, interpolator, evaluator, visualizer, raster_output
    )
    
    # 获取事件列表
    events = config.get_flood_events()
    if events:
        # 测试处理前几个事件
        test_events = events[:2]  # 只处理前2个事件进行测试
        print(f"测试处理事件: {test_events}")
        
        results = processor.process_all_events(test_events)
        
        # 打印报告
        print(processor.get_processing_report())
    else:
        print("没有找到洪水事件数据")

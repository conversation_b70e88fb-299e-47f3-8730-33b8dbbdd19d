#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理所有洪水事件的优化脚本
确保每个洪水场次都有完整的输出
"""

import sys
import gc
import json
import time
from pathlib import Path

print("🎯 Delaunay插值系统 - 处理所有洪水事件")

try:
    # 添加项目路径
    sys.path.append(str(Path(__file__).parent))
    
    from config import DelaunayConfig
    from data_loader import DelaunayDataLoader
    from interpolator import DelaunayInterpolator
    from evaluation import EvaluationMetrics
    
    print("✅ 模块导入成功")
    
    # 加载配置
    config = DelaunayConfig()
    print(f"📋 输出目录: {config.OUTPUT_DIR}")
    
    # 初始化组件
    print("🔧 初始化组件...")
    data_loader = DelaunayDataLoader(config)
    evaluator = EvaluationMetrics(config)
    interpolator = DelaunayInterpolator(config, data_loader, evaluator)
    print("✅ 组件初始化完成")
    
    # 获取所有洪水事件
    flood_events = config.get_flood_events()
    print(f"🌊 发现{len(flood_events)}个洪水事件")
    
    # 检查已完成的事件
    completed_events = []
    incomplete_events = []
    
    for event_name in flood_events:
        event_dir = config.OUTPUT_DIR / event_name
        metrics_file = event_dir / f"{event_name}_metrics.csv"
        summary_file = event_dir / f"{event_name}_summary.json"
        
        if metrics_file.exists() and summary_file.exists():
            completed_events.append(event_name)
        else:
            incomplete_events.append(event_name)
    
    print(f"✅ 已完成事件: {len(completed_events)}个")
    print(f"🔄 需要处理事件: {len(incomplete_events)}个")
    
    if not incomplete_events:
        print("🎉 所有洪水事件已完成处理！")
        sys.exit(0)
    
    print(f"待处理事件: {incomplete_events}")
    
    # 处理未完成的事件
    success_count = 0
    
    for i, event_name in enumerate(incomplete_events):
        print(f"\n🌊 处理进度: {i+1}/{len(incomplete_events)} - {event_name}")
        start_time = time.time()
        
        try:
            # 创建输出目录
            event_output_dir = config.OUTPUT_DIR / event_name
            event_output_dir.mkdir(parents=True, exist_ok=True)
            
            # 检查是否已完成
            metrics_file = event_output_dir / f"{event_name}_metrics.csv"
            summary_file = event_output_dir / f"{event_name}_summary.json"
            
            if metrics_file.exists() and summary_file.exists():
                print(f"✅ 事件{event_name}已完成，跳过")
                success_count += 1
                continue
            
            # 处理插值
            print(f"🔄 处理事件{event_name}的插值...")
            result = interpolator.process_flood_event(event_name)
            
            if not result:
                print(f"❌ 事件{event_name}插值处理失败")
                continue
            
            # 评估结果
            print(f"📊 评估事件{event_name}的结果...")
            event_metrics, summary_stats = evaluator.evaluate_event_results(
                result, event_name, event_output_dir
            )
            
            # 保存汇总统计
            try:
                with open(summary_file, 'w', encoding='utf-8') as f:
                    json.dump(summary_stats, f, ensure_ascii=False, indent=2, default=str)
                print(f"✅ 事件{event_name}汇总统计已保存")
            except Exception as e:
                print(f"⚠️ 事件{event_name}汇总统计保存失败: {e}")
            
            # 输出处理结果
            avg_nse = summary_stats.get('avg_nse', 0)
            nse_above_threshold = summary_stats.get('nse_above_threshold_percentage', 0)
            elapsed_time = time.time() - start_time
            
            print(f"✅ 洪水事件{event_name}处理完成 (耗时: {elapsed_time:.1f}秒)")
            print(f"   - 处理站点数: {len(event_metrics)}")
            print(f"   - 平均NSE: {avg_nse:.4f}")
            print(f"   - NSE>0.7比例: {nse_above_threshold:.1f}%")
            
            success_count += 1
            
        except Exception as e:
            print(f"❌ 处理事件{event_name}时发生异常: {e}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
        
        # 定期清理内存
        if (i + 1) % 3 == 0:
            print("🧹 执行内存清理...")
            gc.collect()
    
    # 最终验证
    print("\n🔍 最终验证所有洪水事件处理状态...")
    final_completed = 0
    final_missing = []
    
    for event_name in flood_events:
        event_dir = config.OUTPUT_DIR / event_name
        metrics_file = event_dir / f"{event_name}_metrics.csv"
        summary_file = event_dir / f"{event_name}_summary.json"
        
        if metrics_file.exists() and summary_file.exists():
            final_completed += 1
        else:
            final_missing.append(event_name)
    
    print(f"✅ 最终完成事件数: {final_completed}/{len(flood_events)}")
    if final_missing:
        print(f"⚠️ 仍未完成事件: {final_missing}")
    else:
        print("🎉 所有洪水事件处理完成！")
    
    print(f"📊 本次处理成功: {success_count}/{len(incomplete_events)}")
    print("🎯 洪水事件处理程序完成")
    
except Exception as e:
    print(f"❌ 程序运行失败: {e}")
    import traceback
    print(f"详细错误信息: {traceback.format_exc()}")
    sys.exit(1)

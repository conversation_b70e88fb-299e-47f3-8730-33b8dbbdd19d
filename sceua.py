import numpy as np
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger()


class SCEUA:
    """
    Shuffled Complex Evolution - University of Arizona (SCE-UA)算法实现
    用于全局优化问题
    """

    def __init__(self, objective_function, param_ranges, n_complexes=6, points_per_complex=1000, 
                 total_generations=100, p_random=0.8, seed=None):
        """
        初始化SCE-UA优化器
        
        参数:
            objective_function: 目标函数，接收参数向量并返回标量值
            param_ranges: 参数范围列表，每个元素为一个元组 (min, max)
            n_complexes: 复杂体数量 (默认: 6)
            points_per_complex: 每个复杂体的点数 (默认: 1000)
            total_generations: 总代数 (默认:100)
            p_random: 随机抽样概率 (默认: 0.8)
            seed: 随机种子 (默认: None)
        """
        self.objective_function = objective_function
        self.param_ranges = np.array(param_ranges)
        self.n_params = len(param_ranges)
        self.n_complexes = n_complexes
        self.points_per_complex = points_per_complex
        self.total_generations = total_generations
        self.p_random = p_random
        
        # 初始化随机数生成器
        self.rng = np.random.default_rng(seed)
        
        # 初始化种群
        self.population = self._initialize_population()
        self.population_values = np.array([self.objective_function(point) for point in self.population])
        
        # 记录最佳解
        self.best_point = self.population[np.argmin(self.population_values)]
        self.best_value = np.min(self.population_values)
        
        # 记录优化过程
        self.history = []
        
        logger.info(f"SCE-UA初始化完成. 参数维度: {self.n_params}, 总种群大小: {self.population.shape[0]}")
        logger.info(f"参数范围: {param_ranges}")
        logger.info(f"初始最佳值: {self.best_value:.4f}")
    
    def _initialize_population(self):
        """
        随机初始化种群，确保满足约束条件
        """
        population = []
        for _ in range(self.n_complexes * self.points_per_complex):
            while True:
                # 随机生成三个系数，确保它们的和为1
                elev_coeff = self.rng.uniform(self.param_ranges[0][0], self.param_ranges[0][1])
                slope_coeff = self.rng.uniform(self.param_ranges[1][0], self.param_ranges[1][1])
                aspect_coeff = 1.0 - elev_coeff - slope_coeff
            
                # 检查aspect_coeff是否在允许的范围内
                if (aspect_coeff >= self.param_ranges[2][0]) and (aspect_coeff <= self.param_ranges[2][1]):
                    # 生成其他参数
                    distance_decay = self.rng.uniform(self.param_ranges[3][0], self.param_ranges[3][1])
                    max_neighbors = self.rng.uniform(self.param_ranges[4][0], self.param_ranges[4][1])
                    population.append([elev_coeff, slope_coeff, aspect_coeff, distance_decay, max_neighbors])
                    break
        return np.array(population)
    def _shuffle_population(self):
        """
        将种群随机打乱并分组为复杂体
        """
        # 打乱种群顺序
        indices = self.rng.permutation(len(self.population))
        shuffled_population = self.population[indices]
        shuffled_values = self.population_values[indices]
        
        # 分组为复杂体
        complexes = []
        complexes_values = []
        for i in range(self.n_complexes):
            start = i * self.points_per_complex
            end = start + self.points_per_complex
            complexes.append(shuffled_population[start:end])
            complexes_values.append(shuffled_values[start:end])
        
        return np.array(complexes), np.array(complexes_values)
    
    def _evolve_complex(self, complex_points, complex_values):
        """
        进化一个复杂体
        """
        # 按目标函数值排序
        sorted_indices = np.argsort(complex_values)
        complex_points = complex_points[sorted_indices]
        complex_values = complex_values[sorted_indices]
        
        # 选择最佳点作为参考点
        reference_point = complex_points[0]
        
        # 生成新点
        new_complex = [reference_point]  # 保留最佳点
        for _ in range(1, self.points_per_complex):
            while True:
                # 随机选择两个不同的点进行差分进化
                a, b = self.rng.choice(self.points_per_complex, 2, replace=False)
                if a == 0 or b == 0:  # 避免使用参考点作为差分点
                    continue
                
                # 生成新候选点
                differential = complex_points[a] - complex_points[b]
                candidate = reference_point + 0.5 * differential
                
                # 限制在参数范围内
                candidate = np.clip(candidate, self.param_ranges[:, 0], self.param_ranges[:, 1])
                
                # 计算目标函数值
                candidate_value = self.objective_function(candidate)
                
                # 如果新点优于最差点，则接受
                if candidate_value < complex_values[-1]:
                    new_complex.append(candidate)
                    break
                else:
                    # 如果达到最大尝试次数，直接使用随机点
                    max_attempts = 10
                    if max_attempts > 0:
                        candidate = self.rng.uniform(self.param_ranges[:, 0], self.param_ranges[:, 1])
                        candidate_value = self.objective_function(candidate)
                        new_complex.append(candidate)
                        break
        
        # 添加随机点
        if self.rng.random() < self.p_random:
            random_point = self.rng.uniform(self.param_ranges[:, 0], self.param_ranges[:, 1])
            random_value = self.objective_function(random_point)
            new_complex[-1] = random_point  # 替换最后一个点为随机点
        
        return np.array(new_complex), np.array([self.objective_function(p) for p in new_complex])
    
    def run(self):
        """
        运行SCE-UA优化
        """
        logger.info("开始SCE-UA优化...")
        
        for generation in range(self.total_generations):
            # 打乱并分组种群
            complexes, complexes_values = self._shuffle_population()
            
            # 进化每个复杂体
            new_complexes = []
            new_complexes_values = []
            for i in range(self.n_complexes):
                complex_points = complexes[i]
                complex_values = complexes_values[i]
                
                new_complex, new_values = self._evolve_complex(complex_points, complex_values)
                new_complexes.append(new_complex)
                new_complexes_values.append(new_values)
            
            # 合并所有复杂体形成新的种群
            self.population = np.vstack(new_complexes)
            self.population_values = np.concatenate(new_complexes_values)
            
            # 更新最佳解
            current_best_idx = np.argmin(self.population_values)
            current_best_value = self.population_values[current_best_idx]
            current_best_point = self.population[current_best_idx]
            
            if current_best_value < self.best_value:
                self.best_value = current_best_value
                self.best_point = current_best_point
                logger.info(f"第 {generation+1} 代: 找到更优解, 最佳值: {self.best_value:.4f}")
            else:
                logger.info(f"第 {generation+1} 代: 最佳值无变化, 当前最佳: {self.best_value:.4f}")
            
            # 记录历史
            self.history.append({
                'generation': generation + 1,
                'best_value': self.best_value,
                'best_point': self.best_point.copy(),
                'population_values': self.population_values.copy()
            })
        
        logger.info("SCE-UA优化完成.")
        logger.info(f"最佳参数: {self.best_point}")
        logger.info(f"最佳目标值: {self.best_value:.4f}")
        
        return self.best_point, self.best_value
    
    def get_history(self):
        """
        获取优化历史记录
        """
        return self.history


if __name__ == "__main__":
    # 示例目标函数（用于测试）
    def sphere_function(x):
        return np.sum(x**2)
    
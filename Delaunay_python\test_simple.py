#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
from pathlib import Path

print("🎯 测试脚本启动")

try:
    # 添加项目路径
    sys.path.append(str(Path(__file__).parent))
    print("✅ 路径设置完成")
    
    from config import DelaunayConfig
    print("✅ 配置模块导入成功")
    
    config = DelaunayConfig()
    print(f"✅ 配置加载成功，输出目录: {config.OUTPUT_DIR}")
    
    # 获取洪水事件列表
    flood_events = config.get_flood_events()
    print(f"✅ 发现{len(flood_events)}个洪水事件")
    
    # 检查已完成的事件
    completed_count = 0
    incomplete_events = []
    
    for event_name in flood_events:
        event_dir = config.OUTPUT_DIR / event_name
        metrics_file = event_dir / f"{event_name}_metrics.csv"
        summary_file = event_dir / f"{event_name}_summary.json"
        
        if metrics_file.exists() and summary_file.exists():
            completed_count += 1
        else:
            incomplete_events.append(event_name)
    
    print(f"✅ 已完成事件: {completed_count}/{len(flood_events)}")
    print(f"🔄 需要处理事件: {len(incomplete_events)}个")
    
    if incomplete_events:
        print(f"待处理事件列表: {incomplete_events[:10]}...")  # 只显示前10个
    else:
        print("🎉 所有洪水事件已完成处理！")
    
    print("🎯 测试脚本完成")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    print(f"详细错误信息: {traceback.format_exc()}")

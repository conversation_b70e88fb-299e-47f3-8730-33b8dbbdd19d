"""
莫兰指数计算模块
实现局部和全局莫兰指数的计算，用于空间自相关性分析
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, Tuple, Optional
from scipy.spatial.distance import pdist, squareform

logger = logging.getLogger(__name__)


class MoranIndex:
    """莫兰指数计算器"""
    
    def __init__(self, config):
        """初始化莫兰指数计算器"""
        self.config = config
    
    def calculate_spatial_weights(self, stations_df: pd.DataFrame, 
                                method: str = 'inverse_distance') -> np.ndarray:
        """计算空间权重矩阵"""
        try:
            coords = stations_df[['经度', '纬度']].values
            n = len(coords)
            
            if method == 'inverse_distance':
                # 计算距离矩阵
                distances = squareform(pdist(coords))
                
                # 避免除零错误
                distances[distances == 0] = 1e-10
                
                # 计算逆距离权重
                weights = 1.0 / distances
                
                # 对角线设为0（站点与自身的权重为0）
                np.fill_diagonal(weights, 0)
                
            elif method == 'distance_band':
                # 距离带权重（在阈值内为1，超出为0）
                distances = squareform(pdist(coords))
                threshold = np.percentile(distances[distances > 0], 25)  # 使用25%分位数作为阈值
                
                weights = np.where((distances > 0) & (distances <= threshold), 1.0, 0.0)
                
            elif method == 'knn':
                # K近邻权重
                k = min(3, n - 1)  # 最多3个邻居
                distances = squareform(pdist(coords))
                weights = np.zeros_like(distances)
                
                for i in range(n):
                    # 找到k个最近邻居
                    neighbor_indices = np.argsort(distances[i])[1:k+1]  # 排除自身
                    weights[i, neighbor_indices] = 1.0
                    
            else:
                raise ValueError(f"不支持的权重计算方法: {method}")
            
            # 行标准化
            row_sums = weights.sum(axis=1)
            row_sums[row_sums == 0] = 1  # 避免除零
            weights = weights / row_sums[:, np.newaxis]
            
            return weights
            
        except Exception as e:
            logger.error(f"计算空间权重矩阵失败: {e}")
            # 返回单位矩阵作为备选
            n = len(stations_df)
            return np.eye(n)
    
    def calculate_global_moran_i(self, values: np.ndarray, weights: np.ndarray) -> Dict:
        """计算全局莫兰指数"""
        try:
            n = len(values)
            
            if n < 2:
                return {'moran_i': 0, 'expected_i': 0, 'variance': 0, 'z_score': 0, 'p_value': 1.0}
            
            # 计算均值
            mean_val = np.mean(values)
            
            # 计算偏差
            deviations = values - mean_val
            
            # 计算莫兰指数
            numerator = 0
            denominator = np.sum(deviations ** 2)
            
            for i in range(n):
                for j in range(n):
                    if i != j:
                        numerator += weights[i, j] * deviations[i] * deviations[j]
            
            # 计算权重总和
            w_sum = np.sum(weights)
            
            if denominator == 0 or w_sum == 0:
                return {'moran_i': 0, 'expected_i': 0, 'variance': 0, 'z_score': 0, 'p_value': 1.0}
            
            moran_i = (n / w_sum) * (numerator / denominator)
            
            # 计算期望值
            expected_i = -1 / (n - 1)
            
            # 计算方差（简化版本）
            variance = (n - 1) / ((n + 1) * w_sum)
            
            # 计算Z分数
            if variance > 0:
                z_score = (moran_i - expected_i) / np.sqrt(variance)
            else:
                z_score = 0
            
            # 计算p值（简化版本，使用标准正态分布）
            from scipy.stats import norm
            p_value = 2 * (1 - norm.cdf(abs(z_score)))
            
            return {
                'moran_i': moran_i,
                'expected_i': expected_i,
                'variance': variance,
                'z_score': z_score,
                'p_value': p_value
            }
            
        except Exception as e:
            logger.error(f"计算全局莫兰指数失败: {e}")
            return {'moran_i': 0, 'expected_i': 0, 'variance': 0, 'z_score': 0, 'p_value': 1.0}
    
    def calculate_local_moran_i(self, values: np.ndarray, weights: np.ndarray) -> np.ndarray:
        """计算局部莫兰指数"""
        try:
            n = len(values)
            
            if n < 2:
                return np.zeros(n)
            
            # 计算均值和标准差
            mean_val = np.mean(values)
            std_val = np.std(values)
            
            if std_val == 0:
                return np.zeros(n)
            
            # 标准化值
            z_values = (values - mean_val) / std_val
            
            # 计算局部莫兰指数
            local_moran = np.zeros(n)
            
            for i in range(n):
                # 计算邻居的加权平均
                weighted_neighbors = np.sum(weights[i] * z_values)
                
                # 局部莫兰指数
                local_moran[i] = z_values[i] * weighted_neighbors
            
            return local_moran
            
        except Exception as e:
            logger.error(f"计算局部莫兰指数失败: {e}")
            return np.zeros(len(values))
    
    def calculate_moran_for_time_point(self, stations_df: pd.DataFrame, 
                                     rainfall_at_time: Dict[str, float]) -> pd.DataFrame:
        """为特定时间点计算莫兰指数"""
        try:
            # 创建包含降雨数据的站点副本
            stations_with_rain = stations_df.copy()
            
            # 添加降雨数据
            rainfall_values = []
            for _, station in stations_with_rain.iterrows():
                station_name = station['站点']
                rainfall = rainfall_at_time.get(station_name, 0.0)
                rainfall_values.append(rainfall)
            
            stations_with_rain['降雨量'] = rainfall_values
            
            # 检查是否有足够的变异性来计算莫兰指数
            unique_values = len(set(rainfall_values))
            if unique_values <= 1:
                # 所有值相同，莫兰指数为0
                stations_with_rain['莫兰指数'] = 0.0
                stations_with_rain['全局莫兰指数'] = 0.0
                return stations_with_rain
            
            # 计算空间权重矩阵
            weights = self.calculate_spatial_weights(stations_with_rain, method='inverse_distance')
            
            # 计算全局莫兰指数
            global_moran = self.calculate_global_moran_i(np.array(rainfall_values), weights)
            
            # 计算局部莫兰指数
            local_moran = self.calculate_local_moran_i(np.array(rainfall_values), weights)
            
            # 添加结果到DataFrame
            stations_with_rain['莫兰指数'] = local_moran
            stations_with_rain['全局莫兰指数'] = global_moran['moran_i']
            
            # 添加全局莫兰指数的统计信息
            stations_with_rain['全局莫兰_期望'] = global_moran['expected_i']
            stations_with_rain['全局莫兰_Z分数'] = global_moran['z_score']
            stations_with_rain['全局莫兰_P值'] = global_moran['p_value']
            
            return stations_with_rain
            
        except Exception as e:
            logger.error(f"计算时间点莫兰指数失败: {e}")
            # 返回原始数据，莫兰指数设为0
            stations_with_rain = stations_df.copy()
            stations_with_rain['降雨量'] = [rainfall_at_time.get(station['站点'], 0.0) 
                                        for _, station in stations_with_rain.iterrows()]
            stations_with_rain['莫兰指数'] = 0.0
            stations_with_rain['全局莫兰指数'] = 0.0
            return stations_with_rain
    
    def analyze_spatial_autocorrelation(self, stations_df: pd.DataFrame, 
                                      rainfall_data: Dict) -> Dict:
        """分析整个时间序列的空间自相关性"""
        try:
            logger.info("正在分析空间自相关性...")
            
            # 获取所有时间点
            all_times = set()
            for station_data in rainfall_data.values():
                all_times.update(station_data['时间'])
            all_times = sorted(list(all_times))
            
            # 存储结果
            global_moran_series = []
            local_moran_stats = []
            
            # 计算空间权重矩阵（只计算一次）
            weights = self.calculate_spatial_weights(stations_df, method='inverse_distance')
            
            for time_point in all_times:
                # 获取该时间点的降雨数据
                rainfall_values = []
                for _, station in stations_df.iterrows():
                    station_name = station['站点']
                    if station_name in rainfall_data:
                        station_data = rainfall_data[station_name]
                        time_mask = station_data['时间'] == time_point
                        if time_mask.any():
                            rainfall = station_data.loc[time_mask, '雨量'].iloc[0]
                        else:
                            rainfall = 0.0
                    else:
                        rainfall = 0.0
                    rainfall_values.append(rainfall)
                
                # 计算全局莫兰指数
                global_moran = self.calculate_global_moran_i(np.array(rainfall_values), weights)
                global_moran['time'] = time_point
                global_moran_series.append(global_moran)
                
                # 计算局部莫兰指数统计
                local_moran = self.calculate_local_moran_i(np.array(rainfall_values), weights)
                local_stats = {
                    'time': time_point,
                    'local_moran_mean': np.mean(local_moran),
                    'local_moran_std': np.std(local_moran),
                    'local_moran_min': np.min(local_moran),
                    'local_moran_max': np.max(local_moran)
                }
                local_moran_stats.append(local_stats)
            
            # 计算总体统计
            global_moran_values = [gm['moran_i'] for gm in global_moran_series]
            
            summary = {
                'global_moran_series': global_moran_series,
                'local_moran_stats': local_moran_stats,
                'overall_stats': {
                    'global_moran_mean': np.mean(global_moran_values),
                    'global_moran_std': np.std(global_moran_values),
                    'global_moran_min': np.min(global_moran_values),
                    'global_moran_max': np.max(global_moran_values),
                    'positive_autocorr_ratio': np.mean([gm > 0 for gm in global_moran_values]),
                    'significant_autocorr_ratio': np.mean([abs(gm) > 0.1 for gm in global_moran_values])
                }
            }
            
            logger.info(f"空间自相关性分析完成，全局莫兰指数均值: {summary['overall_stats']['global_moran_mean']:.4f}")
            
            return summary
            
        except Exception as e:
            logger.error(f"分析空间自相关性失败: {e}")
            return {}
    
    def get_moran_weight(self, test_moran: float, neighbor_morans: np.ndarray) -> float:
        """基于莫兰指数计算权重因子"""
        try:
            if len(neighbor_morans) == 0:
                return 1.0
            
            # 计算莫兰指数差异
            moran_diffs = np.abs(neighbor_morans - test_moran)
            
            # 如果所有差异都为0，返回1
            if np.all(moran_diffs == 0):
                return 1.0
            
            # 计算相似性权重（差异越小，权重越大）
            max_diff = np.max(moran_diffs)
            if max_diff > 0:
                similarity = 1.0 - (moran_diffs / max_diff)
                return np.mean(similarity)
            else:
                return 1.0
                
        except Exception as e:
            logger.warning(f"计算莫兰指数权重失败: {e}")
            return 1.0

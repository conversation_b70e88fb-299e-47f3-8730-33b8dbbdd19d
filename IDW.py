# -*- coding: utf-8 -*-
"""
基于IDW方法的水文点雨量转面雨量空间插值（留一法验证）
仅使用10公里范围内的站点
"""

import os
import time
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
import multiprocessing as mp
import warnings
import re
from tqdm import tqdm
import matplotlib.colors as mcolors
import gc
import rasterio
from rasterio.transform import from_origin

# 忽略警告
warnings.filterwarnings('ignore')

# 设置工作路径
base_path = 'D:/pythondata/spatial_interpolation/'
input_path = os.path.join(base_path, 'input')
terrain_path = os.path.join(base_path, 'terrain')
output_path = os.path.join(base_path, 'output/IDW_10km')
stations_file = os.path.join(base_path, 'stations.csv')

# 创建输出目录
os.makedirs(output_path, exist_ok=True)

# 清理文件名的特殊字符
def clean_filename(filename):
    """清理文件名，移除非法字符"""
    filename = filename.replace(':', '_').replace(' ', '_')
    filename = re.sub(r'[\\/*?:"<>|]', '', filename)
    return filename

# 计算两点之间的地理距离（公里）
def haversine_distance(lon1, lat1, lon2, lat2):
    """
    计算两个经纬度坐标之间的地理距离（公里）
    使用haversine公式
    
    参数:
        lon1, lat1: 第一个点的经度和纬度
        lon2, lat2: 第二个点的经度和纬度
    
    返回:
        两点之间的距离，单位为公里
    """
    # 将角度转换为弧度
    lon1, lat1, lon2, lat2 = map(np.radians, [lon1, lat1, lon2, lat2])
    
    # haversine公式
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2
    c = 2 * np.arcsin(np.sqrt(a))
    r = 6371  # 地球平均半径，单位为公里
    return c * r

# 加载站点信息
def load_stations():
    """加载站点信息数据"""
    try:
        stations_df = pd.read_csv(stations_file, encoding='utf-8')
        print(f"站点信息表头: {stations_df.columns.tolist()}")
        
        required_columns = ['站点', '经度', '纬度']
        for col in required_columns:
            if col not in stations_df.columns:
                raise ValueError(f"站点信息缺少必要列：{col}")
        
        print(f"成功加载{len(stations_df)}个站点信息")
        return stations_df
    except Exception as e:
        print(f"加载站点信息时出错：{e}")
        raise

# 从DEM中提取站点高程信息
def extract_elevation_from_dem(stations_df, dem_file=None):
    """从DEM文件中提取站点的高程信息，对于超出范围的站点使用最近的有效高程值"""
    if dem_file is None:
        dem_files = [f for f in os.listdir(terrain_path) if f.lower().endswith('.asc') and 'dem' in f.lower()]
        if dem_files:
            dem_file = os.path.join(terrain_path, dem_files[0])
        else:
            print("警告：未找到DEM文件，将无法提取高程信息")
            stations_df['高程'] = 0
            return stations_df
    
    try:
        with rasterio.open(dem_file) as dem:
            transform = dem.transform
            dem_data = dem.read(1)
            nodata_value = dem.nodata
            
            print(f"DEM信息: 宽度={dem.width}, 高度={dem.height}, NoData值={nodata_value}")
            
            elevations = []
            out_of_bounds_stations = []
            
            for idx, station in stations_df.iterrows():
                x, y = station['经度'], station['纬度']
                row, col = ~transform * (x, y)
                row, col = int(row), int(col)
                
                if (0 <= row < dem.height and 0 <= col < dem.width and 
                    dem_data[row, col] != nodata_value):
                    elevation = dem_data[row, col]
                    elevations.append(elevation)
                else:
                    print(f"警告：站点{station['站点']}的坐标({x}, {y})超出DEM范围或为无效值，将查找最近的有效高程")
                    out_of_bounds_stations.append((idx, x, y, station['站点']))
                    elevations.append(np.nan)
            
            if out_of_bounds_stations:
                valid_points = []
                valid_values = []
                
                sample_step = max(1, min(dem.width, dem.height) // 100)
                print(f"使用采样步长: {sample_step}采样有效高程点")
                
                for row in range(0, dem.height, sample_step):
                    for col in range(0, dem.width, sample_step):
                        if dem_data[row, col] != nodata_value:
                            lon, lat = transform * (col, row)
                            valid_points.append((lon, lat))
                            valid_values.append(dem_data[row, col])
                
                print(f"找到{len(valid_points)}个有效高程采样点")
                
                if not valid_points:
                    print("错误：DEM中没有找到有效高程点！")
                    stations_df['高程'] = 0
                    return stations_df
                
                for idx, x, y, station_id in out_of_bounds_stations:
                    distances = [np.sqrt((p[0]-x)**2 + (p[1]-y)**2) for p in valid_points]
                    nearest_idx = np.argmin(distances)
                    nearest_elevation = valid_values[nearest_idx]
                    nearest_point = valid_points[nearest_idx]
                    
                    elevations[idx] = nearest_elevation
                    print(f"  站点{station_id}使用位于({nearest_point[0]:.6f}, {nearest_point[1]:.6f})的最近有效高程值: {nearest_elevation}")
            
            stations_df['高程'] = elevations
            
            if stations_df['高程'].isna().any():
                nan_count = stations_df['高程'].isna().sum()
                valid_mean = stations_df['高程'].dropna().mean()
                print(f"警告：仍有{nan_count}个站点无法获取高程信息，使用有效点的平均高程{valid_mean:.2f}代替")
                stations_df['高程'].fillna(valid_mean, inplace=True)
            
            return stations_df
    except Exception as e:
        print(f"从DEM提取高程时出错：{e}")
        import traceback
        traceback.print_exc()
        stations_df['高程'] = 0
        return stations_df

# 加载雨量站点数据
def load_rainfall_data(station_ids):
    """加载所有站点的雨量数据"""
    rainfall_data = {}
    
    for station_id in station_ids:
        try:
            file_path = os.path.join(input_path, f"{station_id}.csv")
            if os.path.exists(file_path):
                df = pd.read_csv(file_path, encoding='utf-8')
                
                if '时间' not in df.columns or '雨量' not in df.columns:
                    print(f"警告：站点{station_id}的数据缺少必要列（时间或雨量）")
                    continue
                
                try:
                    df['时间'] = pd.to_datetime(df['时间'])
                except:
                    print(f"警告：站点{station_id}的时间列格式不正确，尝试其他解析方式")
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S', 
                                '%Y-%m-%d %H:%M', '%Y/%m/%d %H:%M']:
                        try:
                            df['时间'] = pd.to_datetime(df['时间'], format=fmt)
                            break
                        except:
                            continue
                
                df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce')
                df.dropna(subset=['雨量'], inplace=True)
                
                rainfall_data[station_id] = df
                print(f"成功加载站点{station_id}的雨量数据，共{len(df)}条记录")
            else:
                print(f"警告：未找到站点{station_id}的雨量数据文件")
        except Exception as e:
            print(f"加载站点{station_id}的雨量数据时出错：{e}")
    
    return rainfall_data

# 计算IDW权重
def calculate_idw_weights(target_point, points, p=2, distances=None):
    """
    计算IDW(反距离加权)插值的权重
    
    参数:
        target_point: 目标点坐标 [lon, lat]
        points: 邻居点坐标数组 [[lon1, lat1], [lon2, lat2], ...]
        p: 距离衰减的幂次，默认为2
        distances: 预先计算的距离，如果为None则重新计算
    
    返回:
        各邻居点的权重数组
    """
    target_point = np.array(target_point)
    points = np.array(points)
    
    # 如果没有提供预先计算的距离，则计算
    if distances is None:
        # 计算目标点到各站点的距离
        distances = np.array([
            haversine_distance(target_point[0], target_point[1], point[0], point[1])
            for point in points
        ])
    
    # 处理距离为0的情况（当目标点与某个站点重合时）
    if np.any(distances < 1e-10):
        weights = np.zeros_like(distances)
        weights[distances < 1e-10] = 1.0
        return weights
    
    # 计算权重：距离的负p次方
    weights = 1.0 / (distances ** p)
    
    # 归一化权重，使其总和为1
    weights = weights / np.sum(weights)
    
    return weights

# 可视化IDW插值示例（使用距离限制）
def visualize_idw_example(stations_df, output_file=None, max_distance=10.0):
    """
    可视化IDW插值示例，显示几个测试点的权重分布，仅使用指定距离内的站点
    
    参数:
        stations_df: 站点信息DataFrame
        output_file: 输出文件路径
        max_distance: 最大距离阈值（公里）
    """
    plt.figure(figsize=(15, 12))
    
    # 提取站点坐标
    points = stations_df[['经度', '纬度']].values
    
    # 随机选择几个示例站点
    sample_size = min(5, len(stations_df))
    sample_indices = np.random.choice(len(stations_df), sample_size, replace=False)
    
    # 生成随机颜色
    colors = list(mcolors.TABLEAU_COLORS.values())
    
    # 保存权重信息的列表，用于后续生成表格
    weight_info = []
    
    # 绘制所有站点
    plt.scatter(points[:, 0], points[:, 1], c='gray', s=30, alpha=0.5)
    
    # 为每个站点显示ID
    for idx, station in stations_df.iterrows():
        plt.text(station['经度'], station['纬度'] + 0.01, 
                 f"{station['站点']}", ha='center', va='bottom', fontsize=8, alpha=0.7)
    
    # 为每个示例站点绘制IDW权重分布
    for i, sample_idx in enumerate(sample_indices):
        test_station = stations_df.iloc[sample_idx]
        test_station_id = test_station['站点']
        test_point = [test_station['经度'], test_station['纬度']]
        
        # 创建不包含测试站点的临时站点集
        temp_stations = []
        temp_station_ids = []
        temp_distances = []
        
        for idx, station in stations_df.iterrows():
            if idx != sample_idx:  # 排除测试站点自身
                station_id = station['站点']
                station_point = [station['经度'], station['纬度']]
                
                # 计算地理距离
                distance = haversine_distance(
                    test_point[0], test_point[1],
                    station_point[0], station_point[1]
                )
                
                # 只包含在指定距离内的站点
                if distance <= max_distance:
                    temp_stations.append(station_point)
                    temp_station_ids.append(station_id)
                    temp_distances.append(distance)
        
        # 如果没有找到有效的邻居站点，跳过
        if not temp_stations:
            print(f"警告：站点{test_station_id}在{max_distance}公里范围内没有邻居站点")
            continue
        
        # 将列表转换为numpy数组
        temp_stations = np.array(temp_stations)
        temp_distances = np.array(temp_distances)
        
        # 计算IDW权重
        weights = calculate_idw_weights(test_point, temp_stations, distances=temp_distances)
        
        # 随机选择一种颜色
        color = colors[i % len(colors)]
        
        # 绘制测试站点
        plt.scatter(test_point[0], test_point[1], c='red', s=80, 
                    edgecolor='black', alpha=0.7)
        
        # 标注测试站点
        plt.text(test_point[0], test_point[1] + 0.02, 
                 f"{test_station_id} (测试)", ha='center', va='bottom', fontsize=9)
        
        # 绘制距离圈（10公里范围）
        circle = plt.Circle((test_point[0], test_point[1]), max_distance/111.32, 
                          fill=False, color=color, linestyle='--', alpha=0.5)
        plt.gca().add_patch(circle)
        
        # 只显示权重最大的几个站点的连接线，避免图形过于复杂
        top_k = min(5, len(weights))
        top_indices = np.argsort(weights)[-top_k:]
        
        for rank, idx in enumerate(top_indices):
            neighbor_point = temp_stations[idx]
            neighbor_id = temp_station_ids[idx]
            distance = temp_distances[idx]
            weight = weights[idx]
            
            # 绘制连接线，线宽与权重成正比
            plt.plot([test_point[0], neighbor_point[0]], 
                     [test_point[1], neighbor_point[1]], 
                     '--', color=color, alpha=0.7, linewidth=1 + 3 * weight)
            
            # 标注权重
            mid_x = (test_point[0] + neighbor_point[0]) / 2
            mid_y = (test_point[1] + neighbor_point[1]) / 2
            plt.text(mid_x, mid_y, f"{weight:.3f}", 
                     color=color, fontsize=8, ha='center', va='center',
                     bbox=dict(facecolor='white', alpha=0.7, pad=2))
            
            # 高亮相关的站点
            plt.scatter(neighbor_point[0], neighbor_point[1], c='blue', s=50, 
                       edgecolor='black', alpha=0.7)
            
            # 保存权重信息
            weight_info.append({
                '测试站点': test_station_id,
                '邻近站点': neighbor_id,
                '距离(公里)': distance,
                '权重': weight,
                '排名': rank + 1
            })
        
        # 添加有效站点数量信息
        plt.text(test_point[0], test_point[1] - 0.02, 
                 f"有效站点: {len(temp_stations)}/{len(stations_df)-1}", 
                 ha='center', va='top', fontsize=8,
                 bbox=dict(facecolor='white', alpha=0.7, pad=2))
    
    # 设置图表标题和标签
    plt.title(f'IDW插值权重示例 (距离限制: {max_distance}公里)')
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 创建图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='red', edgecolor='black', alpha=0.7, label='测试站点'),
        Patch(facecolor='blue', edgecolor='black', alpha=0.7, label='有效邻近站点'),
        Patch(facecolor='gray', edgecolor='black', alpha=0.5, label='其他站点')
    ]
    plt.legend(handles=legend_elements, loc='best')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"IDW插值权重示例已保存至: {output_file}")
    
    plt.close()
    
    # 将权重信息保存为表格
    weight_df = pd.DataFrame(weight_info)
    return weight_df

# 留一法验证函数（使用距离限制的IDW）
def leave_one_out_validation(timestamp, stations_df, rainfall_data, max_distance=10.0):
    """
    使用留一法和距离限制的IDW方法对特定时间戳的降雨数据进行插值和验证
    
    参数:
        timestamp: 时间戳
        stations_df: 站点信息DataFrame
        rainfall_data: 站点雨量数据字典
        max_distance: 最大距离阈值（公里）
    
    返回:
        包含真实值和预测值的DataFrame
    """
    results = []
    
    # 获取此时间戳下有降雨数据的站点
    available_stations = {}
    for station_id, df in rainfall_data.items():
        if timestamp in df['时间'].values:
            rain_value = df.loc[df['时间'] == timestamp, '雨量'].values[0]
            if not np.isnan(rain_value):
                available_stations[station_id] = rain_value
    
    # 如果可用站点不足，返回空结果
    if len(available_stations) < 2:  # 至少需要2个站点才能进行留一法
        print(f"时间戳{timestamp}下的可用站点不足2个，跳过")
        return pd.DataFrame()
    
    # 对每个有数据的站点进行留一法验证
    for test_station_id in available_stations.keys():
        test_station_info = stations_df[stations_df['站点'] == test_station_id]
        if test_station_info.empty:
            print(f"警告：找不到站点{test_station_id}的信息，跳过")
            continue
            
        test_station_info = test_station_info.iloc[0]
        test_point = [test_station_info['经度'], test_station_info['纬度']]
        
        try:
            # 构建不包含测试站点的临时数据集，并筛选距离
            temp_stations = []
            temp_values = []
            temp_station_ids = []
            temp_distances = []
            
            for station_id, rain_value in available_stations.items():
                if station_id != test_station_id:
                    station_info = stations_df[stations_df['站点'] == station_id]
                    if not station_info.empty:
                        station_info = station_info.iloc[0]
                        station_lon = station_info['经度']
                        station_lat = station_info['纬度']
                        
                        # 计算与测试站点的距离
                        distance = haversine_distance(
                            test_point[0], test_point[1], 
                            station_lon, station_lat
                        )
                        
                        # 只包含在指定距离内的站点
                        if distance <= max_distance:
                            temp_stations.append([station_lon, station_lat])
                            temp_values.append(rain_value)
                            temp_station_ids.append(station_id)
                            temp_distances.append(distance)
            
            # 如果可用站点不足，跳过
            if len(temp_stations) < 1:
                print(f"时间戳{timestamp}下，站点{test_station_id}的在{max_distance}公里范围内没有可用站点，跳过")
                continue
            
            # 计算IDW权重
            weights = calculate_idw_weights(test_point, temp_stations, distances=temp_distances)
            
            # 使用权重进行插值预测
            predicted_value = np.sum(weights * np.array(temp_values))
            
            # 获取真实值
            true_value = available_stations[test_station_id]
            
            # 保存结果
            results.append({
                '时间': timestamp,
                '站点': test_station_id,
                '真实值': true_value,
                '预测值': predicted_value,
                '站点数量': len(temp_stations),
                '总站点数': len(available_stations) - 1,
                '平均距离(公里)': np.mean(temp_distances) if temp_distances else np.nan,
                '最近距离(公里)': np.min(temp_distances) if temp_distances else np.nan,
                '最远距离(公里)': np.max(temp_distances) if temp_distances else np.nan
            })
            
        except Exception as e:
            print(f"站点{test_station_id}在时间戳{timestamp}的留一法验证时出错：{e}")
            import traceback
            traceback.print_exc()
    
    return pd.DataFrame(results)

# 计算评价指标
def calculate_metrics(validation_results):
    """计算插值结果的评价指标"""
    if validation_results.empty:
        return {
            'MAE': np.nan,
            'RMSE': np.nan,
            'R2': np.nan,
            'NSE': np.nan
        }
    
    true_values = validation_results['真实值'].values
    predicted_values = validation_results['预测值'].values
    
    # 平均绝对误差
    mae = mean_absolute_error(true_values, predicted_values)
    
    # 均方根误差
    rmse = np.sqrt(mean_squared_error(true_values, predicted_values))
    
    # 决定系数R²
    r2 = r2_score(true_values, predicted_values)
    
    # 纳什系数
    mean_true = np.mean(true_values)
    numerator = np.sum((true_values - predicted_values) ** 2)
    denominator = np.sum((true_values - mean_true) ** 2)
    nse = 1 - (numerator / denominator) if denominator != 0 else np.nan
    
    return {
        'MAE': mae,
        'RMSE': rmse,
        'R2': r2,
        'NSE': nse
    }

# 生成完整时间序列
def generate_complete_timeseries(all_validation_results, rainfall_data):
    """根据留一法验证结果，生成每个站点的完整降雨量时间序列"""
    # 获取所有站点
    all_stations = set()
    for df in all_validation_results.values():
        if not df.empty:
            all_stations.update(df['站点'].unique())
    
    # 获取所有时间戳
    all_timestamps = set()
    for df in rainfall_data.values():
        all_timestamps.update(df['时间'].unique())
    all_timestamps = sorted(all_timestamps)
    
    # 为每个站点创建完整的时间序列
    complete_series = {}
    
    for station_id in all_stations:
        # 创建一个包含所有时间戳的DataFrame
        station_data = pd.DataFrame({'时间': all_timestamps})
        station_data['雨量'] = np.nan
        
        # 首先填入原始数据
        if station_id in rainfall_data:
            original_data = rainfall_data[station_id]
            for _, row in original_data.iterrows():
                station_data.loc[station_data['时间'] == row['时间'], '雨量'] = row['雨量']
        
        # 然后填入插值结果
        for timestamp, df in all_validation_results.items():
            if not df.empty:
                station_results = df[df['站点'] == station_id]
                if not station_results.empty:
                    for _, row in station_results.iterrows():
                        station_data.loc[station_data['时间'] == row['时间'], '雨量'] = row['预测值']
        
        # 保存完整的时间序列
        complete_series[station_id] = station_data
    
    return complete_series

# 创建雨量栅格（使用距离限制）
def create_rainfall_grid(timestamp, stations_df, rainfall_data, mask_file, output_file, max_distance=10.0):
    """
    为特定时间戳创建流域雨量栅格，使用距离限制的IDW方法
    
    参数:
        timestamp: 时间戳
        stations_df: 站点信息DataFrame
        rainfall_data: 站点雨量数据字典
        mask_file: 流域掩码文件路径
        output_file: 输出栅格文件路径
        max_distance: 最大距离阈值（公里）
    """
    try:
        # 读取流域掩码
        with rasterio.open(mask_file) as mask_src:
            mask = mask_src.read(1)
            transform = mask_src.transform
            crs = mask_src.crs
            
            # 获取栅格的范围
            height, width = mask.shape
            
            # 创建一个空的雨量栅格
            rainfall_grid = np.zeros_like(mask, dtype=np.float32)
            rainfall_grid.fill(np.nan)
            
            # 获取此时间戳的可用站点雨量数据
            station_points = []
            station_values = []
            station_ids = []
            
            for station_id, df in rainfall_data.items():
                if timestamp in df['时间'].values:
                    rain_value = df.loc[df['时间'] == timestamp, '雨量'].values[0]
                    if not np.isnan(rain_value):
                        station_info = stations_df[stations_df['站点'] == station_id]
                        if not station_info.empty:
                            station_info = station_info.iloc[0]
                            station_points.append([station_info['经度'], station_info['纬度']])
                            station_values.append(rain_value)
                            station_ids.append(station_id)
            
            # 如果可用站点太少，则不生成栅格
            if len(station_points) < 2:
                print(f"时间戳{timestamp}的可用站点不足2个，跳过栅格生成")
                return False
            
            # 转换为numpy数组
            station_points = np.array(station_points)
            station_values = np.array(station_values)
            
            # 对流域内的每个像素进行IDW插值，按块处理以减少内存使用
            chunk_size = 100  # 每次处理100行以减少内存使用
            for y_start in range(0, height, chunk_size):
                y_end = min(y_start + chunk_size, height)
                
                for y in range(y_start, y_end):
                    for x in range(width):
                        if mask[y, x] > 0:  # 只处理流域内的像素
                            # 获取像素的地理坐标
                            lon, lat = transform * (x + 0.5, y + 0.5)
                            pixel_point = [lon, lat]
                            
                            # 筛选距离以内的站点
                            valid_points = []
                            valid_values = []
                            valid_distances = []
                            
                            for i, (station_point, value) in enumerate(zip(station_points, station_values)):
                                distance = haversine_distance(
                                    lon, lat, 
                                    station_point[0], station_point[1]
                                )
                                if distance <= max_distance:
                                    valid_points.append(station_point)
                                    valid_values.append(value)
                                    valid_distances.append(distance)
                            
                            # 如果没有有效站点，使用最近的站点
                            if not valid_points:
                                distances = [haversine_distance(lon, lat, p[0], p[1]) for p in station_points]
                                nearest_idx = np.argmin(distances)
                                rainfall_grid[y, x] = station_values[nearest_idx]
                            else:
                                # 计算IDW权重
                                weights = calculate_idw_weights(
                                    pixel_point, valid_points, distances=valid_distances
                                )
                                
                                # 使用权重计算插值结果
                                rainfall_grid[y, x] = np.sum(weights * np.array(valid_values))
            
            # 写入栅格文件
            with rasterio.open(
                output_file,
                'w',
                driver='AAIGrid',
                height=height,
                width=width,
                count=1,
                dtype=rainfall_grid.dtype,
                crs=crs,
                transform=transform,
                nodata=-9999
            ) as dst:
                # 将NaN替换为NoData值
                rainfall_grid_out = rainfall_grid.copy()
                rainfall_grid_out[np.isnan(rainfall_grid_out)] = -9999
                dst.write(rainfall_grid_out, 1)
            
            print(f"时间戳{timestamp}的雨量栅格已保存至: {output_file}")
            return True
            
    except Exception as e:
        print(f"创建雨量栅格时出错：{e}")
        return False

# 处理单个时间戳的函数，用于并行计算
def process_timestamp(args):
    """处理单个时间戳的插值任务，用于并行计算"""
    timestamp, stations_df, rainfall_data, create_grid, mask_file, output_path, max_distance = args
    
    try:
        # 执行留一法验证
        validation_results = leave_one_out_validation(
            timestamp, stations_df, rainfall_data, max_distance
        )
        
        # 如果启用栅格创建且有验证结果
        if create_grid and not validation_results.empty:
            os.makedirs(os.path.join(output_path, 'grids'), exist_ok=True)
            grid_output_file = os.path.join(output_path, 'grids', f"rainfall_{clean_filename(str(timestamp))}.asc")
            create_rainfall_grid(
                timestamp, stations_df, rainfall_data, mask_file, grid_output_file, max_distance
            )
        
        return timestamp, validation_results
    
    except Exception as e:
        print(f"处理时间戳{timestamp}时出错：{e}")
        return timestamp, pd.DataFrame()

# 分批处理时间序列
def process_batches(all_timestamps, stations_df, rainfall_data, create_grid, mask_file, output_path, max_distance=10.0, batch_size=100):
    """分批处理时间序列以减少内存占用"""
    # 获取CPU核心数，但最多使用12个
    num_cores = min(12, mp.cpu_count())
    print(f"使用{num_cores}个CPU核心进行并行计算")
    
    # 将时间戳分成多个批次
    total_batches = (len(all_timestamps) + batch_size - 1) // batch_size
    print(f"将{len(all_timestamps)}个时间戳分成{total_batches}个批次处理，每批{batch_size}个")
    
    all_validation_results = {}
    
    # 逐批处理
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(all_timestamps))
        batch_timestamps = all_timestamps[start_idx:end_idx]
        
        print(f"\n处理第{batch_idx+1}/{total_batches}批，共{len(batch_timestamps)}个时间戳")
        
        # 准备并行处理的参数
        process_args = [
            (timestamp, stations_df, rainfall_data, create_grid, mask_file, output_path, max_distance) 
            for timestamp in batch_timestamps
        ]
        
        # 使用并行处理当前批次
        batch_results = {}
        with mp.Pool(num_cores) as pool, tqdm(total=len(batch_timestamps)) as pbar:
            for timestamp, results in pool.imap_unordered(process_timestamp, process_args):
                batch_results[timestamp] = results
                pbar.update()
        
        # 合并当前批次的结果
        all_validation_results.update(batch_results)
        
        # 保存当前批次的结果
        batch_df = pd.concat(batch_results.values()) if batch_results else pd.DataFrame()
        if not batch_df.empty:
            batch_file = os.path.join(output_path, f'validation_results_batch_{batch_idx+1}.csv')
            batch_df.to_csv(batch_file, index=False, encoding='utf-8')
            print(f"第{batch_idx+1}批验证结果已保存至: {batch_file}")
        
        # 手动清理内存
        del batch_results, batch_df
        gc.collect()
    
    return all_validation_results

# 可视化评估结果
def visualize_validation_results(validation_results, output_file=None):
    """可视化插值评估结果"""
    if validation_results.empty:
        print("没有验证结果可供可视化")
        return
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    # 1. 真实值vs预测值的散点图
    axes[0].scatter(validation_results['真实值'], validation_results['预测值'], 
                alpha=0.6, s=30, c='blue', edgecolor='k')
    
    # 添加一条y=x的参考线
    min_val = min(validation_results['真实值'].min(), validation_results['预测值'].min())
    max_val = max(validation_results['真实值'].max(), validation_results['预测值'].max())
    
    # 保证范围有一定边界
    range_val = max_val - min_val
    min_val = max(0, min_val - range_val * 0.05)
    max_val = max_val + range_val * 0.05
    
    axes[0].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
    
    axes[0].set_title('真实值 vs 预测值')
    axes[0].set_xlabel('真实降雨量')
    axes[0].set_ylabel('预测降雨量')
    axes[0].grid(True, linestyle='--', alpha=0.7)
    
    # 计算并显示评价指标
    metrics = calculate_metrics(validation_results)
    metrics_text = (
        f"评价指标:\n"
        f"MAE: {metrics['MAE']:.4f}\n"
        f"RMSE: {metrics['RMSE']:.4f}\n"
        f"R²: {metrics['R2']:.4f}\n"
        f"NSE: {metrics['NSE']:.4f}"
    )
    axes[0].text(0.05, 0.95, metrics_text, transform=axes[0].transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # 2. 站点数量分布直方图
    if '站点数量' in validation_results.columns:
        axes[1].hist(validation_results['站点数量'], bins=np.arange(0, 20), alpha=0.7)
        axes[1].set_title('参与插值的站点数量分布')
        axes[1].set_xlabel('站点数量')
        axes[1].set_ylabel('频次')
        axes[1].grid(True, linestyle='--', alpha=0.7)
    
    # 3. 平均距离与误差的关系
    if '平均距离(公里)' in validation_results.columns:
        abs_error = np.abs(validation_results['真实值'] - validation_results['预测值'])
        axes[2].scatter(validation_results['平均距离(公里)'], abs_error, alpha=0.6, s=30)
        axes[2].set_title('平均距离与绝对误差的关系')
        axes[2].set_xlabel('平均距离(公里)')
        axes[2].set_ylabel('绝对误差')
        axes[2].grid(True, linestyle='--', alpha=0.7)
    
    # 4. 站点数量与误差的关系
    if '站点数量' in validation_results.columns:
        axes[3].scatter(validation_results['站点数量'], abs_error, alpha=0.6, s=30)
        axes[3].set_title('站点数量与绝对误差的关系')
        axes[3].set_xlabel('站点数量')
        axes[3].set_ylabel('绝对误差')
        axes[3].grid(True, linestyle='--', alpha=0.7)
    
    # 添加总标题
    fig.suptitle('IDW插值验证结果分析（使用10公里范围内的站点）', fontsize=16)
    
    # 调整布局
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"验证结果可视化已保存至: {output_file}")
    
    plt.close()

# 主函数
def main():
    """主函数，执行整个IDW空间插值流程"""
    print("开始执行距离限制的IDW直接空间插值...")
    start_time = time.time()
    
    # 设置距离阈值（公里）
    max_distance = 10.0
    print(f"使用{max_distance}公里范围内的站点进行插值")
    
    try:
        # 加载站点信息
        stations_df = load_stations()
        
        # 提取站点高程信息
        dem_file = os.path.join(terrain_path, 'dem.asc')
        if os.path.exists(dem_file):
            stations_df = extract_elevation_from_dem(stations_df, dem_file)
        else:
            print("未找到DEM文件，将不提取高程信息")
        
        # 获取站点ID列表
        station_ids = stations_df['站点'].unique()
        
        # 加载雨量数据
        rainfall_data = load_rainfall_data(station_ids)
        
        # 检查是否成功加载数据
        if not rainfall_data:
            raise ValueError("未能加载任何雨量数据，请检查输入文件")
        
        # 生成IDW插值示例图
        idw_output_file = os.path.join(output_path, f'idw_interpolation_example_{int(max_distance)}km.png')
        weight_df = visualize_idw_example(stations_df, idw_output_file, max_distance)
        
        # 保存权重信息表格
        weight_table_file = os.path.join(output_path, 'idw_weight_info.csv')
        weight_df.to_csv(weight_table_file, index=False, encoding='utf-8')
        print(f"IDW权重信息表格已保存至: {weight_table_file}")
        
        # 获取所有唯一的时间戳
        all_timestamps = set()
        for df in rainfall_data.values():
            all_timestamps.update(df['时间'].unique())
        all_timestamps = sorted(all_timestamps)
        
        print(f"共有{len(all_timestamps)}个时间戳需要处理")
        
        # 询问用户是否创建栅格
        create_grid = input("是否为每个时间戳创建流域雨量栅格？(y/n): ").lower() == 'y'
        
        # 询问用户批次大小
        try:
            batch_size = int(input("请输入每批处理的时间戳数量（推荐值：50-200）: "))
            if batch_size <= 0:
                batch_size = 100
                print(f"输入无效，使用默认批次大小：{batch_size}")
        except:
            batch_size = 100
            print(f"输入无效，使用默认批次大小：{batch_size}")
        
        # 流域掩码文件路径
        mask_file = os.path.join(terrain_path, 'mask.asc')
        
        # 分批处理时间序列
        all_validation_results = process_batches(
            all_timestamps, stations_df, rainfall_data, 
            create_grid, mask_file, output_path, max_distance, batch_size
        )
        
        # 合并所有验证结果
        all_results_df = pd.concat([df for df in all_validation_results.values() if not df.empty])
        
        if not all_results_df.empty:
            # 保存所有验证结果
            all_results_file = os.path.join(output_path, 'all_validation_results.csv')
            all_results_df.to_csv(all_results_file, index=False, encoding='utf-8')
            print(f"所有验证结果已保存至: {all_results_file}")
            
            # 可视化验证结果
            validation_viz_file = os.path.join(output_path, 'validation_results_plot.png')
            visualize_validation_results(all_results_df, validation_viz_file)
            
            # 计算总体评价指标
            overall_metrics = calculate_metrics(all_results_df)
            print("\n总体评价指标:")
            for metric, value in overall_metrics.items():
                print(f"{metric}: {value:.4f}")
            
            # 计算每个站点的评价指标
            station_metrics = {}
            for station in all_results_df['站点'].unique():
                station_df = all_results_df[all_results_df['站点'] == station]
                station_metrics[station] = calculate_metrics(station_df)
            
            # 创建站点评价指标DataFrame
            station_metrics_df = pd.DataFrame.from_dict(station_metrics, orient='index')
            station_metrics_file = os.path.join(output_path, 'station_metrics.csv')
            station_metrics_df.to_csv(station_metrics_file, encoding='utf-8')
            print(f"站点评价指标已保存至: {station_metrics_file}")
            
            # 统计分析 - 计算站点数量和距离的相关信息
            print("\n站点和距离统计:")
            if '站点数量' in all_results_df.columns:
                print(f"平均参与插值的站点数量: {all_results_df['站点数量'].mean():.2f}")
                print(f"站点数量范围: {all_results_df['站点数量'].min()} - {all_results_df['站点数量'].max()}")
            
            if '平均距离(公里)' in all_results_df.columns:
                print(f"平均站点距离: {all_results_df['平均距离(公里)'].mean():.2f} 公里")
                print(f"最近站点平均距离: {all_results_df['最近距离(公里)'].mean():.2f} 公里")
                print(f"最远站点平均距离: {all_results_df['最远距离(公里)'].mean():.2f} 公里")
            
            # 生成完整的时间序列
            print("生成完整的时间序列...")
            complete_series = generate_complete_timeseries(all_validation_results, rainfall_data)
            
            # 保存每个站点的完整时间序列
            for station_id, df in complete_series.items():
                output_file = os.path.join(output_path, f"{station_id}.csv")
                df.to_csv(output_file, index=False, encoding='utf-8')
                print(f"站点{station_id}的完整时间序列已保存至: {output_file}")
        else:
            print("没有生成任何验证结果，请检查输入数据")
        
        # 计算总运行时间
        end_time = time.time()
        print(f"\nIDW直接空间插值（{max_distance}公里范围）完成，总耗时: {(end_time - start_time)/60:.2f}分钟")
        
    except Exception as e:
        print(f"执行过程中出错：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

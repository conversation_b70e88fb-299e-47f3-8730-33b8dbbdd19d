"""
Kriging插值系统主程序
整合所有模块，实现完整的Kriging插值工作流程
"""

import os
import time
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import gc

# 导入各个模块
from config import Config
from data_processing import DataProcessor
from delaunay_triangulation import DelaunayTriangulation
from moran_index import MoranIndex
from kriging_core import KrigingCore
from evaluation_metrics import EvaluationMetrics

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kriging_interpolation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class KrigingInterpolation:
    """Kriging插值主类"""
    
    def __init__(self, config: Config):
        """初始化Kriging插值系统"""
        self.config = config
        
        # 初始化各个模块
        self.data_processor = DataProcessor(config)
        self.delaunay_tri = DelaunayTriangulation(config)
        self.moran_calculator = MoranIndex(config)
        self.kriging_core = KrigingCore(config)
        self.evaluator = EvaluationMetrics(config)
        
        # 数据存储
        self.stations_df = None
        self.rainfall_data = None
        self.triangulation_info = None
        self.interpolation_results = None
        
        logger.info("Kriging插值系统初始化完成")
    
    def load_data(self):
        """加载数据"""
        try:
            logger.info("="*60)
            logger.info("开始加载数据")
            logger.info("="*60)
            
            # 加载站点数据
            self.stations_df = self.data_processor.load_stations_data()
            
            # 加载降雨数据
            self.rainfall_data = self.data_processor.load_rainfall_data()
            
            # 验证数据一致性
            self.stations_df, self.rainfall_data = self.data_processor.validate_data_consistency(
                self.stations_df, self.rainfall_data
            )
            
            # 加载地形数据（如果启用）
            if self.config.enable_terrain_enhancement:
                terrain_data = self.data_processor.load_terrain_data()
                if terrain_data:
                    self.stations_df = self.data_processor.extract_terrain_features_for_stations(
                        self.stations_df
                    )
            
            logger.info(f"数据加载完成: {len(self.stations_df)} 个站点, {len(self.rainfall_data)} 个降雨文件")
            
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            raise
    
    def build_spatial_structure(self):
        """构建空间结构"""
        try:
            logger.info("="*60)
            logger.info("构建空间结构")
            logger.info("="*60)
            
            # 构建Delaunay三角网
            triangulation = self.delaunay_tri.build_triangulation(self.stations_df)
            self.triangulation_info = self.delaunay_tri.get_triangulation_info()
            
            # 计算空间权重矩阵（用于莫兰指数）
            if self.config.enable_moran_weighting:
                self.moran_calculator.calculate_spatial_weights(self.stations_df)
            
            # 绘制三角网图（如果启用）
            if self.config.output_delaunay_plot:
                self.delaunay_tri.plot_triangulation(self.stations_df)

            logger.info("空间结构构建完成")
            
        except Exception as e:
            logger.error(f"空间结构构建失败: {e}")
            raise
    
    def run_interpolation(self):
        """运行插值"""
        try:
            logger.info("="*60)
            logger.info("开始Kriging插值")
            logger.info("="*60)
            
            # 获取时间序列信息
            time_info = self.data_processor.get_time_series_info(self.rainfall_data)
            
            if not time_info:
                raise ValueError("无法获取时间序列信息")
            
            # 执行留一法交叉验证
            validation_results = self.leave_one_out_validation()
            
            self.interpolation_results = validation_results
            
            logger.info(f"Kriging插值完成: 处理了 {len(validation_results)} 个验证点")

            # 保存Delaunay三角网记录（在留一法分析完成后）
            delaunay_records_path = os.path.join(self.config.output_dir, 'delaunay_records.csv')
            self.delaunay_tri.save_triangulation_records(delaunay_records_path)
            logger.info(f"Delaunay三角网记录已保存: {delaunay_records_path}")
            
        except Exception as e:
            logger.error(f"Kriging插值失败: {e}")
            raise
    
    def leave_one_out_validation(self) -> List[Dict]:
        """留一法交叉验证"""
        try:
            logger.info("执行留一法交叉验证...")
            
            validation_results = []
            station_names = self.stations_df['站点'].tolist()
            
            # 获取第一个时间点的数据作为示例
            first_station = list(self.rainfall_data.keys())[0]
            first_df = self.rainfall_data[first_station]
            
            if len(first_df) == 0:
                raise ValueError("降雨数据为空")
            
            # 选择几个时间点进行验证（避免计算时间过长）
            max_time_points = min(50, len(first_df))
            time_indices = np.linspace(0, len(first_df)-1, max_time_points, dtype=int)
            
            for time_idx in time_indices:
                time_point = first_df.iloc[time_idx]['时间']
                
                # 构建该时间点的降雨数据
                rainfall_at_time = {}
                for station_name in station_names:
                    if str(station_name) in self.rainfall_data:
                        station_df = self.rainfall_data[str(station_name)]
                        matching_rows = station_df[station_df['时间'] == time_point]
                        
                        if len(matching_rows) > 0:
                            rainfall_at_time[station_name] = matching_rows['雨量'].iloc[0]
                        else:
                            rainfall_at_time[station_name] = 0.0
                    else:
                        rainfall_at_time[station_name] = 0.0
                
                # 对每个站点进行留一法验证
                for target_station in station_names:
                    try:
                        result = self.validate_single_station(
                            target_station, rainfall_at_time, time_point
                        )
                        
                        if result:
                            validation_results.append(result)
                            
                    except Exception as e:
                        logger.warning(f"站点 {target_station} 在时间 {time_point} 的验证失败: {e}")
                        continue
                
                # 每处理10个时间点输出一次进度
                if (time_idx + 1) % 10 == 0:
                    logger.info(f"已处理 {time_idx + 1}/{len(time_indices)} 个时间点")
            
            logger.info(f"留一法交叉验证完成: {len(validation_results)} 个有效验证点")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"留一法交叉验证失败: {e}")
            return []
    
    def validate_single_station(self, target_station: str, 
                              rainfall_at_time: Dict[str, float],
                              time_point: str) -> Optional[Dict]:
        """验证单个站点"""
        try:
            # 获取目标站点的观测值
            observed_value = rainfall_at_time.get(target_station, 0.0)
            
            # 找到邻近站点
            neighbor_indices = self.delaunay_tri.find_neighbors_by_triangulation(
                target_station, self.stations_df
            )
            
            if len(neighbor_indices) == 0:
                return None
            
            # 获取邻近站点信息
            neighbor_stations = self.stations_df.iloc[neighbor_indices]
            neighbor_names = neighbor_stations['站点'].tolist()
            
            # 获取邻近站点的降雨值
            neighbor_values = []
            neighbor_coords = []
            
            for _, neighbor_row in neighbor_stations.iterrows():
                neighbor_name = neighbor_row['站点']
                neighbor_value = rainfall_at_time.get(neighbor_name, 0.0)
                
                neighbor_values.append(neighbor_value)
                neighbor_coords.append([neighbor_row['经度'], neighbor_row['纬度']])
            
            neighbor_values = np.array(neighbor_values)
            neighbor_coords = np.array(neighbor_coords)
            
            # 获取目标站点坐标
            target_info = self.stations_df[self.stations_df['站点'] == target_station].iloc[0]
            target_coords = np.array([target_info['经度'], target_info['纬度']])
            
            # 执行Kriging插值
            predicted_value, kriging_variance = self.kriging_core.ordinary_kriging(
                target_coords, neighbor_coords, neighbor_values
            )
            
            # 应用莫兰指数权重（如果启用）
            if self.config.enable_moran_weighting:
                target_idx = self.stations_df[self.stations_df['站点'] == target_station].index[0]
                moran_weights = self.moran_calculator.calculate_moran_weights(
                    target_idx, neighbor_indices, neighbor_values
                )
                
                # 结合莫兰权重和Kriging权重
                moran_prediction = np.sum(moran_weights * neighbor_values)
                
                # 加权平均
                predicted_value = (self.config.distance_weight * predicted_value + 
                                 self.config.moran_weight * moran_prediction)
            
            # 确保预测值非负
            predicted_value = max(0.0, predicted_value)
            
            return {
                'station': target_station,
                'time': time_point,
                'observed': float(observed_value),
                'predicted': float(predicted_value),
                'kriging_variance': float(kriging_variance),
                'neighbor_count': len(neighbor_indices),
                'neighbor_stations': neighbor_names
            }
            
        except Exception as e:
            logger.debug(f"单站点验证失败 {target_station}: {e}")
            return None
    
    def evaluate_results(self) -> Dict:
        """评价插值结果"""
        try:
            logger.info("="*60)
            logger.info("评价插值结果")
            logger.info("="*60)
            
            if not self.interpolation_results:
                raise ValueError("没有插值结果可供评价")
            
            # 提取观测值和预测值
            observed = [r['observed'] for r in self.interpolation_results]
            predicted = [r['predicted'] for r in self.interpolation_results]
            
            # 计算评价指标
            metrics = self.evaluator.calculate_all_metrics(
                np.array(observed), np.array(predicted)
            )
            
            # 创建散点图
            self.evaluator.create_scatter_plot(
                np.array(observed), np.array(predicted),
                title="Kriging插值留一法验证"
            )
            
            # 创建残差图
            self.evaluator.create_residual_plot(
                np.array(observed), np.array(predicted)
            )
            
            # 保存评价指标
            self.evaluator.save_metrics_to_csv(metrics)
            
            # 输出性能总结
            summary = self.evaluator.get_performance_summary(metrics)
            logger.info(f"\n{summary}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"评价插值结果失败: {e}")
            return {}
    
    def save_interpolation_results(self):
        """保存插值结果"""
        try:
            if not self.interpolation_results:
                logger.warning("没有插值结果可保存")
                return
            
            # 转换为DataFrame
            results_df = pd.DataFrame(self.interpolation_results)
            
            # 保存详细结果
            output_dirs = self.config.get_output_dirs()
            results_path = os.path.join(output_dirs['points'], 'leave_one_out_results.csv')
            results_df.to_csv(results_path, index=False, encoding='utf-8')
            
            logger.info(f"插值结果已保存: {results_path}")
            
        except Exception as e:
            logger.error(f"保存插值结果失败: {e}")
    
    def cleanup(self):
        """清理内存"""
        try:
            # 清理各个模块
            self.data_processor.cleanup()
            self.delaunay_tri.cleanup()
            self.moran_calculator.cleanup()
            self.kriging_core.cleanup()
            self.evaluator.cleanup()
            
            # 清理数据
            self.stations_df = None
            self.rainfall_data = None
            self.interpolation_results = None
            
            # 强制垃圾回收
            gc.collect()
            
            logger.info("内存清理完成")
            
        except Exception as e:
            logger.warning(f"内存清理失败: {e}")
    
    def run_complete_workflow(self):
        """运行完整的Kriging插值工作流程"""
        start_time = time.time()
        
        try:
            logger.info("="*60)
            logger.info("开始Kriging插值完整工作流程")
            logger.info("="*60)
            
            # 1. 加载数据
            self.load_data()
            
            # 2. 构建空间结构
            self.build_spatial_structure()
            
            # 3. 执行插值
            self.run_interpolation()
            
            # 4. 评价结果
            evaluation_results = self.evaluate_results()
            
            # 5. 保存结果
            self.save_interpolation_results()
            
            # 6. 清理内存
            self.cleanup()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            logger.info("="*60)
            logger.info(f"Kriging插值工作流程完成！总耗时: {total_time:.2f} 秒")
            logger.info(f"结果保存在: {self.config.output_dir}")
            logger.info("="*60)
            
            return evaluation_results
            
        except Exception as e:
            logger.error(f"Kriging插值工作流程失败: {e}")
            raise

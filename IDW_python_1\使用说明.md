# IDW空间插值系统使用说明

## 系统概述

本系统是基于Delaunay三角剖分权重的IDW（反距离权重）空间插值系统，专为珠江流域降雨数据设计。系统已成功处理43个洪水事件，具有完整的批量处理、精度评估和可视化功能。

## 快速开始（新手推荐）

### 第一步：环境准备
```bash
# 进入IDW_python目录
cd IDW_python

# 安装依赖包
pip install -r requirements.txt
```

### 第二步：检查数据
确保以下文件存在：
- `../input_another/` - 洪水事件数据目录
- `../stations.csv` - 站点信息文件  
- `../output/Delaunay/delaunay_analysis_summary.csv` - Delaunay权重文件

### 第三步：运行系统
```bash
# 方法1：直接运行（推荐）
python main.py

# 方法2：单事件演示
python main.py --demo

# 方法3：使用快速运行脚本（交互式）
python quick_run.py
```

### 第四步：查看结果
运行完成后，结果保存在 `../output/IDW/` 目录：
- `batch_processing_summary.csv` - 批量处理汇总
- `plots/` - 可视化图表
- `各事件文件夹/` - 每个事件的详细结果

## 参数调整指南

### 主要参数位置（config.py文件）

1. **IDW权重指数** (第20行)
   ```python
   IDW_POWER = 2.0  # 建议范围：1.5-3.0
   ```
   - 增大：距离衰减更快，局部特征更明显
   - 减小：距离衰减更慢，平滑效果更强

2. **最大站点数** (第25行)
   ```python
   MAX_STATIONS = 10  # 建议范围：5-15
   ```
   - 增加：可能提高精度但增加计算量
   - 减少：计算更快但可能降低精度

3. **搜索半径** (第28行)
   ```python
   SEARCH_RADIUS = 0.5  # 建议范围：0.3-1.0度
   ```
   - 增大：使用更多远距离站点
   - 减小：只使用近距离站点

4. **栅格输出** (第45行)
   ```python
   GENERATE_RASTER = False  # 建议设为False节省时间
   ```

5. **并行计算** (第55行)
   ```python
   USE_PARALLEL = True  # 建议开启以提高速度
   N_PROCESSES = 0      # 0=自动检测CPU核心数
   ```

## 系统性能结果

基于43个洪水事件的完整测试：

### 整体统计
- **处理成功率**: 100.0% (43/43)
- **平均处理时间**: 约10.8秒（全部43个事件）
- **平均每事件**: 约0.25秒

### 精度指标
- **平均MAE**: 74.29 ± 39.58 mm
- **平均RMSE**: 98.93 ± 53.98 mm
- **平均NSE**: -0.446 ± 0.564
- **平均相关系数**: 0.144

### NSE性能分析
- **NSE > 0**: 11/43 (25.6%) - 可接受性能
- **NSE > 0.5**: 1/43 (2.3%) - 良好性能
- **NSE > 0.75**: 0/43 (0.0%) - 优秀性能

### 最佳/最差事件
- **最佳事件**: 2019-4 (NSE: 0.676)
- **最差事件**: 2014-4 (NSE: -1.499)

## 输出文件说明

### 主要输出文件
```
output/IDW/
├── batch_processing_summary.csv    # 所有事件汇总结果
├── idw_interpolation.log           # 运行日志
├── plots/                          # 可视化图表
│   ├── IDW_metrics_summary.png     # 指标汇总图
│   ├── IDW_statistics_summary.png  # 统计汇总图
│   ├── validation_results_*.png    # 各事件验证图
│   └── station_map_*.png           # 各事件站点图
└── 事件文件夹/                     # 各事件详细结果
    ├── *_validation_summary.csv    # 验证汇总
    └── *_validation_details.csv    # 验证详情
```

### 关键指标说明
- **MAE** (平均绝对误差): 越小越好，单位mm
- **RMSE** (均方根误差): 越小越好，单位mm
- **NSE** (纳什效率系数): 越接近1越好，>0.5为可接受
- **相关系数**: 越接近1越好，表示线性相关性

## 常见问题解决

### 1. 运行出错
**问题**: 程序运行时报错
**解决**:
- 检查Python环境：`python --version` (需要3.7+)
- 安装依赖包：`pip install -r requirements.txt`
- 检查数据文件路径是否正确
- 查看日志文件：`idw_interpolation.log`

### 2. 精度不理想
**问题**: NSE值为负数或MAE过大
**解决**:
- 调整IDW_POWER参数（试试1.5或3.0）
- 增加MAX_STATIONS（试试15）
- 调整SEARCH_RADIUS（试试0.8）
- 检查Delaunay权重文件质量

### 3. 运行速度慢
**问题**: 处理时间过长
**解决**:
- 确保GENERATE_RASTER = False
- 确保USE_PARALLEL = True
- 减少处理的事件数量（修改input_another目录）
- 增加N_PROCESSES值

### 4. 内存不足
**问题**: 内存溢出错误
**解决**:
- 设置MEMORY_OPTIMIZATION = True
- 减少N_PROCESSES值
- 关闭栅格输出
- 分批处理事件

## 高级功能

### 1. 生成额外图表
```bash
python generate_summary_plots.py
```

### 2. 单独处理特定事件
修改main.py中的事件列表，或创建自定义脚本。

### 3. 自定义评估指标
在evaluation_metrics.py中添加新的评估函数。

### 4. 导出结果到其他格式
系统支持CSV格式，可以轻松导入Excel或其他分析软件。

## 技术特点

1. **智能权重计算**: 优先使用Delaunay三角剖分权重，自动回退到距离权重
2. **高效并行处理**: 支持多核CPU并行计算
3. **完整验证体系**: 留一法验证和多种评估指标
4. **丰富可视化**: 自动生成验证图表和汇总图表
5. **新手友好**: 配置文件管理，详细的操作指南

## 联系支持

如遇到问题，请：
1. 检查本使用说明
2. 查看README.md文件
3. 检查运行日志文件
4. 确认数据文件格式和路径

## 版本信息

- **版本**: 1.0
- **更新日期**: 2024年12月
- **适用范围**: 珠江流域降雨空间插值研究
- **技术基础**: Delaunay三角剖分 + IDW插值算法

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay三角剖分插值系统高级可视化模块

作者: 空间插值研究团队
日期: 2024年12月
版本: 2.0

功能：
1. 所有洪水场次的评价指标可视化
2. 站点年平均指标分析
3. 各个站点多年评价指标可视化
4. 时间趋势分析
5. 性能分类统计
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging
from typing import Dict, List, Optional, Tuple
import warnings
from matplotlib.gridspec import GridSpec
from matplotlib.patches import Rectangle
import matplotlib.dates as mdates
from datetime import datetime
import glob
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

class DelaunayAdvancedVisualizer:
    """Delaunay插值结果高级可视化器"""

    def __init__(self, output_dir: Path):
        self.output_dir = output_dir
        self.viz_dir = output_dir / 'advanced_visualizations'
        self.viz_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)

        # 设置绘图样式
        plt.style.use('default')
        sns.set_palette("husl")

        # 定义颜色方案
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'warning': '#C73E1D',
            'info': '#6A994E',
            'light': '#F5F5F5',
            'dark': '#2C3E50'
        }

        # 性能等级颜色
        self.performance_colors = {
            'Excellent': '#2E8B57',
            'Good': '#32CD32',
            'Satisfactory': '#FFD700',
            'Unsatisfactory': '#FFA500',
            'Unacceptable': '#FF6347',
            'Invalid': '#808080'
        }

        # 加载站点名称映射
        self.station_names = self.load_station_names()

    def load_station_names(self) -> Dict[str, str]:
        """加载站点代码到中文名称的映射"""
        try:
            # 读取水晏泰森.xlsx文件
            station_file = self.output_dir.parent.parent / '水晏泰森.xlsx'
            if station_file.exists():
                df = pd.read_excel(station_file)
                if 'PSTCD' in df.columns and 'NAME' in df.columns:
                    # 创建站点代码到名称的映射，处理大小写不敏感
                    name_mapping = {}
                    for _, row in df.iterrows():
                        station_code = str(row['PSTCD']).upper()  # 转为大写
                        station_name = str(row['NAME'])
                        name_mapping[station_code] = station_name

                    self.logger.info(f"成功加载 {len(name_mapping)} 个站点名称映射")
                    return name_mapping
                else:
                    self.logger.warning("水晏泰森.xlsx文件中缺少PSTCD或NAME列")
            else:
                self.logger.warning(f"未找到水晏泰森.xlsx文件: {station_file}")
        except Exception as e:
            self.logger.error(f"加载站点名称映射失败: {e}")

        return {}

    def get_station_display_name(self, station_code: str) -> str:
        """获取站点的显示名称（中文名称）"""
        if not station_code:
            return station_code

        # 处理大小写不敏感
        station_code_upper = str(station_code).upper()

        if station_code_upper in self.station_names:
            return self.station_names[station_code_upper]
        else:
            # 如果没找到映射，返回原始代码
            return station_code
    
    def load_all_metrics(self, metrics_dir: Path) -> pd.DataFrame:
        """加载所有事件的评价指标数据"""
        self.logger.info("加载所有事件的评价指标数据...")
        
        all_metrics = []
        csv_files = list(metrics_dir.glob('*_metrics.csv'))
        
        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
                all_metrics.append(df)
            except Exception as e:
                self.logger.warning(f"加载文件失败 {file_path}: {e}")
        
        if not all_metrics:
            raise ValueError("没有找到有效的评价指标文件")
        
        combined_df = pd.concat(all_metrics, ignore_index=True)
        
        # 提取年份信息
        combined_df['年份'] = combined_df['事件名称'].str.extract(r'(\d{4})').astype(int)
        combined_df['事件序号'] = combined_df['事件名称'].str.extract(r'-(\d+)').astype(int)
        
        self.logger.info(f"成功加载 {len(combined_df)} 条记录，涵盖 {len(csv_files)} 个事件")
        return combined_df
    
    def classify_performance(self, nse: float) -> str:
        """根据NSE值分类性能"""
        if np.isnan(nse):
            return "Invalid"
        elif nse > 0.75:
            return "Excellent"
        elif nse > 0.65:
            return "Good"
        elif nse > 0.50:
            return "Satisfactory"
        elif nse > 0.20:
            return "Unsatisfactory"
        else:
            return "Unacceptable"
    
    def plot_overall_metrics_distribution(self, df: pd.DataFrame):
        """绘制整体评价指标分布图"""
        self.logger.info("生成整体评价指标分布图...")
        
        fig = plt.figure(figsize=(20, 16))
        gs = GridSpec(4, 3, figure=fig, hspace=0.3, wspace=0.3)
        
        # 主要指标
        main_metrics = ['NSE', 'RMSE', 'MAE', 'Correlation']
        
        # 1. 指标分布直方图 (第一行)
        for i, metric in enumerate(main_metrics):
            ax = fig.add_subplot(gs[0, i] if i < 3 else gs[1, 0])
            
            values = df[metric].dropna()
            if len(values) > 0:
                ax.hist(values, bins=30, alpha=0.7, color=self.colors['primary'], 
                       edgecolor='black', density=True)
                
                # 添加统计线
                mean_val = values.mean()
                median_val = values.median()
                ax.axvline(mean_val, color='red', linestyle='--', linewidth=2, 
                          label=f'均值: {mean_val:.3f}')
                ax.axvline(median_val, color='orange', linestyle='--', linewidth=2, 
                          label=f'中位数: {median_val:.3f}')
                
                ax.set_xlabel(metric, fontsize=12)
                ax.set_ylabel('密度', fontsize=12)
                ax.set_title(f'{metric} 分布', fontsize=14, fontweight='bold')
                ax.legend()
                ax.grid(True, alpha=0.3)
        
        # 2. NSE性能分类饼图 (第一行最后一个位置)
        ax_pie = fig.add_subplot(gs[0, 3] if len(main_metrics) <= 3 else gs[1, 1])
        df['性能等级'] = df['NSE'].apply(self.classify_performance)
        performance_counts = df['性能等级'].value_counts()
        
        colors = [self.performance_colors.get(level, '#808080') for level in performance_counts.index]
        wedges, texts, autotexts = ax_pie.pie(performance_counts.values, 
                                             labels=performance_counts.index,
                                             autopct='%1.1f%%',
                                             colors=colors,
                                             startangle=90)
        ax_pie.set_title('NSE性能等级分布', fontsize=14, fontweight='bold')
        
        # 3. 年度趋势分析 (第二行)
        ax_trend = fig.add_subplot(gs[1, 2])
        yearly_stats = df.groupby('年份')['NSE'].agg(['mean', 'std', 'count']).reset_index()
        
        ax_trend.errorbar(yearly_stats['年份'], yearly_stats['mean'], 
                         yerr=yearly_stats['std'], marker='o', linewidth=2, 
                         markersize=8, capsize=5, capthick=2)
        ax_trend.set_xlabel('年份', fontsize=12)
        ax_trend.set_ylabel('平均NSE', fontsize=12)
        ax_trend.set_title('年度NSE趋势', fontsize=14, fontweight='bold')
        ax_trend.grid(True, alpha=0.3)
        
        # 4. 事件评价指标热力图 (第三行，跨两列)
        ax_heatmap = fig.add_subplot(gs[2, :2])
        
        # 选择部分事件进行展示
        event_metrics = df.groupby('事件名称')[main_metrics].mean()
        event_metrics_sample = event_metrics.head(20)  # 显示前20个事件
        
        sns.heatmap(event_metrics_sample.T, annot=True, fmt='.3f', cmap='RdYlBu_r',
                   ax=ax_heatmap, cbar_kws={'label': '指标值'})
        ax_heatmap.set_title('事件评价指标热力图 (前20个事件)', fontsize=14, fontweight='bold')
        ax_heatmap.set_xlabel('事件名称', fontsize=12)
        ax_heatmap.set_ylabel('评价指标', fontsize=12)
        
        # 5. 指标相关性矩阵 (第三行最后一列)
        ax_corr = fig.add_subplot(gs[2, 2])
        correlation_matrix = df[main_metrics].corr()
        
        sns.heatmap(correlation_matrix, annot=True, fmt='.3f', cmap='coolwarm',
                   center=0, ax=ax_corr, square=True)
        ax_corr.set_title('指标相关性矩阵', fontsize=14, fontweight='bold')
        
        # 6. 统计摘要表 (第四行)
        ax_stats = fig.add_subplot(gs[3, :])
        
        # 计算统计摘要
        stats_data = []
        for metric in main_metrics:
            values = df[metric].dropna()
            if len(values) > 0:
                stats_data.append({
                    '指标': metric,
                    '均值': f"{values.mean():.4f}",
                    '标准差': f"{values.std():.4f}",
                    '最小值': f"{values.min():.4f}",
                    '最大值': f"{values.max():.4f}",
                    '中位数': f"{values.median():.4f}",
                    '样本数': len(values)
                })
        
        stats_df = pd.DataFrame(stats_data)
        
        # 创建表格
        table = ax_stats.table(cellText=stats_df.values,
                              colLabels=stats_df.columns,
                              cellLoc='center',
                              loc='center',
                              bbox=[0, 0, 1, 1])
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 2)
        
        # 设置表格样式
        for i in range(len(stats_df.columns)):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        ax_stats.axis('off')
        ax_stats.set_title('评价指标统计摘要', fontsize=14, fontweight='bold', pad=20)
        
        plt.suptitle('Delaunay插值系统整体评价指标分析', fontsize=18, fontweight='bold', y=0.98)
        
        # 保存图片
        save_path = self.viz_dir / 'overall_metrics_distribution.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        self.logger.info(f"整体评价指标分布图已保存: {save_path}")
        return save_path
    
    def plot_station_annual_analysis(self, df: pd.DataFrame):
        """绘制站点年平均分析图"""
        self.logger.info("生成站点年平均分析图...")
        
        # 计算站点年平均指标
        station_annual = df.groupby(['站点代码', '年份']).agg({
            'NSE': 'mean',
            'RMSE': 'mean', 
            'MAE': 'mean',
            'Correlation': 'mean',
            'Sample_Count': 'sum'
        }).reset_index()
        
        # 计算站点总体平均
        station_overall = df.groupby('站点代码').agg({
            'NSE': 'mean',
            'RMSE': 'mean',
            'MAE': 'mean', 
            'Correlation': 'mean',
            'Sample_Count': 'sum'
        }).reset_index()
        
        fig = plt.figure(figsize=(20, 16))
        gs = GridSpec(4, 3, figure=fig, hspace=0.3, wspace=0.3)
        
        # 1. 站点NSE排名 (第一行，跨三列)
        ax1 = fig.add_subplot(gs[0, :])
        station_nse_sorted = station_overall.sort_values('NSE', ascending=True)
        
        # 选择显示的站点数量
        max_stations = min(25, len(station_nse_sorted))
        display_stations = station_nse_sorted.tail(max_stations)
        
        colors = [self.performance_colors[self.classify_performance(nse)] 
                 for nse in display_stations['NSE']]
        
        bars = ax1.barh(range(len(display_stations)), display_stations['NSE'], 
                       color=colors, alpha=0.8, edgecolor='black')
        
        ax1.set_yticks(range(len(display_stations)))
        ax1.set_yticklabels(display_stations['站点代码'], fontsize=10)
        ax1.set_xlabel('平均NSE', fontsize=12)
        ax1.set_title(f'站点NSE性能排名 (前{max_stations}名)', fontsize=16, fontweight='bold')
        
        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, display_stations['NSE'])):
            ax1.text(value + 0.01 if value >= 0 else value - 0.01, i, f'{value:.3f}',
                    va='center', ha='left' if value >= 0 else 'right',
                    fontweight='bold', fontsize=9)
        
        # 添加参考线
        ax1.axvline(x=0, color='black', linestyle='-', alpha=0.3)
        ax1.axvline(x=0.5, color='orange', linestyle='--', alpha=0.7, label='满意线 (0.5)')
        ax1.axvline(x=0.7, color='green', linestyle='--', alpha=0.7, label='良好线 (0.7)')
        ax1.legend()
        ax1.grid(True, alpha=0.3, axis='x')
        
        # 2. 站点指标分布箱线图 (第二行)
        metrics_for_box = ['NSE', 'RMSE', 'MAE']
        for i, metric in enumerate(metrics_for_box):
            ax = fig.add_subplot(gs[1, i])
            
            # 选择前15个站点进行展示
            top_stations = station_overall.nlargest(15, 'NSE')['站点代码'].tolist()
            plot_data = df[df['站点代码'].isin(top_stations)]
            
            sns.boxplot(data=plot_data, x='站点代码', y=metric, ax=ax)
            ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right')
            ax.set_title(f'前15站点{metric}分布', fontsize=12, fontweight='bold')
            ax.grid(True, alpha=0.3)
        
        # 3. 年度变化趋势 (第三行，前两列)
        ax3 = fig.add_subplot(gs[2, :2])
        
        # 选择几个代表性站点
        representative_stations = station_overall.nlargest(5, 'NSE')['站点代码'].tolist()
        
        for station in representative_stations:
            station_data = station_annual[station_annual['站点代码'] == station]
            if len(station_data) > 1:
                ax3.plot(station_data['年份'], station_data['NSE'], 
                        marker='o', linewidth=2, label=station, markersize=6)
        
        ax3.set_xlabel('年份', fontsize=12)
        ax3.set_ylabel('NSE', fontsize=12)
        ax3.set_title('代表性站点NSE年度变化趋势', fontsize=14, fontweight='bold')
        ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax3.grid(True, alpha=0.3)
        
        # 4. 站点性能分类统计 (第三行最后一列)
        ax4 = fig.add_subplot(gs[2, 2])
        
        station_overall['性能等级'] = station_overall['NSE'].apply(self.classify_performance)
        performance_counts = station_overall['性能等级'].value_counts()
        
        colors = [self.performance_colors.get(level, '#808080') for level in performance_counts.index]
        bars = ax4.bar(performance_counts.index, performance_counts.values, 
                      color=colors, alpha=0.8, edgecolor='black')
        
        ax4.set_xlabel('性能等级', fontsize=12)
        ax4.set_ylabel('站点数量', fontsize=12)
        ax4.set_title('站点性能等级分布', fontsize=14, fontweight='bold')
        
        # 添加数值标签
        for bar, value in zip(bars, performance_counts.values):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                    str(value), ha='center', va='bottom', fontweight='bold')
        
        ax4.grid(True, alpha=0.3, axis='y')
        
        plt.suptitle('Delaunay插值系统站点年平均分析', fontsize=18, fontweight='bold', y=0.98)
        
        # 保存图片
        save_path = self.viz_dir / 'station_annual_analysis.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        self.logger.info(f"站点年平均分析图已保存: {save_path}")
        return save_path

    def plot_station_multiyear_performance(self, df: pd.DataFrame):
        """绘制各个站点多年评价指标可视化"""
        self.logger.info("生成各个站点多年评价指标可视化...")

        # 获取所有站点
        all_stations = df['站点代码'].unique()

        # 分批处理站点（每批8个站点）
        batch_size = 8
        station_batches = [all_stations[i:i+batch_size] for i in range(0, len(all_stations), batch_size)]

        saved_paths = []

        for batch_idx, stations in enumerate(station_batches):
            fig = plt.figure(figsize=(20, 16))
            gs = GridSpec(4, 2, figure=fig, hspace=0.4, wspace=0.3)

            # 为每个站点创建子图
            for i, station in enumerate(stations):
                if i >= 8:  # 每页最多8个站点
                    break

                row = i // 2
                col = i % 2
                ax = fig.add_subplot(gs[row, col])

                # 获取该站点的数据
                station_data = df[df['站点代码'] == station].copy()

                if len(station_data) == 0:
                    ax.text(0.5, 0.5, f'站点 {station}\n无数据', ha='center', va='center',
                           transform=ax.transAxes, fontsize=12)
                    ax.set_title(f'站点 {station}', fontsize=12, fontweight='bold')
                    continue

                # 按年份和事件分组
                station_data['年份_事件'] = station_data['事件名称']
                station_data = station_data.sort_values('年份_事件')

                # 绘制NSE趋势线
                ax.plot(range(len(station_data)), station_data['NSE'],
                       'o-', linewidth=2, markersize=4, color=self.colors['primary'],
                       label='NSE')

                # 添加性能等级背景色
                for j, nse in enumerate(station_data['NSE']):
                    performance = self.classify_performance(nse)
                    color = self.performance_colors.get(performance, '#808080')
                    ax.axvspan(j-0.4, j+0.4, alpha=0.2, color=color)

                # 设置标签和标题
                ax.set_xlabel('事件序号', fontsize=10)
                ax.set_ylabel('NSE', fontsize=10)
                ax.set_title(f'站点 {station} 多年NSE表现', fontsize=12, fontweight='bold')

                # 添加参考线
                ax.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, linewidth=1)
                ax.axhline(y=0.7, color='green', linestyle='--', alpha=0.7, linewidth=1)

                # 设置x轴标签
                if len(station_data) <= 10:
                    ax.set_xticks(range(len(station_data)))
                    ax.set_xticklabels(station_data['事件名称'], rotation=45, ha='right', fontsize=8)
                else:
                    # 如果事件太多，只显示部分标签
                    step = max(1, len(station_data) // 5)
                    indices = range(0, len(station_data), step)
                    ax.set_xticks(indices)
                    ax.set_xticklabels([station_data.iloc[i]['事件名称'] for i in indices],
                                     rotation=45, ha='right', fontsize=8)

                ax.grid(True, alpha=0.3)

                # 添加统计信息
                mean_nse = station_data['NSE'].mean()
                std_nse = station_data['NSE'].std()
                ax.text(0.02, 0.98, f'均值: {mean_nse:.3f}\n标准差: {std_nse:.3f}',
                       transform=ax.transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                       fontsize=9)

            plt.suptitle(f'Delaunay插值系统站点多年表现分析 (第{batch_idx+1}页)',
                        fontsize=16, fontweight='bold', y=0.98)

            # 保存图片
            save_path = self.viz_dir / f'station_multiyear_performance_page_{batch_idx+1}.png'
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            saved_paths.append(save_path)
            self.logger.info(f"站点多年表现分析图第{batch_idx+1}页已保存: {save_path}")

        return saved_paths

    def plot_temporal_trends_analysis(self, df: pd.DataFrame):
        """绘制时间趋势分析图"""
        self.logger.info("生成时间趋势分析图...")

        fig = plt.figure(figsize=(20, 16))
        gs = GridSpec(4, 3, figure=fig, hspace=0.3, wspace=0.3)

        # 1. 年度整体趋势 (第一行，跨三列)
        ax1 = fig.add_subplot(gs[0, :])

        yearly_stats = df.groupby('年份').agg({
            'NSE': ['mean', 'std', 'count'],
            'RMSE': 'mean',
            'MAE': 'mean',
            'Correlation': 'mean'
        }).round(4)

        # 绘制NSE年度趋势
        years = yearly_stats.index
        nse_mean = yearly_stats[('NSE', 'mean')]
        nse_std = yearly_stats[('NSE', 'std')]

        ax1.errorbar(years, nse_mean, yerr=nse_std, marker='o', linewidth=3,
                    markersize=8, capsize=5, capthick=2, color=self.colors['primary'])
        ax1.fill_between(years, nse_mean - nse_std, nse_mean + nse_std,
                        alpha=0.2, color=self.colors['primary'])

        ax1.set_xlabel('年份', fontsize=12)
        ax1.set_ylabel('NSE', fontsize=12)
        ax1.set_title('年度NSE趋势变化', fontsize=16, fontweight='bold')
        ax1.grid(True, alpha=0.3)

        # 添加趋势线
        z = np.polyfit(years, nse_mean, 1)
        p = np.poly1d(z)
        ax1.plot(years, p(years), '--', color='red', linewidth=2,
                label=f'趋势线 (斜率: {z[0]:.4f})')
        ax1.legend()

        # 2. 月度分析 (如果有月份信息)
        ax2 = fig.add_subplot(gs[1, 0])

        # 从事件名称中提取月份信息（假设格式为YYYY-M）
        df['月份'] = df['事件名称'].str.extract(r'-(\d+)').astype(int)
        monthly_stats = df.groupby('月份')['NSE'].agg(['mean', 'count']).reset_index()

        bars = ax2.bar(monthly_stats['月份'], monthly_stats['mean'],
                      color=self.colors['secondary'], alpha=0.7, edgecolor='black')
        ax2.set_xlabel('月份', fontsize=12)
        ax2.set_ylabel('平均NSE', fontsize=12)
        ax2.set_title('月度NSE分布', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3, axis='y')

        # 添加数值标签
        for bar, value in zip(bars, monthly_stats['mean']):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=9)

        # 3. 年度事件数量统计
        ax3 = fig.add_subplot(gs[1, 1])

        event_counts = df.groupby('年份')['事件名称'].nunique()
        bars = ax3.bar(event_counts.index, event_counts.values,
                      color=self.colors['info'], alpha=0.7, edgecolor='black')
        ax3.set_xlabel('年份', fontsize=12)
        ax3.set_ylabel('事件数量', fontsize=12)
        ax3.set_title('年度洪水事件数量', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3, axis='y')

        # 添加数值标签
        for bar, value in zip(bars, event_counts.values):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    str(value), ha='center', va='bottom', fontsize=10, fontweight='bold')

        # 4. 年度性能分类变化
        ax4 = fig.add_subplot(gs[1, 2])

        df['性能等级'] = df['NSE'].apply(self.classify_performance)
        performance_by_year = df.groupby(['年份', '性能等级']).size().unstack(fill_value=0)

        # 计算百分比
        performance_pct = performance_by_year.div(performance_by_year.sum(axis=1), axis=0) * 100

        # 堆叠柱状图
        bottom = np.zeros(len(performance_pct))
        for level in ['优秀', '良好', '满意', '不满意', '不可接受']:
            if level in performance_pct.columns:
                ax4.bar(performance_pct.index, performance_pct[level],
                       bottom=bottom, label=level,
                       color=self.performance_colors.get(level, '#808080'),
                       alpha=0.8)
                bottom += performance_pct[level]

        ax4.set_xlabel('年份', fontsize=12)
        ax4.set_ylabel('百分比 (%)', fontsize=12)
        ax4.set_title('年度性能等级分布变化', fontsize=14, fontweight='bold')
        ax4.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        # 5. 多指标年度对比雷达图 (第三行，前两列)
        ax5 = fig.add_subplot(gs[2, :2], projection='polar')

        # 选择几个代表年份
        recent_years = sorted(df['年份'].unique())[-5:]  # 最近5年
        metrics = ['NSE', 'Correlation', 'RMSE_inv', 'MAE_inv']  # RMSE和MAE取倒数便于雷达图显示

        # 计算各年份的标准化指标
        for year in recent_years:
            year_data = df[df['年份'] == year]
            values = [
                year_data['NSE'].mean(),
                year_data['Correlation'].mean(),
                1 / (year_data['RMSE'].mean() + 0.1),  # 避免除零
                1 / (year_data['MAE'].mean() + 0.1)
            ]

            # 标准化到0-1范围
            max_vals = [1, 1, 10, 10]  # 各指标的最大期望值
            normalized_values = [v/m for v, m in zip(values, max_vals)]

            angles = np.linspace(0, 2*np.pi, len(metrics), endpoint=False).tolist()
            normalized_values += normalized_values[:1]  # 闭合图形
            angles += angles[:1]

            ax5.plot(angles, normalized_values, 'o-', linewidth=2,
                    label=f'{year}年', markersize=6)
            ax5.fill(angles, normalized_values, alpha=0.1)

        ax5.set_xticks(angles[:-1])
        ax5.set_xticklabels(['NSE', '相关系数', 'RMSE(倒数)', 'MAE(倒数)'])
        ax5.set_title('年度多指标对比雷达图', fontsize=14, fontweight='bold', pad=20)
        ax5.legend(bbox_to_anchor=(1.3, 1), loc='upper left')

        # 6. 时间序列热力图 (第三行最后一列和第四行)
        ax6 = fig.add_subplot(gs[2, 2])

        # 创建年份-月份的NSE热力图
        pivot_data = df.pivot_table(values='NSE', index='年份', columns='月份', aggfunc='mean')

        sns.heatmap(pivot_data, annot=True, fmt='.3f', cmap='RdYlBu_r',
                   ax=ax6, cbar_kws={'label': 'NSE'})
        ax6.set_title('年份-月份NSE热力图', fontsize=14, fontweight='bold')
        ax6.set_xlabel('月份', fontsize=12)
        ax6.set_ylabel('年份', fontsize=12)

        # 7. 统计摘要表 (第四行)
        ax7 = fig.add_subplot(gs[3, :])

        # 年度统计摘要
        yearly_summary = df.groupby('年份').agg({
            'NSE': ['mean', 'std', 'min', 'max'],
            'RMSE': 'mean',
            'MAE': 'mean',
            'Correlation': 'mean',
            '事件名称': 'nunique'
        }).round(4)

        # 重命名列
        yearly_summary.columns = ['NSE均值', 'NSE标准差', 'NSE最小值', 'NSE最大值',
                                 'RMSE均值', 'MAE均值', '相关系数均值', '事件数量']

        # 创建表格
        table = ax7.table(cellText=yearly_summary.values,
                         rowLabels=yearly_summary.index,
                         colLabels=yearly_summary.columns,
                         cellLoc='center',
                         loc='center',
                         bbox=[0, 0, 1, 1])
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1, 1.5)

        # 设置表格样式
        for i in range(len(yearly_summary.columns)):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')

        for i in range(len(yearly_summary)):
            table[(i+1, -1)].set_facecolor('#E8F5E8')

        ax7.axis('off')
        ax7.set_title('年度统计摘要表', fontsize=14, fontweight='bold', pad=20)

        plt.suptitle('Delaunay插值系统时间趋势分析', fontsize=18, fontweight='bold', y=0.98)

        # 保存图片
        save_path = self.viz_dir / 'temporal_trends_analysis.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        self.logger.info(f"时间趋势分析图已保存: {save_path}")
        return save_path

    def plot_performance_classification_analysis(self, df: pd.DataFrame):
        """绘制性能分类详细分析图"""
        self.logger.info("生成性能分类详细分析图...")

        fig = plt.figure(figsize=(20, 12))
        gs = GridSpec(3, 4, figure=fig, hspace=0.3, wspace=0.3)

        # 添加性能等级列
        df['性能等级'] = df['NSE'].apply(self.classify_performance)

        # 1. 整体性能分类饼图 (第一行第一列)
        ax1 = fig.add_subplot(gs[0, 0])
        performance_counts = df['性能等级'].value_counts()
        colors = [self.performance_colors.get(level, '#808080') for level in performance_counts.index]

        wedges, texts, autotexts = ax1.pie(performance_counts.values,
                                          labels=performance_counts.index,
                                          autopct='%1.1f%%',
                                          colors=colors,
                                          startangle=90)
        ax1.set_title('整体性能分类分布', fontsize=14, fontweight='bold')

        # 2. 年度性能分类堆叠图 (第一行第二、三列)
        ax2 = fig.add_subplot(gs[0, 1:3])
        performance_by_year = df.groupby(['年份', '性能等级']).size().unstack(fill_value=0)

        bottom = np.zeros(len(performance_by_year))
        for level in ['优秀', '良好', '满意', '不满意', '不可接受', '无效']:
            if level in performance_by_year.columns:
                ax2.bar(performance_by_year.index, performance_by_year[level],
                       bottom=bottom, label=level,
                       color=self.performance_colors.get(level, '#808080'),
                       alpha=0.8, edgecolor='white')
                bottom += performance_by_year[level]

        ax2.set_xlabel('年份', fontsize=12)
        ax2.set_ylabel('记录数量', fontsize=12)
        ax2.set_title('年度性能分类分布', fontsize=14, fontweight='bold')
        ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        # 3. 性能等级统计表 (第一行第四列)
        ax3 = fig.add_subplot(gs[0, 3])

        # 计算详细统计
        performance_stats = []
        for level in performance_counts.index:
            level_data = df[df['性能等级'] == level]
            stats = {
                '等级': level,
                '数量': len(level_data),
                '占比(%)': f"{len(level_data)/len(df)*100:.1f}",
                'NSE范围': f"{level_data['NSE'].min():.3f}~{level_data['NSE'].max():.3f}",
                '平均RMSE': f"{level_data['RMSE'].mean():.3f}"
            }
            performance_stats.append(stats)

        stats_df = pd.DataFrame(performance_stats)

        table = ax3.table(cellText=stats_df.values,
                         colLabels=stats_df.columns,
                         cellLoc='center',
                         loc='center',
                         bbox=[0, 0, 1, 1])
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1, 1.5)

        # 设置表格样式
        for i in range(len(stats_df.columns)):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')

        ax3.axis('off')
        ax3.set_title('性能等级统计表', fontsize=14, fontweight='bold')

        # 4. 站点性能分类分布 (第二行第一、二列)
        ax4 = fig.add_subplot(gs[1, :2])

        station_performance = df.groupby('站点代码')['性能等级'].agg(lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else '无效')
        station_perf_counts = station_performance.value_counts()

        bars = ax4.bar(station_perf_counts.index, station_perf_counts.values,
                      color=[self.performance_colors.get(level, '#808080') for level in station_perf_counts.index],
                      alpha=0.8, edgecolor='black')

        ax4.set_xlabel('性能等级', fontsize=12)
        ax4.set_ylabel('站点数量', fontsize=12)
        ax4.set_title('站点主要性能等级分布', fontsize=14, fontweight='bold')

        # 添加数值标签
        for bar, value in zip(bars, station_perf_counts.values):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    str(value), ha='center', va='bottom', fontweight='bold')

        ax4.grid(True, alpha=0.3, axis='y')

        # 5. NSE分布直方图按性能等级着色 (第二行第三、四列)
        ax5 = fig.add_subplot(gs[1, 2:])

        # 为每个性能等级绘制直方图
        for level in ['优秀', '良好', '满意', '不满意', '不可接受']:
            if level in df['性能等级'].values:
                level_data = df[df['性能等级'] == level]['NSE']
                ax5.hist(level_data, bins=20, alpha=0.6,
                        color=self.performance_colors.get(level, '#808080'),
                        label=f'{level} ({len(level_data)})',
                        edgecolor='black', linewidth=0.5)

        ax5.set_xlabel('NSE', fontsize=12)
        ax5.set_ylabel('频数', fontsize=12)
        ax5.set_title('NSE分布直方图（按性能等级）', fontsize=14, fontweight='bold')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

        # 添加性能等级分界线
        ax5.axvline(x=0.2, color='red', linestyle='--', alpha=0.7, linewidth=2)
        ax5.axvline(x=0.5, color='orange', linestyle='--', alpha=0.7, linewidth=2)
        ax5.axvline(x=0.65, color='yellow', linestyle='--', alpha=0.7, linewidth=2)
        ax5.axvline(x=0.75, color='green', linestyle='--', alpha=0.7, linewidth=2)

        # 6. 事件性能分类热力图 (第三行)
        ax6 = fig.add_subplot(gs[2, :])

        # 创建事件-性能等级的交叉表
        event_performance = df.groupby(['事件名称', '性能等级']).size().unstack(fill_value=0)

        # 计算百分比
        event_performance_pct = event_performance.div(event_performance.sum(axis=1), axis=0) * 100

        # 只显示前20个事件
        if len(event_performance_pct) > 20:
            event_performance_pct = event_performance_pct.head(20)

        sns.heatmap(event_performance_pct, annot=True, fmt='.1f', cmap='RdYlBu_r',
                   ax=ax6, cbar_kws={'label': '百分比 (%)'})
        ax6.set_title('事件性能等级分布热力图 (前20个事件)', fontsize=14, fontweight='bold')
        ax6.set_xlabel('性能等级', fontsize=12)
        ax6.set_ylabel('事件名称', fontsize=12)

        plt.suptitle('Delaunay插值系统性能分类详细分析', fontsize=18, fontweight='bold', y=0.98)

        # 保存图片
        save_path = self.viz_dir / 'performance_classification_analysis.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        self.logger.info(f"性能分类详细分析图已保存: {save_path}")
        return save_path

    def generate_comprehensive_report(self, df: pd.DataFrame):
        """生成综合分析报告"""
        self.logger.info("生成综合分析报告...")

        report_path = self.viz_dir / 'comprehensive_analysis_report.md'

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# Delaunay三角剖分插值系统综合可视化分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 基本统计信息
            f.write("## 1. 基本统计信息\n\n")
            f.write(f"- **总记录数**: {len(df):,}\n")
            f.write(f"- **洪水事件数**: {df['事件名称'].nunique()}\n")
            f.write(f"- **验证站点数**: {df['站点代码'].nunique()}\n")
            f.write(f"- **时间跨度**: {df['年份'].min()}-{df['年份'].max()}\n")
            f.write(f"- **平均每年事件数**: {df.groupby('年份')['事件名称'].nunique().mean():.1f}\n\n")

            # 整体性能指标
            f.write("## 2. 整体性能指标\n\n")
            metrics = ['NSE', 'RMSE', 'MAE', 'Correlation']
            f.write("| 指标 | 均值 | 标准差 | 最小值 | 最大值 | 中位数 |\n")
            f.write("|------|------|--------|--------|--------|---------|\n")

            for metric in metrics:
                values = df[metric].dropna()
                f.write(f"| {metric} | {values.mean():.4f} | {values.std():.4f} | "
                       f"{values.min():.4f} | {values.max():.4f} | {values.median():.4f} |\n")
            f.write("\n")

            # 性能分类统计
            f.write("## 3. 性能分类统计\n\n")
            df['性能等级'] = df['NSE'].apply(self.classify_performance)
            performance_counts = df['性能等级'].value_counts()

            f.write("| 性能等级 | 数量 | 占比 |\n")
            f.write("|----------|------|------|\n")
            for level, count in performance_counts.items():
                percentage = (count / len(df)) * 100
                f.write(f"| {level} | {count} | {percentage:.1f}% |\n")
            f.write("\n")

            # 年度趋势分析
            f.write("## 4. 年度趋势分析\n\n")
            yearly_stats = df.groupby('年份')['NSE'].agg(['mean', 'std', 'count'])

            f.write("| 年份 | 平均NSE | 标准差 | 事件数 |\n")
            f.write("|------|---------|--------|--------|\n")
            for year, stats in yearly_stats.iterrows():
                f.write(f"| {year} | {stats['mean']:.4f} | {stats['std']:.4f} | {stats['count']} |\n")
            f.write("\n")

            # 最佳和最差表现
            f.write("## 5. 最佳和最差表现\n\n")

            # 最佳事件
            best_events = df.groupby('事件名称')['NSE'].mean().nlargest(5)
            f.write("### 最佳表现事件 (Top 5)\n")
            for i, (event, nse) in enumerate(best_events.items(), 1):
                f.write(f"{i}. **{event}**: NSE = {nse:.4f}\n")
            f.write("\n")

            # 最佳站点
            best_stations = df.groupby('站点代码')['NSE'].mean().nlargest(5)
            f.write("### 最佳表现站点 (Top 5)\n")
            for i, (station, nse) in enumerate(best_stations.items(), 1):
                f.write(f"{i}. **{station}**: 平均NSE = {nse:.4f}\n")
            f.write("\n")

            # 最差事件
            worst_events = df.groupby('事件名称')['NSE'].mean().nsmallest(5)
            f.write("### 最差表现事件 (Bottom 5)\n")
            for i, (event, nse) in enumerate(worst_events.items(), 1):
                f.write(f"{i}. **{event}**: NSE = {nse:.4f}\n")
            f.write("\n")

            # 结论和建议
            f.write("## 6. 结论和建议\n\n")
            avg_nse = df['NSE'].mean()

            if avg_nse > 0.7:
                f.write("- Delaunay三角剖分插值方法在本研究区域表现**优秀**\n")
            elif avg_nse > 0.5:
                f.write("- Delaunay三角剖分插值方法在本研究区域表现**良好**\n")
            elif avg_nse > 0.2:
                f.write("- Delaunay三角剖分插值方法在本研究区域表现**一般**\n")
            else:
                f.write("- Delaunay三角剖分插值方法在本研究区域表现**较差**\n")

            f.write("- 不同站点和事件的插值精度存在显著差异\n")
            f.write("- 建议针对表现较差的站点和事件进行深入分析\n")
            f.write("- 可考虑结合地形、气象等因子优化插值方法\n\n")

            # 生成的图表列表
            f.write("## 7. 生成的可视化图表\n\n")
            f.write("1. **整体评价指标分布图** (`overall_metrics_distribution.png`)\n")
            f.write("2. **站点年平均分析图** (`station_annual_analysis.png`)\n")
            f.write("3. **站点多年表现分析图** (`station_multiyear_performance_page_*.png`)\n")
            f.write("4. **时间趋势分析图** (`temporal_trends_analysis.png`)\n")
            f.write("5. **性能分类详细分析图** (`performance_classification_analysis.png`)\n\n")

            f.write("---\n")
            f.write(f"**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("**分析工具**: Delaunay三角剖分插值系统高级可视化模块\n")

        self.logger.info(f"综合分析报告已保存: {report_path}")
        return report_path

    def generate_all_visualizations(self, metrics_dir: Path):
        """生成所有可视化图表"""
        self.logger.info("开始生成所有可视化图表...")

        try:
            # 加载数据
            df = self.load_all_metrics(metrics_dir)

            # 生成各种可视化图表
            generated_files = []

            # 1. 整体评价指标分布
            path1 = self.plot_overall_metrics_distribution(df)
            generated_files.append(path1)

            # 2. 站点年平均分析
            path2 = self.plot_station_annual_analysis(df)
            generated_files.append(path2)

            # 3. 站点多年表现分析
            paths3 = self.plot_station_multiyear_performance(df)
            generated_files.extend(paths3)

            # 4. 时间趋势分析
            path4 = self.plot_temporal_trends_analysis(df)
            generated_files.append(path4)

            # 5. 性能分类详细分析
            path5 = self.plot_performance_classification_analysis(df)
            generated_files.append(path5)

            # 6. 生成综合报告
            report_path = self.generate_comprehensive_report(df)
            generated_files.append(report_path)

            self.logger.info(f"所有可视化图表生成完成，共生成 {len(generated_files)} 个文件")

            # 打印生成的文件列表
            print("\n" + "="*80)
            print("Delaunay插值系统高级可视化分析完成")
            print("="*80)
            print(f"数据统计:")
            print(f"  - 总记录数: {len(df):,}")
            print(f"  - 洪水事件数: {df['事件名称'].nunique()}")
            print(f"  - 验证站点数: {df['站点代码'].nunique()}")
            print(f"  - 时间跨度: {df['年份'].min()}-{df['年份'].max()}")
            print(f"\n生成的文件:")
            for i, file_path in enumerate(generated_files, 1):
                print(f"  {i}. {file_path.name}")
            print(f"\n所有文件保存在: {self.viz_dir}")
            print("="*80)

            return generated_files

        except Exception as e:
            self.logger.error(f"生成可视化图表失败: {e}")
            raise


def main():
    """主程序"""
    import sys
    from pathlib import Path

    # 设置路径
    current_dir = Path(__file__).parent
    output_dir = current_dir.parent / 'output' / 'Delaunay_interpolation'
    metrics_dir = output_dir / 'metrics'

    # 检查输入目录
    if not metrics_dir.exists():
        print(f"错误: 评价指标目录不存在: {metrics_dir}")
        print("请先运行Delaunay插值分析生成评价指标数据")
        return 1

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        # 创建可视化器
        visualizer = DelaunayAdvancedVisualizer(output_dir)

        # 生成所有可视化图表
        generated_files = visualizer.generate_all_visualizations(metrics_dir)

        print(f"\n✅ 高级可视化分析完成！")
        print(f"📁 结果保存在: {visualizer.viz_dir}")

        return 0

    except Exception as e:
        print(f"\n❌ 可视化分析失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay插值系统内存优化运行脚本

专门用于内存受限环境的轻量级运行版本
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from pathlib import Path
import warnings
import json
import gc

warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from config import DelaunayConfig
from data_loader import DelaunayDataLoader
from evaluation_metrics import EvaluationMetrics
from delaunay_interpolator import DelaunayInterpolator
from memory_monitor import get_memory_monitor, check_memory_and_optimize

def setup_minimal_logging(config):
    """设置最小化日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("Delaunay插值系统（内存优化版）启动")
    return logger

def save_minimal_results(results, output_dir, filename):
    """保存结果（最小化版本）"""
    try:
        filepath = output_dir / filename
        
        if filename.endswith('.csv'):
            # 使用更少内存的方式保存CSV
            results.to_csv(filepath, index=True, encoding='utf-8', float_format='%.6f')
        elif filename.endswith('.json'):
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        logging.info(f"结果已保存: {filepath}")
        
    except Exception as e:
        logging.error(f"保存结果失败: {e}")

def process_event_minimal(event_name, interpolator, evaluator, config):
    """处理单个洪水事件（最小化版本）"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"开始处理洪水事件: {event_name}")
        
        # 检查内存
        memory_monitor = get_memory_monitor()
        memory_before = memory_monitor.get_memory_usage()
        logger.info(f"处理前内存: {memory_before['rss_mb']:.1f}MB")
        
        # 创建事件输出目录
        event_output_dir = config.get_event_output_dir(event_name)
        
        # 进行插值
        event_results = interpolator.interpolate_flood_event(event_name, memory_efficient=True)
        
        if not event_results:
            logger.warning(f"洪水事件{event_name}无插值结果")
            return None
        
        # 计算评估指标
        event_metrics = evaluator.evaluate_event_performance(event_results)
        
        # 只保存关键结果
        try:
            # 保存评估指标
            metrics_df = pd.DataFrame(event_metrics).T
            save_minimal_results(metrics_df, event_output_dir, f"{event_name}_metrics.csv")
            del metrics_df
            
            # 计算并保存汇总统计
            summary_stats = evaluator.calculate_summary_statistics(event_metrics)
            save_minimal_results(summary_stats, event_output_dir, f"{event_name}_summary.json")
            
            logger.info(f"洪水事件{event_name}处理完成")
            logger.info(f"  - 处理站点数: {len(event_results)}")
            logger.info(f"  - 平均NSE: {summary_stats.get('NSE_mean', np.nan):.4f}")
            logger.info(f"  - NSE>0.7比例: {summary_stats.get('NSE_good_ratio', 0):.1%}")
            
            # 创建轻量级返回结果
            result = {
                'event_name': event_name,
                'metrics': event_metrics,
                'summary': summary_stats,
                'station_count': len(event_results)
            }
            
            # 立即清理大型数据
            del event_results
            gc.collect()
            
            # 检查内存使用
            memory_after = memory_monitor.get_memory_usage()
            logger.info(f"处理后内存: {memory_after['rss_mb']:.1f}MB")
            
            return result
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            return None
        
    except Exception as e:
        logger.error(f"处理洪水事件{event_name}失败: {e}")
        gc.collect()
        return None

def main():
    """主函数（内存优化版本）"""
    try:
        print("Delaunay插值系统启动（内存优化版）...")
        
        # 初始化内存监控
        memory_monitor = get_memory_monitor()
        initial_memory = memory_monitor.get_memory_usage()
        print(f"初始内存使用: {initial_memory['rss_mb']:.1f}MB")
        
        # 初始化配置（内存优化）
        config = DelaunayConfig()
        
        # 强制启用内存优化设置
        config.MEMORY_EFFICIENT_MODE = True
        config.MAX_EVENTS_IN_MEMORY = 1
        config.CHUNK_SIZE = 50
        config.BATCH_SIZE = 5
        config.GENERATE_PLOTS = False  # 关闭图表生成
        config.SAVE_DETAILED_RESULTS = False  # 只保存汇总结果
        
        # 验证配置
        config_errors = config.validate_config()
        if config_errors:
            print("配置验证失败:")
            for error in config_errors:
                print(f"  - {error}")
            return 1
        
        # 设置最小化日志
        logger = setup_minimal_logging(config)
        logger.info("内存优化模式已启用")
        
        # 初始化组件
        data_loader = DelaunayDataLoader(config)
        evaluator = EvaluationMetrics(config)
        interpolator = DelaunayInterpolator(config, data_loader, evaluator)
        
        # 加载Delaunay分析结果
        logger.info("加载Delaunay分析结果...")
        delaunay_analysis = data_loader.load_delaunay_analysis()
        logger.info(f"成功加载{len(delaunay_analysis)}个站点的分析结果")
        
        # 获取洪水事件列表
        flood_events = config.get_flood_events()
        logger.info(f"发现{len(flood_events)}个洪水事件")
        
        if not flood_events:
            logger.warning("未发现洪水事件，程序退出")
            return 1
        
        # 逐个处理洪水事件（严格的内存管理）
        all_results = []
        
        for i, event_name in enumerate(flood_events):
            logger.info(f"处理洪水事件 {i+1}/{len(flood_events)}: {event_name}")
            
            # 处理前检查内存
            check_memory_and_optimize(threshold_mb=500)
            
            result = process_event_minimal(event_name, interpolator, evaluator, config)
            
            if result:
                all_results.append(result)
            
            # 处理后强制清理内存
            if hasattr(data_loader, 'clear_cache'):
                data_loader.clear_cache()
            
            memory_monitor.optimize_memory()
            
            # 记录内存状态
            current_memory = memory_monitor.get_memory_usage()
            logger.info(f"事件{event_name}完成后内存: {current_memory['rss_mb']:.1f}MB")
        
        # 生成最终报告
        logger.info("生成最终报告...")
        
        # 收集所有指标
        all_metrics = {}
        overall_summary = {
            'total_events': len(all_results),
            'total_stations': 0,
            'events_summary': {}
        }
        
        for result in all_results:
            if result is None:
                continue
                
            event_name = result['event_name']
            event_metrics = result['metrics']
            event_summary = result['summary']
            
            all_metrics[event_name] = event_metrics
            overall_summary['events_summary'][event_name] = event_summary
            overall_summary['total_stations'] += len(event_metrics)
        
        # 计算总体统计
        all_station_metrics = {}
        for event_metrics in all_metrics.values():
            all_station_metrics.update(event_metrics)
        
        overall_stats = evaluator.calculate_summary_statistics(all_station_metrics)
        overall_summary['overall_statistics'] = overall_stats
        
        # 保存总体报告
        save_minimal_results(overall_summary, config.OUTPUT_DIR, "overall_report.json")
        
        # 生成详细的总体指标表
        all_metrics_df = pd.DataFrame()
        for event_name, event_metrics in all_metrics.items():
            for station_id, station_metrics in event_metrics.items():
                row = station_metrics.copy()
                row['event_name'] = event_name
                row['station_id'] = station_id
                all_metrics_df = pd.concat([all_metrics_df, pd.DataFrame([row])], ignore_index=True)
        
        if not all_metrics_df.empty:
            save_minimal_results(all_metrics_df, config.OUTPUT_DIR, "all_stations_metrics.csv")
        
        # 打印总体统计
        logger.info("="*60)
        logger.info("Delaunay插值系统总体性能报告（内存优化版）")
        logger.info("="*60)
        logger.info(f"处理洪水事件数: {overall_summary['total_events']}")
        logger.info(f"总站点数: {overall_summary['total_stations']}")
        logger.info(f"平均NSE: {overall_stats.get('NSE_mean', np.nan):.4f}")
        logger.info(f"NSE>0.7站点数: {overall_stats.get('good_nse_stations', 0)}")
        logger.info(f"NSE>0.7比例: {overall_stats.get('NSE_good_ratio', 0):.1%}")
        logger.info(f"平均MAE: {overall_stats.get('MAE_mean', np.nan):.4f}")
        logger.info(f"平均RMSE: {overall_stats.get('RMSE_mean', np.nan):.4f}")
        
        # 显示内存使用情况
        final_memory = memory_monitor.get_memory_usage()
        logger.info(f"最终内存使用: {final_memory['rss_mb']:.1f}MB")
        logger.info("="*60)
        
        logger.info("Delaunay插值系统运行完成！")
        logger.info(f"结果保存在: {config.OUTPUT_DIR}")
        
        return 0
        
    except Exception as e:
        print(f"程序运行失败: {e}")
        if 'logger' in locals():
            logger.error(f"程序运行失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

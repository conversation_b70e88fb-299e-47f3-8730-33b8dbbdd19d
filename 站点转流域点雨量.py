"""输入
├─ 2009-1/点雨量├─80629100.csv
├─ 80607800.csv
....
└─80629700.csv
├─ 2009-2/点雨量├─80629100.csv
├─ 80607800.csv
....
└─80629700.csv
.....
└─ 2023-1/点雨量├─80629100.csv
├─ 80607800.csv
....
└─80629700.csv
——————————————————
输出：
————————————————————————
.D:/pythondate/spline/
├─ 2009-1/点雨量.csv
├─ 2009-2/点雨量.csv
.....
└─ 2023-1/点雨量.csv
——————————————————————————"""



import os 
import pandas as pd 
 
# 定义输入和输出路径 
input_base_path = r"D:/蒙江流域/奕方处理数据"  # 输入路径根目录，根据实际情况修改 
output_base_path = 'D:/pythondata/spline/'  # 输出路径根目录 
 
# 确保输出目录存在 
if not os.path.exists(output_base_path):  
    os.makedirs(output_base_path)  
 
# 遍历输入路径下的所有文件夹 
for year_month_dir in os.listdir(input_base_path):  
    if os.path.isdir(os.path.join(input_base_path,  year_month_dir)): 
        point_rainfall_dir = os.path.join(input_base_path,  year_month_dir, '点雨量') 
        if os.path.exists(point_rainfall_dir):  
            # 用于存储每个文件的数据 
            data_frames = {} 
            # 遍历点雨量文件夹下的所有 CSV 文件 
            for csv_file in os.listdir(point_rainfall_dir):  
                if csv_file.endswith('.csv'):  
                    station_id = os.path.splitext(csv_file)[0]  
                    file_path = os.path.join(point_rainfall_dir,  csv_file) 
                    # 读取 CSV 文件 
                    df = pd.read_csv(file_path)  
                    # 设置时间列为索引 
                    df.set_index('时间', inplace=True) 
                    data_frames[station_id] = df 
 
            # 合并所有数据 
            merged_df = pd.concat(data_frames,  axis=1) 
            # 重命名列 
            merged_df.columns  = [col[0] for col in merged_df.columns]  
 
            # 保存合并后的数据到输出路径 
            output_file_path = os.path.join(output_base_path,  f'{year_month_dir}/点雨量.csv') 
            output_dir = os.path.dirname(output_file_path)  
            if not os.path.exists(output_dir):  
                os.makedirs(output_dir)  
            merged_df.to_csv(output_file_path)  
 
print("数据整合完成。") 
import os
import pandas as pd

# 定义包含所有站点 CSV 文件的文件夹路径
folder_path = 'D:/pythondata/spline/all'  # 请替换为实际的文件夹
##D:/pythondata/spatial_interpolation/output/PRISM/2009-1/points
###D:/pythondata/spline/全年/2012-2/点雨量
# 初始化一个空字典，用于存储每个站点的数据
site_data = {}

# 遍历文件夹中的所有 CSV 文件
for filename in os.listdir(folder_path):
    if filename.endswith('.csv'):
        # 获取站点 ID（文件名去掉扩展名）
        site_id = os.path.splitext(filename)[0]
        # 读取 CSV 文件
        file_path = os.path.join(folder_path, filename)
        df = pd.read_csv(file_path)
        # 将时间列设置为索引
        df.set_index('时间', inplace=True)
        # 提取雨量列
        site_data[site_id] = df['雨量']

# 将字典中的数据转换为 DataFrame
merged_df = pd.DataFrame(site_data)

# 重置索引，使时间成为一列
merged_df.reset_index(inplace=True)

# 保存合并后的数据到新的 CSV 文件
output_file_path = 'D:/pythondata/spatial_interpolation/input_二次筛选/all/rain.csv'  # 可自定义输出文件路径
merged_df.to_csv(output_file_path, index=False)

print(f"合并完成，结果已保存到 {output_file_path}")
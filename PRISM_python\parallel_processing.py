"""
并行处理模块
实现多进程并行计算支持
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Any
from multiprocessing import Pool, cpu_count
from functools import partial
import gc
from tqdm import tqdm

logger = logging.getLogger(__name__)


class ParallelProcessor:
    """并行处理器类"""
    
    def __init__(self, config):
        """初始化并行处理器"""
        self.config = config
        self.num_cores = min(config.num_cores, cpu_count())
        logger.info(f"并行处理器初始化，使用 {self.num_cores} 个核心")
    
    def process_time_point_interpolation(self, args: Tuple) -> Dict:
        """处理单个时间点的插值（用于并行计算）"""
        try:
            (time_point, stations_with_terrain, rainfall_at_time, 
             delaunay_tri_info, prism_core_config, moran_calculator_config) = args
            
            # 重新创建必要的对象（因为多进程无法共享复杂对象）
            from prism_core import PRISMCore
            from moran_index import MoranIndex
            from delaunay_triangulation import DelaunayTriangulation
            
            # 创建本地对象
            prism_core = PRISMCore(prism_core_config)
            moran_calculator = MoranIndex(moran_calculator_config)
            delaunay_tri = DelaunayTriangulation(prism_core_config)
            
            # 重建三角网（使用传递的信息）
            delaunay_tri.triangulation = delaunay_tri_info['delaunay']
            delaunay_tri.points = delaunay_tri_info['points']
            delaunay_tri.kdtree = delaunay_tri_info['kdtree']
            delaunay_tri.stations_df = stations_with_terrain

            # 重建站点索引映射（这是关键！）
            station_names = stations_with_terrain['站点'].values
            delaunay_tri.station_indices = {name: idx for idx, name in enumerate(station_names)}
            
            # 检查是否为全零降雨
            rainfall_values = list(rainfall_at_time.values())
            if all(value == 0 for value in rainfall_values):
                # 全零降雨，直接返回零值结果
                n_stations = len(stations_with_terrain)
                return {
                    'time_point': time_point,
                    'observed_values': np.zeros(n_stations),
                    'predicted_values': np.zeros(n_stations),
                    'interpolation_methods': ['零降雨'] * n_stations,
                    'interpolation_details': [{'method': '零降雨'}] * n_stations,
                    'rainfall_data': rainfall_at_time
                }
            
            # 计算莫兰指数
            stations_with_moran = moran_calculator.calculate_moran_for_time_point(
                stations_with_terrain, rainfall_at_time
            )
            
            # 执行留一法插值
            observed_values = []
            predicted_values = []
            interpolation_methods = []
            interpolation_details = []
            
            for station_idx in range(len(stations_with_moran)):
                station_name = stations_with_moran.iloc[station_idx]['站点']
                
                # 获取真实观测值
                observed_value = rainfall_at_time.get(station_name, 0.0)
                observed_values.append(observed_value)
                
                # 创建留一法数据集（移除当前站点）
                leave_one_out_rainfall = rainfall_at_time.copy()
                leave_one_out_rainfall.pop(station_name, None)
                
                # 找到邻近站点
                neighbor_indices = delaunay_tri.find_neighbors_by_triangulation(
                    station_name, stations_with_moran, max_neighbors=4
                )
                
                # 过滤掉测试站点本身
                neighbor_indices = [idx for idx in neighbor_indices 
                                  if stations_with_moran.iloc[idx]['站点'] != station_name]
                
                if len(neighbor_indices) >= 2:  # 至少需要2个邻站
                    # 执行PRISM插值
                    predicted_value, interp_detail = prism_core.interpolate_point(
                        station_idx, neighbor_indices, stations_with_moran, leave_one_out_rainfall
                    )
                    
                    predicted_values.append(predicted_value)
                    interpolation_methods.append(interp_detail['method'])
                    interpolation_details.append(interp_detail)
                else:
                    # 邻站不足，使用简单平均
                    if leave_one_out_rainfall:
                        avg_value = np.mean(list(leave_one_out_rainfall.values()))
                    else:
                        avg_value = 0.0
                    
                    predicted_values.append(avg_value)
                    interpolation_methods.append('简单平均')
                    interpolation_details.append({
                        'method': '简单平均',
                        'neighbor_stations': [],
                        'weights': []
                    })
            
            return {
                'time_point': time_point,
                'observed_values': np.array(observed_values),
                'predicted_values': np.array(predicted_values),
                'interpolation_methods': interpolation_methods,
                'interpolation_details': interpolation_details,
                'rainfall_data': rainfall_at_time
            }
            
        except Exception as e:
            logger.error(f"处理时间点 {time_point} 失败: {e}")
            # 返回空结果
            n_stations = len(stations_with_terrain) if 'stations_with_terrain' in locals() else 0
            return {
                'time_point': time_point,
                'observed_values': np.zeros(n_stations),
                'predicted_values': np.zeros(n_stations),
                'interpolation_methods': ['失败'] * n_stations,
                'interpolation_details': [{'method': '失败'}] * n_stations,
                'rainfall_data': rainfall_at_time if 'rainfall_at_time' in locals() else {}
            }
    
    def parallel_interpolation(self, time_points: List, stations_with_terrain: pd.DataFrame,
                             rainfall_data: Dict, delaunay_tri, prism_core, 
                             moran_calculator) -> Dict:
        """并行执行时间序列插值"""
        try:
            logger.info(f"开始并行插值处理，共 {len(time_points)} 个时间点")
            
            # 准备三角网信息（用于传递给子进程）
            delaunay_tri_info = {
                'delaunay': delaunay_tri.triangulation,
                'points': delaunay_tri.points,
                'kdtree': delaunay_tri.kdtree
            }
            
            # 准备参数列表
            args_list = []
            for time_point in time_points:
                # 获取该时间点的降雨数据
                rainfall_at_time = {}
                for station_name in stations_with_terrain['站点']:
                    if station_name in rainfall_data:
                        station_data = rainfall_data[station_name]
                        time_mask = station_data['时间'] == time_point
                        if time_mask.any():
                            rainfall_at_time[station_name] = station_data.loc[time_mask, '雨量'].iloc[0]
                        else:
                            rainfall_at_time[station_name] = 0.0
                    else:
                        rainfall_at_time[station_name] = 0.0
                
                args_list.append((
                    time_point,
                    stations_with_terrain,
                    rainfall_at_time,
                    delaunay_tri_info,
                    prism_core.config,
                    moran_calculator.config
                ))
            
            # 执行并行计算
            results = {}
            
            if self.num_cores > 1:
                # 多进程并行
                with Pool(self.num_cores) as pool:
                    parallel_results = list(tqdm(
                        pool.imap(self.process_time_point_interpolation, args_list),
                        total=len(args_list),
                        desc="并行插值进度"
                    ))
                
                # 整理结果
                for result in parallel_results:
                    if result and 'time_point' in result:
                        results[result['time_point']] = result
            else:
                # 单进程处理
                for args in tqdm(args_list, desc="串行插值进度"):
                    result = self.process_time_point_interpolation(args)
                    if result and 'time_point' in result:
                        results[result['time_point']] = result
            
            logger.info(f"并行插值完成，成功处理 {len(results)} 个时间点")
            
            # 强制垃圾回收
            if self.config.memory_efficient:
                gc.collect()
            
            return results
            
        except Exception as e:
            logger.error(f"并行插值处理失败: {e}")
            return {}
    
    def process_batch_folders(self, folder_list: List[str], process_function) -> Dict:
        """并行处理批量文件夹"""
        try:
            logger.info(f"开始并行处理 {len(folder_list)} 个文件夹")
            
            if self.num_cores > 1 and len(folder_list) > 1:
                # 多进程并行处理文件夹
                with Pool(min(self.num_cores, len(folder_list))) as pool:
                    batch_results = list(tqdm(
                        pool.imap(process_function, folder_list),
                        total=len(folder_list),
                        desc="批量处理进度"
                    ))
                
                # 整理结果
                results = {}
                for i, result in enumerate(batch_results):
                    if result:
                        results[folder_list[i]] = result
            else:
                # 串行处理
                results = {}
                for folder in tqdm(folder_list, desc="串行批量处理"):
                    result = process_function(folder)
                    if result:
                        results[folder] = result
            
            logger.info(f"批量处理完成，成功处理 {len(results)} 个文件夹")
            return results
            
        except Exception as e:
            logger.error(f"批量并行处理失败: {e}")
            return {}
    
    def chunk_processing(self, data_list: List, chunk_size: int, process_function) -> List:
        """分块并行处理大数据集"""
        try:
            # 将数据分块
            chunks = [data_list[i:i + chunk_size] for i in range(0, len(data_list), chunk_size)]
            
            logger.info(f"数据分为 {len(chunks)} 个块进行处理")
            
            all_results = []
            
            if self.num_cores > 1:
                # 并行处理块
                with Pool(self.num_cores) as pool:
                    chunk_results = list(tqdm(
                        pool.imap(process_function, chunks),
                        total=len(chunks),
                        desc="分块处理进度"
                    ))
                
                # 合并结果
                for chunk_result in chunk_results:
                    if chunk_result:
                        all_results.extend(chunk_result)
            else:
                # 串行处理块
                for chunk in tqdm(chunks, desc="串行分块处理"):
                    chunk_result = process_function(chunk)
                    if chunk_result:
                        all_results.extend(chunk_result)
            
            return all_results
            
        except Exception as e:
            logger.error(f"分块并行处理失败: {e}")
            return []
    
    def get_optimal_chunk_size(self, total_items: int, target_chunks: int = None) -> int:
        """计算最优分块大小"""
        if target_chunks is None:
            target_chunks = self.num_cores * 2  # 每个核心处理2个块
        
        chunk_size = max(1, total_items // target_chunks)
        
        # 确保块大小合理
        min_chunk_size = 10
        max_chunk_size = 1000
        
        chunk_size = max(min_chunk_size, min(chunk_size, max_chunk_size))
        
        return chunk_size

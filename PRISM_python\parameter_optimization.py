"""
PRISM参数优化模块
自动优化插值参数以提高NSE等评价指标
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional
from scipy.optimize import minimize, differential_evolution
import json
import os
from datetime import datetime

from config import Config
from prism_main import PRISMInterpolation

logger = logging.getLogger(__name__)


class PRISMParameterOptimizer:
    """PRISM参数优化器"""
    
    def __init__(self, base_config: Config):
        """初始化参数优化器"""
        self.base_config = base_config
        self.optimization_history = []
        self.best_params = None
        self.best_score = -np.inf
        
    def optimize_parameters(self, target_metric: str = 'NSE', 
                          optimization_method: str = 'differential_evolution',
                          max_iterations: int = 50) -> Dict:
        """优化PRISM参数
        
        Args:
            target_metric: 目标优化指标 ('NSE', 'R2', 'RMSE_inv')
            optimization_method: 优化方法 ('differential_evolution', 'minimize')
            max_iterations: 最大迭代次数
        
        Returns:
            优化结果字典
        """
        try:
            logger.info(f"开始参数优化，目标指标: {target_metric}")
            
            # 定义参数边界
            bounds = self._get_parameter_bounds()
            
            # 定义目标函数
            def objective_function(params):
                return self._evaluate_parameters(params, target_metric)
            
            # 执行优化
            if optimization_method == 'differential_evolution':
                result = differential_evolution(
                    objective_function,
                    bounds,
                    maxiter=max_iterations,
                    popsize=10,
                    seed=42,
                    disp=True,
                    callback=self._optimization_callback
                )
            else:
                # 使用当前参数作为初始值
                x0 = self._config_to_params(self.base_config)
                result = minimize(
                    objective_function,
                    x0,
                    bounds=bounds,
                    method='L-BFGS-B',
                    options={'maxiter': max_iterations, 'disp': True}
                )
            
            # 处理优化结果
            if result.success or hasattr(result, 'x'):
                optimal_params = result.x
                optimal_config = self._params_to_config(optimal_params)
                optimal_score = -result.fun if hasattr(result, 'fun') else self.best_score
                
                optimization_result = {
                    'success': True,
                    'optimal_params': optimal_params.tolist(),
                    'optimal_config': optimal_config.to_dict(),
                    'optimal_score': optimal_score,
                    'iterations': len(self.optimization_history),
                    'optimization_history': self.optimization_history,
                    'target_metric': target_metric,
                    'method': optimization_method
                }
                
                logger.info(f"参数优化成功完成，最优{target_metric}: {optimal_score:.4f}")
                return optimization_result
            else:
                logger.error("参数优化失败")
                return {'success': False, 'message': '优化算法未收敛'}
                
        except Exception as e:
            logger.error(f"参数优化过程中出错: {e}")
            return {'success': False, 'message': str(e)}
    
    def _get_parameter_bounds(self) -> List[Tuple[float, float]]:
        """获取参数优化边界"""
        bounds = [
            (2, 5),        # neighbor_count
            (1.0, 3.0),    # distance_power
            (0.1, 0.7),    # elevation_weight
            (0.1, 0.5),    # slope_weight
            (0.1, 0.4),    # aspect_weight
            (0.0, 0.3),    # moran_weight
            (0.1, 2.0),    # rainfall_threshold
            (10.0, 45.0)   # min_triangle_angle
        ]
        return bounds
    
    def _config_to_params(self, config: Config) -> np.ndarray:
        """将配置转换为参数数组"""
        params = np.array([
            config.neighbor_count,
            config.distance_power,
            config.elevation_weight,
            config.slope_weight,
            config.aspect_weight,
            config.moran_weight,
            config.rainfall_threshold,
            config.min_triangle_angle
        ])
        return params
    
    def _params_to_config(self, params: np.ndarray) -> Config:
        """将参数数组转换为配置"""
        # 创建配置副本
        config = Config(
            input_dir=self.base_config.input_dir,
            terrain_dir=self.base_config.terrain_dir,
            output_dir=self.base_config.output_dir,
            stations_file=self.base_config.stations_file,
            num_cores=self.base_config.num_cores,
            batch_size=self.base_config.batch_size,
            memory_efficient=self.base_config.memory_efficient,
            output_raster=False,  # 优化时关闭栅格输出以节省时间
            output_delaunay_plot=False,
            output_weight_info=False,
            output_evaluation=True,
            debug_mode=self.base_config.debug_mode,
            verbose_logging=False  # 减少日志输出
        )
        
        # 设置优化参数
        config.neighbor_count = int(round(params[0]))
        config.distance_power = params[1]
        config.elevation_weight = params[2]
        config.slope_weight = params[3]
        config.aspect_weight = params[4]
        config.moran_weight = params[5]
        config.rainfall_threshold = params[6]
        config.min_triangle_angle = params[7]
        
        # 确保权重总和为1
        total_weight = (config.elevation_weight + config.slope_weight + 
                       config.aspect_weight + config.moran_weight)
        if total_weight > 1.0:
            # 按比例缩放
            scale_factor = 0.9 / total_weight  # 留10%给距离权重
            config.elevation_weight *= scale_factor
            config.slope_weight *= scale_factor
            config.aspect_weight *= scale_factor
            config.moran_weight *= scale_factor
        
        return config
    
    def _evaluate_parameters(self, params: np.ndarray, target_metric: str) -> float:
        """评估参数组合的性能"""
        try:
            # 转换参数为配置
            config = self._params_to_config(params)
            
            # 创建临时输出目录
            temp_output = os.path.join(self.base_config.output_dir, 'temp_optimization')
            config.output_dir = temp_output
            
            # 运行PRISM插值
            prism = PRISMInterpolation(config)
            evaluation_results = prism.run_complete_workflow()
            
            # 提取目标指标
            if evaluation_results and 'overall_metrics' in evaluation_results:
                metrics = evaluation_results['overall_metrics']
                
                if target_metric == 'NSE':
                    score = metrics.get('NSE', -np.inf)
                elif target_metric == 'R2':
                    score = metrics.get('R2', -np.inf)
                elif target_metric == 'RMSE_inv':
                    rmse = metrics.get('RMSE', np.inf)
                    score = -rmse if rmse != np.inf else -np.inf
                else:
                    score = metrics.get('NSE', -np.inf)
                
                # 记录优化历史
                param_dict = {
                    'neighbor_count': int(round(params[0])),
                    'distance_power': params[1],
                    'elevation_weight': params[2],
                    'slope_weight': params[3],
                    'aspect_weight': params[4],
                    'moran_weight': params[5],
                    'rainfall_threshold': params[6],
                    'min_triangle_angle': params[7],
                    target_metric: score
                }
                self.optimization_history.append(param_dict)
                
                # 更新最佳结果
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = params.copy()
                
                logger.info(f"参数评估: {target_metric}={score:.4f}, "
                           f"邻站数={int(round(params[0]))}, "
                           f"高程权重={params[2]:.3f}")
                
                # 清理临时文件
                self._cleanup_temp_files(temp_output)
                
                return -score  # 优化算法是最小化，所以返回负值
            else:
                logger.warning("评估失败，返回最差分数")
                return np.inf
                
        except Exception as e:
            logger.error(f"参数评估出错: {e}")
            return np.inf
    
    def _optimization_callback(self, xk, convergence=None):
        """优化过程回调函数"""
        iteration = len(self.optimization_history)
        if iteration % 5 == 0:
            logger.info(f"优化进度: 第{iteration}次迭代, 当前最佳分数: {self.best_score:.4f}")
    
    def _cleanup_temp_files(self, temp_dir: str):
        """清理临时文件"""
        try:
            if os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir)
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")
    
    def save_optimization_results(self, results: Dict, output_dir: str):
        """保存优化结果"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存优化结果JSON
            results_file = os.path.join(output_dir, f"optimization_results_{timestamp}.json")
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=4, ensure_ascii=False)
            
            # 保存优化历史CSV
            if self.optimization_history:
                history_df = pd.DataFrame(self.optimization_history)
                history_file = os.path.join(output_dir, f"optimization_history_{timestamp}.csv")
                history_df.to_csv(history_file, index=False, encoding='utf-8')
            
            # 保存最优配置
            if results.get('success') and 'optimal_config' in results:
                optimal_config = Config(**results['optimal_config'])
                config_file = os.path.join(output_dir, f"optimal_config_{timestamp}.json")
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(optimal_config.to_dict(), f, indent=4, ensure_ascii=False)
            
            logger.info(f"优化结果已保存到: {output_dir}")
            
        except Exception as e:
            logger.error(f"保存优化结果失败: {e}")


def optimize_poor_performance_events(batch_results: Dict, 
                                   nse_threshold: float = 0.5) -> Dict:
    """优化表现较差的洪水事件
    
    Args:
        batch_results: 批量处理结果
        nse_threshold: NSE阈值，低于此值的事件将被优化
    
    Returns:
        优化结果字典
    """
    try:
        logger.info(f"开始优化NSE < {nse_threshold}的洪水事件")
        
        poor_events = []
        optimization_results = {}
        
        # 识别表现较差的事件
        for event_name, event_result in batch_results.items():
            if isinstance(event_result, dict) and 'overall_nse' in event_result:
                nse = event_result['overall_nse']
                if nse < nse_threshold:
                    poor_events.append((event_name, nse))
        
        logger.info(f"发现 {len(poor_events)} 个需要优化的事件")
        
        # 对每个表现较差的事件进行优化
        for event_name, original_nse in poor_events:
            logger.info(f"正在优化事件: {event_name} (原始NSE: {original_nse:.4f})")
            
            # 创建该事件的配置
            base_config = Config()
            base_config.input_dir = os.path.join(base_config.batch_input_root, event_name)
            base_config.output_dir = os.path.join(base_config.batch_output_root, event_name)
            
            # 创建优化器
            optimizer = PRISMParameterOptimizer(base_config)
            
            # 执行优化
            opt_result = optimizer.optimize_parameters(
                target_metric='NSE',
                optimization_method='differential_evolution',
                max_iterations=30  # 减少迭代次数以节省时间
            )
            
            if opt_result.get('success'):
                improved_nse = opt_result['optimal_score']
                improvement = improved_nse - original_nse
                
                logger.info(f"事件 {event_name} 优化完成: "
                           f"NSE从 {original_nse:.4f} 提升到 {improved_nse:.4f} "
                           f"(提升 {improvement:.4f})")
                
                optimization_results[event_name] = {
                    'original_nse': original_nse,
                    'optimized_nse': improved_nse,
                    'improvement': improvement,
                    'optimal_config': opt_result['optimal_config'],
                    'optimization_history': opt_result['optimization_history']
                }
                
                # 保存优化结果
                opt_output_dir = os.path.join(base_config.output_dir, 'optimization')
                optimizer.save_optimization_results(opt_result, opt_output_dir)
            else:
                logger.warning(f"事件 {event_name} 优化失败")
                optimization_results[event_name] = {
                    'original_nse': original_nse,
                    'optimization_failed': True,
                    'error_message': opt_result.get('message', '未知错误')
                }
        
        return optimization_results
        
    except Exception as e:
        logger.error(f"批量优化过程中出错: {e}")
        return {}

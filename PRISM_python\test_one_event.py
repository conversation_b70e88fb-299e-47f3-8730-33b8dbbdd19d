"""
测试单个洪水事件
"""

import os
import sys
import logging

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from config import Config
from prism_main import PRISMInterpolation

# 设置简单日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_one_event():
    """测试单个事件"""
    event_name = "2009-1"
    
    print(f"测试事件: {event_name}")
    print("="*40)
    
    try:
        # 创建配置
        config = Config(
            input_dir=f"D:/pythondata/spatial_interpolation/input_another/{event_name}",
            terrain_dir="D:/pythondata/spatial_interpolation/terrain/90",
            output_dir=f"D:/pythondata/spatial_interpolation/output/PRISM/{event_name}_test",
            stations_file="D:/pythondata/spatial_interpolation/stations.csv",
            num_cores=4,  # 减少核心数
            memory_efficient=True,
            output_raster=False,  # 关闭栅格输出
            output_delaunay_plot=True,
            output_evaluation=True,
            verbose_logging=True
        )
        
        print(f"输入目录: {config.input_dir}")
        print(f"输出目录: {config.output_dir}")
        
        # 检查输入数据
        if not os.path.exists(config.input_dir):
            print(f"❌ 输入目录不存在")
            return False
        
        csv_files = [f for f in os.listdir(config.input_dir) if f.endswith('.csv')]
        print(f"发现 {len(csv_files)} 个CSV文件")
        
        if len(csv_files) == 0:
            print("❌ 没有CSV文件")
            return False
        
        # 运行PRISM插值
        print("开始PRISM插值...")
        prism = PRISMInterpolation(config)
        
        # 分步骤运行以便调试
        print("1. 加载数据...")
        data_loaded = prism.load_data()
        if not data_loaded:
            print("❌ 数据加载失败")
            return False
        print("✅ 数据加载成功")
        
        print("2. 构建Delaunay三角网...")
        triangulation_built = prism.build_triangulation()
        if not triangulation_built:
            print("❌ 三角网构建失败")
            return False
        print("✅ 三角网构建成功")
        
        print("3. 运行插值...")
        interpolation_done = prism.run_interpolation()
        if not interpolation_done:
            print("❌ 插值失败")
            return False
        print("✅ 插值完成")
        
        print("4. 评价结果...")
        evaluation_results = prism.evaluate_results()
        if not evaluation_results:
            print("❌ 评价失败")
            return False
        print("✅ 评价完成")
        
        # 显示结果
        if 'overall_metrics' in evaluation_results:
            metrics = evaluation_results['overall_metrics']
            print(f"\n评价结果:")
            print(f"  NSE: {metrics.get('NSE', 0):.4f}")
            print(f"  R²:  {metrics.get('R2', 0):.4f}")
            print(f"  RMSE: {metrics.get('RMSE', 0):.2f} mm")
            print(f"  MAE: {metrics.get('MAE', 0):.2f} mm")
            
            nse = metrics.get('NSE', 0)
            if nse > 0.75:
                print(f"  评价: 优秀 ✅")
            elif nse > 0.5:
                print(f"  评价: 良好 ⚠️")
            else:
                print(f"  评价: 需要优化 ❌")
        
        print(f"\n✅ 测试成功完成！")
        print(f"结果保存在: {config.output_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.error(f"测试失败", exc_info=True)
        return False


if __name__ == "__main__":
    success = test_one_event()
    if success:
        print("\n🎉 单事件测试成功！")
    else:
        print("\n❌ 单事件测试失败！")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay三角剖分插值系统改进可视化启动脚本

作者: 空间插值研究团队
日期: 2024年12月
版本: 2.1

改进功能：
1. 参考水晏泰森.xlsx中的NAME列获取中文站点名称
2. 处理站点代码大小写不敏感的问题
3. 确保所有洪水场次都能完整显示，必要时分多张图
4. 除了站点名为中文外，其他标签保持英文
"""

import sys
import os
from pathlib import Path
import logging

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_requirements():
    """检查运行要求"""
    print("Checking requirements...")
    
    # 检查Python包
    required_packages = ['pandas', 'numpy', 'matplotlib', 'seaborn', 'openpyxl']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required Python packages: {', '.join(missing_packages)}")
        print(f"Please run: pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ Python packages check passed")
    return True

def check_data_availability():
    """检查数据可用性"""
    print("\nChecking data availability...")
    
    # 检查输出目录
    output_dir = current_dir.parent / 'output' / 'Delaunay_interpolation'
    if not output_dir.exists():
        print(f"❌ Delaunay interpolation output directory does not exist: {output_dir}")
        print("Please run Delaunay interpolation analysis first")
        return False
    
    # 检查评价指标目录
    metrics_dir = output_dir / 'metrics'
    if not metrics_dir.exists():
        print(f"❌ Metrics directory does not exist: {metrics_dir}")
        print("Please run Delaunay interpolation analysis first")
        return False
    
    # 检查评价指标文件
    csv_files = list(metrics_dir.glob('*_metrics.csv'))
    if not csv_files:
        print(f"❌ No CSV files found in metrics directory: {metrics_dir}")
        print("Please run Delaunay interpolation analysis first")
        return False
    
    print(f"✅ Found {len(csv_files)} evaluation metric files")
    
    # 检查水晏泰森.xlsx文件
    station_file = current_dir.parent / '水晏泰森.xlsx'
    if not station_file.exists():
        print(f"⚠️  Station name file not found: {station_file}")
        print("Station codes will be used instead of Chinese names")
    else:
        print(f"✅ Station name file found: {station_file}")
    
    return True

def run_improved_visualization():
    """运行改进的可视化分析"""
    print("\nStarting improved visualization analysis...")
    print("="*60)
    
    try:
        from improved_visualization import ImprovedDelaunayVisualizer
        
        # 设置路径
        output_dir = current_dir.parent / 'output' / 'Delaunay_interpolation'
        metrics_dir = output_dir / 'metrics'
        
        # 创建可视化器
        visualizer = ImprovedDelaunayVisualizer(output_dir)
        
        # 生成所有可视化图表
        generated_files = visualizer.generate_all_visualizations(metrics_dir)
        
        print("\n" + "="*60)
        print("🎉 Improved visualization analysis completed!")
        print("="*60)
        
        return True, generated_files
        
    except Exception as e:
        print(f"\n❌ Visualization analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False, []

def show_results(generated_files):
    """显示结果"""
    if not generated_files:
        return
    
    print(f"\n📊 Generated visualization files ({len(generated_files)} files):")
    print("-" * 60)
    
    # 按类型分组显示
    file_groups = {
        'Overview': [],
        'All Events Heatmaps': [],
        'Station Multi-year Performance': [],
        'Station Annual Ranking': []
    }
    
    for file_path in generated_files:
        filename = file_path.name
        if 'comprehensive_overview' in filename:
            file_groups['Overview'].append(filename)
        elif 'all_events_heatmap' in filename:
            file_groups['All Events Heatmaps'].append(filename)
        elif 'multiyear_performance' in filename:
            file_groups['Station Multi-year Performance'].append(filename)
        elif 'annual_ranking' in filename:
            file_groups['Station Annual Ranking'].append(filename)
    
    for group_name, files in file_groups.items():
        if files:
            print(f"\n📁 {group_name}:")
            for filename in sorted(files):
                print(f"   • {filename}")
    
    # 显示保存路径
    viz_dir = generated_files[0].parent
    print(f"\n📂 All files saved in: {viz_dir}")
    
    # 提供使用建议
    print(f"\n💡 Key Features:")
    print(f"   ✅ Chinese station names from 水晏泰森.xlsx")
    print(f"   ✅ Case-insensitive station code matching")
    print(f"   ✅ All flood events displayed (split into multiple pages)")
    print(f"   ✅ All stations displayed (split into multiple pages)")
    print(f"   ✅ English labels except for station names")
    print(f"   ✅ High resolution (300 DPI) for academic publication")

def main():
    """主函数"""
    print("="*80)
    print("Delaunay Interpolation System - Improved Visualization Analysis")
    print("="*80)
    print("Improvements:")
    print("• Uses Chinese station names from 水晏泰森.xlsx")
    print("• Case-insensitive station code matching")
    print("• All flood events displayed (multiple pages if needed)")
    print("• All stations displayed (multiple pages if needed)")
    print("• English labels except for station names")
    print("="*80)
    
    try:
        # 1. 检查运行要求
        if not check_requirements():
            return 1
        
        # 2. 检查数据可用性
        if not check_data_availability():
            return 1
        
        # 3. 询问用户是否继续
        print(f"\nReady to start improved visualization analysis...")
        response = input("Continue? (y/n): ").strip().lower()
        if response not in ['y', 'yes', '是']:
            print("Operation cancelled by user")
            return 0
        
        # 4. 运行可视化分析
        success, generated_files = run_improved_visualization()
        if not success:
            return 1
        
        # 5. 显示结果
        show_results(generated_files)
        
        print(f"\n" + "="*80)
        print("✅ Improved visualization analysis completed!")
        print("🎯 You can now view the generated charts for in-depth analysis")
        print("="*80)
        
        return 0
        
    except KeyboardInterrupt:
        print(f"\n\nOperation interrupted by user")
        return 1
    except Exception as e:
        print(f"\nProgram execution failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误
        format='%(levelname)s: %(message)s'
    )
    
    exit_code = main()
    sys.exit(exit_code)

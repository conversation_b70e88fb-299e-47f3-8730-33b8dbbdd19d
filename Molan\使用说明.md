# 批量莫兰指数分析系统使用说明

## 📁 **文件结构**

```
Molan/
├── batch_molan_analysis.py    # 完整版批量莫兰指数分析（包含显著性检验）
├── test_molan.py             # 测试版（只分析前3个洪水事件）
├── 使用说明.md               # 本文档
└── 代码修改说明.md           # 详细的代码修改记录
```

## 🎯 **系统功能**

### 1. **批量处理能力**
- 自动处理 `input_another` 目录下的所有洪水事件
- 支持43个洪水事件的批量分析（2015-1到2023-1）

### 2. **多层次分析**
- **整体组**：所有站点（剔除指定站点后）
- **大化组**：stations.csv第2-19行的站点
- **太平组**：stations.csv第20-34行的站点  
- **水晏组**：stations.csv第35-37行的站点

### 3. **站点剔除功能**
- **整体组剔除**：80606500, 80607500, 805g2300
- **大化组剔除**：80607800, 80608500, 80628800
- **太平组剔除**：80607500, 80609500, 80633100
- **水晏组剔除**：80609500, 80633500, 80633800

### 4. **统计显著性检验**
- 使用置换检验（Permutation Test）计算p值
- 计算z分数和显著性水平
- 显著性水平：`***`(p<0.001), `**`(p<0.01), `*`(p<0.05), `.`(p<0.1), `NS`(p≥0.1)

## 🚀 **运行方法**

### 方法1：完整版分析（推荐）
```bash
cd Molan
python batch_molan_analysis.py
```

### 方法2：测试版分析（快速验证）
```bash
cd Molan
python test_molan.py
```

## 📊 **输出结果**

### 1. **主要输出文件**
- `莫兰指数分析详细结果.csv` - 包含所有分析结果和显著性检验
- `莫兰指数显著性检验汇总表.csv` - 各组显著性统计汇总
- `莫兰指数分析汇总报告.txt` - 详细统计报告

### 2. **按分组汇总**
- `整体_所有洪水事件莫兰指数.csv`
- `大化_所有洪水事件莫兰指数.csv`
- `太平_所有洪水事件莫兰指数.csv`
- `水晏_所有洪水事件莫兰指数.csv`

### 3. **按洪水事件分别保存**
- `2015-1_莫兰指数分析.csv`
- `2015-2_莫兰指数分析.csv`
- ...（每个洪水事件一个文件）

## 📋 **结果字段说明**

| 字段名 | 说明 |
|--------|------|
| flood_event | 洪水事件名称 |
| group | 分组名称（整体/大化/太平/水晏） |
| reference_station | 参考站点ID |
| top_1_station | 莫兰指数最高的站点 |
| top_1_moran | 最高莫兰指数值 |
| top_1_p_value | 最高莫兰指数的p值 |
| top_1_z_score | 最高莫兰指数的z分数 |
| top_1_significance | 最高莫兰指数的显著性水平 |
| top_2_station | 莫兰指数第二高的站点 |
| top_2_moran | 第二高莫兰指数值 |
| ... | （类似字段用于第2、3高的站点） |
| avg_moran | 前3个站点的平均莫兰指数 |
| max_moran | 最高莫兰指数值 |
| significant_count | 显著站点对数量 |

## ⚙️ **配置修改**

### 1. **启用/禁用站点剔除**
在主函数中修改：
```python
exclude_stations = True   # 剔除指定站点
exclude_stations = False  # 保留所有站点
```

### 2. **自定义剔除站点**
修改 `excluded_stations` 字典：
```python
self.excluded_stations = {
    '整体': ['80606500', '80607500'],  # 自定义剔除列表
    '大化': [],                       # 空列表表示不剔除
    '太平': ['80607500'],             # 只剔除部分站点
    '水晏': ['80609500', '80633500', '80633800']
}
```

### 3. **调整置换检验次数**
在 `calculate_moran_index_with_significance` 方法中：
```python
n_permutations=199  # 默认值，可调整为99（快速）或999（精确）
```

## 📈 **测试结果示例**

基于前3个洪水事件的测试结果：

### 站点分组统计
- **整体组**：33个站点（剔除3个后）
- **大化组**：15个站点（剔除3个后）
- **太平组**：12个站点（剔除3个后）
- **水晏组**：1个站点（剔除2个后，不足分析）

### 显著性检验结果
- **整体组**：93.9%的站点对显著相关
- **大化组**：100%的站点对显著相关
- **太平组**：81.5%的站点对显著相关

## ⚠️ **注意事项**

### 1. **数据要求**
- 确保 `input_another` 目录包含洪水事件文件夹
- 每个洪水事件文件夹包含站点CSV文件（格式：时间,雨量）
- `stations.csv` 文件包含站点位置信息（格式：站点,经度,纬度）

### 2. **运行环境**
- 需要安装：pandas, numpy, scipy
- 推荐Python 3.7+

### 3. **性能考虑**
- 完整版分析需要较长时间（约30-60分钟）
- 测试版只需几分钟，适合验证功能
- 可通过减少置换次数提高速度

### 4. **水晏组特殊情况**
- 由于剔除站点较多，水晏组可能只剩1个站点
- 需要至少2个站点才能进行莫兰指数分析
- 可考虑减少水晏组的剔除站点数量

## 🔧 **故障排除**

### 1. **文件路径错误**
确保在Molan目录下运行，或修改路径配置：
```python
stations_file = "../stations.csv"
input_dir = "../input_another"
output_dir = "../output/molan"
```

### 2. **内存不足**
- 使用测试版本进行小规模验证
- 减少置换检验次数
- 分批处理洪水事件

### 3. **站点数量不足**
- 检查站点分组配置
- 减少剔除站点数量
- 确认stations.csv中的站点ID格式

## 📞 **技术支持**

如有问题，请检查：
1. 文件路径是否正确
2. 数据格式是否符合要求
3. Python环境和依赖包是否完整
4. 参考代码修改说明文档进行配置调整

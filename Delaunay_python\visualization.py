#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay插值系统可视化模块

生成插值结果的可视化图表
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np
import seaborn as sns
from pathlib import Path
import logging
from typing import Dict, List, Optional
import warnings

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class DelaunayVisualization:
    """Delaunay插值可视化类"""
    
    def __init__(self, config):
        self.config = config
        
        # 设置绘图样式
        plt.style.use('default')
        sns.set_palette("husl")
        
    def plot_station_timeseries(self, station_id: str, station_results: pd.DataFrame, 
                               output_dir: Path, event_name: str = None):
        """绘制单个站点的时间序列对比图"""
        try:
            if station_results.empty:
                logger.warning(f"站点{station_id}结果为空，跳过绘图")
                return
            
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.config.FIGURE_SIZE, 
                                         sharex=True, height_ratios=[3, 1])
            
            # 获取有效数据
            valid_data = station_results.dropna(subset=['observed', 'predicted'])
            
            if not valid_data.empty:
                # 上图：时间序列对比
                ax1.plot(station_results.index, station_results['observed'], 
                        'b-', label='观测值', linewidth=1.5, alpha=0.8)
                ax1.plot(station_results.index, station_results['predicted'], 
                        'r--', label='插值结果', linewidth=1.5, alpha=0.8)
                
                ax1.set_ylabel('降雨量 (mm)', fontsize=12)
                ax1.set_title(f'站点{station_id}降雨量时间序列对比', fontsize=14, fontweight='bold')
                ax1.legend(fontsize=10)
                ax1.grid(True, alpha=0.3)
                
                # 下图：误差时间序列
                ax2.plot(station_results.index, station_results['error'], 
                        'g-', linewidth=1, alpha=0.7)
                ax2.axhline(y=0, color='k', linestyle='-', alpha=0.5)
                ax2.set_ylabel('误差 (mm)', fontsize=12)
                ax2.set_xlabel('时间', fontsize=12)
                ax2.set_title('插值误差', fontsize=12)
                ax2.grid(True, alpha=0.3)
                
                # 格式化x轴
                ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
                ax2.xaxis.set_major_locator(mdates.DayLocator(interval=1))
                plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
            
            plt.tight_layout()
            
            # 保存图片
            if event_name:
                filename = f"{event_name}_{station_id}_timeseries.{self.config.PLOT_FORMAT}"
            else:
                filename = f"{station_id}_timeseries.{self.config.PLOT_FORMAT}"
            
            filepath = output_dir / filename
            plt.savefig(filepath, dpi=self.config.PLOT_DPI, bbox_inches='tight')
            plt.close()
            
            logger.debug(f"保存站点{station_id}时间序列图: {filepath}")
            
        except Exception as e:
            logger.error(f"绘制站点{station_id}时间序列图失败: {e}")
            plt.close()
    
    def plot_scatter_comparison(self, station_id: str, station_results: pd.DataFrame, 
                              output_dir: Path, event_name: str = None):
        """绘制观测值vs预测值散点图"""
        try:
            if station_results.empty:
                return
            
            # 获取有效数据
            valid_data = station_results.dropna(subset=['observed', 'predicted'])
            
            if valid_data.empty:
                logger.warning(f"站点{station_id}无有效数据对，跳过散点图")
                return
            
            fig, ax = plt.subplots(figsize=(8, 8))
            
            observed = valid_data['observed'].values
            predicted = valid_data['predicted'].values
            
            # 绘制散点图
            ax.scatter(observed, predicted, alpha=0.6, s=30, edgecolors='black', linewidth=0.5)
            
            # 绘制1:1线
            min_val = min(np.min(observed), np.min(predicted))
            max_val = max(np.max(observed), np.max(predicted))
            ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='1:1线')
            
            # 计算并显示统计信息
            from evaluation_metrics import EvaluationMetrics
            metrics_calc = EvaluationMetrics(self.config)
            metrics = metrics_calc.calculate_all_metrics(observed, predicted)
            
            # 添加统计信息文本
            stats_text = f'R² = {metrics["R2"]:.3f}\n'
            stats_text += f'NSE = {metrics["NSE"]:.3f}\n'
            stats_text += f'RMSE = {metrics["RMSE"]:.3f}\n'
            stats_text += f'MAE = {metrics["MAE"]:.3f}\n'
            stats_text += f'样本数 = {metrics["Count"]}'
            
            ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, fontsize=10,
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            
            ax.set_xlabel('观测值 (mm)', fontsize=12)
            ax.set_ylabel('预测值 (mm)', fontsize=12)
            ax.set_title(f'站点{station_id}观测值vs预测值', fontsize=14, fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 设置相等的坐标轴比例
            ax.set_aspect('equal', adjustable='box')
            
            plt.tight_layout()
            
            # 保存图片
            if event_name:
                filename = f"{event_name}_{station_id}_scatter.{self.config.PLOT_FORMAT}"
            else:
                filename = f"{station_id}_scatter.{self.config.PLOT_FORMAT}"
            
            filepath = output_dir / filename
            plt.savefig(filepath, dpi=self.config.PLOT_DPI, bbox_inches='tight')
            plt.close()
            
            logger.debug(f"保存站点{station_id}散点图: {filepath}")
            
        except Exception as e:
            logger.error(f"绘制站点{station_id}散点图失败: {e}")
            plt.close()
    
    def plot_metrics_summary(self, event_metrics: Dict[str, Dict[str, float]], 
                           output_dir: Path, event_name: str):
        """绘制评估指标汇总图"""
        try:
            if not event_metrics:
                logger.warning(f"事件{event_name}无评估指标数据")
                return
            
            # 准备数据
            stations = list(event_metrics.keys())
            metrics_names = ['NSE', 'MAE', 'RMSE', 'R2']
            
            # 创建子图
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            axes = axes.flatten()
            
            for i, metric_name in enumerate(metrics_names):
                ax = axes[i]
                
                # 提取指标值
                values = []
                valid_stations = []
                
                for station_id in stations:
                    value = event_metrics[station_id].get(metric_name, np.nan)
                    if not np.isnan(value):
                        values.append(value)
                        valid_stations.append(station_id)
                
                if not values:
                    ax.text(0.5, 0.5, f'无有效{metric_name}数据', 
                           ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{metric_name}分布')
                    continue
                
                # 绘制柱状图
                bars = ax.bar(range(len(values)), values, alpha=0.7)
                
                # 设置颜色
                if metric_name == 'NSE':
                    # NSE: 绿色表示好(>0.7)，红色表示差
                    colors = ['green' if v > self.config.NSE_THRESHOLD else 'red' for v in values]
                elif metric_name in ['MAE', 'RMSE']:
                    # MAE, RMSE: 值越小越好
                    colors = ['green' if v < np.median(values) else 'orange' for v in values]
                else:
                    # R2: 值越大越好
                    colors = ['green' if v > np.median(values) else 'orange' for v in values]
                
                for bar, color in zip(bars, colors):
                    bar.set_color(color)
                
                # 设置标签和标题
                ax.set_xticks(range(len(values)))
                ax.set_xticklabels(valid_stations, rotation=45, ha='right')
                ax.set_ylabel(metric_name)
                ax.set_title(f'{metric_name}分布 (均值: {np.mean(values):.3f})')
                ax.grid(True, alpha=0.3)
                
                # 添加阈值线
                if metric_name == 'NSE':
                    ax.axhline(y=self.config.NSE_THRESHOLD, color='red', 
                              linestyle='--', alpha=0.7, label=f'阈值 ({self.config.NSE_THRESHOLD})')
                    ax.legend()
            
            plt.suptitle(f'洪水事件{event_name}评估指标汇总', fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            # 保存图片
            filename = f"{event_name}_metrics_summary.{self.config.PLOT_FORMAT}"
            filepath = output_dir / filename
            plt.savefig(filepath, dpi=self.config.PLOT_DPI, bbox_inches='tight')
            plt.close()
            
            logger.info(f"保存事件{event_name}指标汇总图: {filepath}")
            
        except Exception as e:
            logger.error(f"绘制事件{event_name}指标汇总图失败: {e}")
            plt.close()
    
    def plot_overall_summary(self, all_metrics: Dict[str, Dict[str, Dict[str, float]]], 
                           output_dir: Path):
        """绘制所有事件的总体汇总图"""
        try:
            if not all_metrics:
                logger.warning("无总体评估指标数据")
                return
            
            # 收集所有NSE值
            all_nse_values = []
            event_nse_means = []
            event_names = []
            
            for event_name, event_metrics in all_metrics.items():
                nse_values = []
                for station_metrics in event_metrics.values():
                    nse = station_metrics.get('NSE', np.nan)
                    if not np.isnan(nse):
                        nse_values.append(nse)
                        all_nse_values.append(nse)
                
                if nse_values:
                    event_nse_means.append(np.mean(nse_values))
                    event_names.append(event_name)
            
            if not all_nse_values:
                logger.warning("无有效NSE数据")
                return
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
            
            # 左图：NSE分布直方图
            ax1.hist(all_nse_values, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            ax1.axvline(x=self.config.NSE_THRESHOLD, color='red', linestyle='--', 
                       linewidth=2, label=f'阈值 ({self.config.NSE_THRESHOLD})')
            ax1.set_xlabel('NSE值')
            ax1.set_ylabel('频次')
            ax1.set_title('所有站点NSE分布')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 计算统计信息
            good_nse_count = sum(1 for nse in all_nse_values if nse > self.config.NSE_THRESHOLD)
            good_ratio = good_nse_count / len(all_nse_values)
            
            stats_text = f'总站点数: {len(all_nse_values)}\n'
            stats_text += f'NSE > {self.config.NSE_THRESHOLD}: {good_nse_count}\n'
            stats_text += f'优良率: {good_ratio:.1%}\n'
            stats_text += f'平均NSE: {np.mean(all_nse_values):.3f}'
            
            ax1.text(0.05, 0.95, stats_text, transform=ax1.transAxes, fontsize=10,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            
            # 右图：各事件NSE均值
            if event_nse_means:
                colors = ['green' if v > self.config.NSE_THRESHOLD else 'red' for v in event_nse_means]
                bars = ax2.bar(range(len(event_nse_means)), event_nse_means, color=colors, alpha=0.7)
                
                ax2.set_xticks(range(len(event_names)))
                ax2.set_xticklabels(event_names, rotation=45, ha='right')
                ax2.set_ylabel('平均NSE')
                ax2.set_title('各洪水事件NSE均值')
                ax2.axhline(y=self.config.NSE_THRESHOLD, color='red', linestyle='--', 
                           alpha=0.7, label=f'阈值 ({self.config.NSE_THRESHOLD})')
                ax2.legend()
                ax2.grid(True, alpha=0.3)
            
            plt.suptitle('Delaunay插值总体性能评估', fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            # 保存图片
            filename = f"overall_summary.{self.config.PLOT_FORMAT}"
            filepath = output_dir / filename
            plt.savefig(filepath, dpi=self.config.PLOT_DPI, bbox_inches='tight')
            plt.close()
            
            logger.info(f"保存总体汇总图: {filepath}")
            
        except Exception as e:
            logger.error(f"绘制总体汇总图失败: {e}")
            plt.close()

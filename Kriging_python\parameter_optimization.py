"""
Kriging插值系统参数优化模块
自动优化Kriging参数以提高插值精度
"""

import os
import time
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from scipy.optimize import minimize, differential_evolution
import warnings
warnings.filterwarnings('ignore')

from config import Config
from kriging_main import KrigingInterpolation

logger = logging.getLogger(__name__)


class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self, config: Config):
        """初始化参数优化器"""
        self.config = config
        self.optimization_history = []
        self.best_params = {}
        self.best_score = -np.inf
        
    def optimize_parameters(self, target_metric: str = 'NSE') -> Dict:
        """优化Kriging参数"""
        try:
            logger.info("="*60)
            logger.info("开始Kriging参数优化")
            logger.info("="*60)
            
            if not self.config.enable_parameter_optimization:
                logger.info("参数优化未启用，跳过优化")
                return {}
            
            # 定义参数搜索空间
            param_bounds = self._get_parameter_bounds()
            
            # 定义目标函数
            def objective_function(params):
                return self._evaluate_parameter_set(params, target_metric)
            
            # 执行优化
            best_result = None
            
            # 尝试多种优化方法
            optimization_methods = [
                ('differential_evolution', self._optimize_with_differential_evolution),
                ('grid_search', self._optimize_with_grid_search),
                ('random_search', self._optimize_with_random_search)
            ]
            
            for method_name, method_func in optimization_methods:
                try:
                    logger.info(f"尝试优化方法: {method_name}")
                    
                    result = method_func(objective_function, param_bounds, target_metric)
                    
                    if result and result.get('score', -np.inf) > self.best_score:
                        self.best_score = result['score']
                        self.best_params = result['params']
                        best_result = result
                        
                        logger.info(f"方法 {method_name} 找到更好的参数: {target_metric}={self.best_score:.4f}")
                    
                except Exception as e:
                    logger.warning(f"优化方法 {method_name} 失败: {e}")
                    continue
            
            if best_result:
                # 应用最佳参数
                self._apply_optimized_parameters(best_result['params'])
                
                # 保存优化结果
                self._save_optimization_results(best_result, target_metric)
                
                logger.info("="*60)
                logger.info(f"参数优化完成！最佳 {target_metric}: {self.best_score:.4f}")
                logger.info("="*60)
                
                return best_result
            else:
                logger.warning("参数优化失败，使用默认参数")
                return {}
            
        except Exception as e:
            logger.error(f"参数优化失败: {e}")
            return {}
    
    def _get_parameter_bounds(self) -> Dict:
        """获取参数搜索边界"""
        bounds = {
            'variogram_model': ['spherical', 'exponential', 'gaussian'],
            'neighbor_count': (2, 8),
            'moran_weight': (0.1, 0.8),
            'distance_weight': (0.2, 0.9),
            'rainfall_threshold': (0.1, 2.0)
        }
        
        # 如果启用地形增强，添加地形权重参数
        if self.config.enable_terrain_enhancement:
            bounds.update({
                'elevation_weight': (0.0, 0.5),
                'slope_weight': (0.0, 0.3),
                'aspect_weight': (0.0, 0.3)
            })
        
        return bounds
    
    def _evaluate_parameter_set(self, params: Dict, target_metric: str) -> float:
        """评估参数集合"""
        try:
            # 创建临时配置
            temp_config = Config(**self.config.to_dict())
            
            # 应用参数
            for param_name, param_value in params.items():
                if hasattr(temp_config, param_name):
                    setattr(temp_config, param_name, param_value)
            
            # 确保权重归一化
            if temp_config.enable_moran_weighting:
                total_weight = temp_config.moran_weight + temp_config.distance_weight
                if total_weight > 0:
                    temp_config.moran_weight /= total_weight
                    temp_config.distance_weight /= total_weight
            
            # 运行Kriging插值
            kriging = KrigingInterpolation(temp_config)
            results = kriging.run_complete_workflow()
            
            if results and target_metric in results:
                score = results[target_metric]
                
                # 记录优化历史
                self.optimization_history.append({
                    'params': params.copy(),
                    'score': score,
                    'metric': target_metric,
                    'timestamp': time.time()
                })
                
                logger.debug(f"参数评估: {target_metric}={score:.4f}, 参数={params}")
                
                return score
            else:
                return -np.inf
                
        except Exception as e:
            logger.debug(f"参数评估失败: {e}")
            return -np.inf
    
    def _optimize_with_differential_evolution(self, objective_func, param_bounds, target_metric) -> Optional[Dict]:
        """使用差分进化算法优化"""
        try:
            # 准备连续参数的边界
            continuous_params = []
            continuous_bounds = []
            discrete_params = {}
            
            for param_name, bounds in param_bounds.items():
                if isinstance(bounds, tuple):
                    continuous_params.append(param_name)
                    continuous_bounds.append(bounds)
                else:
                    # 离散参数，随机选择一个
                    discrete_params[param_name] = np.random.choice(bounds)
            
            if not continuous_bounds:
                return None
            
            def objective_wrapper(x):
                params = discrete_params.copy()
                for i, param_name in enumerate(continuous_params):
                    params[param_name] = x[i]
                return -objective_func(params)  # 最小化负值
            
            # 执行优化
            result = differential_evolution(
                objective_wrapper,
                continuous_bounds,
                maxiter=self.config.optimization_iterations,
                popsize=5,
                seed=42
            )
            
            if result.success:
                # 构建最佳参数
                best_params = discrete_params.copy()
                for i, param_name in enumerate(continuous_params):
                    best_params[param_name] = result.x[i]
                
                return {
                    'params': best_params,
                    'score': -result.fun,
                    'method': 'differential_evolution'
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"差分进化优化失败: {e}")
            return None
    
    def _optimize_with_grid_search(self, objective_func, param_bounds, target_metric) -> Optional[Dict]:
        """使用网格搜索优化"""
        try:
            # 创建参数网格
            param_grid = []
            
            # 简化的网格搜索
            variogram_models = param_bounds.get('variogram_model', ['spherical'])
            neighbor_counts = [2, 3, 5] if 'neighbor_count' in param_bounds else [3]
            moran_weights = [0.2, 0.3, 0.5] if 'moran_weight' in param_bounds else [0.3]
            
            for model in variogram_models:
                for neighbor_count in neighbor_counts:
                    for moran_weight in moran_weights:
                        params = {
                            'variogram_model': model,
                            'neighbor_count': neighbor_count,
                            'moran_weight': moran_weight,
                            'distance_weight': 1.0 - moran_weight
                        }
                        param_grid.append(params)
            
            # 评估所有参数组合
            best_score = -np.inf
            best_params = None
            
            for params in param_grid:
                score = objective_func(params)
                
                if score > best_score:
                    best_score = score
                    best_params = params
            
            if best_params:
                return {
                    'params': best_params,
                    'score': best_score,
                    'method': 'grid_search'
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"网格搜索优化失败: {e}")
            return None
    
    def _optimize_with_random_search(self, objective_func, param_bounds, target_metric) -> Optional[Dict]:
        """使用随机搜索优化"""
        try:
            best_score = -np.inf
            best_params = None
            
            n_iterations = self.config.optimization_iterations * 2
            
            for i in range(n_iterations):
                # 随机生成参数
                params = {}
                
                for param_name, bounds in param_bounds.items():
                    if isinstance(bounds, tuple):
                        # 连续参数
                        params[param_name] = np.random.uniform(bounds[0], bounds[1])
                    else:
                        # 离散参数
                        params[param_name] = np.random.choice(bounds)
                
                # 确保权重归一化
                if 'moran_weight' in params and 'distance_weight' in params:
                    total = params['moran_weight'] + params['distance_weight']
                    if total > 0:
                        params['moran_weight'] /= total
                        params['distance_weight'] /= total
                
                # 评估参数
                score = objective_func(params)
                
                if score > best_score:
                    best_score = score
                    best_params = params
                
                if (i + 1) % 5 == 0:
                    logger.debug(f"随机搜索进度: {i+1}/{n_iterations}, 当前最佳: {best_score:.4f}")
            
            if best_params:
                return {
                    'params': best_params,
                    'score': best_score,
                    'method': 'random_search'
                }
            
            return None
            
        except Exception as e:
            logger.warning(f"随机搜索优化失败: {e}")
            return None
    
    def _apply_optimized_parameters(self, optimized_params: Dict):
        """应用优化后的参数"""
        try:
            logger.info("应用优化后的参数...")
            
            for param_name, param_value in optimized_params.items():
                if hasattr(self.config, param_name):
                    old_value = getattr(self.config, param_name)
                    setattr(self.config, param_name, param_value)
                    
                    logger.info(f"  {param_name}: {old_value} -> {param_value}")
            
            logger.info("参数应用完成")
            
        except Exception as e:
            logger.error(f"应用优化参数失败: {e}")
    
    def _save_optimization_results(self, result: Dict, target_metric: str):
        """保存优化结果"""
        try:
            output_dirs = self.config.get_output_dirs()
            
            # 保存优化历史
            if self.optimization_history:
                history_df = pd.DataFrame(self.optimization_history)
                history_path = os.path.join(output_dirs['evaluation'], 'optimization_history.csv')
                history_df.to_csv(history_path, index=False, encoding='utf-8')
            
            # 保存最佳参数
            best_params_path = os.path.join(output_dirs['evaluation'], 'optimized_parameters.json')
            
            optimization_summary = {
                'target_metric': target_metric,
                'best_score': self.best_score,
                'best_params': self.best_params,
                'optimization_method': result.get('method', 'unknown'),
                'total_evaluations': len(self.optimization_history),
                'optimization_time': time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            import json
            with open(best_params_path, 'w', encoding='utf-8') as f:
                json.dump(optimization_summary, f, indent=4, ensure_ascii=False)
            
            logger.info(f"优化结果已保存: {best_params_path}")
            
        except Exception as e:
            logger.error(f"保存优化结果失败: {e}")
    
    def load_optimized_parameters(self, file_path: str) -> bool:
        """加载优化后的参数"""
        try:
            if not os.path.exists(file_path):
                logger.warning(f"优化参数文件不存在: {file_path}")
                return False
            
            import json
            with open(file_path, 'r', encoding='utf-8') as f:
                optimization_data = json.load(f)
            
            if 'best_params' in optimization_data:
                self._apply_optimized_parameters(optimization_data['best_params'])
                self.best_params = optimization_data['best_params']
                self.best_score = optimization_data.get('best_score', 0)
                
                logger.info(f"已加载优化参数: {file_path}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"加载优化参数失败: {e}")
            return False
    
    def get_optimization_summary(self) -> Dict:
        """获取优化总结"""
        return {
            'best_score': self.best_score,
            'best_params': self.best_params,
            'total_evaluations': len(self.optimization_history),
            'optimization_enabled': self.config.enable_parameter_optimization
        }
    
    def cleanup(self):
        """清理内存"""
        try:
            self.optimization_history.clear()
            self.best_params.clear()
            logger.debug("参数优化器内存清理完成")
        except Exception as e:
            logger.warning(f"参数优化器内存清理失败: {e}")

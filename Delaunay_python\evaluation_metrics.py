#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay插值系统评估指标计算

计算NSE、MAE、RMSE等评估指标
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple
import warnings

warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class EvaluationMetrics:
    """评估指标计算类"""
    
    def __init__(self, config):
        self.config = config
        self.metrics_cache = {}
    
    def calculate_mae(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算平均绝对误差 (MAE)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return np.nan
            
            # 确保数组长度一致
            if len(observed) != len(predicted):
                logger.warning("观测值和预测值长度不一致")
                return np.nan
            
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if not np.any(mask):
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            mae = np.mean(np.abs(obs_clean - pred_clean))
            return mae
            
        except Exception as e:
            logger.error(f"计算MAE失败: {e}")
            return np.nan
    
    def calculate_rmse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算均方根误差 (RMSE)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return np.nan
            
            # 确保数组长度一致
            if len(observed) != len(predicted):
                logger.warning("观测值和预测值长度不一致")
                return np.nan
            
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if not np.any(mask):
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            rmse = np.sqrt(np.mean((obs_clean - pred_clean) ** 2))
            return rmse
            
        except Exception as e:
            logger.error(f"计算RMSE失败: {e}")
            return np.nan
    
    def calculate_nse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算Nash-Sutcliffe效率系数 (NSE)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return np.nan
            
            # 确保数组长度一致
            if len(observed) != len(predicted):
                logger.warning("观测值和预测值长度不一致")
                return np.nan
            
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if not np.any(mask):
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            # 计算观测值的方差
            obs_var = np.var(obs_clean)
            if obs_var == 0:
                # 如果观测值方差为0，检查预测值是否完全匹配
                return 1.0 if np.allclose(obs_clean, pred_clean) else 0.0
            
            # 计算NSE
            numerator = np.sum((obs_clean - pred_clean) ** 2)
            denominator = np.sum((obs_clean - np.mean(obs_clean)) ** 2)
            
            if denominator == 0:
                return np.nan
            
            nse = 1 - (numerator / denominator)
            return nse
            
        except Exception as e:
            logger.error(f"计算NSE失败: {e}")
            return np.nan
    
    def calculate_correlation(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算相关系数 (R)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return np.nan
            
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if not np.any(mask) or np.sum(mask) < 2:
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            # 计算相关系数
            correlation_matrix = np.corrcoef(obs_clean, pred_clean)
            if correlation_matrix.shape == (2, 2):
                r = correlation_matrix[0, 1]
                return r if not np.isnan(r) else 0.0
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"计算相关系数失败: {e}")
            return np.nan
    
    def calculate_r_squared(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算决定系数 (R²)"""
        try:
            r = self.calculate_correlation(observed, predicted)
            if np.isnan(r):
                return np.nan
            return r ** 2
            
        except Exception as e:
            logger.error(f"计算R²失败: {e}")
            return np.nan
    
    def calculate_bias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算偏差 (Bias)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return np.nan
            
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if not np.any(mask):
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            bias = np.mean(pred_clean - obs_clean)
            return bias
            
        except Exception as e:
            logger.error(f"计算Bias失败: {e}")
            return np.nan
    
    def calculate_pbias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算百分比偏差 (PBIAS)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return np.nan
            
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if not np.any(mask):
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            obs_sum = np.sum(obs_clean)
            if obs_sum == 0:
                return np.nan
            
            pbias = 100 * np.sum(pred_clean - obs_clean) / obs_sum
            return pbias
            
        except Exception as e:
            logger.error(f"计算PBIAS失败: {e}")
            return np.nan
    
    def calculate_all_metrics(self, observed: np.ndarray, predicted: np.ndarray) -> Dict[str, float]:
        """计算所有评估指标"""
        try:
            # 确保输入为numpy数组
            observed = np.asarray(observed)
            predicted = np.asarray(predicted)
            
            # 移除NaN值对
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            valid_count = np.sum(mask)
            
            if valid_count == 0:
                logger.warning("没有有效的观测-预测值对")
                return self._get_empty_metrics()
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            # 计算所有指标
            metrics = {
                'MAE': self.calculate_mae(observed, predicted),
                'RMSE': self.calculate_rmse(observed, predicted),
                'NSE': self.calculate_nse(observed, predicted),
                'R': self.calculate_correlation(observed, predicted),
                'R2': self.calculate_r_squared(observed, predicted),
                'Bias': self.calculate_bias(observed, predicted),
                'PBIAS': self.calculate_pbias(observed, predicted),
                'Count': valid_count,
                'Mean_Observed': np.mean(obs_clean),
                'Mean_Predicted': np.mean(pred_clean),
                'Std_Observed': np.std(obs_clean),
                'Std_Predicted': np.std(pred_clean)
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算评估指标失败: {e}")
            return self._get_empty_metrics()
    
    def _get_empty_metrics(self) -> Dict[str, float]:
        """返回空的指标字典"""
        return {
            'MAE': np.nan,
            'RMSE': np.nan,
            'NSE': np.nan,
            'R': np.nan,
            'R2': np.nan,
            'Bias': np.nan,
            'PBIAS': np.nan,
            'Count': 0,
            'Mean_Observed': np.nan,
            'Mean_Predicted': np.nan,
            'Std_Observed': np.nan,
            'Std_Predicted': np.nan
        }
    
    def evaluate_station_performance(self, station_results: pd.DataFrame) -> Dict[str, float]:
        """评估单个站点的整体性能"""
        try:
            if station_results.empty:
                return self._get_empty_metrics()
            
            observed = station_results['observed'].values
            predicted = station_results['predicted'].values
            
            return self.calculate_all_metrics(observed, predicted)
            
        except Exception as e:
            logger.error(f"评估站点性能失败: {e}")
            return self._get_empty_metrics()
    
    def evaluate_event_performance(self, event_results: Dict[str, pd.DataFrame]) -> Dict[str, Dict[str, float]]:
        """评估整个洪水事件的性能"""
        try:
            station_metrics = {}
            
            for station_id, results_df in event_results.items():
                metrics = self.evaluate_station_performance(results_df)
                station_metrics[station_id] = metrics
            
            return station_metrics
            
        except Exception as e:
            logger.error(f"评估事件性能失败: {e}")
            return {}
    
    def calculate_summary_statistics(self, all_metrics: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """计算汇总统计"""
        try:
            if not all_metrics:
                return {}
            
            # 收集所有指标值
            metric_names = ['MAE', 'RMSE', 'NSE', 'R', 'R2', 'Bias', 'PBIAS']
            summary = {}
            
            for metric_name in metric_names:
                values = []
                for station_metrics in all_metrics.values():
                    value = station_metrics.get(metric_name, np.nan)
                    if not np.isnan(value):
                        values.append(value)
                
                if values:
                    summary[f'{metric_name}_mean'] = np.mean(values)
                    summary[f'{metric_name}_std'] = np.std(values)
                    summary[f'{metric_name}_min'] = np.min(values)
                    summary[f'{metric_name}_max'] = np.max(values)
                    summary[f'{metric_name}_median'] = np.median(values)
                else:
                    summary[f'{metric_name}_mean'] = np.nan
                    summary[f'{metric_name}_std'] = np.nan
                    summary[f'{metric_name}_min'] = np.nan
                    summary[f'{metric_name}_max'] = np.nan
                    summary[f'{metric_name}_median'] = np.nan
            
            # 计算NSE > 0.7的站点比例
            nse_values = []
            for station_metrics in all_metrics.values():
                nse = station_metrics.get('NSE', np.nan)
                if not np.isnan(nse):
                    nse_values.append(nse)
            
            if nse_values:
                good_nse_count = sum(1 for nse in nse_values if nse > self.config.NSE_THRESHOLD)
                summary['NSE_good_ratio'] = good_nse_count / len(nse_values)
                summary['total_stations'] = len(nse_values)
                summary['good_nse_stations'] = good_nse_count
            else:
                summary['NSE_good_ratio'] = 0.0
                summary['total_stations'] = 0
                summary['good_nse_stations'] = 0
            
            return summary
            
        except Exception as e:
            logger.error(f"计算汇总统计失败: {e}")
            return {}

# OI插值系统初始化文件
"""
最优插值法(OI)空间插值系统

本系统实现了基于最优插值法的点雨量到面雨量的空间插值，
使用Delaunay三角网进行站点筛选，并应用留一法进行验证。

主要功能：
1. Delaunay三角网构建和站点选择
2. 莫兰指数权重计算
3. 最优插值算法
4. 留一法验证
5. 评价指标计算
6. 栅格输出
7. 批量处理

作者：空间插值专家
日期：2024年
"""

__version__ = "1.0.0"
__author__ = "空间插值专家"

# 导入主要模块
from config import Config
from oi_main import OIInterpolation
from easy_run import run_oi_interpolation

__all__ = [
    'Config',
    'OIInterpolation', 
    'run_oi_interpolation'
]

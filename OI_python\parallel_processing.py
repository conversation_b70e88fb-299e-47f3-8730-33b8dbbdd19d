# -*- coding: utf-8 -*-
"""
并行处理模块

负责多进程处理时间步插值
"""

import multiprocessing as mp
import numpy as np
import pandas as pd
import time
import logging
import traceback
from typing import List, Tuple, Dict, Any
from oi_core import leave_one_out_validation

def init_worker():
    """
    初始化工作进程的函数
    """
    import signal
    signal.signal(signal.SIGINT, signal.SIG_IGN)

def process_time_step(args: <PERSON><PERSON>) -> Dict[str, Any]:
    """
    处理单个时间步的插值
    
    Args:
        args: 包含处理所需所有参数的元组
            (time_index, stations, rainfall_data_slice, tri, cov_matrix, config)
    
    Returns:
        Dict[str, Any]: 包含处理结果的字典
    """
    try:
        time_index, stations, rainfall_data_slice, tri, cov_matrix, config = args
        
        # 记录开始时间
        start_time = time.time()
        
        # 添加调试信息
        print(f"开始处理时间步: {time_index}")
        
        # 进行留一法验证
        validation_df = leave_one_out_validation(
            stations, rainfall_data_slice, tri, time_index, cov_matrix, config
        )
        
        # 记录结束时间
        end_time = time.time()
        
        # 计算统计指标
        if not validation_df.empty:
            mae = np.mean(np.abs(validation_df['error']))
            rmse = np.sqrt(np.mean(validation_df['error']**2))
        else:
            mae = np.nan
            rmse = np.nan
        
        # 返回结果
        result = {
            'time_index': time_index,
            'validation': validation_df,
            'mae': mae,
            'rmse': rmse,
            'processing_time': end_time - start_time,
            'success': True
        }
        
        print(f"完成时间步 {time_index} 的处理，耗时: {end_time - start_time:.2f}秒")
        
        return result
    
    except Exception as e:
        print(f"处理时间步 {time_index if 'time_index' in locals() else 'unknown'} 时出错: {str(e)}")
        print(traceback.format_exc())
        return {
            'time_index': time_index if 'time_index' in locals() else None,
            'error': str(e),
            'success': False
        }

def process_in_batches(time_indices: List[pd.Timestamp], stations: pd.DataFrame, 
                      rainfall_data: pd.DataFrame, tri, cov_matrix, config,
                      num_processes: int, batch_size: int) -> List[Dict[str, Any]]:
    """
    分批处理时间步，避免内存溢出
    
    Args:
        time_indices: 时间索引列表
        stations: 站点信息DataFrame
        rainfall_data: 降雨数据DataFrame
        tri: Delaunay三角网对象
        cov_matrix: 协方差矩阵
        config: 配置对象
        num_processes: 并行进程数
        batch_size: 批处理大小
    
    Returns:
        List[Dict[str, Any]]: 所有处理结果列表
    """
    all_results = []
    num_batches = (len(time_indices) + batch_size - 1) // batch_size
    
    logging.info(f"开始分批处理，共 {num_batches} 个批次，每批 {batch_size} 个时间步")
    
    # 处理每个批次
    for i in range(num_batches):
        print(f"处理批次 {i+1}/{num_batches}")
        
        # 获取当前批次的时间索引
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, len(time_indices))
        batch_time_indices = time_indices[start_idx:end_idx]
        
        # 只提取需要的时间步数据，减少内存使用
        batch_rainfall_data = rainfall_data.loc[batch_time_indices].copy()
        
        # 准备参数列表
        args_list = [
            (time_index, stations, batch_rainfall_data, tri, cov_matrix, config) 
            for time_index in batch_time_indices
        ]
        
        # 判断是否使用并行处理
        use_parallel = num_processes > 1 and len(batch_time_indices) > 1
        
        if use_parallel:
            # 使用多进程处理
            try:
                # 使用更安全的多进程方法
                ctx = mp.get_context('spawn')  # 在Windows上使用spawn方法更可靠
                batch_results = []
                
                with ctx.Pool(processes=num_processes, initializer=init_worker) as pool:
                    # 使用map_async以避免阻塞
                    async_result = pool.map_async(process_time_step, args_list)
                    
                    # 添加进度显示
                    while not async_result.ready():
                        print(f"等待批次 {i+1}/{num_batches} 完成处理...")
                        time.sleep(5)  # 每5秒更新一次状态
                    
                    # 获取处理结果
                    batch_results = async_result.get()
            
            except Exception as e:
                logging.error(f"多进程处理失败，切换到串行处理: {e}")
                print(f"多进程处理失败: {e}")
                use_parallel = False
        
        if not use_parallel:
            # 串行处理
            batch_results = []
            for args in args_list:
                result = process_time_step(args)
                batch_results.append(result)
        
        # 收集结果
        all_results.extend(batch_results)
        
        # 释放内存
        del batch_results
        del batch_rainfall_data
        del args_list
        import gc
        gc.collect()
        
        print(f"批次 {i+1}/{num_batches} 处理完成")
    
    logging.info(f"所有批次处理完成，共处理 {len(all_results)} 个时间步")
    return all_results

def process_single_time_step_debug(time_index: pd.Timestamp, stations: pd.DataFrame,
                                 rainfall_data: pd.DataFrame, tri, cov_matrix, config) -> Dict[str, Any]:
    """
    单步调试：处理单个时间步进行测试
    
    Args:
        time_index: 时间索引
        stations: 站点信息DataFrame
        rainfall_data: 降雨数据DataFrame
        tri: Delaunay三角网对象
        cov_matrix: 协方差矩阵
        config: 配置对象
    
    Returns:
        Dict[str, Any]: 处理结果
    """
    try:
        logging.info(f"单步调试：处理时间步 {time_index}")
        
        # 只提取当前时间步的数据
        single_time_data = rainfall_data.loc[[time_index]]
        
        # 准备参数
        args = (time_index, stations, single_time_data, tri, cov_matrix, config)
        
        # 处理
        result = process_time_step(args)
        
        if result['success']:
            logging.info(f"单步调试成功完成")
            logging.info(f"  - MAE: {result.get('mae', 'N/A')}")
            logging.info(f"  - RMSE: {result.get('rmse', 'N/A')}")
            logging.info(f"  - 处理时间: {result.get('processing_time', 'N/A'):.2f}秒")
        else:
            logging.error(f"单步调试失败: {result.get('error', 'Unknown error')}")
        
        return result
    
    except Exception as e:
        logging.error(f"单步调试时出错: {e}")
        logging.error(traceback.format_exc())
        return {
            'time_index': time_index,
            'error': str(e),
            'success': False
        }

def estimate_processing_time(num_time_steps: int, num_stations: int, 
                           num_processes: int) -> float:
    """
    估算处理时间
    
    Args:
        num_time_steps: 时间步数
        num_stations: 站点数
        num_processes: 并行进程数
    
    Returns:
        float: 估算的处理时间（秒）
    """
    # 基于经验的时间估算公式
    # 每个时间步每个站点大约需要0.01秒（串行）
    base_time_per_step = 0.01 * num_stations
    
    # 考虑并行效率（通常为70-80%）
    parallel_efficiency = 0.75
    
    # 总时间估算
    estimated_time = (num_time_steps * base_time_per_step) / (num_processes * parallel_efficiency)
    
    return estimated_time

def print_processing_estimate(num_time_steps: int, num_stations: int, 
                            num_processes: int):
    """
    打印处理时间估算
    
    Args:
        num_time_steps: 时间步数
        num_stations: 站点数
        num_processes: 并行进程数
    """
    estimated_seconds = estimate_processing_time(num_time_steps, num_stations, num_processes)
    
    # 转换为更友好的时间格式
    if estimated_seconds < 60:
        time_str = f"{estimated_seconds:.1f} 秒"
    elif estimated_seconds < 3600:
        minutes = estimated_seconds / 60
        time_str = f"{minutes:.1f} 分钟"
    else:
        hours = estimated_seconds / 3600
        time_str = f"{hours:.1f} 小时"
    
    print(f"\n处理时间估算:")
    print(f"  - 时间步数: {num_time_steps}")
    print(f"  - 站点数: {num_stations}")
    print(f"  - 并行进程数: {num_processes}")
    print(f"  - 估算处理时间: {time_str}")
    print(f"  - 注意：实际时间可能因系统性能而有所不同\n")

# -*- coding: utf-8 -*-
"""
基于自然邻近点法的水文点雨量转面雨量空间插值（内存优化版）
"""

import os
import time
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial import Voronoi, voronoi_plot_2d
from scipy.interpolate import NearestNDInterpolator, LinearNDInterpolator
from rasterio import features
from rasterio.transform import from_origin
import rasterio
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
import multiprocessing as mp
from datetime import datetime
import warnings
import math
import re
from tqdm import tqdm
import matplotlib.colors as mcolors
import gc  # 用于垃圾回收
from shapely.geometry import Polygon, Point

# 忽略警告
warnings.filterwarnings('ignore')

# 设置工作路径
base_path = 'D:/pythondata/spatial_interpolation/'
input_path = os.path.join(base_path, 'input/2017-2/点雨量')
terrain_path = os.path.join(base_path, 'terrain')
output_path = os.path.join(base_path, 'output/NaturalNeighbor')
stations_file = os.path.join(base_path, 'stations.csv')

# 创建输出目录（如果不存在）
os.makedirs(output_path, exist_ok=True)

# 定义一个函数用于安全地创建文件名（移除特殊字符）
def clean_filename(filename):
    """
    清理文件名，移除非法字符
    
    参数:
        filename: 原始文件名
    
    返回:
        清理后的文件名
    """
    # 替换冒号和空格
    filename = filename.replace(':', '_').replace(' ', '_')
    # 移除其他不允许的字符
    filename = re.sub(r'[\\/*?:"<>|]', '', filename)
    return filename

# 加载站点信息
def load_stations():
    """
    加载站点信息数据
    
    返回:
        包含站点ID、经纬度信息的DataFrame
    """
    try:
        # 读取站点信息文件
        stations_df = pd.read_csv(stations_file, encoding='utf-8')
        print(f"站点信息表头: {stations_df.columns.tolist()}")
        
        # 检查必要列是否存在
        required_columns = ['站点', '经度', '纬度']
        for col in required_columns:
            if col not in stations_df.columns:
                raise ValueError(f"站点信息缺少必要列：{col}")
        
        print(f"成功加载{len(stations_df)}个站点信息")
        return stations_df
    except Exception as e:
        print(f"加载站点信息时出错：{e}")
        raise

# 从DEM中提取站点高程信息
# 从DEM中提取站点高程信息（修改版）
def extract_elevation_from_dem(stations_df, dem_file=None):
    """
    从DEM文件中提取站点的高程信息，对于超出范围的站点使用最近的有效栅格值
    
    参数:
        stations_df: 站点信息DataFrame
        dem_file: DEM文件路径，如果为None则自动查找
    
    返回:
        添加了高程信息的站点DataFrame
    """
    # 如果未指定DEM文件，则在terrain目录下寻找
    if dem_file is None:
        dem_files = [f for f in os.listdir(terrain_path) if f.lower().endswith('.asc') and 'dem' in f.lower()]
        if dem_files:
            dem_file = os.path.join(terrain_path, dem_files[0])
        else:
            print("警告：未找到DEM文件，将无法提取高程信息")
            stations_df['高程'] = 0
            return stations_df
    
    try:
        # 打开DEM文件
        with rasterio.open(dem_file) as dem:
            # 获取DEM的坐标系统和变换
            transform = dem.transform
            dem_data = dem.read(1)
            
            # 为每个站点提取高程
            elevations = []
            out_of_bounds_stations = []
            
            for idx, station in stations_df.iterrows():
                # 将经纬度转换为像素坐标
                x, y = station['经度'], station['纬度']
                row, col = ~transform * (x, y)
                row, col = int(row), int(col)
                
                # 检查坐标是否在栅格范围内
                if 0 <= row < dem.height and 0 <= col < dem.width:
                    # 读取该位置的高程值
                    elevation = dem_data[row, col]
                    elevations.append(elevation)
                else:
                    # 记录超出范围的站点
                    print(f"警告：站点{station['站点']}的坐标({x}, {y})超出DEM范围，将使用最近的有效高程")
                    out_of_bounds_stations.append((idx, x, y, station['站点']))
                    elevations.append(np.nan)
            
            # 处理超出DEM范围的站点
            if out_of_bounds_stations:
                # 创建有效的DEM边界点
                valid_points = []
                valid_values = []
                
                # 采样DEM边界点（为了效率，每隔几个像素采样一次）
                sample_step = 10
                # 上边界
                for col in range(0, dem.width, sample_step):
                    valid_points.append(transform * (col, 0))
                    valid_values.append(dem_data[0, col])
                # 下边界
                for col in range(0, dem.width, sample_step):
                    valid_points.append(transform * (col, dem.height-1))
                    valid_values.append(dem_data[dem.height-1, col])
                # 左边界
                for row in range(0, dem.height, sample_step):
                    valid_points.append(transform * (0, row))
                    valid_values.append(dem_data[row, 0])
                # 右边界
                for row in range(0, dem.height, sample_step):
                    valid_points.append(transform * (dem.width-1, row))
                    valid_values.append(dem_data[row, dem.width-1])
                
                # 为每个超出范围的站点找到最近的有效高程值
                for idx, x, y, station_id in out_of_bounds_stations:
                    station_point = np.array([x, y])
                    distances = [np.sqrt(np.sum((np.array(p) - station_point)**2)) for p in valid_points]
                    nearest_idx = np.argmin(distances)
                    nearest_elevation = valid_values[nearest_idx]
                    
                    # 更新高程值
                    elevations[idx] = nearest_elevation
                    print(f"  站点{station_id}使用最近的有效高程值: {nearest_elevation}")
            
            # 将高程添加到站点信息中
            stations_df['高程'] = elevations
            
            # 检查是否仍有NaN值（如果无法找到有效的最近点）
            if stations_df['高程'].isna().any():
                nan_count = stations_df['高程'].isna().sum()
                print(f"警告：仍有{nan_count}个站点无法获取高程信息，使用平均高程代替")
                stations_df['高程'].fillna(stations_df['高程'].mean(), inplace=True)
            
            return stations_df
    except Exception as e:
        print(f"从DEM提取高程时出错：{e}")
        stations_df['高程'] = 0
        return stations_df

# 加载雨量站点数据
def load_rainfall_data(station_ids):
    """
    加载所有站点的雨量数据
    
    参数:
        station_ids: 站点ID列表
    
    返回:
        包含所有站点雨量数据的字典，格式为{station_id: dataframe}
    """
    rainfall_data = {}
    
    for station_id in station_ids:
        try:
            file_path = os.path.join(input_path, f"{station_id}.csv")
            if os.path.exists(file_path):
                # 读取雨量数据文件
                df = pd.read_csv(file_path, encoding='utf-8')
                
                # 检查必要列是否存在
                if '时间' not in df.columns or '雨量' not in df.columns:
                    print(f"警告：站点{station_id}的数据缺少必要列（时间或雨量）")
                    continue
                
                # 确保时间列的格式正确
                try:
                    df['时间'] = pd.to_datetime(df['时间'])
                except:
                    print(f"警告：站点{station_id}的时间列格式不正确，尝试其他解析方式")
                    # 尝试常见的时间格式
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S', 
                                '%Y-%m-%d %H:%M', '%Y/%m/%d %H:%M']:
                        try:
                            df['时间'] = pd.to_datetime(df['时间'], format=fmt)
                            break
                        except:
                            continue
                
                # 确保雨量列是数值类型
                df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce')
                
                # 处理缺失值
                df.dropna(subset=['雨量'], inplace=True)
                
                rainfall_data[station_id] = df
                print(f"成功加载站点{station_id}的雨量数据，共{len(df)}条记录")
            else:
                print(f"警告：未找到站点{station_id}的雨量数据文件")
        except Exception as e:
            print(f"加载站点{station_id}的雨量数据时出错：{e}")
    
    return rainfall_data

# 计算自然邻近点及权重
def find_natural_neighbors(target_point, points, k=5):
    """
    找到目标点的自然邻近点并计算权重
    
    参数:
        target_point: 目标点坐标 [x, y]
        points: 所有站点坐标
        k: 考虑的最近邻点数量，默认为5
    
    返回:
        邻近点索引列表和对应的权重
    """
    # 将输入转换为numpy数组
    target_point = np.array(target_point)
    points = np.array(points)
    
    # 计算所有点到目标点的欧氏距离
    distances = np.sqrt(np.sum((points - target_point)**2, axis=1))
    
    # 找到k个最近邻点的索引
    nearest_indices = np.argsort(distances)[:k]
    nearest_distances = distances[nearest_indices]
    
    # 如果目标点与某个点重合，直接返回该点
    if np.any(nearest_distances < 1e-10):
        zero_idx = np.where(nearest_distances < 1e-10)[0][0]
        weights = np.zeros(len(nearest_indices))
        weights[zero_idx] = 1.0
        return nearest_indices, weights
    
    # 计算权重（基于Shepard方法的自然邻近点修正）
    # 使用距离的平方倒数作为权重基础
    raw_weights = 1.0 / (nearest_distances ** 2)
    
    # 归一化权重
    weights = raw_weights / np.sum(raw_weights)
    
    return nearest_indices, weights

# 可视化Voronoi图及测试点
def visualize_voronoi_neighbors(stations_df, output_file=None):
    """
    可视化所有站点的Voronoi图，并显示邻近点关系
    
    参数:
        stations_df: 站点信息DataFrame
        output_file: 输出文件路径
    """
    plt.figure(figsize=(15, 12))
    
    # 提取站点坐标
    points = stations_df[['经度', '纬度']].values
    
    # 创建Voronoi图
    try:
        vor = Voronoi(points)
        # 绘制Voronoi图
        voronoi_plot_2d(vor, ax=plt.gca(), show_vertices=False, 
                        line_colors='gray', line_width=1, line_alpha=0.6,
                        point_size=5)
    except Exception as e:
        print(f"创建Voronoi图时出错: {e}")
        # 如果Voronoi创建失败，至少绘制点
        plt.scatter(points[:, 0], points[:, 1], c='blue', s=30)
    
    # 为每个站点显示ID
    for idx, station in stations_df.iterrows():
        plt.text(station['经度'], station['纬度'] + 0.01, 
                 f"{station['站点']}", ha='center', va='bottom', fontsize=8)
    
    # 绘制自然邻近点示例
    # 选择几个示例站点
    sample_size = min(5, len(stations_df))
    sample_indices = np.random.choice(len(stations_df), sample_size, replace=False)
    
    # 生成随机颜色
    colors = list(mcolors.TABLEAU_COLORS.values())
    
    # 保存邻近点信息的列表，用于后续生成表格
    neighbor_info = []
    
    # 为每个示例站点绘制其自然邻近点关系
    for i, sample_idx in enumerate(sample_indices):
        test_station = stations_df.iloc[sample_idx]
        test_station_id = test_station['站点']
        test_point = [test_station['经度'], test_station['纬度']]
        
        # 创建不包含测试站点的临时站点集
        temp_stations = stations_df.drop(sample_idx).copy()
        temp_points = temp_stations[['经度', '纬度']].values
        
        # 找到自然邻近点
        neighbor_indices, weights = find_natural_neighbors(test_point, temp_points)
        
        # 随机选择一种颜色
        color = colors[i % len(colors)]
        
        # 绘制测试站点
        plt.scatter(test_point[0], test_point[1], c='red', s=80, 
                    edgecolor='black', alpha=0.7)
        
        # 标注测试站点
        plt.text(test_point[0], test_point[1] + 0.02, 
                 f"{test_station_id} (测试)", ha='center', va='bottom', fontsize=9)
        
        # 绘制连接线和邻近点
        for j, idx in enumerate(neighbor_indices):
            neighbor = temp_stations.iloc[idx]
            neighbor_point = [neighbor['经度'], neighbor['纬度']]
            
            # 绘制连接线
            plt.plot([test_point[0], neighbor_point[0]], 
                     [test_point[1], neighbor_point[1]], 
                     '--', color=color, alpha=0.7, linewidth=1.5)
            
            # 标注权重
            mid_x = (test_point[0] + neighbor_point[0]) / 2
            mid_y = (test_point[1] + neighbor_point[1]) / 2
            plt.text(mid_x, mid_y, f"{weights[j]:.3f}", 
                     color=color, fontsize=8, ha='center', va='center',
                     bbox=dict(facecolor='white', alpha=0.7, pad=2))
            
            # 保存邻近点信息
            neighbor_info.append({
                '测试站点': test_station_id,
                '邻近站点': neighbor['站点'],
                '权重': weights[j],
                '距离': np.sqrt(np.sum((np.array(test_point) - np.array(neighbor_point))**2))
            })
    
    # 设置图表标题和标签
    plt.title('站点Voronoi图与自然邻近点示例')
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 创建图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='red', edgecolor='black', alpha=0.7, label='测试站点'),
        Patch(facecolor='blue', edgecolor='black', alpha=0.7, label='邻近站点')
    ]
    plt.legend(handles=legend_elements, loc='best')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Voronoi图与自然邻近点示例已保存至: {output_file}")
    
    plt.close()
    
    # 将邻近点信息保存为表格
    neighbor_df = pd.DataFrame(neighbor_info)
    return neighbor_df

# 留一法验证函数（使用自然邻近点）
def leave_one_out_validation(timestamp, stations_df, rainfall_data):
    """
    使用留一法和自然邻近点法对特定时间戳的降雨数据进行插值和验证
    
    参数:
        timestamp: 时间戳
        stations_df: 站点信息DataFrame
        rainfall_data: 站点雨量数据字典
    
    返回:
        包含真实值和预测值的DataFrame
    """
    results = []
    
    # 获取此时间戳下有降雨数据的站点
    available_stations = {}
    for station_id, df in rainfall_data.items():
        if timestamp in df['时间'].values:
            rain_value = df.loc[df['时间'] == timestamp, '雨量'].values[0]
            if not np.isnan(rain_value):
                available_stations[station_id] = rain_value
    
    # 如果可用站点不足，返回空结果
    if len(available_stations) < 4:  # 至少需要4个站点才能进行留一法
        print(f"时间戳{timestamp}下的可用站点不足4个，跳过")
        return pd.DataFrame()
    
    # 对每个有数据的站点进行留一法验证
    for test_station_id in available_stations.keys():
        test_station_info = stations_df[stations_df['站点'] == test_station_id]
        if test_station_info.empty:
            print(f"警告：找不到站点{test_station_id}的信息，跳过")
            continue
            
        test_station_info = test_station_info.iloc[0]
        test_point = [test_station_info['经度'], test_station_info['纬度']]
        
        # 构建不包含测试站点的临时数据集
        temp_stations = stations_df[stations_df['站点'] != test_station_id].copy()
        
        # 筛选有降雨数据的站点
        temp_stations_with_data = []
        temp_values = []
        for _, station in temp_stations.iterrows():
            station_id = station['站点']
            if station_id in available_stations:
                temp_stations_with_data.append(station)
                temp_values.append(available_stations[station_id])
        
        # 如果可用站点不足，跳过
        if len(temp_stations_with_data) < 3:
            print(f"时间戳{timestamp}下，站点{test_station_id}的可用邻居站点不足3个，跳过")
            continue
        
        # 将临时站点转换为DataFrame
        temp_stations_df = pd.DataFrame(temp_stations_with_data)
        temp_points = temp_stations_df[['经度', '纬度']].values
        
        try:
            # 找到自然邻近点和权重
            neighbor_indices, weights = find_natural_neighbors(test_point, temp_points)
            
            # 获取邻近站点ID和降雨值
            neighbor_stations = []
            neighbor_values = []
            for idx in neighbor_indices:
                station_id = temp_stations_df.iloc[idx]['站点']
                if station_id in available_stations:
                    neighbor_stations.append(station_id)
                    neighbor_values.append(available_stations[station_id])
                
            # 如果邻近站点不足，跳过
            if len(neighbor_stations) < 3:
                print(f"时间戳{timestamp}下，站点{test_station_id}的有效邻近站点不足3个，跳过")
                continue
            
            # 使用权重进行插值预测
            predicted_value = np.sum(weights[:len(neighbor_values)] * np.array(neighbor_values))
            
            # 获取真实值
            true_value = available_stations[test_station_id]
            
            # 保存结果
            results.append({
                '时间': timestamp,
                '站点': test_station_id,
                '真实值': true_value,
                '预测值': predicted_value,
                '邻居站点': ','.join(neighbor_stations),
                '权重': ','.join([f"{w:.4f}" for w in weights[:len(neighbor_stations)]])
            })
            
        except Exception as e:
            print(f"站点{test_station_id}在时间戳{timestamp}的留一法验证时出错：{e}")
    
    return pd.DataFrame(results)

# 计算评价指标
def calculate_metrics(validation_results):
    """
    计算插值结果的评价指标
    
    参数:
        validation_results: 包含真实值和预测值的DataFrame
    
    返回:
        包含各种评价指标的字典
    """
    if validation_results.empty:
        return {
            'MAE': np.nan,
            'RMSE': np.nan,
            'R2': np.nan,
            'NSE': np.nan
        }
    
    true_values = validation_results['真实值'].values
    predicted_values = validation_results['预测值'].values
    
    # 平均绝对误差
    mae = mean_absolute_error(true_values, predicted_values)
    
    # 均方根误差
    rmse = np.sqrt(mean_squared_error(true_values, predicted_values))
    
    # 决定系数R²
    r2 = r2_score(true_values, predicted_values)
    
    # 纳什系数
    mean_true = np.mean(true_values)
    numerator = np.sum((true_values - predicted_values) ** 2)
    denominator = np.sum((true_values - mean_true) ** 2)
    nse = 1 - (numerator / denominator) if denominator != 0 else np.nan
    
    return {
        'MAE': mae,
        'RMSE': rmse,
        'R2': r2,
        'NSE': nse
    }

# 根据留一法结果生成每个站点的完整降雨量时间序列
def generate_complete_timeseries(all_validation_results, rainfall_data):
    """
    根据留一法验证结果，生成每个站点的完整降雨量时间序列
    
    参数:
        all_validation_results: 所有时间戳的留一法验证结果
        rainfall_data: 原始雨量数据字典
    
    返回:
        包含完整时间序列的字典，格式为{station_id: dataframe}
    """
    # 获取所有站点
    all_stations = set()
    for df in all_validation_results.values():
        if not df.empty:
            all_stations.update(df['站点'].unique())
    
    # 获取所有时间戳
    all_timestamps = set()
    for df in rainfall_data.values():
        all_timestamps.update(df['时间'].unique())
    all_timestamps = sorted(all_timestamps)
    
    # 为每个站点创建完整的时间序列
    complete_series = {}
    
    for station_id in all_stations:
        # 创建一个包含所有时间戳的DataFrame
        station_data = pd.DataFrame({'时间': all_timestamps})
        station_data['雨量'] = np.nan
        
        # 首先填入原始数据
        if station_id in rainfall_data:
            original_data = rainfall_data[station_id]
            for _, row in original_data.iterrows():
                station_data.loc[station_data['时间'] == row['时间'], '雨量'] = row['雨量']
        
        # 然后填入插值结果
        for timestamp, df in all_validation_results.items():
            if not df.empty:
                station_results = df[df['站点'] == station_id]
                if not station_results.empty:
                    for _, row in station_results.iterrows():
                        station_data.loc[station_data['时间'] == row['时间'], '雨量'] = row['预测值']
        
        # 保存完整的时间序列
        complete_series[station_id] = station_data
    
    return complete_series

# 创建流域雨量栅格（使用自然邻近点法）
def create_rainfall_grid(timestamp, stations_df, rainfall_data, mask_file, output_file):
    """
    为特定时间戳创建流域雨量栅格，使用自然邻近点法
    
    参数:
        timestamp: 时间戳
        stations_df: 站点信息DataFrame
        rainfall_data: 雨量数据字典
        mask_file: 流域掩码文件路径
        output_file: 输出栅格文件路径
    """
    try:
        # 读取流域掩码
        with rasterio.open(mask_file) as mask_src:
            mask = mask_src.read(1)
            transform = mask_src.transform
            crs = mask_src.crs
            
            # 获取栅格的范围
            height, width = mask.shape
            
            # 创建一个空的雨量栅格
            rainfall_grid = np.zeros_like(mask, dtype=np.float32)
            rainfall_grid.fill(np.nan)
            
            # 获取此时间戳的可用站点雨量数据
            available_stations = {}
            for station_id, df in rainfall_data.items():
                if timestamp in df['时间'].values:
                    rain_value = df.loc[df['时间'] == timestamp, '雨量'].values[0]
                    if not np.isnan(rain_value):
                        station_info = stations_df[stations_df['站点'] == station_id].iloc[0]
                        available_stations[station_id] = {
                            'value': rain_value,
                            'coord': [station_info['经度'], station_info['纬度']]
                        }
            
            # 如果可用站点太少，则不生成栅格
            if len(available_stations) < 3:
                print(f"时间戳{timestamp}的可用站点不足3个，跳过栅格生成")
                return False
            
            # 提取站点坐标和雨量值
            points = np.array([available_stations[sid]['coord'] for sid in available_stations])
            values = np.array([available_stations[sid]['value'] for sid in available_stations])
            
            # 使用LinearNDInterpolator作为自然邻近点插值的近似
            # 这个插值器会创建一个基于三角剖分的线性插值函数
            try:
                interpolator = LinearNDInterpolator(points, values, fill_value=np.nan)
            except Exception as e:
                print(f"创建插值器时出错：{e}，尝试使用最近邻插值")
                interpolator = NearestNDInterpolator(points, values)
            
            # 对流域内的每个像素进行插值，按块处理以减少内存使用
            chunk_size = 100  # 每次处理100行以减少内存使用
            for y_start in range(0, height, chunk_size):
                y_end = min(y_start + chunk_size, height)
                
                # 创建网格点
                grid_points = []
                grid_indices = []
                
                for y in range(y_start, y_end):
                    for x in range(width):
                        if mask[y, x] > 0:  # 只处理流域内的像素
                            # 获取像素的地理坐标
                            lon, lat = transform * (x + 0.5, y + 0.5)
                            grid_points.append([lon, lat])
                            grid_indices.append((y, x))
                
                if not grid_points:
                    continue
                
                # 进行批量插值
                grid_points = np.array(grid_points)
                interpolated_values = interpolator(grid_points)
                
                # 将插值结果填入栅格
                for idx, (y, x) in enumerate(grid_indices):
                    rainfall_grid[y, x] = interpolated_values[idx]
                
                # 对于插值结果为NaN的像素，使用自然邻近点直接计算
                for idx, (y, x) in enumerate(grid_indices):
                    if np.isnan(rainfall_grid[y, x]):
                        pixel_point = grid_points[idx]
                        
                        # 找到自然邻近点和权重
                        neighbor_indices, weights = find_natural_neighbors(pixel_point, points)
                        
                        # 使用权重计算插值结果
                        rainfall_grid[y, x] = np.sum(weights * values[neighbor_indices])
            
            # 写入栅格文件
            with rasterio.open(
                output_file,
                'w',
                driver='AAIGrid',
                height=height,
                width=width,
                count=1,
                dtype=rainfall_grid.dtype,
                crs=crs,
                transform=transform,
                nodata=-9999
            ) as dst:
                # 将NaN替换为NoData值
                rainfall_grid_out = rainfall_grid.copy()
                rainfall_grid_out[np.isnan(rainfall_grid_out)] = -9999
                dst.write(rainfall_grid_out, 1)
            
            print(f"时间戳{timestamp}的雨量栅格已保存至: {output_file}")
            return True
            
    except Exception as e:
        print(f"创建雨量栅格时出错：{e}")
        return False

# 处理单个时间戳的函数，用于并行计算
def process_timestamp(args):
    """
    处理单个时间戳的插值任务，用于并行计算
    
    参数:
        args: 包含以下参数的元组:
            timestamp: 时间戳
            stations_df: 站点信息DataFrame
            rainfall_data: 雨量数据字典
            create_grid: 是否创建栅格
            mask_file: 流域掩码文件路径
            output_path: 输出路径
    
    返回:
        包含验证结果的DataFrame
    """
    timestamp, stations_df, rainfall_data, create_grid, mask_file, output_path = args
    
    try:
        # 执行留一法验证
        validation_results = leave_one_out_validation(timestamp, stations_df, rainfall_data)
        
        # 如果启用栅格创建且有验证结果
        if create_grid and not validation_results.empty:
            os.makedirs(os.path.join(output_path, 'grids'), exist_ok=True)
            grid_output_file = os.path.join(output_path, 'grids', f"rainfall_{clean_filename(str(timestamp))}.asc")
            create_rainfall_grid(timestamp, stations_df, rainfall_data, mask_file, grid_output_file)
        
        return timestamp, validation_results
    
    except Exception as e:
        print(f"处理时间戳{timestamp}时出错：{e}")
        return timestamp, pd.DataFrame()

# 分批处理时间序列
def process_batches(all_timestamps, stations_df, rainfall_data, create_grid, mask_file, output_path, batch_size=100):
    """
    分批处理时间序列以减少内存占用
    
    参数:
        all_timestamps: 所有需要处理的时间戳
        stations_df: 站点信息DataFrame
        rainfall_data: 雨量数据字典
        create_grid: 是否创建栅格
        mask_file: 流域掩码文件路径
        output_path: 输出路径
        batch_size: 每批处理的时间戳数量
    
    返回:
        所有验证结果的字典
    """
    # 获取CPU核心数，但最多使用12个
    num_cores = min(12, mp.cpu_count())
    print(f"使用{num_cores}个CPU核心进行并行计算")
    
    # 将时间戳分成多个批次
    total_batches = (len(all_timestamps) + batch_size - 1) // batch_size
    print(f"将{len(all_timestamps)}个时间戳分成{total_batches}个批次处理，每批{batch_size}个")
    
    all_validation_results = {}
    
    # 逐批处理
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(all_timestamps))
        batch_timestamps = all_timestamps[start_idx:end_idx]
        
        print(f"\n处理第{batch_idx+1}/{total_batches}批，共{len(batch_timestamps)}个时间戳")
        
        # 准备并行处理的参数
        process_args = [(timestamp, stations_df, rainfall_data, create_grid, mask_file, output_path) 
                       for timestamp in batch_timestamps]
        
        # 使用并行处理当前批次
        batch_results = {}
        with mp.Pool(num_cores) as pool, tqdm(total=len(batch_timestamps)) as pbar:
            for timestamp, results in pool.imap_unordered(process_timestamp, process_args):
                batch_results[timestamp] = results
                pbar.update()
        
        # 合并当前批次的结果
        all_validation_results.update(batch_results)
        
        # 保存当前批次的结果
        batch_df = pd.concat(batch_results.values()) if batch_results else pd.DataFrame()
        if not batch_df.empty:
            batch_file = os.path.join(output_path, f'validation_results_batch_{batch_idx+1}.csv')
            batch_df.to_csv(batch_file, index=False, encoding='utf-8')
            print(f"第{batch_idx+1}批验证结果已保存至: {batch_file}")
        
        # 手动清理内存
        del batch_results, batch_df
        gc.collect()
    
    return all_validation_results

# 主函数
def main():
    """
    主函数，执行整个自然邻近点空间插值流程
    """
    print("开始执行自然邻近点空间插值...")
    start_time = time.time()
    
    try:
        # 加载站点信息
        stations_df = load_stations()
        
        # 提取站点高程信息
        dem_file = os.path.join(terrain_path, 'dem.asc')
        if os.path.exists(dem_file):
            stations_df = extract_elevation_from_dem(stations_df, dem_file)
        else:
            print("未找到DEM文件，将不提取高程信息")
        
        # 获取站点ID列表
        station_ids = stations_df['站点'].unique()
        
        # 加载雨量数据
        rainfall_data = load_rainfall_data(station_ids)
        
        # 检查是否成功加载数据
        if not rainfall_data:
            raise ValueError("未能加载任何雨量数据，请检查输入文件")
        
        # 生成Voronoi图和自然邻近点示例图
        neighbor_output_file = os.path.join(output_path, 'voronoi_natural_neighbors.png')
        neighbor_df = visualize_voronoi_neighbors(stations_df, neighbor_output_file)
        
        # 保存邻近点信息表格
        neighbor_table_file = os.path.join(output_path, 'natural_neighbor_info.csv')
        neighbor_df.to_csv(neighbor_table_file, index=False, encoding='utf-8')
        print(f"自然邻近点信息表格已保存至: {neighbor_table_file}")
        
        # 获取所有唯一的时间戳
        all_timestamps = set()
        for df in rainfall_data.values():
            all_timestamps.update(df['时间'].unique())
        all_timestamps = sorted(all_timestamps)
        
        print(f"共有{len(all_timestamps)}个时间戳需要处理")
        
        # 询问用户是否创建栅格
        create_grid = input("是否为每个时间戳创建流域雨量栅格？(y/n): ").lower() == 'y'
        
        # 询问用户批次大小
        try:
            batch_size = int(input("请输入每批处理的时间戳数量（推荐值：50-200）: "))
            if batch_size <= 0:
                batch_size = 100
                print(f"输入无效，使用默认批次大小：{batch_size}")
        except:
            batch_size = 100
            print(f"输入无效，使用默认批次大小：{batch_size}")
        
        # 流域掩码文件路径
        mask_file = os.path.join(terrain_path, 'mask.asc')
        
        # 分批处理时间序列
        all_validation_results = process_batches(
            all_timestamps, stations_df, rainfall_data, 
            create_grid, mask_file, output_path, batch_size
        )
        
        # 合并所有验证结果
        all_results_df = pd.concat([df for df in all_validation_results.values() if not df.empty])
        
        if not all_results_df.empty:
            # 保存所有验证结果
            all_results_file = os.path.join(output_path, 'all_validation_results.csv')
            all_results_df.to_csv(all_results_file, index=False, encoding='utf-8')
            print(f"所有验证结果已保存至: {all_results_file}")
            
            # 计算总体评价指标
            overall_metrics = calculate_metrics(all_results_df)
            print("\n总体评价指标:")
            for metric, value in overall_metrics.items():
                print(f"{metric}: {value:.4f}")
            
            # 计算每个站点的评价指标
            station_metrics = {}
            for station in all_results_df['站点'].unique():
                station_df = all_results_df[all_results_df['站点'] == station]
                station_metrics[station] = calculate_metrics(station_df)
            
            # 创建站点评价指标DataFrame
            station_metrics_df = pd.DataFrame.from_dict(station_metrics, orient='index')
            station_metrics_file = os.path.join(output_path, 'station_metrics.csv')
            station_metrics_df.to_csv(station_metrics_file, encoding='utf-8')
            print(f"站点评价指标已保存至: {station_metrics_file}")
            
            # 生成完整的时间序列
            print("生成完整的时间序列...")
            complete_series = generate_complete_timeseries(all_validation_results, rainfall_data)
            
            # 保存每个站点的完整时间序列
            for station_id, df in complete_series.items():
                output_file = os.path.join(output_path, f"{station_id}.csv")
                df.to_csv(output_file, index=False, encoding='utf-8')
                print(f"站点{station_id}的完整时间序列已保存至: {output_file}")
        else:
            print("没有生成任何验证结果，请检查输入数据")
        
        # 计算总运行时间
        end_time = time.time()
        print(f"\n自然邻近点空间插值完成，总耗时: {(end_time - start_time)/60:.2f}分钟")
        
    except Exception as e:
        print(f"执行过程中出错：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

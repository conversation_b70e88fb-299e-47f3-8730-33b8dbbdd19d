# 空间降雨插值方法技术实现细节补充

## 一、核心算法实现细节

### 1. OI插值法核心实现

#### 协方差矩阵构建
```python
def calculate_covariance_matrix(stations, config):
    """构建站点间协方差矩阵"""
    n_stations = len(stations)
    cov_matrix = np.zeros((n_stations, n_stations))
    
    for i in range(n_stations):
        for j in range(n_stations):
            if i == j:
                # 对角线元素：观测方差 + 观测误差方差
                cov_matrix[i, j] = config['observation_variance'] + config['error_variance']
            else:
                # 非对角线元素：基于距离的协方差函数
                distance = calculate_distance(stations.iloc[i], stations.iloc[j])
                cov_matrix[i, j] = covariance_function(distance, config)
    
    return cov_matrix
```

#### 最优权重计算
```python
def calculate_optimal_weights(target_station, neighbor_stations, cov_matrix):
    """计算最优插值权重"""
    # 构建协方差向量
    cov_vector = []
    for neighbor in neighbor_stations:
        distance = calculate_distance(target_station, neighbor)
        cov_vector.append(covariance_function(distance, config))
    
    # 求解线性方程组：C * w = c
    weights = np.linalg.solve(cov_matrix, cov_vector)
    
    return weights
```

### 2. PRISM插值法核心实现

#### 地形权重计算
```python
def calculate_terrain_weight(target_point, neighbor_points, terrain_data):
    """计算地形特征权重"""
    weights = []
    
    for neighbor in neighbor_points:
        # 高程差异权重
        elev_diff = abs(target_point['elevation'] - neighbor['elevation'])
        elev_weight = np.exp(-elev_diff / config['elevation_scale'])
        
        # 坡度相似性权重
        slope_diff = abs(target_point['slope'] - neighbor['slope'])
        slope_weight = np.exp(-slope_diff / config['slope_scale'])
        
        # 坡向相似性权重
        aspect_diff = calculate_aspect_difference(target_point['aspect'], neighbor['aspect'])
        aspect_weight = np.exp(-aspect_diff / config['aspect_scale'])
        
        # 综合地形权重
        terrain_weight = elev_weight * slope_weight * aspect_weight
        weights.append(terrain_weight)
    
    return np.array(weights)
```

#### 权重融合策略
```python
def combine_weights(distance_weights, terrain_weights, moran_weights, config):
    """融合多种权重"""
    # 归一化各权重
    distance_weights = distance_weights / np.sum(distance_weights)
    terrain_weights = terrain_weights / np.sum(terrain_weights)
    moran_weights = moran_weights / np.sum(moran_weights)
    
    # 加权融合
    final_weights = (config['distance_weight_ratio'] * distance_weights +
                    config['terrain_weight_ratio'] * terrain_weights +
                    config['moran_weight_ratio'] * moran_weights)
    
    # 再次归一化
    final_weights = final_weights / np.sum(final_weights)
    
    return final_weights
```

### 3. Kriging插值法核心实现

#### 变异函数建模
```python
def fit_variogram(distances, semivariances, model_type='spherical'):
    """拟合变异函数模型"""
    
    def spherical_model(h, nugget, sill, range_param):
        """球状模型"""
        result = np.zeros_like(h)
        mask = h <= range_param
        result[mask] = nugget + (sill - nugget) * (1.5 * h[mask] / range_param - 
                                                   0.5 * (h[mask] / range_param) ** 3)
        result[~mask] = sill
        return result
    
    def exponential_model(h, nugget, sill, range_param):
        """指数模型"""
        return nugget + (sill - nugget) * (1 - np.exp(-3 * h / range_param))
    
    # 参数优化
    from scipy.optimize import minimize
    
    def objective(params):
        nugget, sill, range_param = params
        if model_type == 'spherical':
            predicted = spherical_model(distances, nugget, sill, range_param)
        elif model_type == 'exponential':
            predicted = exponential_model(distances, nugget, sill, range_param)
        return np.sum((semivariances - predicted) ** 2)
    
    # 初始参数估计
    initial_params = [0.1 * np.var(semivariances), 
                     np.var(semivariances), 
                     np.max(distances) / 3]
    
    result = minimize(objective, initial_params, method='L-BFGS-B',
                     bounds=[(0, None), (0, None), (0, None)])
    
    return result.x
```

#### Kriging方程求解
```python
def solve_kriging_system(coords, values, target_coord, variogram_params):
    """求解Kriging方程组"""
    n = len(coords)
    
    # 构建Kriging矩阵
    K = np.zeros((n + 1, n + 1))
    
    # 填充变异函数值
    for i in range(n):
        for j in range(n):
            distance = np.linalg.norm(coords[i] - coords[j])
            K[i, j] = variogram_function(distance, variogram_params)
    
    # 添加拉格朗日约束
    K[n, :n] = 1
    K[:n, n] = 1
    K[n, n] = 0
    
    # 构建右端向量
    b = np.zeros(n + 1)
    for i in range(n):
        distance = np.linalg.norm(coords[i] - target_coord)
        b[i] = variogram_function(distance, variogram_params)
    b[n] = 1
    
    # 求解线性方程组
    weights = np.linalg.solve(K, b)
    
    return weights[:n]  # 返回插值权重（不包括拉格朗日乘子）
```

### 4. IDW插值法核心实现

#### 自适应权重计算
```python
def calculate_idw_weights(target_point, neighbor_points, power=2.0, min_distance=1e-6):
    """计算IDW权重，包含数值稳定性处理"""
    distances = []
    
    for neighbor in neighbor_points:
        dist = calculate_distance(target_point, neighbor)
        # 避免除零错误
        dist = max(dist, min_distance)
        distances.append(dist)
    
    distances = np.array(distances)
    
    # 计算权重
    weights = 1.0 / (distances ** power)
    
    # 归一化
    weights = weights / np.sum(weights)
    
    # 数值稳定性检查
    if np.any(np.isnan(weights)) or np.any(np.isinf(weights)):
        # 如果出现数值问题，使用等权重
        weights = np.ones(len(neighbor_points)) / len(neighbor_points)
    
    return weights
```

## 二、数据质量控制实现

### 1. 异常值检测
```python
def detect_outliers(data, method='iqr', threshold=3.0):
    """检测异常值"""
    if method == 'iqr':
        Q1 = np.percentile(data, 25)
        Q3 = np.percentile(data, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = (data < lower_bound) | (data > upper_bound)
    
    elif method == 'zscore':
        z_scores = np.abs((data - np.mean(data)) / np.std(data))
        outliers = z_scores > threshold
    
    return outliers
```

### 2. 数据完整性检查
```python
def check_data_completeness(rainfall_data, missing_threshold=0.5):
    """检查数据完整性"""
    completeness_report = {}
    
    for station_id, station_data in rainfall_data.items():
        total_records = len(station_data)
        missing_records = station_data.isnull().sum()
        completeness_ratio = 1 - (missing_records / total_records)
        
        completeness_report[station_id] = {
            'total_records': total_records,
            'missing_records': missing_records,
            'completeness_ratio': completeness_ratio,
            'is_valid': completeness_ratio >= (1 - missing_threshold)
        }
    
    return completeness_report
```

## 三、并行计算实现

### 1. 多进程并行框架
```python
def parallel_interpolation(time_indices, stations, rainfall_data, config, n_processes=8):
    """并行插值计算"""
    from multiprocessing import Pool, Manager
    
    # 创建共享数据结构
    manager = Manager()
    results_dict = manager.dict()
    
    # 分割任务
    chunk_size = len(time_indices) // n_processes
    chunks = [time_indices[i:i + chunk_size] for i in range(0, len(time_indices), chunk_size)]
    
    # 创建进程池
    with Pool(n_processes) as pool:
        # 提交任务
        futures = []
        for chunk in chunks:
            future = pool.apply_async(
                process_time_chunk,
                args=(chunk, stations, rainfall_data, config, results_dict)
            )
            futures.append(future)
        
        # 等待所有任务完成
        for future in futures:
            future.get()
    
    return dict(results_dict)
```

### 2. 内存优化策略
```python
def memory_efficient_processing(large_dataset, batch_size=1000):
    """内存高效的数据处理"""
    results = []
    
    for i in range(0, len(large_dataset), batch_size):
        batch = large_dataset[i:i + batch_size]
        
        # 处理批次数据
        batch_results = process_batch(batch)
        results.extend(batch_results)
        
        # 清理内存
        del batch
        gc.collect()
    
    return results
```

## 四、评价指标计算实现

### 1. 鲁棒的NSE计算
```python
def calculate_nse_robust(observed, predicted, epsilon=1e-10):
    """鲁棒的NSE计算，避免数值问题"""
    # 移除NaN值
    valid_mask = ~(np.isnan(observed) | np.isnan(predicted))
    obs_valid = observed[valid_mask]
    pred_valid = predicted[valid_mask]
    
    if len(obs_valid) == 0:
        return np.nan
    
    # 计算均值
    obs_mean = np.mean(obs_valid)
    
    # 计算分子和分母
    numerator = np.sum((obs_valid - pred_valid) ** 2)
    denominator = np.sum((obs_valid - obs_mean) ** 2)
    
    # 避免除零
    if denominator < epsilon:
        return 0.0 if numerator < epsilon else -np.inf
    
    nse = 1 - numerator / denominator
    
    # 数值范围检查
    if np.isnan(nse) or np.isinf(nse):
        return -999.0  # 标记为无效值
    
    return nse
```

### 2. 综合评价指标
```python
def calculate_comprehensive_metrics(observed, predicted):
    """计算综合评价指标"""
    metrics = {}
    
    # 基础统计指标
    metrics['MAE'] = np.mean(np.abs(observed - predicted))
    metrics['RMSE'] = np.sqrt(np.mean((observed - predicted) ** 2))
    metrics['NSE'] = calculate_nse_robust(observed, predicted)
    
    # 相关性指标
    correlation_matrix = np.corrcoef(observed, predicted)
    metrics['R'] = correlation_matrix[0, 1] if not np.isnan(correlation_matrix[0, 1]) else 0.0
    metrics['R2'] = metrics['R'] ** 2
    
    # 偏差指标
    metrics['Bias'] = np.mean(predicted - observed)
    metrics['PBIAS'] = 100 * np.sum(predicted - observed) / np.sum(observed) if np.sum(observed) != 0 else 0.0
    
    # KGE指标
    r = metrics['R']
    alpha = np.std(predicted) / np.std(observed) if np.std(observed) != 0 else 1.0
    beta = np.mean(predicted) / np.mean(observed) if np.mean(observed) != 0 else 1.0
    metrics['KGE'] = 1 - np.sqrt((r - 1)**2 + (alpha - 1)**2 + (beta - 1)**2)
    
    return metrics
```

## 五、可视化实现

### 1. 插值结果可视化
```python
def visualize_interpolation_results(interpolated_grid, stations, title="插值结果"):
    """可视化插值结果"""
    import matplotlib.pyplot as plt
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 绘制插值等值线
    contour = ax.contourf(interpolated_grid, levels=20, cmap='Blues')
    plt.colorbar(contour, ax=ax, label='降雨量 (mm)')
    
    # 绘制站点位置
    ax.scatter(stations['经度'], stations['纬度'], c='red', s=50, marker='o', 
              edgecolors='black', linewidth=1, label='观测站点')
    
    # 设置标题和标签
    ax.set_title(title, fontsize=16, fontweight='bold')
    ax.set_xlabel('经度', fontsize=12)
    ax.set_ylabel('纬度', fontsize=12)
    ax.legend()
    
    plt.tight_layout()
    return fig
```

### 2. 评价指标对比图
```python
def plot_method_comparison(results_dict, metrics=['NSE', 'RMSE', 'R2']):
    """绘制方法对比图"""
    import matplotlib.pyplot as plt
    
    methods = list(results_dict.keys())
    n_metrics = len(metrics)
    
    fig, axes = plt.subplots(1, n_metrics, figsize=(15, 5))
    
    for i, metric in enumerate(metrics):
        values = [results_dict[method][metric] for method in methods]
        
        bars = axes[i].bar(methods, values, color=['blue', 'green', 'red', 'orange'])
        axes[i].set_title(f'{metric} 对比', fontsize=14)
        axes[i].set_ylabel(metric, fontsize=12)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{value:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    return fig
```

---

*这些技术实现细节展示了各插值方法的核心算法和工程实现要点，为深入理解和应用提供了技术支撑。*

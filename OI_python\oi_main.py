# -*- coding: utf-8 -*-
"""
OI插值主程序模块

整合所有功能模块，提供完整的OI插值处理流程
"""

import os
import pandas as pd
import numpy as np
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional

from config import Config
from data_processing import (
    read_stations, read_rainfall_data, read_mask,
    filter_stations_by_data, analyze_data_quality
)
from delaunay_triangulation import DelaunayTriangulation
from oi_core import calculate_covariance_matrix
from parallel_processing import (
    process_in_batches, process_single_time_step_debug,
    print_processing_estimate
)
from evaluation_metrics import (
    evaluate_interpolation_results, create_evaluation_summary,
    save_evaluation_results, print_evaluation_summary
)
from raster_processing import process_raster_output

class OIInterpolation:
    """
    OI插值主类
    
    整合所有功能，提供完整的OI插值处理流程
    """
    
    def __init__(self, config: Config):
        """
        初始化OI插值对象
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.stations = None
        self.rainfall_data = None
        self.mask_array = None
        self.mask_header = None
        self.tri = None
        self.cov_matrix = None
        self.results = {}
        
        # 设置日志
        self._setup_logging()
        
        # 验证配置
        if not config.validate():
            raise ValueError("配置验证失败")
    
    def _setup_logging(self):
        """
        设置日志系统
        """
        # 创建日志目录
        log_dir = os.path.join(self.config.output_dir, "logs")
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置日志文件名
        log_file = os.path.join(log_dir, f"oi_interpolation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # 配置日志
        log_level = getattr(logging, self.config.log_level.upper(), logging.INFO)
        
        handlers = []
        if self.config.save_log_to_file:
            handlers.append(logging.FileHandler(log_file, encoding='utf-8'))
        if self.config.show_log_in_console:
            handlers.append(logging.StreamHandler())
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=handlers,
            force=True  # 重新配置日志
        )
        
        logging.info("OI插值系统启动")
        logging.info(f"日志文件: {log_file}")
    
    def load_data(self):
        """
        加载所有必要的数据
        """
        logging.info("开始加载数据...")
        
        # 读取站点信息
        logging.info("读取站点信息...")
        self.stations = read_stations(self.config.stations_file)
        
        # 读取降雨数据
        logging.info("读取降雨数据...")
        station_ids = self.stations['station_id'].tolist()
        self.rainfall_data = read_rainfall_data(self.config.input_dir, station_ids)
        
        # 过滤站点（只保留有数据的站点）
        self.stations = filter_stations_by_data(self.stations, self.rainfall_data)
        
        # 读取掩膜文件
        logging.info("读取掩膜文件...")
        self.mask_array, self.mask_header = read_mask(self.config.mask_file)
        
        # 分析数据质量
        logging.info("分析数据质量...")
        data_quality = analyze_data_quality(self.rainfall_data)
        self.results['data_quality'] = data_quality
        
        logging.info("数据加载完成")
        logging.info(f"  - 有效站点数: {len(self.stations)}")
        logging.info(f"  - 时间步数: {len(self.rainfall_data)}")
        logging.info(f"  - 掩膜尺寸: {self.mask_array.shape}")
    
    def create_triangulation(self):
        """
        创建Delaunay三角网
        """
        logging.info("创建Delaunay三角网...")

        # 使用标准化的DelaunayTriangulation类
        self.delaunay_triangulator = DelaunayTriangulation(self.config)
        self.tri = self.delaunay_triangulator.build_triangulation(self.stations)

        # 生成三角网图
        if self.config.generate_delaunay_plot:
            logging.info("生成Delaunay三角网图...")
            plot_path = os.path.join(self.config.output_dir, 'delaunay_triangulation.png')
            saved_path = self.delaunay_triangulator.plot_triangulation(
                output_path=plot_path, show_weights=False
            )
            if saved_path:
                self.results['delaunay_plot_path'] = saved_path

        # 执行留一法分析
        logging.info("执行留一法分析...")
        loo_results = self.delaunay_triangulator.perform_leave_one_out_analysis()

        # 保存三角网记录
        records_path = os.path.join(self.config.output_dir, 'delaunay_records.csv')
        saved_records = self.delaunay_triangulator.save_triangulation_records(records_path)
        if saved_records:
            self.results['triangulation_records_path'] = saved_records

        logging.info("Delaunay三角网创建完成")
    
    def calculate_covariance(self):
        """
        计算协方差矩阵
        """
        logging.info("计算协方差矩阵...")
        self.cov_matrix = calculate_covariance_matrix(self.stations, self.rainfall_data)
        logging.info("协方差矩阵计算完成")
    
    def run_interpolation(self):
        """
        运行插值计算
        """
        logging.info("开始插值计算...")
        
        # 准备时间索引列表
        time_indices = self.rainfall_data.index.tolist()
        
        # 调试模式：只处理少量数据
        if self.config.debug_mode:
            logging.info(f"调试模式：只处理前 {self.config.debug_time_steps} 个时间步")
            time_indices = time_indices[:self.config.debug_time_steps]
        
        # 打印处理时间估算
        print_processing_estimate(
            len(time_indices), len(self.stations), self.config.num_processes
        )
        
        # 单步调试（可选）
        if self.config.debug_mode and len(time_indices) > 0:
            logging.info("执行单步调试...")
            first_result = process_single_time_step_debug(
                time_indices[0], self.stations, self.rainfall_data,
                self.tri, self.cov_matrix, self.config
            )
            
            if not first_result['success']:
                logging.error("单步调试失败，终止处理")
                return
            
            # 移除已处理的时间步
            time_indices = time_indices[1:]
        
        # 如果没有剩余时间步，直接返回
        if not time_indices:
            logging.info("没有剩余时间步需要处理")
            return
        
        # 分批处理时间步
        logging.info(f"开始分批处理 {len(time_indices)} 个时间步...")
        start_time = time.time()
        
        results = process_in_batches(
            time_indices, self.stations, self.rainfall_data,
            self.tri, self.cov_matrix, self.config,
            self.config.num_processes, self.config.batch_size
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        logging.info(f"插值计算完成，总耗时: {processing_time:.2f} 秒")
        
        # 处理结果
        self._process_results(results)
    
    def _process_results(self, results: List[Dict]):
        """
        处理插值结果
        
        Args:
            results: 插值结果列表
        """
        logging.info("处理插值结果...")
        
        # 收集所有验证结果
        all_interpolated_values = {}
        validation_results = []
        
        successful_results = 0
        failed_results = 0
        
        for result in results:
            if not result.get('success', False):
                failed_results += 1
                logging.error(f"时间步 {result.get('time_index', 'unknown')} 处理失败: {result.get('error', 'Unknown error')}")
                continue
            
            successful_results += 1
            time_index = result['time_index']
            validation_df = result['validation']
            
            # 收集验证结果
            if not validation_df.empty:
                validation_df['time_index'] = time_index
                validation_results.append(validation_df)
            
            # 收集插值结果
            if not validation_df.empty:
                interpolated_values = {}
                for _, row in validation_df.iterrows():
                    interpolated_values[row['station_id']] = row['interpolated_value']
                all_interpolated_values[time_index] = interpolated_values
        
        logging.info(f"结果处理完成，成功: {successful_results}, 失败: {failed_results}")
        
        # 保存验证结果
        if validation_results:
            all_validation = pd.concat(validation_results, ignore_index=True)
            validation_file = os.path.join(self.config.output_dir, "validation_results.csv")
            all_validation.to_csv(validation_file, index=False, encoding='utf-8')
            logging.info(f"验证结果已保存到: {validation_file}")
            
            # 计算总体评价指标
            overall_metrics = create_evaluation_summary(all_validation)
            
            # 保存评价指标
            metrics_file = save_evaluation_results(
                overall_metrics, self.config.output_dir,
                os.path.basename(self.config.input_dir)
            )
            
            # 打印评价摘要
            print_evaluation_summary(overall_metrics)
            
            self.results['validation_file'] = validation_file
            self.results['metrics_file'] = metrics_file
            self.results['overall_metrics'] = overall_metrics
        
        # 生成点雨量输出文件
        if self.config.generate_point_output and all_interpolated_values:
            self._generate_point_output(all_interpolated_values)
        
        self.results['interpolated_values'] = all_interpolated_values
    
    def _generate_point_output(self, all_interpolated_values: Dict):
        """
        生成点雨量输出文件
        
        Args:
            all_interpolated_values: 所有插值结果字典
        """
        logging.info("生成点雨量输出文件...")
        
        # 创建插值结果DataFrame
        interpolated_df = pd.DataFrame.from_dict(all_interpolated_values, orient='index')
        interpolated_file = os.path.join(self.config.output_dir, "interpolated_rainfall.csv")
        interpolated_df.to_csv(interpolated_file, encoding='utf-8')
        logging.info(f"插值结果已保存到: {interpolated_file}")
        
        # 创建与输入格式一致的点雨量输出文件夹
        point_output_dir = os.path.join(self.config.output_dir, "point_rainfall")
        os.makedirs(point_output_dir, exist_ok=True)
        
        # 为每个站点创建CSV文件
        for station_id in self.stations['station_id']:
            if station_id in interpolated_df.columns:
                # 提取该站点的所有时间步的雨量
                station_data = interpolated_df[station_id].reset_index()
                station_data.columns = ["时间", "雨量"]  # 重命名列以匹配输入格式
                
                # 保存为CSV文件
                station_file = os.path.join(point_output_dir, f"{station_id}.csv")
                station_data.to_csv(station_file, index=False, encoding='utf-8')
        
        logging.info(f"点雨量输出文件生成完成，共 {len(self.stations)} 个站点")
        
        self.results['interpolated_file'] = interpolated_file
        self.results['point_output_dir'] = point_output_dir
    
    def generate_raster_output(self):
        """
        生成栅格输出
        """
        if not self.config.generate_raster:
            logging.info("跳过栅格输出生成")
            return
        
        logging.info("开始生成栅格输出...")
        
        # 确定要处理的时间索引
        time_indices = None
        if self.config.debug_mode:
            time_indices = self.rainfall_data.index.tolist()[:self.config.debug_time_steps]
        
        # 生成栅格输出
        raster_stats = process_raster_output(
            self.stations, self.rainfall_data, self.mask_array, self.mask_header,
            self.cov_matrix, self.config.output_dir, time_indices, 
            generate_areal_rainfall=True
        )
        
        self.results['raster_stats'] = raster_stats
        logging.info("栅格输出生成完成")
    
    def run_complete_workflow(self):
        """
        运行完整的工作流程
        """
        try:
            start_time = time.time()
            
            # 1. 加载数据
            self.load_data()
            
            # 2. 创建三角网
            self.create_triangulation()
            
            # 3. 计算协方差矩阵
            self.calculate_covariance()
            
            # 4. 运行插值
            self.run_interpolation()
            
            # 5. 生成栅格输出（可选）
            self.generate_raster_output()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            logging.info(f"完整工作流程完成，总耗时: {total_time:.2f} 秒")
            
            # 保存处理摘要
            self._save_processing_summary(total_time)
            
            return True
        
        except Exception as e:
            logging.error(f"工作流程执行失败: {e}")
            import traceback
            logging.error(traceback.format_exc())
            return False
    
    def _save_processing_summary(self, total_time: float):
        """
        保存处理摘要
        
        Args:
            total_time: 总处理时间
        """
        try:
            summary = {
                'processing_time': total_time,
                'config': self.config.to_dict(),
                'data_info': {
                    'num_stations': len(self.stations) if self.stations is not None else 0,
                    'num_time_steps': len(self.rainfall_data) if self.rainfall_data is not None else 0,
                    'mask_shape': self.mask_array.shape if self.mask_array is not None else None
                },
                'results': {k: v for k, v in self.results.items() if not isinstance(v, (pd.DataFrame, np.ndarray))}
            }
            
            summary_file = os.path.join(self.config.output_dir, "processing_summary.json")
            
            import json
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
            
            logging.info(f"处理摘要已保存到: {summary_file}")
            
        except Exception as e:
            logging.error(f"保存处理摘要时出错: {e}")

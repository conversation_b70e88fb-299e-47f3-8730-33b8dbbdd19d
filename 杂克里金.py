# 导入必要的库
import os
import sys
import time
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib import cm
from matplotlib.colors import Normalize
import matplotlib.font_manager as fm
from scipy.spatial import Delaunay
from scipy.interpolate import griddata, LinearNDInterpolator
from sklearn.metrics import mean_absolute_error, mean_squared_error
from concurrent.futures import ProcessPoolExecutor, as_completed
import warnings
from tqdm import tqdm
import gc
import psutil
from libpysal.weights.distance import DistanceBand
from esda.moran import Moran

# 尝试导入pykrige库，用于克里金插值
try:
    from pykrige.ok import OrdinaryKriging
    from pykrige.uk import UniversalKriging
    PYKRIGE_AVAILABLE = True
except ImportError:
    PYKRIGE_AVAILABLE = False
    warnings.warn("pykrige库未安装，将使用简化的克里金实现")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("rainfall_interpolation.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("RainfallInterpolation")

# 忽略警告
warnings.filterwarnings("ignore")

class MemoryTracker:
    """内存使用跟踪器"""
    
    def __init__(self):
        """初始化内存跟踪器"""
        self.start_times = {}
        self.memory_usage = {}
        self.durations = {}
    
    def start(self, function_name):
        """开始跟踪函数的内存使用"""
        self.start_times[function_name] = time.time()
        self.memory_usage[function_name] = psutil.Process().memory_info().rss / 1024 / 1024  # MB
    
    def end(self, function_name):
        """结束跟踪函数的内存使用"""
        if function_name in self.start_times:
            duration = time.time() - self.start_times[function_name]
            memory_diff = psutil.Process().memory_info().rss / 1024 / 1024 - self.memory_usage[function_name]
            
            if function_name not in self.durations:
                self.durations[function_name] = []
            
            self.durations[function_name].append((duration, memory_diff))
            
            logger.debug(f"函数 {function_name} 执行时间: {duration:.2f}秒, 内存变化: {memory_diff:.2f}MB")
    
    def summary(self):
        """输出内存使用摘要"""
        logger.info("内存使用摘要:")
        
        for function_name, measurements in self.durations.items():
            total_duration = sum(duration for duration, _ in measurements)
            avg_duration = total_duration / len(measurements)
            avg_memory = sum(memory for _, memory in measurements) / len(measurements)
            
            logger.info(f"函数 {function_name}: 调用 {len(measurements)} 次, 平均时间 {avg_duration:.2f}秒, 平均内存变化 {avg_memory:.2f}MB")

class RainfallInterpolation:
    """降雨空间插值类，实现多种插值方法和评估"""
    
    def __init__(self, base_path, input_folder, terrain_folder, output_folder, stations_file):
        """
        初始化降雨空间插值类
        
        参数:
        base_path: 基础路径
        input_folder: 输入文件夹名称
        terrain_folder: 地形文件夹名称
        output_folder: 输出文件夹名称
        stations_file: 站点文件名称
        """
        # 设置路径
        self.base_path = base_path
        self.input_folder = os.path.join(base_path, input_folder)
        self.terrain_folder = os.path.join(base_path, terrain_folder)
        self.output_folder = os.path.join(base_path, output_folder)
        self.stations_file = os.path.join(base_path, stations_file)
        
        # 创建输出文件夹（如果不存在）
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)
            logger.info(f"创建输出文件夹: {self.output_folder}")
        
        # 初始化数据存储
        self.stations_data = None  # 站点信息
        self.rainfall_data = {}    # 降雨数据
        self.mask_data = None      # 流域掩码
        self.grid_x = None         # 网格X坐标
        self.grid_y = None         # 网格Y坐标
        
        # 初始化内存跟踪器
        self.memory_tracker = MemoryTracker()
        
        logger.info("降雨空间插值类初始化完成")
    
    def load_stations(self):
        """加载站点信息"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("load_stations")
            
            # 读取站点CSV文件
            self.stations_data = pd.read_csv(self.stations_file, encoding='utf-8')
            logger.info(f"成功加载站点数据，共{len(self.stations_data)}个站点")
            logger.debug(f"站点数据前5行:\n{self.stations_data.head()}")
            
            # 结束内存跟踪
            self.memory_tracker.end("load_stations")
            return True
        except Exception as e:
            logger.error(f"加载站点数据失败: {str(e)}")
            return False
    
    def load_mask(self):
        """加载流域掩码文件"""
        mask_file = os.path.join(self.terrain_folder, "mask.asc")
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("load_mask")
            
            # 打开ASC文件
            with open(mask_file, 'r') as f:
                # 读取头信息
                ncols = int(f.readline().strip().split()[1])
                nrows = int(f.readline().strip().split()[1])
                xllcorner = float(f.readline().strip().split()[1])
                yllcorner = float(f.readline().strip().split()[1])
                cellsize = float(f.readline().strip().split()[1])
                nodata_value = float(f.readline().strip().split()[1])
                
                # 读取栅格数据
                data = []
                for i in range(nrows):
                    line = f.readline().strip().split()
                    data.append([float(val) for val in line])
                
                # 保存掩码数据
                self.mask_data = {
                    'ncols': ncols,
                    'nrows': nrows,
                    'xllcorner': xllcorner,
                    'yllcorner': yllcorner,
                    'cellsize': cellsize,
                    'nodata_value': nodata_value,
                    'data': np.array(data)
                }
                
                # 创建网格坐标
                x = np.linspace(xllcorner, xllcorner + cellsize * ncols, ncols)
                y = np.linspace(yllcorner + cellsize * nrows, yllcorner, nrows)
                self.grid_x, self.grid_y = np.meshgrid(x, y)
                
                logger.info(f"成功加载掩码文件: {mask_file}")
                logger.debug(f"掩码文件尺寸: {nrows} x {ncols}")
                
                # 结束内存跟踪
                self.memory_tracker.end("load_mask")
                return True
        except Exception as e:
            logger.error(f"加载掩码文件失败: {str(e)}")
            return False
    
    def load_rainfall_data(self):
        """加载所有点雨量数据"""
        if self.stations_data is None:
            logger.error("站点数据未加载，请先加载站点数据")
            return False
        
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("load_rainfall_data")
            
            # 获取输入文件夹中的所有CSV文件
            csv_files = [f for f in os.listdir(self.input_folder) if f.endswith('.csv')]
            logger.info(f"发现{len(csv_files)}个CSV文件")
            
            # 检查站点文件中的站点是否都有对应的数据文件
            station_ids = [str(station_id) for station_id in self.stations_data['站点']]
            missing_stations = [station_id for station_id in station_ids if f"{station_id}.csv" not in csv_files]
            if missing_stations:
                logger.warning(f"以下站点没有对应的降雨数据文件: {missing_stations}")
            
            # 加载每个站点的降雨数据
            for station_id in station_ids:
                file_path = os.path.join(self.input_folder, f"{station_id}.csv")
                if os.path.exists(file_path):
                    try:
                        # 读取CSV文件
                        data = pd.read_csv(file_path, encoding='utf-8')
                        # 确保列名为"时间"和"雨量"
                        if "时间" in data.columns and "雨量" in data.columns:
                            # 转换时间列为datetime格式
                            data['时间'] = pd.to_datetime(data['时间'])
                            # 存储数据
                            self.rainfall_data[station_id] = data
                            logger.debug(f"成功加载站点{station_id}的降雨数据，共{len(data)}条记录")
                        else:
                            logger.warning(f"站点{station_id}的数据文件列名不符合要求，应为'时间'和'雨量'")
                    except Exception as e:
                        logger.error(f"加载站点{station_id}的降雨数据失败: {str(e)}")
            
            logger.info(f"成功加载{len(self.rainfall_data)}个站点的降雨数据")
            
            # 确保所有站点的时间序列一致
            self.standardize_time_series()
            
            # 结束内存跟踪
            self.memory_tracker.end("load_rainfall_data")
            return True
        except Exception as e:
            logger.error(f"加载降雨数据失败: {str(e)}")
            return False
    
    def standardize_time_series(self):
        """标准化所有站点的时间序列，确保时间步一致"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("standardize_time_series")
            
            # 获取所有站点的时间列表
            all_times = []
            for station_id, data in self.rainfall_data.items():
                all_times.extend(data['时间'].tolist())
            
            # 获取唯一的时间点并排序
            unique_times = sorted(list(set(all_times)))
            logger.info(f"所有站点共有{len(unique_times)}个不同时间点")
            
            # 为每个站点创建完整的时间序列
            for station_id in self.rainfall_data:
                station_data = self.rainfall_data[station_id]
                # 创建一个新的DataFrame，包含所有时间点
                full_time_series = pd.DataFrame({'时间': unique_times})
                # 合并现有数据
                full_data = pd.merge(full_time_series, station_data, on='时间', how='left')
                # 填充缺失值为0（假设缺失代表无降雨）
                full_data['雨量'].fillna(0, inplace=True)
                # 更新数据
                self.rainfall_data[station_id] = full_data
            
            logger.info("所有站点的时间序列已标准化")
            
            # 结束内存跟踪
            self.memory_tracker.end("standardize_time_series")
            return True
        except Exception as e:
            logger.error(f"标准化时间序列失败: {str(e)}")
            return False
    
    def get_all_times(self):
        """获取所有唯一的时间点"""
        if not self.rainfall_data:
            logger.error("降雨数据未加载")
            return []
        
        # 获取第一个站点的时间序列（所有站点时间序列应该已经标准化）
        first_station_id = list(self.rainfall_data.keys())[0]
        return self.rainfall_data[first_station_id]['时间'].tolist()
    
    def prepare_data_for_time(self, time_point):
        """为特定时间点准备插值数据"""
        if not self.rainfall_data or not self.stations_data:
            logger.error("降雨数据或站点数据未加载")
            return None
        
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("prepare_data_for_time")
            
            # 创建一个DataFrame存储该时间点所有站点的降雨量
            data = []
            for idx, row in self.stations_data.iterrows():
                station_id = str(row['站点'])
                if station_id in self.rainfall_data:
                    station_data = self.rainfall_data[station_id]
                    # 找到对应时间点的数据
                    time_data = station_data[station_data['时间'] == time_point]
                    if not time_data.empty:
                        rainfall = time_data['雨量'].values[0]
                        data.append({
                            '站点': station_id,
                            '经度': row['经度'],
                            '纬度': row['纬度'],
                            '雨量': rainfall
                        })
            
            time_data = pd.DataFrame(data)
            logger.debug(f"时间点{time_point}的数据已准备，共{len(time_data)}个站点")
            
            # 结束内存跟踪
            self.memory_tracker.end("prepare_data_for_time")
            return time_data
        except Exception as e:
            logger.error(f"为时间点{time_point}准备数据失败: {str(e)}")
            return None
    
    def calculate_moran_i(self, data):
        """计算莫兰指数，评估空间自相关性"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("calculate_moran_i")
            
            # 提取经纬度和降雨量
            if data is None or data.empty:
                return {'I': 0, 'p_value': 1, 'z_score': 0}
            coords = data[['经度', '纬度']].values
            values = data['雨量'].values
            
            # 如果所有值相同，莫兰指数没有意义
            if np.all(values == values[0]):
                logger.warning("所有站点降雨量相同，莫兰指数无法计算")
                self.memory_tracker.end("calculate_moran_i")
                return {'I': 0, 'p_value': 1, 'z_score': 0}
            
            # 检查是否有足够的数据计算莫兰指数
            if len(coords) < 20:
                logger.warning("数据点不足，无法计算莫兰指数")
                self.memory_tracker.end("calculate_moran_i")
                return {'I': 0, 'p_value': 1, 'z_score': 0}
            
            # 创建基于距离的空间权重矩阵
            # 使用平均距离作为阈值
            dists = np.zeros((len(coords), len(coords)))
            for i in range(len(coords)):
                for j in range(len(coords)):
                    dists[i, j] = np.sqrt(np.sum((coords[i] - coords[j])**2))
            
            # 使用平均距离作为阈值
            threshold = np.mean(dists)
            w = DistanceBand(coords, threshold=threshold, binary=True)
            
            # 计算莫兰指数
            moran = Moran(values, w)
            
            logger.info(f"莫兰指数计算结果: I={moran.I:.4f}, p值={moran.p_sim:.4f}")
            
            result = {
                'I': moran.I,
                'p_value': moran.p_sim,
                'z_score': moran.z_sim
            }
            
            # 结束内存跟踪
            self.memory_tracker.end("calculate_moran_i")
            return result
        except Exception as e:
            logger.error(f"计算莫兰指数失败: {str(e)}")
            self.memory_tracker.end("calculate_moran_i")
            return {'I': 0, 'p_value': 1, 'z_score': 0}
    
    def delaunay_triangulation(self, data, point):
        """使用Delaunay三角网选择临近站点"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("delaunay_triangulation")
            
            # 提取站点坐标
            coords = data[['经度', '纬度']].values
            
            # 如果站点数量少于4，直接返回所有站点
            if len(coords) < 4:
                logger.warning("站点数量少于4，无法构建Delaunay三角网，返回所有站点")
                self.memory_tracker.end("delaunay_triangulation")
                return data
            
            # 创建Delaunay三角剖分
            tri = Delaunay(coords)
            
            # 找到包含目标点的三角形
            simplex = tri.find_simplex(point)
            
            result = None
            if simplex >= 0:
                # 获取三角形的顶点索引
                vertices = tri.simplices[simplex]
                # 返回这些顶点对应的站点数据
                result = data.iloc[vertices]
            else:
                # 如果点不在任何三角形内，找最近的站点
                distances = np.sqrt(np.sum((coords - point)**2, axis=1))
                # 获取最近的三个站点
                nearest_indices = np.argsort(distances)[:3]
                result = data.iloc[nearest_indices]
            
            # 结束内存跟踪
            self.memory_tracker.end("delaunay_triangulation")
            return result
        
        except Exception as e:
            logger.error(f"Delaunay三角网站点筛选失败: {str(e)}")
            self.memory_tracker.end("delaunay_triangulation")
            # 返回原始数据
            return data
    
    def ordinary_kriging(self, data, grid_x, grid_y, mask=None):
        """普通克里金插值"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("ordinary_kriging")
            
            # 提取站点坐标和降雨量
            coords = data[['经度', '纬度']].values
            values = data['雨量'].values
            
            # 检查数据
            if np.all(values == 0):
                logger.info("所有站点降雨量为0，跳过克里金插值")
                self.memory_tracker.end("ordinary_kriging")
                return np.zeros_like(grid_x)
            
            # 创建结果数组
            z = np.zeros_like(grid_x)
            
            # 创建掩码版本，标记需要计算的点
            compute_mask = np.ones_like(grid_x, dtype=bool)
            if mask is not None:
                compute_mask = (mask > 0)
            
            # 使用pykrige库（如果可用）
            if PYKRIGE_AVAILABLE:
                # 创建一个平面的坐标和值数组
                lons = coords[:, 0]
                lats = coords[:, 1]
                
                # 计算莫兰指数，选择变异函数模型
                moran_result = self.calculate_moran_i(data)
                variogram_model = 'gaussian' if moran_result['I'] > 0.3 else 'spherical'
                #根据空间自相关性选择合适模型，强相关性时用 'gaussian'，中等相关性用 'spherical'，弱相关性用 'exponential'
                # 创建普通克里金模型
                OK = OrdinaryKriging(
                    lons, lats, values,
                    variogram_model=variogram_model,
                    verbose=False,
                    enable_plotting=False
                )
                
                # 准备网格
                grid_shape = grid_x.shape
                grid_x_1d = grid_x[compute_mask]
                grid_y_1d = grid_y[compute_mask]
                
                # 执行克里金插值
                z_1d, _ = OK.execute('points', grid_x_1d, grid_y_1d)
                
                # 将结果填充到原始网格
                z[compute_mask] = z_1d
                
                # 对掩码外区域设置为NaN
                if mask is not None:
                    z[~compute_mask] = np.nan
            else:
                # 备选方法：使用scipy的griddata
                logger.warning("使用scipy.interpolate.griddata进行普通克里金插值的近似")
                
                # 展平网格
                points = np.column_stack([grid_x[compute_mask], grid_y[compute_mask]])
                
                # 执行插值
                z_flat = griddata(coords, values, points, method='linear', fill_value=0)
                
                # 将结果填充到原始网格
                z[compute_mask] = z_flat
                
                # 对掩码外区域设置为NaN
                if mask is not None:
                    z[~compute_mask] = np.nan
            
            # 确保结果非负
            z[z < 0] = 0
            
            logger.info("普通克里金插值完成")
            
            # 结束内存跟踪
            self.memory_tracker.end("ordinary_kriging")
            return z
        except Exception as e:
            logger.error(f"普通克里金插值失败: {str(e)}")
            self.memory_tracker.end("ordinary_kriging")
            return None
    
    def empirical_bayesian_kriging(self, data, grid_x, grid_y, mask=None):
        """经验贝叶斯克里金插值"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("empirical_bayesian_kriging")
            
            # 提取站点坐标和降雨量
            coords = data[['经度', '纬度']].values
            values = data['雨量'].values
            
            # 检查数据
            if np.all(values == 0):
                logger.info("所有站点降雨量为0，跳过经验贝叶斯克里金插值")
                self.memory_tracker.end("empirical_bayesian_kriging")
                return np.zeros_like(grid_x)
            
            # 创建结果数组
            z = np.zeros_like(grid_x)
            
            # 创建掩码版本，标记需要计算的点
            compute_mask = np.ones_like(grid_x, dtype=bool)
            if mask is not None:
                compute_mask = (mask > 0)
            
            # 计算空间自相关
            moran_result = self.calculate_moran_i(data)
            
            if PYKRIGE_AVAILABLE:
                # 使用空间自相关结果选择合适的变异函数模型
                variogram_model = 'gaussian' if moran_result['I'] > 0.1 else 'spherical'
                
                # 提取坐标
                lons = coords[:, 0]
                lats = coords[:, 1]
                
                # 创建克里金模型
                UK = UniversalKriging(
                    lons, lats, values,
                    variogram_model=variogram_model,
                    verbose=False,
                    enable_plotting=False
                )
                
                # 准备网格
                grid_shape = grid_x.shape
                grid_x_1d = grid_x[compute_mask]
                grid_y_1d = grid_y[compute_mask]
                
                # 执行克里金插值
                z_1d, _ = UK.execute('points', grid_x_1d, grid_y_1d)
                
                # 将结果填充到原始网格
                z[compute_mask] = z_1d
                
                # 对掩码外区域设置为NaN
                if mask is not None:
                    z[~compute_mask] = np.nan
            else:
                # 没有pykrige库时的备选方法
                logger.warning("使用基于局部权重的方法近似经验贝叶斯克里金")
                
                # 使用移动窗口局部加权插值模拟EBK
                for i in range(grid_x.shape[0]):
                    for j in range(grid_x.shape[1]):
                        # 如果该点不在计算范围内，跳过
                        if not compute_mask[i, j]:
                            continue
                        
                        # 当前点坐标
                        point = np.array([grid_x[i, j], grid_y[i, j]])
                        
                        # 找到临近站点
                        nearby_data = self.delaunay_triangulation(data, point)
                        
                        if len(nearby_data) > 0:
                            # 提取坐标和值
                            nearby_coords = nearby_data[['经度', '纬度']].values
                            nearby_values = nearby_data['雨量'].values
                            
                            # 计算距离权重
                            distances = np.sqrt(np.sum((nearby_coords - point)**2, axis=1))
                            weights = 1.0 / (distances + 1e-6)**2
                            weights = weights / np.sum(weights)
                            
                            # 加权平均
                            z[i, j] = np.sum(weights * nearby_values)
                        else:
                            # 如果没有找到临近站点，使用全局IDW
                            distances = np.sqrt(np.sum((coords - point)**2, axis=1))
                            weights = 1.0 / (distances + 1e-6)**2
                            weights = weights / np.sum(weights)
                            z[i, j] = np.sum(weights * values)
            
            # 确保结果非负
            z[z < 0] = 0
            
            logger.info("经验贝叶斯克里金插值完成")
            
            # 结束内存跟踪
            self.memory_tracker.end("empirical_bayesian_kriging")
            return z
        except Exception as e:
            logger.error(f"经验贝叶斯克里金插值失败: {str(e)}")
            self.memory_tracker.end("empirical_bayesian_kriging")
            return None
    
    def local_polynomial(self, data, grid_x, grid_y, mask=None, degree=4):
        """局部多项式插值"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("local_polynomial")
            
            # 提取站点坐标和降雨量
            coords = data[['经度', '纬度']].values
            values = data['雨量'].values
            
            # 检查数据
            if np.all(values == 0):
                logger.info("所有站点降雨量为0，跳过局部多项式插值")
                self.memory_tracker.end("local_polynomial")
                return np.zeros_like(grid_x)
            
            # 创建结果数组
            z = np.zeros_like(grid_x)
            
            # 创建掩码版本，标记需要计算的点
            compute_mask = np.ones_like(grid_x, dtype=bool)
            if mask is not None:
                compute_mask = (mask > 0)
            
            # 计算空间自相关
            moran_result = self.calculate_moran_i(data)
            
            # 根据空间自相关确定搜索半径和权重衰减
            spatial_correlation = moran_result['I']
            search_radius = 0.1 if spatial_correlation > 0.3 else 0.2  # 单位与坐标系统相同
            
            # 对每个网格点进行局部多项式插值
            # 使用生成器减少内存使用
            def grid_points_generator():
                for i in range(grid_x.shape[0]):
                    for j in range(grid_x.shape[1]):
                        if compute_mask[i, j]:
                            yield (i, j, grid_x[i, j], grid_y[i, j])
            
            for i, j, x, y in grid_points_generator():
                # 当前点坐标
                point = np.array([x, y])
                
                # 计算每个站点到当前点的距离
                distances = np.sqrt(np.sum((coords - point)**2, axis=1))
                
                # 选择最近的站点或在搜索半径内的站点
                if spatial_correlation > 0.3:
                    # 强空间相关：使用最近的10个站点
                    k = min(10, len(distances))
                    nearest_indices = np.argsort(distances)[:k]
                else:
                    # 弱空间相关：使用搜索半径内的站点
                    nearest_indices = np.where(distances <= search_radius)[0]
                    if len(nearest_indices) < 3:  # 至少需要3个点
                        k = min(3, len(distances))
                        nearest_indices = np.argsort(distances)[:k]
                
                if len(nearest_indices) < 3:  # 需要至少3个点来拟合二次多项式
                    # 如果站点太少，使用IDW作为替代
                    weights = 1.0 / (distances[nearest_indices] + 1e-6)
                    weights = weights / np.sum(weights)
                    z[i, j] = np.sum(weights * values[nearest_indices])
                else:
                    # 获取最近站点的坐标和值
                    local_coords = coords[nearest_indices]
                    local_values = values[nearest_indices]
                    
                    # 构建多项式特征
                    X = np.ones((len(nearest_indices), 1))  # 常数项
                    
                    if degree >= 1:
                        # 一次项
                        X = np.hstack((X, local_coords))
                    
                    if degree >= 2:
                        # 二次项
                        x_squared = local_coords[:, 0]**2
                        y_squared = local_coords[:, 1]**2
                        xy = local_coords[:, 0] * local_coords[:, 1]
                        X = np.hstack((X, np.column_stack((x_squared, y_squared, xy))))
                    
                    # 使用最小二乘法求解
                    try:
                        # 使用正则化防止过拟合
                        beta = np.linalg.lstsq(X, local_values, rcond=None)[0]
                        
                        # 预测当前点的值
                        X_pred = np.array([1])  # 常数项
                        
                        if degree >= 1:
                            # 一次项
                            X_pred = np.append(X_pred, point)
                        
                        if degree >= 2:
                            # 二次项
                            X_pred = np.append(X_pred, [point[0]**2, point[1]**2, point[0]*point[1]])
                        
                        z[i, j] = np.dot(X_pred, beta)
                    except np.linalg.LinAlgError:
                        # 如果矩阵求解失败，回退到IDW
                        weights = 1.0 / (distances[nearest_indices] + 1e-6)
                        weights = weights / np.sum(weights)
                        z[i, j] = np.sum(weights * local_values)
            
            # 确保结果非负
            z[z < 0] = 0
            
            # 对掩码外区域设置为NaN
            if mask is not None:
                z[~compute_mask] = np.nan
            
            logger.info("局部多项式插值完成")
            
            # 结束内存跟踪
            self.memory_tracker.end("local_polynomial")
            return z
        except Exception as e:
            logger.error(f"局部多项式插值失败: {str(e)}")
            self.memory_tracker.end("local_polynomial")
            return None
    
    def cokriging(self, data, grid_x, grid_y, mask=None):
        """协克里金插值"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("cokriging")
            
            # 提取站点坐标和降雨量
            coords = data[['经度', '纬度']].values
            values = data['雨量'].values
            
            # 检查数据
            if np.all(values == 0):
                logger.info("所有站点降雨量为0，跳过协克里金插值")
                self.memory_tracker.end("cokriging")
                return np.zeros_like(grid_x)
            
            # 创建结果数组
            z = np.zeros_like(grid_x)
            
            # 创建掩码版本，标记需要计算的点
            compute_mask = np.ones_like(grid_x, dtype=bool)
            if mask is not None:
                compute_mask = (mask > 0)
            
            # 计算莫兰指数
            moran_result = self.calculate_moran_i(data)
            spatial_correlation = moran_result['I']
            
            # 协克里金简化实现
            logger.warning("使用普通克里金作为协克里金的替代")
            
            # 使用移动窗口局部权重插值
            for i in range(grid_x.shape[0]):
                for j in range(grid_x.shape[1]):
                    # 如果该点不在计算范围内，跳过
                    if not compute_mask[i, j]:
                        continue
                    
                    # 当前点坐标
                    point = np.array([grid_x[i, j], grid_y[i, j]])
                    
                    # 找到临近站点
                    nearby_data = self.delaunay_triangulation(data, point)
                    
                    # 根据空间自相关调整权重
                    if spatial_correlation > 0.3:  # 强空间相关
                        # 使用基于距离的指数权重
                        nearby_coords = nearby_data[['经度', '纬度']].values
                        nearby_values = nearby_data['雨量'].values
                        distances = np.sqrt(np.sum((nearby_coords - point)**2, axis=1))
                        weights = np.exp(-3 * distances / np.max(distances))
                        weights = weights / np.sum(weights)
                        z[i, j] = np.sum(weights * nearby_values)
                    else:  # 弱空间相关
                        # 使用基于距离的IDW
                        nearby_coords = nearby_data[['经度', '纬度']].values
                        nearby_values = nearby_data['雨量'].values
                        distances = np.sqrt(np.sum((nearby_coords - point)**2, axis=1))
                        weights = 1.0 / (distances + 1e-6)**2
                        weights = weights / np.sum(weights)
                        z[i, j] = np.sum(weights * nearby_values)
            
            # 确保结果非负
            z[z < 0] = 0
            
            # 对掩码外区域设置为NaN
            if mask is not None:
                z[~compute_mask] = np.nan
            
            logger.info("协克里金插值完成")
            
            # 结束内存跟踪
            self.memory_tracker.end("cokriging")
            return z
        except Exception as e:
            logger.error(f"协克里金插值失败: {str(e)}")
            self.memory_tracker.end("cokriging")
            return None
    
    def inverse_distance_weighting(self, data, grid_x, grid_y, mask=None, power=2):
        """反距离加权插值"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("inverse_distance_weighting")
            
            # 提取站点坐标和降雨量
            coords = data[['经度', '纬度']].values
            values = data['雨量'].values
            
            # 检查数据
            if np.all(values == 0):
                logger.info("所有站点降雨量为0，跳过IDW插值")
                self.memory_tracker.end("inverse_distance_weighting")
                return np.zeros_like(grid_x)
            
            # 创建结果数组
            z = np.zeros_like(grid_x)
            
            # 创建掩码版本，标记需要计算的点
            compute_mask = np.ones_like(grid_x, dtype=bool)
            if mask is not None:
                compute_mask = (mask > 0)
            
            # 计算空间自相关
            moran_result = self.calculate_moran_i(data)
            
            # 根据空间自相关调整功率参数
            if moran_result['I'] > 0.3:
                # 强空间相关，使用较小的功率
                adjusted_power = power
            else:
                # 弱空间相关，使用较大的功率
                adjusted_power = power + 1
            
            # 使用scipy的griddata进行IDW插值
            points = coords
            xi = np.column_stack([grid_x[compute_mask], grid_y[compute_mask]])
            
            # 计算距离矩阵
            # 使用矢量化操作以提高效率
            zi = np.zeros(xi.shape[0])
            
            # 批处理计算以减少内存使用
            batch_size = 10000  # 根据内存调整
            num_batches = (xi.shape[0] + batch_size - 1) // batch_size
            
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, xi.shape[0])
                
                xi_batch = xi[start_idx:end_idx]
                
                # 计算批处理点到所有站点的距离
                distances = np.zeros((xi_batch.shape[0], points.shape[0]))
                for i in range(xi_batch.shape[0]):
                    distances[i, :] = np.sqrt(np.sum((points - xi_batch[i])**2, axis=1))
                
                # 避免除以零
                distances[distances < 1e-10] = 1e-10
                
                # 计算权重
                weights = 1.0 / (distances**adjusted_power)
                weights_sum = np.sum(weights, axis=1)
                weights = weights / weights_sum[:, np.newaxis]
                
                # 计算插值结果
                zi_batch = np.sum(weights * values, axis=1)
                zi[start_idx:end_idx] = zi_batch
                
                # 清理内存
                del distances, weights, weights_sum, zi_batch
                gc.collect()
            
            # 将结果填充到原始网格
            z[compute_mask] = zi
            
            # 对掩码外区域设置为NaN
            if mask is not None:
                z[~compute_mask] = np.nan
            
            # 确保结果非负
            z[z < 0] = 0
            
            logger.info("反距离加权插值完成")
            
            # 结束内存跟踪
            self.memory_tracker.end("inverse_distance_weighting")
            return z
        except Exception as e:
            logger.error(f"反距离加权插值失败: {str(e)}")
            self.memory_tracker.end("inverse_distance_weighting")
            return None
    
    def leave_one_out_validation(self, data, method):
        """留一法交叉验证"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("leave_one_out_validation")
            
            # 创建一个DataFrame存储验证结果
            results = []
            
            # 对每个站点进行留一验证
            for idx, row in data.iterrows():
                # 复制数据，排除当前站点
                validation_data = data.drop(idx).copy()
                actual_value = row['雨量']
                
                # 如果实际值为0，且大多数站点也为0，则跳过（避免不必要的计算）
                if actual_value == 0 and (validation_data['雨量'] == 0).mean() > 0.8:
                    predicted_value = 0
                else:
                    # 获取当前站点的坐标
                    point = np.array([row['经度'], row['纬度']])
                    
                    # 根据选择的方法进行插值
                    if method == 'ordinary_kriging':
                        predicted_value = self._predict_at_point_ordinary_kriging(validation_data, point)
                    elif method == 'empirical_bayesian_kriging':
                        predicted_value = self._predict_at_point_empirical_bayesian_kriging(validation_data, point)
                    elif method == 'local_polynomial':
                        predicted_value = self._predict_at_point_local_polynomial(validation_data, point)
                    elif method == 'cokriging':
                        predicted_value = self._predict_at_point_cokriging(validation_data, point)
                    elif method == 'idw':
                        predicted_value = self._predict_at_point_idw(validation_data, point)
                    else:
                        logger.warning(f"未知插值方法: {method}")
                        self.memory_tracker.end("leave_one_out_validation")
                        return None
                
                # 确保预测值非负
                predicted_value = max(0, predicted_value)
                
                # 存储结果
                results.append({
                    '站点': row['站点'],
                    '经度': row['经度'],
                    '纬度': row['纬度'],
                    '实际值': actual_value,
                    '预测值': predicted_value
                })
            
            results_df = pd.DataFrame(results)
            
            # 计算评价指标
            mae = mean_absolute_error(results_df['实际值'], results_df['预测值'])
            rmse = np.sqrt(mean_squared_error(results_df['实际值'], results_df['预测值']))
            
            # 计算纳什系数
            mean_actual = np.mean(results_df['实际值'])
            ss_tot = np.sum((results_df['实际值'] - mean_actual)**2)
            ss_res = np.sum((results_df['实际值'] - results_df['预测值'])**2)
            
            # 避免除以零
            nash = 1 - (ss_res / ss_tot) if ss_tot > 0 else np.nan
            
            logger.info(f"留一法验证完成: 方法={method}, MAE={mae:.4f}, RMSE={rmse:.4f}, Nash={nash:.4f}")
            
            validation_result = {
                'results': results_df,
                'metrics': {
                    'MAE': mae,
                    'RMSE': rmse,
                    'Nash': nash
                }
            }
            
            # 结束内存跟踪
            self.memory_tracker.end("leave_one_out_validation")
            return validation_result
        except Exception as e:
            logger.error(f"留一法验证失败: {str(e)}")
            self.memory_tracker.end("leave_one_out_validation")
            return None
    def _predict_at_point_ordinary_kriging(self, data, point):
        """使用普通克里金在单点预测"""
        try:
            # 提取站点坐标和降雨量
            coords = data[['经度', '纬度']].values
            values = data['雨量'].values
            
            if PYKRIGE_AVAILABLE:
                # 使用pykrige
                lons = coords[:, 0]
                lats = coords[:, 1]
                
                # 计算莫兰指数
                moran_result = self.calculate_moran_i(data)
                variogram_model = 'gaussian' if moran_result['I'] > 0.1 else 'spherical'
                
                # 创建普通克里金模型
                OK = OrdinaryKriging(
                    lons, lats, values,
                    variogram_model=variogram_model,
                    verbose=False,
                    enable_plotting=False
                )
                
                # 预测
                predicted_value, _ = OK.execute('points', [point[0]], [point[1]])
                return predicted_value[0]
            else:
                logger.error(f"普通克里金单点预测失败,使用IDW")
                
                # 备选方法：IDW
                distances = np.sqrt(np.sum((coords - point)**2, axis=1))
                weights = 1.0 / (distances + 1e-6)**2
                weights = weights / np.sum(weights)
                return np.sum(weights * values)
        
        except Exception as e:
            logger.error(f"普通克里金单点预测失败: {str(e)}")
            # 回退到简单平均
            return np.mean(data['雨量'])
    
    def _predict_at_point_empirical_bayesian_kriging(self, data, point):
        """使用经验贝叶斯克里金在单点预测"""
        try:
            # 提取站点坐标和降雨量
            coords = data[['经度', '纬度']].values
            values = data['雨量'].values
            
            # 计算莫兰指数
            moran_result = self.calculate_moran_i(data)
            
            if PYKRIGE_AVAILABLE:
                # 使用pykrige的通用克里金作为EBK的近似
                lons = coords[:, 0]
                lats = coords[:, 1]
                
                variogram_model = 'gaussian' if moran_result['I'] > 0.1 else 'spherical'
                
                # 创建通用克里金模型
                UK = UniversalKriging(
                    lons, lats, values,
                    variogram_model=variogram_model,
                    verbose=False,
                    enable_plotting=False
                )
                
                # 预测
                predicted_value, _ = UK.execute('points', [point[0]], [point[1]])
                return predicted_value[0]
            else:
                logger.error(f"经验贝叶斯克里金单点预测失败,使用备选方法：基于空间自相关调整的IDW")
                # 备选方法：基于空间自相关调整的IDW
                distances = np.sqrt(np.sum((coords - point)**2, axis=1))
                
                # 根据空间自相关调整权重
                power = 2 if moran_result['I'] > 0.1 else 3
                weights = 1.0 / (distances + 1e-6)**power
                weights = weights / np.sum(weights)
                return np.sum(weights * values)
        
        except Exception as e:
            logger.error(f"经验贝叶斯克里金单点预测失败: {str(e)}")
            # 回退到简单平均
            return np.mean(data['雨量'])
    
    def _predict_at_point_local_polynomial(self, data, point):
        """使用局部多项式在单点预测"""
        try:
            # 提取站点坐标和降雨量
            coords = data[['经度', '纬度']].values
            values = data['雨量'].values
            
            # 计算每个站点到当前点的距离
            distances = np.sqrt(np.sum((coords - point)**2, axis=1))
            
            # 选择最近的站点
            k = min(10, len(distances))
            nearest_indices = np.argsort(distances)[:k]
            
            if k < 3:
                # 如果站点太少，使用IDW
                weights = 1.0 / (distances[nearest_indices] + 1e-6)
                weights = weights / np.sum(weights)
                return np.sum(weights * values[nearest_indices])
            else:
                # 获取最近站点数据
                local_coords = coords[nearest_indices]
                local_values = values[nearest_indices]
                
                # 构建多项式特征
                X = np.ones((k, 1))  # 常数项
                X = np.hstack((X, local_coords))  # 一次项
                
                # 二次项
                x_squared = local_coords[:, 0]**2
                y_squared = local_coords[:, 1]**2
                xy = local_coords[:, 0] * local_coords[:, 1]
                X = np.hstack((X, np.column_stack((x_squared, y_squared, xy))))
                
                try:
                    # 最小二乘法求解
                    beta = np.linalg.lstsq(X, local_values, rcond=None)[0]
                    
                    # 预测
                    X_pred = np.array([1, point[0], point[1], point[0]**2, point[1]**2, point[0]*point[1]])
                    return np.dot(X_pred, beta)
                except np.linalg.LinAlgError:
                    logger.error(f"局部多项式单点预测失败,使用备选方法：IDW")
                
                    # 回退到IDW
                    weights = 1.0 / (distances[nearest_indices] + 1e-6)
                    weights = weights / np.sum(weights)
                    return np.sum(weights * local_values)
        
        except Exception as e:
            logger.error(f"局部多项式单点预测失败: {str(e)}")
            # 回退到简单平均
            return np.mean(data['雨量'])
    
    def _predict_at_point_cokriging(self, data, point):
        """使用协克里金在单点预测"""
        try:
            # 提取站点坐标和降雨量
            coords = data[['经度', '纬度']].values
            values = data['雨量'].values
            
            # 计算莫兰指数
            moran_result = self.calculate_moran_i(data)
            
            # 计算距离
            distances = np.sqrt(np.sum((coords - point)**2, axis=1))
            
            if moran_result['I'] > 0.3:  # 强空间相关
                # 使用指数权重
                weights = np.exp(-3 * distances / np.max(distances))
            else:  # 弱空间相关
                logger.error(f"协克里金单点预测失败,使用备选方法：IDW")
                # 使用IDW权重
                weights = 1.0 / (distances + 1e-6)**2
            
            weights = weights / np.sum(weights)
            return np.sum(weights * values)
        
        except Exception as e:
            logger.error(f"协克里金单点预测失败: {str(e)}")
            # 回退到简单平均
            return np.mean(data['雨量'])
    
    def _predict_at_point_idw(self, data, point):
        """使用IDW在单点预测"""
        try:
            # 提取站点坐标和降雨量
            coords = data[['经度', '纬度']].values
            values = data['雨量'].values
            
            # 计算距离
            distances = np.sqrt(np.sum((coords - point)**2, axis=1))
            
            # 计算权重
            weights = 1.0 / (distances + 1e-6)**2
            weights = weights / np.sum(weights)
            
            # 加权平均
            return np.sum(weights * values)
        
        except Exception as e:
            logger.error(f"IDW单点预测失败: {str(e)}")
            # 回退到简单平均
            return np.mean(data['雨量'])
    
    def save_asc_file(self, data, time_point, method, output_dir):
        """保存ASC文件"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("save_asc_file")
            
            # 创建文件名（替换冒号和空格等特殊字符）
            time_str = str(time_point).replace(':', '_').replace(' ', '_').replace('-', '_')
            filename = os.path.join(output_dir, f"{method}_{time_str}.asc")
            
            # 获取掩码信息
            ncols = self.mask_data['ncols']
            nrows = self.mask_data['nrows']
            xllcorner = self.mask_data['xllcorner']
            yllcorner = self.mask_data['yllcorner']
            cellsize = self.mask_data['cellsize']
            nodata_value = self.mask_data['nodata_value']
            
            # 写入ASC文件
            with open(filename, 'w') as f:
                f.write(f"ncols {ncols}\n")
                f.write(f"nrows {nrows}\n")
                f.write(f"xllcorner {xllcorner}\n")
                f.write(f"yllcorner {yllcorner}\n")
                f.write(f"cellsize {cellsize}\n")
                f.write(f"NODATA_value {nodata_value}\n")
                
                for i in range(nrows):
                    row_data = []
                    for j in range(ncols):
                        if np.isnan(data[i, j]):
                            row_data.append(str(nodata_value))
                        else:
                            row_data.append(f"{data[i, j]:.4f}")
                    f.write(' '.join(row_data) + '\n')
            
            logger.info(f"保存ASC文件: {filename}")
            
            # 结束内存跟踪
            self.memory_tracker.end("save_asc_file")
            return filename
        except Exception as e:
            logger.error(f"保存ASC文件失败: {str(e)}")
            self.memory_tracker.end("save_asc_file")
            return None
    
    def visualize_interpolation(self, data, time_point, method, validation_results=None):
        """可视化插值结果"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("visualize_interpolation")
            
            plt.figure(figsize=(14, 10))
            
            # 创建子图
            if validation_results is not None:
                plt.subplot(1, 2, 1)
            
            # 获取掩码数据用于绘图
            mask = self.mask_data['data']
            
            # 创建掩码版本的数据用于绘图
            masked_data = np.copy(data)
            masked_data[mask == 0] = np.nan
            
            # 设置颜色映射
            cmap = cm.get_cmap('Blues')
            norm = Normalize(vmin=0, vmax=np.nanmax(masked_data) if np.nanmax(masked_data) > 0 else 1)
            
            # 绘制插值结果
            im = plt.pcolormesh(self.grid_x, self.grid_y, masked_data, cmap=cmap, norm=norm)
            plt.colorbar(im, label='降雨量 (mm)')
            
            # 绘制站点位置
            time_data = self.prepare_data_for_time(time_point)
            plt.scatter(time_data['经度'], time_data['纬度'], c=time_data['雨量'], 
                      cmap=cmap, norm=norm, edgecolors='black', s=50)
            
            # 添加站点标签
            for idx, row in time_data.iterrows():
                plt.text(row['经度'], row['纬度'], str(row['站点']), 
                       fontsize=8, ha='center', va='bottom')
            
            plt.title(f"{time_point} - {method}插值结果")
            plt.xlabel('经度')
            plt.ylabel('纬度')
            plt.grid(True)
            
            # 如果有验证结果，绘制散点图
            if validation_results is not None:
                plt.subplot(1, 2, 2)
                results = validation_results['results']
                metrics = validation_results['metrics']
                
                plt.scatter(results['实际值'], results['预测值'])
                
                # 添加1:1线
                max_val = max(results['实际值'].max(), results['预测值'].max())
                if max_val > 0:
                    plt.plot([0, max_val], [0, max_val], 'r--')
                else:
                    plt.plot([0, 1], [0, 1], 'r--')
                
                plt.xlabel('实际降雨量 (mm)')
                plt.ylabel('预测降雨量 (mm)')
                plt.title(f"留一法验证结果\nMAE={metrics['MAE']:.4f}, RMSE={metrics['RMSE']:.4f}, Nash={metrics['Nash']:.4f}")
                plt.grid(True)
            
            plt.tight_layout()
            
            # 保存图像
            time_str = str(time_point).replace(':', '_').replace(' ', '_').replace('-', '_')
            fig_path = os.path.join(self.output_folder, f"{method}_{time_str}.png")
            plt.savefig(fig_path, dpi=300)
            plt.close()
            
            logger.info(f"可视化结果已保存: {fig_path}")
            
            # 结束内存跟踪
            self.memory_tracker.end("visualize_interpolation")
            return fig_path
        except Exception as e:
            logger.error(f"可视化插值结果失败: {str(e)}")
            self.memory_tracker.end("visualize_interpolation")
            return None
    
    def process_time_point(self, time_point, methods, save_asc=True, visualize=True):
        """处理单个时间点的插值"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start(f"process_time_point_{time_point}")
            
            logger.info(f"开始处理时间点: {time_point}")
            
            # 准备该时间点的数据
            time_data = self.prepare_data_for_time(time_point)
            
            if time_data is None and not time_data.empty:
                logger.warning(f"时间点{time_point}没有数据")
                self.memory_tracker.end(f"process_time_point_{time_point}")
                return None
            
            # 检查是否所有站点都为零降雨
            if (time_data['雨量'].sum() == 0):
                logger.info(f"时间点{time_point}所有站点降雨量为0，跳过插值")
                self.memory_tracker.end(f"process_time_point_{time_point}")
                return {
                    'time': time_point,
                    'all_zero': True,
                    'methods': {}
                }
            
            # 获取掩码数据
            mask = self.mask_data['data'] if self.mask_data else None
            
            results = {'time': time_point, 'all_zero': False, 'methods': {}}
            
            # 执行插值
            for method in methods:
                method_results = {'interpolation': None, 'validation': None, 'asc_file': None, 'visualization': None}
                
                # 插值
                if method == 'ordinary_kriging':
                    z = self.ordinary_kriging(time_data, self.grid_x, self.grid_y, mask)
                elif method == 'empirical_bayesian_kriging':
                    z = self.empirical_bayesian_kriging(time_data, self.grid_x, self.grid_y, mask)
                elif method == 'local_polynomial':
                    z = self.local_polynomial(time_data, self.grid_x, self.grid_y, mask)
                elif method == 'cokriging':
                    z = self.cokriging(time_data, self.grid_x, self.grid_y, mask)
                elif method == 'idw':
                    z = self.inverse_distance_weighting(time_data, self.grid_x, self.grid_y, mask)
                else:
                    logger.warning(f"未知插值方法: {method}")
                    continue
                
                if z is None:
                    logger.warning(f"时间点{time_point}使用{method}插值失败")
                    continue
                
                method_results['interpolation'] = z
                
                # 留一法验证
                validation_results = self.leave_one_out_validation(time_data, method)
                method_results['validation'] = validation_results
                
                # 保存ASC文件
                if save_asc:
                    method_dir = os.path.join(self.output_folder, method)
                    if not os.path.exists(method_dir):
                        os.makedirs(method_dir)
                    
                    asc_file = self.save_asc_file(z, time_point, method, method_dir)
                    method_results['asc_file'] = asc_file
                
                # 可视化
                if visualize:
                    vis_file = self.visualize_interpolation(z, time_point, method, validation_results)
                    method_results['visualization'] = vis_file
                
                results['methods'][method] = method_results
                
                # 清理内存
                del z
                gc.collect()
            
            logger.info(f"时间点{time_point}处理完成")
            
            # 结束内存跟踪
            self.memory_tracker.end(f"process_time_point_{time_point}")
            return results
        except Exception as e:
            logger.error(f"处理时间点{time_point}失败: {str(e)}")
            self.memory_tracker.end(f"process_time_point_{time_point}")
            return None
    def process_all_data(self, methods=None, save_asc=True, visualize=True, parallel=True, max_workers=None, chunk_size=5):
        """处理所有时间点的数据"""
        # 默认方法
        if methods is None:
            methods = ['ordinary_kriging', 'empirical_bayesian_kriging', 'local_polynomial', 'cokriging', 'idw']
        
        # 获取所有唯一的时间点
        unique_times = self.get_all_times()
        
        logger.info(f"开始处理{len(unique_times)}个时间点，使用方法: {methods}")
        
        results = {}
        
        # 初始化进度条
        progress_bar = tqdm(total=len(unique_times), desc="处理时间点")
        
        if parallel and len(unique_times) > 1:
            # 并行处理
            max_workers = max_workers or min(os.cpu_count(), 4)
            logger.info(f"启用并行处理，最大工作进程数: {max_workers}")
            
            # 分批处理以减少内存使用
            for i in range(0, len(unique_times), chunk_size):
                chunk_times = unique_times[i:i+chunk_size]
                logger.info(f"处理时间批次 {i//chunk_size + 1}/{(len(unique_times) + chunk_size - 1)//chunk_size}，共{len(chunk_times)}个时间点")
                
                with ProcessPoolExecutor(max_workers=max_workers) as executor:
                    # 提交批次任务
                    future_to_time = {
                        executor.submit(self.process_time_point, time_point, methods, save_asc, visualize): time_point
                        for time_point in chunk_times
                    }
                    
                    # 收集结果
                    for future in as_completed(future_to_time):
                        time_point = future_to_time[future]
                        try:
                            result = future.result()
                            if result:
                                results[time_point] = result
                        except Exception as e:
                            logger.error(f"处理时间点{time_point}出错: {str(e)}")
                        
                        progress_bar.update(1)
                
                # 手动触发垃圾回收
                gc.collect()
        else:
            # 串行处理
            for time_point in unique_times:
                result = self.process_time_point(time_point, methods, save_asc, visualize)
                if result:
                    results[time_point] = result
                progress_bar.update(1)
                
                # 定期触发垃圾回收
                if progress_bar.n % 5 == 0:
                    gc.collect()
        
        progress_bar.close()
        
        # 为每个站点整合留一法验证结果
        self.integrate_validation_results(results, methods)
        
        logger.info("所有时间点处理完成")
        self.memory_tracker.summary()
        return results
    
    def integrate_validation_results(self, results, methods):
        """整合所有时间点的留一法验证结果，为每个站点生成时间序列"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("integrate_validation_results")
            
            # 为每个方法创建一个字典，存储每个站点的实际值和预测值
            station_results = {method: {} for method in methods}
            
            # 遍历所有时间点和方法
            for time_point, time_result in results.items():
                if time_result['all_zero']:
                    continue
                
                for method, method_result in time_result['methods'].items():
                    validation = method_result.get('validation')
                    if validation is None:
                        continue
                    
                    val_results = validation['results']
                    
                    # 对每个站点，存储该时间点的实际值和预测值
                    for _, row in val_results.iterrows():
                        station_id = row['站点']
                        
                        if station_id not in station_results[method]:
                            station_results[method][station_id] = {
                                'times': [],
                                'actual': [],
                                'predicted': []
                            }
                        
                        station_results[method][station_id]['times'].append(time_point)
                        station_results[method][station_id]['actual'].append(row['实际值'])
                        station_results[method][station_id]['predicted'].append(row['预测值'])
            
            # 为每个方法和站点创建CSV文件
            for method in methods:
                method_dir = os.path.join(self.output_folder, method)
                validation_dir = os.path.join(method_dir, "validation")
                
                if not os.path.exists(validation_dir):
                    os.makedirs(validation_dir)
                
                # 对每个站点创建CSV文件
                for station_id, data in station_results[method].items():
                    if not data['times']:
                        continue
                    
                    # 创建DataFrame
                    df = pd.DataFrame({
                        '时间': data['times'],
                        '实际值': data['actual'],
                        '预测值': data['predicted']
                    })
                    
                    # 排序
                    df.sort_values('时间', inplace=True)
                    
                    # 保存CSV
                    csv_file = os.path.join(validation_dir, f"{station_id}.csv")
                    df.to_csv(csv_file, index=False, encoding='utf-8')
                    
                    logger.info(f"已保存站点{station_id}的验证结果: {csv_file}")
                    
                    # 创建验证结果可视化
                    self.visualize_station_validation(df, station_id, method, validation_dir)
            
            logger.info("所有站点的验证结果整合完成")
            
            # 结束内存跟踪
            self.memory_tracker.end("integrate_validation_results")
        except Exception as e:
            logger.error(f"整合验证结果失败: {str(e)}")
            self.memory_tracker.end("integrate_validation_results")
    
    def visualize_station_validation(self, df, station_id, method, output_dir):
        """可视化单个站点的验证结果"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("visualize_station_validation")
            
            plt.figure(figsize=(14, 8))
            
            # 时间序列对比图
            plt.subplot(2, 1, 1)
            plt.plot(df['时间'], df['实际值'], 'b-', label='实际值')
            plt.plot(df['时间'], df['预测值'], 'r--', label='预测值')
            plt.title(f"站点{station_id} - {method}留一法验证结果")
            plt.xlabel('时间')
            plt.ylabel('降雨量 (mm)')
            plt.legend()
            plt.grid(True)
            
            # 散点图和1:1线
            plt.subplot(2, 1, 2)
            plt.scatter(df['实际值'], df['预测值'])
            
            # 添加1:1线
            max_val = max(df['实际值'].max(), df['预测值'].max())
            if max_val > 0:
                plt.plot([0, max_val], [0, max_val], 'r--')
            else:
                plt.plot([0, 1], [0, 1], 'r--')
            
            # 计算评价指标
            mae = mean_absolute_error(df['实际值'], df['预测值'])
            rmse = np.sqrt(mean_squared_error(df['实际值'], df['预测值']))
            
            # 计算纳什系数
            mean_actual = np.mean(df['实际值'])
            ss_tot = np.sum((df['实际值'] - mean_actual)**2)
            ss_res = np.sum((df['实际值'] - df['预测值'])**2)
            
            # 避免除以零
            nash = 1 - (ss_res / ss_tot) if ss_tot > 0 else np.nan
            
            plt.title(f"散点图对比 (MAE={mae:.4f}, RMSE={rmse:.4f}, Nash={nash:.4f})")
            plt.xlabel('实际降雨量 (mm)')
            plt.ylabel('预测降雨量 (mm)')
            plt.grid(True)
            
            plt.tight_layout()
            
            # 保存图像
            fig_path = os.path.join(output_dir, f"{station_id}_validation.png")
            plt.savefig(fig_path, dpi=300)
            plt.close()
            
            logger.info(f"站点{station_id}的验证结果可视化已保存: {fig_path}")
            
            # 结束内存跟踪
            self.memory_tracker.end("visualize_station_validation")
            return fig_path
        except Exception as e:
            logger.error(f"站点{station_id}的验证结果可视化失败: {str(e)}")
            self.memory_tracker.end("visualize_station_validation")
            return None
    
    def evaluate_methods(self, results, methods=None):
        """评估所有方法的性能"""
        if methods is None:
            methods = ['ordinary_kriging', 'empirical_bayesian_kriging', 'local_polynomial', 'cokriging', 'idw']
        
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("evaluate_methods")
            
            # 创建存储所有方法指标的字典
            all_metrics = {method: {'MAE': [], 'RMSE': [], 'Nash': []} for method in methods}
            
            # 收集每个时间点的评价指标
            for time_point, time_result in results.items():
                if time_result['all_zero']:
                    continue
                
                for method, method_result in time_result['methods'].items():
                    validation = method_result.get('validation')
                    if validation is None:
                        continue
                    
                    metrics = validation['metrics']
                    all_metrics[method]['MAE'].append(metrics['MAE'])
                    all_metrics[method]['RMSE'].append(metrics['RMSE'])
                    all_metrics[method]['Nash'].append(metrics['Nash'])
            
            # 计算每个方法的平均指标
            avg_metrics = {}
            for method in methods:
                if not all_metrics[method]['MAE']:
                    logger.warning(f"方法{method}没有可用的评价指标")
                    continue
                
                avg_metrics[method] = {
                    'MAE': np.mean(all_metrics[method]['MAE']),
                    'RMSE': np.mean(all_metrics[method]['RMSE']),
                    'Nash': np.nanmean([n for n in all_metrics[method]['Nash'] if not np.isnan(n)])
                }
            
            # 创建结果表格
            metrics_df = pd.DataFrame(avg_metrics).T
            metrics_df.index.name = '方法'
            
            # 保存结果
            metrics_file = os.path.join(self.output_folder, "methods_evaluation.csv")
            metrics_df.to_csv(metrics_file)
            
            # 可视化结果
            self.visualize_methods_comparison(metrics_df)
            
            logger.info(f"方法评估完成，结果已保存至: {metrics_file}")
            
            # 结束内存跟踪
            self.memory_tracker.end("evaluate_methods")
            return metrics_df
        except Exception as e:
            logger.error(f"评估方法失败: {str(e)}")
            self.memory_tracker.end("evaluate_methods")
            return None
    
    def visualize_methods_comparison(self, metrics_df):
        """可视化不同方法的比较结果"""
        try:
            # 开始跟踪内存使用
            self.memory_tracker.start("visualize_methods_comparison")
            
            plt.figure(figsize=(15, 10))
            
            # 绘制条形图
            metrics = ['MAE', 'RMSE', 'Nash']
            colors = ['blue', 'red', 'green']
            
            for i, metric in enumerate(metrics):
                plt.subplot(1, 3, i+1)
                bars = plt.bar(metrics_df.index, metrics_df[metric], color=colors[i])
                
                # 添加数值标签
                for bar in bars:
                    height = bar.get_height()
                    plt.text(bar.get_x() + bar.get_width()/2., height,
                           f'{height:.4f}', ha='center', va='bottom')
                
                plt.title(f'{metric}指标比较')
                plt.ylabel(metric)
                plt.xticks(rotation=45)
                plt.grid(axis='y')
            
            plt.tight_layout()
            
            # 保存图像
            fig_path = os.path.join(self.output_folder, "methods_comparison.png")
            plt.savefig(fig_path, dpi=300)
            plt.close()
            
            logger.info(f"方法比较可视化已保存: {fig_path}")
            
            # 结束内存跟踪
            self.memory_tracker.end("visualize_methods_comparison")
            return fig_path
        except Exception as e:
            logger.error(f"方法比较可视化失败: {str(e)}")
            self.memory_tracker.end("visualize_methods_comparison")
            return None
    
    def run(self, methods=None, save_asc=True, visualize=True, parallel=True, max_workers=None, chunk_size=5):
        """运行完整的插值流程"""
        logger.info("开始运行降雨空间插值流程")
        
        # 加载站点数据
        if not self.load_stations():
            logger.error("无法加载站点数据，终止运行")
            return False
        
        # 加载掩码文件
        if not self.load_mask():
            logger.error("无法加载掩码文件，终止运行")
            return False
        
        # 加载降雨数据
        if not self.load_rainfall_data():
            logger.error("无法加载降雨数据，终止运行")
            return False
        
        # 处理所有数据
        results = self.process_all_data(methods, save_asc, visualize, parallel, max_workers, chunk_size)
        
        # 评估方法
        self.evaluate_methods(results, methods)
        
        logger.info("降雨空间插值流程完成")
        return True


# 主程序
def main():
    """主函数"""
    # 设置基础路径和文件夹
    base_path = "D:/pythondata/spatial_interpolation"
    input_folder = "input/less"
    terrain_folder = "terrain"
    output_folder = "output/kriging/less"
    stations_file = "stations.csv"
    
    # 创建插值对象
    interpolator = RainfallInterpolation(
        base_path=base_path,
        input_folder=input_folder,
        terrain_folder=terrain_folder,
        output_folder=output_folder,
        stations_file=stations_file
    )
    
    # 指定插值方法
    methods = [
        'ordinary_kriging',      # 普通克里金
        'empirical_bayesian_kriging',  # 经验贝叶斯克里金
        'local_polynomial',      # 局部多项式
        'cokriging',             # 协克里金
        'idw'                    # 反距离加权
    ]
    
    # 运行插值流程
    interpolator.run(
        methods=methods, 
        save_asc=False,          # 保存ASC文件
        visualize=False,         # 生成可视化结果
        parallel=True,          # 使用并行计算
        max_workers=20,          # 最大工作进程数
        chunk_size=20            # 每批处理的时间点数
    )


if __name__ == "__main__":
    main()

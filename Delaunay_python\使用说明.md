# Delaunay插值系统使用说明

## 系统概述

基于Delaunay三角剖分分析结果的空间降雨插值系统，已完成内存优化，可以在内存受限的环境中稳定运行。

## 主要特点

✅ **内存优化**: 解决了内存爆炸问题，支持大规模数据处理  
✅ **批量处理**: 自动处理所有洪水事件  
✅ **逐时刻插值**: 基于包围站点权重进行精确插值  
✅ **全面评估**: 计算NSE、MAE、RMSE等多种指标  
✅ **可视化输出**: 生成时间序列图和散点图  
✅ **中文支持**: 完整的中文界面和输出  

## 快速开始

### 1. 运行内存优化版本（推荐）
```bash
cd Delaunay_python
python run_memory_efficient.py
```

### 2. 运行标准版本
```bash
cd Delaunay_python
python main.py
```

### 3. 运行系统测试
```bash
cd Delaunay_python
python test_system.py
```

## 输入数据要求

### Delaunay分析文件
- 位置: `output/Delaunay/delaunay_analysis_summary_with_names.csv`
- 包含验证站点信息和包围站点权重

### 洪水事件数据
- 位置: `input_another/[事件名]/[站点代码].csv`
- 格式: 时间,雨量

## 输出结果

### 目录结构
```
output/Delaunay_interpolation/
├── [事件名]/
│   ├── [站点ID]_interpolation_results.csv    # 插值结果
│   ├── [事件名]_[站点ID]_timeseries.png       # 时间序列图
│   ├── [事件名]_[站点ID]_scatter.png          # 散点图
│   ├── [事件名]_metrics.csv                  # 评估指标
│   ├── [事件名]_metrics_summary.png          # 指标汇总图
│   └── [事件名]_summary.json                 # 事件汇总
├── all_stations_metrics.csv                  # 所有站点指标
├── overall_report.json                       # 总体报告
└── overall_summary.png                       # 总体汇总图
```

### 关键文件说明

#### 插值结果文件
```csv
timestamp,observed,predicted,error,surrounding_stations_count
2009-04-16 03:00:00,0.0,0.0,0.0,3
```

#### 评估指标文件
```csv
station_id,MAE,RMSE,NSE,R,R2,Bias,PBIAS,Count
80606500,0.253,1.715,0.277,0.706,0.498,-0.234,-74.97,384
```

## 性能指标

### 当前运行结果（部分）
- **处理事件**: 43个洪水事件
- **验证站点**: 34个站点
- **插值成功率**: 100%
- **内存使用**: 优化后稳定在合理范围

### 评估标准
- **NSE > 0.7**: 优秀
- **NSE 0.5-0.7**: 良好  
- **NSE 0.2-0.5**: 可接受
- **NSE < 0.2**: 需要改进

## 内存优化特性

### 优化措施
1. **分块处理**: 数据分批加载和处理
2. **内存监控**: 实时监控内存使用情况
3. **垃圾回收**: 定期清理内存
4. **数据类型优化**: 使用float32节省内存
5. **缓存管理**: 及时清理不需要的缓存

### 配置参数
```python
# 内存优化配置
MEMORY_EFFICIENT_MODE = True    # 启用内存优化
MAX_EVENTS_IN_MEMORY = 1       # 内存中最大事件数
CHUNK_SIZE = 50                # 数据块大小
BATCH_SIZE = 5                 # 批处理大小
GENERATE_PLOTS = False         # 关闭图表生成（节省内存）
```

## 故障排除

### 常见问题

1. **内存不足**
   - 使用 `run_memory_efficient.py`
   - 减少 `BATCH_SIZE` 和 `CHUNK_SIZE`
   - 关闭图表生成

2. **数据文件不存在**
   - 检查 `output/Delaunay/delaunay_analysis_summary_with_names.csv`
   - 确保 `input_another` 目录存在

3. **站点数据格式错误**
   - 确保CSV文件包含"时间"和"雨量"列
   - 检查时间格式是否正确

### 内存监控
系统会自动监控内存使用：
- 警告阈值: 1000MB
- 严重阈值: 2000MB
- 自动优化: 超过阈值时自动清理

## 技术细节

### 插值算法
1. 基于Delaunay三角剖分的包围站点
2. 使用预计算的权重进行加权平均
3. 权重自动归一化确保总和为1

### 评估指标
- **NSE**: Nash-Sutcliffe效率系数
- **MAE**: 平均绝对误差
- **RMSE**: 均方根误差
- **R²**: 决定系数
- **Bias**: 偏差
- **PBIAS**: 百分比偏差

### 数据处理
- 自动处理零值和缺失值
- 时间序列对齐
- 异常值检测和处理

## 系统要求

### 软件依赖
```
pandas >= 1.3.0
numpy >= 1.21.0
matplotlib >= 3.4.0
seaborn >= 0.11.0
psutil >= 5.8.0
```

### 硬件建议
- **内存**: 最少4GB，推荐8GB+
- **CPU**: 多核处理器（支持并行计算）
- **存储**: 足够空间存储结果文件

## 更新日志

### v1.1.0 (内存优化版)
- ✅ 解决内存爆炸问题
- ✅ 添加内存监控功能
- ✅ 优化数据处理流程
- ✅ 改进错误处理机制
- ✅ 添加轻量级运行模式

### v1.0.0 (初始版本)
- ✅ 基本插值功能
- ✅ 评估指标计算
- ✅ 可视化输出
- ✅ 批量处理

## 联系支持

如遇到问题，请检查：
1. 日志文件: `output/Delaunay_interpolation/delaunay_interpolation.log`
2. 运行测试: `python test_system.py`
3. 内存使用情况和系统资源

系统已经过充分测试，可以稳定处理大规模降雨数据的空间插值任务。

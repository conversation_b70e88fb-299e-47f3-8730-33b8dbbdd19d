#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终中文字体测试和可视化生成

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def setup_chinese_font():
    """设置中文字体"""
    print("Setting up Chinese font...")
    
    # 方法1：直接设置字体
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        print("✅ Method 1: Set SimHei font")
        return True
    except Exception as e:
        print(f"❌ Method 1 failed: {e}")
    
    # 方法2：使用matplotlib.rcParams
    try:
        matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        matplotlib.rcParams['axes.unicode_minus'] = False
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        print("✅ Method 2: Set Chinese fonts via rcParams")
        return True
    except Exception as e:
        print(f"❌ Method 2 failed: {e}")
    
    return False

def load_station_mapping():
    """加载站点映射"""
    print("Loading station mapping...")
    
    try:
        df = pd.read_excel('水晏泰森.xlsx')
        mapping = {}
        
        for _, row in df.iterrows():
            code = str(row['PSTCD']).strip()
            name = str(row['NAME']).strip()
            
            # 存储多种格式
            for variant in [code, code.upper(), code.lower()]:
                mapping[variant] = name
        
        print(f"✅ Loaded {len(set(mapping.values()))} unique station names")
        return mapping
        
    except Exception as e:
        print(f"❌ Failed to load station mapping: {e}")
        return {}

def create_final_test_visualization():
    """创建最终测试可视化"""
    print("Creating final test visualization...")
    
    # 设置中文字体
    setup_chinese_font()
    
    # 加载站点映射
    station_mapping = load_station_mapping()
    
    if not station_mapping:
        print("❌ No station mapping available")
        return
    
    # 获取一些示例站点
    sample_stations = [
        ('80606500', '大化'),
        ('80633800', '太平'),
        ('80634200', '水岩'),
        ('805g2300', '百龙滩'),
        ('80607500', '太平'),
        ('80609500', '水晏'),
        ('806d2941', '佛子坪'),
        ('80631000', '大塘')
    ]
    
    # 验证映射
    verified_stations = []
    for code, expected_name in sample_stations:
        found_name = None
        for variant in [code, code.upper(), code.lower()]:
            if variant in station_mapping:
                found_name = station_mapping[variant]
                break
        
        if found_name:
            verified_stations.append((code, found_name))
            print(f"✅ {code} -> {found_name}")
        else:
            print(f"❌ {code} -> NOT FOUND")
    
    if not verified_stations:
        print("❌ No verified stations found")
        return
    
    # 创建测试图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 图1：站点排名（使用中文名称）
    codes = [code for code, name in verified_stations]
    names = [name for code, name in verified_stations]
    values = np.random.uniform(0.2, 0.8, len(verified_stations))
    
    # 按值排序
    sorted_indices = np.argsort(values)
    sorted_names = [names[i] for i in sorted_indices]
    sorted_codes = [codes[i] for i in sorted_indices]
    sorted_values = [values[i] for i in sorted_indices]
    
    # 绘制水平条形图
    bars1 = ax1.barh(range(len(sorted_names)), sorted_values, 
                     color='skyblue', alpha=0.8, edgecolor='black')
    
    ax1.set_yticks(range(len(sorted_names)))
    ax1.set_yticklabels(sorted_names, fontsize=12)
    ax1.set_xlabel('NSE Value', fontsize=12)
    ax1.set_title('Station Performance Ranking\n(Chinese Station Names)', 
                  fontsize=14, fontweight='bold')
    
    # 添加站点代码标注
    for i, (bar, code, value) in enumerate(zip(bars1, sorted_codes, sorted_values)):
        ax1.text(value + 0.01, i, f'({code})', va='center', fontsize=10)
    
    ax1.grid(True, alpha=0.3, axis='x')
    
    # 图2：时间序列示例（使用中文名称）
    time_points = pd.date_range('2020-01-01', periods=10, freq='D')
    
    for i, (code, name) in enumerate(verified_stations[:4]):  # 只显示前4个
        values = np.random.uniform(0.3, 0.7, len(time_points)) + np.random.normal(0, 0.1, len(time_points))
        ax2.plot(time_points, values, 'o-', linewidth=2, markersize=6, 
                label=f'{name} ({code})')
    
    ax2.set_xlabel('Date', fontsize=12)
    ax2.set_ylabel('NSE Value', fontsize=12)
    ax2.set_title('Multi-Station Performance Time Series\n(Chinese Station Names)', 
                  fontsize=14, fontweight='bold')
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 旋转x轴标签
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # 保存图片
    save_path = Path('final_chinese_test_result.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"✅ Final test visualization saved: {save_path}")
    
    # 创建简单的单图测试
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 只测试中文显示
    test_names = ['大化', '太平', '水岩', '百龙滩', '佛子坪']
    test_values = [0.65, 0.72, 0.58, 0.81, 0.45]
    
    bars = ax.bar(range(len(test_names)), test_values, 
                  color=['red', 'orange', 'yellow', 'green', 'blue'], 
                  alpha=0.7, edgecolor='black')
    
    ax.set_xticks(range(len(test_names)))
    ax.set_xticklabels(test_names, fontsize=14, fontweight='bold')
    ax.set_ylabel('NSE Value', fontsize=12)
    ax.set_title('Chinese Font Display Test\n中文字体显示测试', 
                 fontsize=16, fontweight='bold')
    
    # 添加数值标签
    for bar, value in zip(bars, test_values):
        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
               f'{value:.2f}', ha='center', va='bottom', 
               fontsize=12, fontweight='bold')
    
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_ylim(0, 1)
    
    plt.tight_layout()
    
    # 保存简单测试图
    simple_path = Path('simple_chinese_test.png')
    plt.savefig(simple_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"✅ Simple Chinese test saved: {simple_path}")
    
    return save_path, simple_path

def main():
    """主函数"""
    print("="*60)
    print("Final Chinese Font Display Test")
    print("="*60)
    
    try:
        paths = create_final_test_visualization()
        
        print(f"\n" + "="*60)
        print("Test completed!")
        print("Please check the generated images:")
        print("- final_chinese_test_result.png")
        print("- simple_chinese_test.png")
        print("="*60)
        
        # 检查图片是否真的显示了中文
        print("\nIf you can see Chinese characters in the images,")
        print("then the font setup is working correctly.")
        print("If you see squares or question marks,")
        print("then there's still a font issue.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

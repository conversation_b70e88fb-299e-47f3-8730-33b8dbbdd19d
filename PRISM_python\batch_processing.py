"""
批量处理模块
支持批量处理多个文件夹的降雨数据
"""

import os
import pandas as pd
import logging
from typing import List, Dict, Optional
from datetime import datetime
import glob

from config import Config
from prism_main import PRISMInterpolation

logger = logging.getLogger(__name__)


class BatchProcessor:
    """批量处理器类"""
    
    def __init__(self, config: Config):
        """初始化批量处理器"""
        self.config = config
        self.batch_results = {}
        self.summary_metrics = []
    
    def get_batch_folders(self) -> List[str]:
        """获取需要批量处理的文件夹列表"""
        try:
            if self.config.batch_folders:
                # 使用指定的文件夹列表
                folders = self.config.batch_folders
            else:
                # 自动发现所有文件夹
                folders = []
                if os.path.exists(self.config.batch_input_root):
                    for item in os.listdir(self.config.batch_input_root):
                        item_path = os.path.join(self.config.batch_input_root, item)
                        if os.path.isdir(item_path):
                            folders.append(item)
                
                # 按名称排序
                folders.sort()
            
            # 验证文件夹是否存在且包含数据
            valid_folders = []
            for folder in folders:
                folder_path = os.path.join(self.config.batch_input_root, folder)
                if os.path.exists(folder_path):
                    # 检查是否包含CSV文件
                    csv_files = glob.glob(os.path.join(folder_path, "*.csv"))
                    if csv_files:
                        valid_folders.append(folder)
                    else:
                        logger.warning(f"文件夹 {folder} 不包含CSV文件，跳过")
                else:
                    logger.warning(f"文件夹 {folder} 不存在，跳过")
            
            logger.info(f"找到 {len(valid_folders)} 个有效的批量处理文件夹")
            return valid_folders
            
        except Exception as e:
            logger.error(f"获取批量处理文件夹失败: {e}")
            return []
    
    def process_single_folder(self, folder_name: str) -> Optional[Dict]:
        """处理单个文件夹"""
        try:
            logger.info(f"开始处理文件夹: {folder_name}")
            
            # 创建该文件夹的配置副本
            folder_config = Config(
                input_dir=os.path.join(self.config.batch_input_root, folder_name),
                terrain_dir=self.config.terrain_dir,
                output_dir=os.path.join(self.config.batch_output_root, folder_name),
                stations_file=self.config.stations_file,
                neighbor_count=self.config.neighbor_count,
                min_triangle_angle=self.config.min_triangle_angle,
                distance_power=self.config.distance_power,
                elevation_weight=self.config.elevation_weight,
                slope_weight=self.config.slope_weight,
                aspect_weight=self.config.aspect_weight,
                moran_weight=self.config.moran_weight,
                rainfall_threshold=self.config.rainfall_threshold,
                num_cores=self.config.num_cores,
                batch_size=self.config.batch_size,
                memory_efficient=self.config.memory_efficient,
                output_raster=self.config.output_raster,
                output_delaunay_plot=self.config.output_delaunay_plot,
                output_weight_info=self.config.output_weight_info,
                output_evaluation=self.config.output_evaluation,
                debug_mode=self.config.debug_mode,
                verbose_logging=self.config.verbose_logging
            )
            
            # 创建PRISM插值实例
            prism_interpolation = PRISMInterpolation(folder_config)
            
            # 运行完整工作流程
            evaluation_results = prism_interpolation.run_complete_workflow()
            
            # 提取关键指标
            if evaluation_results:
                folder_summary = {
                    'folder_name': folder_name,
                    'processing_time': datetime.now(),
                    'status': 'success'
                }
                
                # 添加总体指标
                if 'overall_metrics' in evaluation_results:
                    overall = evaluation_results['overall_metrics']
                    folder_summary.update({
                        'overall_mae': overall.get('MAE', 0),
                        'overall_rmse': overall.get('RMSE', 0),
                        'overall_nse': overall.get('NSE', 0),
                        'overall_r': overall.get('R', 0),
                        'overall_r2': overall.get('R2', 0),
                        'overall_count': overall.get('Count', 0)
                    })
                
                # 添加非零降雨指标
                if 'nonzero_metrics' in evaluation_results:
                    nonzero = evaluation_results['nonzero_metrics']
                    folder_summary.update({
                        'nonzero_mae': nonzero.get('MAE', 0),
                        'nonzero_rmse': nonzero.get('RMSE', 0),
                        'nonzero_nse': nonzero.get('NSE', 0),
                        'nonzero_r': nonzero.get('R', 0),
                        'nonzero_r2': nonzero.get('R2', 0),
                        'nonzero_count': nonzero.get('Count', 0)
                    })
                
                # 添加统计信息
                if 'statistics' in evaluation_results:
                    stats = evaluation_results['statistics']
                    folder_summary.update({
                        'total_time_points': stats.get('total_time_points', 0),
                        'total_samples': stats.get('total_samples', 0),
                        'nonzero_samples': stats.get('nonzero_samples', 0),
                        'zero_ratio': stats.get('zero_ratio', 0)
                    })
                
                logger.info(f"文件夹 {folder_name} 处理成功")
                return folder_summary
            else:
                logger.warning(f"文件夹 {folder_name} 处理完成但无评价结果")
                return {
                    'folder_name': folder_name,
                    'processing_time': datetime.now(),
                    'status': 'no_evaluation'
                }
            
        except Exception as e:
            logger.error(f"处理文件夹 {folder_name} 失败: {e}")
            return {
                'folder_name': folder_name,
                'processing_time': datetime.now(),
                'status': 'failed',
                'error_message': str(e)
            }
    
    def run_batch_processing(self) -> Dict:
        """运行批量处理"""
        try:
            logger.info("="*60)
            logger.info("开始批量处理")
            logger.info("="*60)
            
            # 获取文件夹列表
            folders = self.get_batch_folders()
            
            if not folders:
                logger.warning("没有找到需要处理的文件夹")
                return {}
            
            # 处理每个文件夹
            for folder in folders:
                folder_result = self.process_single_folder(folder)
                if folder_result:
                    self.batch_results[folder] = folder_result
                    self.summary_metrics.append(folder_result)
            
            # 生成批量处理报告
            self.generate_batch_report()
            
            logger.info("="*60)
            logger.info(f"批量处理完成！成功处理 {len(self.batch_results)} 个文件夹")
            logger.info("="*60)
            
            return self.batch_results
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return {}
    
    def generate_batch_report(self):
        """生成批量处理报告"""
        try:
            if not self.summary_metrics:
                logger.warning("没有批量处理结果可生成报告")
                return
            
            # 创建批量处理输出目录
            batch_output_dir = os.path.join(self.config.batch_output_root, "batch_summary")
            os.makedirs(batch_output_dir, exist_ok=True)
            
            # 创建汇总DataFrame
            summary_df = pd.DataFrame(self.summary_metrics)
            
            # 保存详细汇总
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_file = os.path.join(batch_output_dir, f"batch_summary_{timestamp}.csv")
            summary_df.to_csv(summary_file, index=False, encoding='utf-8')
            
            # 生成文本报告
            report_file = os.path.join(batch_output_dir, f"batch_report_{timestamp}.txt")
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("PRISM批量处理报告\n")
                f.write("=" * 50 + "\n\n")
                
                # 处理统计
                successful_folders = summary_df[summary_df['status'] == 'success']
                failed_folders = summary_df[summary_df['status'] == 'failed']
                
                f.write(f"处理统计:\n")
                f.write(f"  总文件夹数: {len(summary_df)}\n")
                f.write(f"  成功处理: {len(successful_folders)}\n")
                f.write(f"  处理失败: {len(failed_folders)}\n")
                f.write(f"  成功率: {len(successful_folders)/len(summary_df)*100:.1f}%\n\n")
                
                # 成功处理的文件夹指标汇总
                if len(successful_folders) > 0:
                    f.write("成功处理文件夹的指标汇总:\n")
                    f.write("-" * 30 + "\n")
                    
                    # 总体指标
                    if 'overall_nse' in successful_folders.columns:
                        f.write("总体指标平均值:\n")
                        f.write(f"  NSE: {successful_folders['overall_nse'].mean():.4f}\n")
                        f.write(f"  RMSE: {successful_folders['overall_rmse'].mean():.4f}\n")
                        f.write(f"  MAE: {successful_folders['overall_mae'].mean():.4f}\n")
                        f.write(f"  R²: {successful_folders['overall_r2'].mean():.4f}\n\n")
                    
                    # 非零降雨指标
                    if 'nonzero_nse' in successful_folders.columns:
                        f.write("非零降雨指标平均值:\n")
                        f.write(f"  NSE: {successful_folders['nonzero_nse'].mean():.4f}\n")
                        f.write(f"  RMSE: {successful_folders['nonzero_rmse'].mean():.4f}\n")
                        f.write(f"  MAE: {successful_folders['nonzero_mae'].mean():.4f}\n")
                        f.write(f"  R²: {successful_folders['nonzero_r2'].mean():.4f}\n\n")
                    
                    # 最佳和最差表现
                    if 'overall_nse' in successful_folders.columns:
                        best_nse_idx = successful_folders['overall_nse'].idxmax()
                        worst_nse_idx = successful_folders['overall_nse'].idxmin()
                        
                        f.write("表现最佳文件夹 (按NSE):\n")
                        f.write(f"  文件夹: {successful_folders.loc[best_nse_idx, 'folder_name']}\n")
                        f.write(f"  NSE: {successful_folders.loc[best_nse_idx, 'overall_nse']:.4f}\n\n")
                        
                        f.write("表现最差文件夹 (按NSE):\n")
                        f.write(f"  文件夹: {successful_folders.loc[worst_nse_idx, 'folder_name']}\n")
                        f.write(f"  NSE: {successful_folders.loc[worst_nse_idx, 'overall_nse']:.4f}\n\n")
                
                # 失败文件夹列表
                if len(failed_folders) > 0:
                    f.write("处理失败的文件夹:\n")
                    f.write("-" * 30 + "\n")
                    for _, row in failed_folders.iterrows():
                        f.write(f"  {row['folder_name']}: {row.get('error_message', '未知错误')}\n")
            
            # 打印简要报告
            self.print_batch_summary()
            
            logger.info(f"批量处理报告已保存:")
            logger.info(f"  详细汇总: {summary_file}")
            logger.info(f"  文本报告: {report_file}")
            
        except Exception as e:
            logger.error(f"生成批量处理报告失败: {e}")
    
    def print_batch_summary(self):
        """打印批量处理摘要"""
        try:
            if not self.summary_metrics:
                return
            
            summary_df = pd.DataFrame(self.summary_metrics)
            successful_folders = summary_df[summary_df['status'] == 'success']
            
            print("\n" + "="*60)
            print("批量处理结果摘要")
            print("="*60)
            
            print(f"总文件夹数: {len(summary_df)}")
            print(f"成功处理: {len(successful_folders)}")
            print(f"处理失败: {len(summary_df) - len(successful_folders)}")
            
            if len(successful_folders) > 0 and 'overall_nse' in successful_folders.columns:
                print(f"\n平均指标 (成功处理的文件夹):")
                print(f"  NSE:  {successful_folders['overall_nse'].mean():.4f}")
                print(f"  RMSE: {successful_folders['overall_rmse'].mean():.4f}")
                print(f"  MAE:  {successful_folders['overall_mae'].mean():.4f}")
                print(f"  R²:   {successful_folders['overall_r2'].mean():.4f}")
            
            print("="*60 + "\n")
            
        except Exception as e:
            logger.error(f"打印批量处理摘要失败: {e}")

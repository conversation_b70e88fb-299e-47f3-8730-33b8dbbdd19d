#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面检查所有洪水事件的NSE值

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def comprehensive_nse_check():
    """全面检查所有洪水事件的NSE值"""
    
    metrics_dir = Path('output/Delaunay_interpolation/metrics')
    if not metrics_dir.exists():
        print(f"❌ 评价指标目录不存在: {metrics_dir}")
        return None
    
    csv_files = list(metrics_dir.glob('*_metrics.csv'))
    if not csv_files:
        print(f"❌ 未找到评价指标文件")
        return None
    
    print(f"📊 找到{len(csv_files)}个评价指标文件")
    print("="*80)
    
    # 收集所有数据
    all_data = []
    low_nse_records = []
    event_summary = {}
    
    # 按年份排序文件
    csv_files.sort(key=lambda x: x.stem)
    
    for csv_file in csv_files:
        try:
            df = pd.read_csv(csv_file, encoding='utf-8')
            
            if 'NSE' not in df.columns:
                print(f"⚠️  文件{csv_file.name}中没有NSE列")
                continue
            
            event_name = csv_file.stem.replace('_metrics', '')
            
            # 统计该事件的NSE情况
            nse_values = df['NSE'].dropna()
            if len(nse_values) == 0:
                event_summary[event_name] = {
                    'total_stations': len(df),
                    'valid_nse_count': 0,
                    'low_nse_count': 0,
                    'min_nse': np.nan,
                    'max_nse': np.nan,
                    'mean_nse': np.nan
                }
                continue
            
            low_nse_count = (nse_values < -10).sum()
            
            event_summary[event_name] = {
                'total_stations': len(df),
                'valid_nse_count': len(nse_values),
                'low_nse_count': low_nse_count,
                'min_nse': nse_values.min(),
                'max_nse': nse_values.max(),
                'mean_nse': nse_values.mean()
            }
            
            # 收集所有数据
            for _, row in df.iterrows():
                nse_value = row.get('NSE', np.nan)
                station_code = row.get('站点代码', 'Unknown')
                
                if pd.notna(nse_value):
                    all_data.append({
                        'event_name': event_name,
                        'station_code': station_code,
                        'NSE': nse_value,
                        'RMSE': row.get('RMSE', np.nan),
                        'MAE': row.get('MAE', np.nan),
                        'file': csv_file.name
                    })
                    
                    # 检查是否小于-10
                    if nse_value < -10:
                        low_nse_records.append({
                            'event_name': event_name,
                            'station_code': station_code,
                            'NSE': nse_value,
                            'RMSE': row.get('RMSE', np.nan),
                            'MAE': row.get('MAE', np.nan),
                            'file': csv_file.name
                        })
            
            # 打印每个事件的摘要
            summary = event_summary[event_name]
            status = "🔴" if summary['low_nse_count'] > 0 else "🟢"
            print(f"{status} {event_name}: {summary['valid_nse_count']}/{summary['total_stations']}站点有效, "
                  f"NSE范围[{summary['min_nse']:.4f}, {summary['max_nse']:.4f}], "
                  f"平均NSE: {summary['mean_nse']:.4f}, "
                  f"NSE<-10: {summary['low_nse_count']}个")
        
        except Exception as e:
            print(f"❌ 读取文件{csv_file.name}失败: {e}")
    
    print("="*80)
    
    # 总体统计
    if all_data:
        all_df = pd.DataFrame(all_data)
        total_records = len(all_df)
        low_nse_count = len(low_nse_records)
        
        print(f"📊 总体统计:")
        print(f"   总记录数: {total_records:,}")
        print(f"   NSE < -10记录数: {low_nse_count:,}")
        print(f"   NSE < -10比例: {low_nse_count/total_records*100:.2f}%")
        print(f"   NSE最小值: {all_df['NSE'].min():.4f}")
        print(f"   NSE最大值: {all_df['NSE'].max():.4f}")
        print(f"   NSE平均值: {all_df['NSE'].mean():.4f}")
        print(f"   NSE中位数: {all_df['NSE'].median():.4f}")
        
        # 按年份统计
        print(f"\n📅 按年份统计NSE < -10的情况:")
        year_stats = {}
        for record in low_nse_records:
            year = record['event_name'][:4]
            if year not in year_stats:
                year_stats[year] = 0
            year_stats[year] += 1
        
        for year in sorted(year_stats.keys()):
            print(f"   {year}年: {year_stats[year]}个")
        
        # 按站点统计
        print(f"\n🏢 按站点统计NSE < -10的情况:")
        station_stats = {}
        for record in low_nse_records:
            station = record['station_code']
            if station not in station_stats:
                station_stats[station] = []
            station_stats[station].append(record['event_name'])
        
        for station in sorted(station_stats.keys(), key=lambda x: len(station_stats[x]), reverse=True):
            events = station_stats[station]
            print(f"   {station}: {len(events)}次 ({', '.join(events)})")
        
        if low_nse_records:
            print(f"\n🚨 详细的NSE < -10记录:")
            print("-" * 80)
            low_nse_df = pd.DataFrame(low_nse_records)
            low_nse_df = low_nse_df.sort_values('NSE')
            
            for i, (_, row) in enumerate(low_nse_df.iterrows(), 1):
                print(f"{i:2d}. {row['event_name']:<8} {row['station_code']:<12} "
                      f"NSE: {row['NSE']:>10.4f} RMSE: {row['RMSE']:>8.4f} "
                      f"MAE: {row['MAE']:>8.4f}")
        else:
            print(f"\n✅ 未发现NSE < -10的记录")
    
    return {
        'all_data': all_data,
        'low_nse_records': low_nse_records,
        'event_summary': event_summary
    }

def save_comprehensive_report(results):
    """保存全面检查报告"""
    if not results:
        return
    
    output_dir = Path('output/Delaunay_interpolation/comprehensive_nse_check')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细数据
    if results['all_data']:
        all_data_df = pd.DataFrame(results['all_data'])
        all_data_file = output_dir / f'all_nse_data_{timestamp}.csv'
        all_data_df.to_csv(all_data_file, index=False, encoding='utf-8')
        print(f"📁 所有NSE数据已保存: {all_data_file}")
    
    if results['low_nse_records']:
        low_nse_df = pd.DataFrame(results['low_nse_records'])
        low_nse_file = output_dir / f'comprehensive_low_nse_records_{timestamp}.csv'
        low_nse_df.to_csv(low_nse_file, index=False, encoding='utf-8')
        print(f"📁 低NSE记录已保存: {low_nse_file}")
    
    # 保存事件摘要
    if results['event_summary']:
        event_summary_df = pd.DataFrame.from_dict(results['event_summary'], orient='index')
        event_summary_df.index.name = 'event_name'
        event_summary_file = output_dir / f'event_summary_{timestamp}.csv'
        event_summary_df.to_csv(event_summary_file, encoding='utf-8')
        print(f"📁 事件摘要已保存: {event_summary_file}")

def main():
    """主程序"""
    try:
        print("="*80)
        print("全面检查所有洪水事件的NSE值")
        print("Comprehensive NSE Check for All Flood Events")
        print("="*80)
        
        # 执行全面检查
        results = comprehensive_nse_check()
        
        if results:
            # 保存报告
            save_comprehensive_report(results)
            
            print("\n" + "="*80)
            print("✅ 全面NSE检查完成")
            print("="*80)
        else:
            print("\n❌ 全面NSE检查失败")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 全面NSE检查失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

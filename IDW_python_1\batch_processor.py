"""
IDW空间插值批量处理模块

主要功能：
1. 批量处理多个洪水事件
2. 汇总评估结果
3. 生成批量报告
4. 并行处理支持

作者：空间插值系统
版本：1.0
"""

import os
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
import time
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class BatchProcessor:
    """批量处理器
    
    负责批量处理多个洪水事件的IDW插值
    """
    
    def __init__(self, data_processor, interpolator, raster_processor=None,
                 visualizer=None, use_parallel: bool = True, n_processes: int = None):
        """
        初始化批量处理器
        
        参数:
            data_processor: 数据处理器
            interpolator: IDW插值器
            raster_processor: 栅格处理器
            visualizer: 可视化器
            use_parallel: 是否使用并行处理
            n_processes: 并行进程数
        """
        self.data_processor = data_processor
        self.interpolator = interpolator
        self.raster_processor = raster_processor
        self.visualizer = visualizer
        self.use_parallel = use_parallel
        
        # 设置进程数
        if n_processes is None or n_processes <= 0:
            self.n_processes = mp.cpu_count()
        else:
            self.n_processes = min(n_processes, mp.cpu_count())
        
        logger.info(f"批量处理器初始化完成，使用{self.n_processes}个进程")
    
    def process_all_events(self, output_dir: str, generate_raster: bool = False,
                          calculate_areal: bool = True) -> Dict:
        """
        处理所有洪水事件
        
        参数:
            output_dir: 输出目录
            generate_raster: 是否生成栅格
            calculate_areal: 是否计算面雨量
            
        返回:
            Dict: 批量处理结果
        """
        try:
            # 获取所有事件
            events = self.data_processor.get_event_list()
            if not events:
                logger.error("没有找到可处理的事件")
                return {}
            
            logger.info(f"开始批量处理{len(events)}个洪水事件")
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 处理事件
            if self.use_parallel and len(events) > 1:
                results = self._process_events_parallel(
                    events, output_dir, generate_raster, calculate_areal
                )
            else:
                results = self._process_events_sequential(
                    events, output_dir, generate_raster, calculate_areal
                )
            
            # 生成汇总报告
            summary_results = self._generate_summary_report(results, output_dir)
            
            # 生成可视化
            if self.visualizer:
                self._generate_batch_visualizations(results, output_dir)
            
            logger.info(f"批量处理完成，共处理{len(results)}个事件")
            return summary_results
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return {}
    
    def _process_events_sequential(self, events: List[str], output_dir: str,
                                 generate_raster: bool, calculate_areal: bool) -> Dict:
        """顺序处理事件"""
        results = {}
        
        for i, event in enumerate(events):
            logger.info(f"处理事件 {i+1}/{len(events)}: {event}")
            
            try:
                result = self._process_single_event(
                    event, output_dir, generate_raster, calculate_areal
                )
                if result:
                    results[event] = result
                    logger.info(f"事件{event}处理完成")
                else:
                    logger.warning(f"事件{event}处理失败")
                    
            except Exception as e:
                logger.error(f"处理事件{event}时出错: {e}")
                continue
        
        return results
    
    def _process_events_parallel(self, events: List[str], output_dir: str,
                               generate_raster: bool, calculate_areal: bool) -> Dict:
        """并行处理事件"""
        results = {}
        
        # 创建进程池
        with ProcessPoolExecutor(max_workers=self.n_processes) as executor:
            # 提交任务
            future_to_event = {
                executor.submit(
                    self._process_single_event_wrapper,
                    event, output_dir, generate_raster, calculate_areal
                ): event for event in events
            }
            
            # 收集结果
            for future in as_completed(future_to_event):
                event = future_to_event[future]
                try:
                    result = future.result()
                    if result:
                        results[event] = result
                        logger.info(f"事件{event}处理完成")
                    else:
                        logger.warning(f"事件{event}处理失败")
                        
                except Exception as e:
                    logger.error(f"处理事件{event}时出错: {e}")
        
        return results
    
    def _process_single_event_wrapper(self, event: str, output_dir: str,
                                    generate_raster: bool, calculate_areal: bool) -> Optional[Dict]:
        """单事件处理包装器（用于并行处理）"""
        try:
            return self._process_single_event(event, output_dir, generate_raster, calculate_areal)
        except Exception as e:
            logger.error(f"处理事件{event}失败: {e}")
            return None
    
    def _process_single_event(self, event: str, output_dir: str,
                            generate_raster: bool, calculate_areal: bool) -> Optional[Dict]:
        """处理单个事件"""
        try:
            start_time = time.time()
            
            # 加载事件数据
            station_data = self.data_processor.load_event_data(event)
            if not station_data:
                logger.warning(f"无法加载事件{event}的数据")
                return None
            
            # 准备插值数据
            rainfall_dict, time_series = self.data_processor.prepare_interpolation_data(station_data)
            if not rainfall_dict:
                logger.warning(f"事件{event}没有有效的降雨数据")
                return None
            
            # 执行留一法验证
            validation_results = self.interpolator.validate_interpolation(rainfall_dict)
            if not validation_results:
                logger.warning(f"事件{event}验证失败")
                return None
            
            # 创建事件输出目录
            event_output_dir = os.path.join(output_dir, event)
            os.makedirs(event_output_dir, exist_ok=True)
            
            # 保存验证结果
            self._save_validation_results(validation_results, event_output_dir, event)
            
            # 生成栅格（如果需要）
            areal_stats = {}
            if generate_raster and self.raster_processor:
                raster_file = os.path.join(event_output_dir, f'{event}_rainfall.asc')
                rainfall_grid = self.raster_processor.generate_interpolation_grid(
                    self.interpolator, rainfall_dict, output_file=raster_file
                )
                
                # 计算面雨量（如果需要）
                if calculate_areal and rainfall_grid is not None:
                    areal_stats = self.raster_processor.calculate_areal_rainfall(rainfall_grid)
            
            # 生成可视化（如果需要）
            if self.visualizer:
                # 验证结果图
                self.visualizer.plot_validation_results(validation_results, event)
                
                # 站点分布图
                stations_df = self.data_processor.load_stations()
                if stations_df is not None:
                    self.visualizer.plot_station_map(stations_df, rainfall_dict, event)
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 汇总结果
            result = {
                'event': event,
                'processing_time': processing_time,
                'n_stations': len(rainfall_dict),
                'total_rainfall': sum(rainfall_dict.values()),
                'max_rainfall': max(rainfall_dict.values()) if rainfall_dict else 0,
                'validation_results': validation_results,
                'areal_stats': areal_stats
            }
            
            # 添加主要评估指标
            result.update({
                'mae': validation_results.get('mae', np.nan),
                'rmse': validation_results.get('rmse', np.nan),
                'nse': validation_results.get('nse', np.nan),
                'correlation': validation_results.get('correlation', np.nan),
                'bias': validation_results.get('bias', np.nan)
            })
            
            return result
            
        except Exception as e:
            logger.error(f"处理事件{event}失败: {e}")
            return None
    
    def _save_validation_results(self, validation_results: Dict, output_dir: str, event: str):
        """保存验证结果"""
        try:
            # 保存详细结果
            if 'station_results' in validation_results:
                station_results = validation_results['station_results']
                
                # 转换为DataFrame
                results_data = []
                for station, result in station_results.items():
                    results_data.append({
                        'Station': station,
                        'Observed': result['observed'],
                        'Predicted': result['predicted'],
                        'Error': result['error'],
                        'Abs_Error': result['abs_error'],
                        'Method': result['method'],
                        'Used_Stations': ','.join(result['used_stations'])
                    })
                
                results_df = pd.DataFrame(results_data)
                
                # 保存CSV文件
                csv_file = os.path.join(output_dir, f'{event}_validation_details.csv')
                results_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            # 保存汇总指标
            summary_data = {
                'Event': event,
                'MAE': validation_results.get('mae', np.nan),
                'RMSE': validation_results.get('rmse', np.nan),
                'NSE': validation_results.get('nse', np.nan),
                'Correlation': validation_results.get('correlation', np.nan),
                'Bias': validation_results.get('bias', np.nan),
                'N_Stations': validation_results.get('n_stations', 0)
            }
            
            summary_df = pd.DataFrame([summary_data])
            summary_file = os.path.join(output_dir, f'{event}_validation_summary.csv')
            summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
            
        except Exception as e:
            logger.error(f"保存验证结果失败: {e}")
    
    def _generate_summary_report(self, results: Dict, output_dir: str) -> Dict:
        """生成汇总报告"""
        try:
            if not results:
                return {}
            
            # 汇总数据
            summary_data = []
            for event, result in results.items():
                summary_data.append({
                    'Event': event,
                    'Processing_Time': result.get('processing_time', 0),
                    'N_Stations': result.get('n_stations', 0),
                    'Total_Rainfall': result.get('total_rainfall', 0),
                    'Max_Rainfall': result.get('max_rainfall', 0),
                    'MAE': result.get('mae', np.nan),
                    'RMSE': result.get('rmse', np.nan),
                    'NSE': result.get('nse', np.nan),
                    'Correlation': result.get('correlation', np.nan),
                    'Bias': result.get('bias', np.nan)
                })
            
            # 创建DataFrame
            summary_df = pd.DataFrame(summary_data)
            
            # 保存汇总文件
            summary_file = os.path.join(output_dir, 'batch_processing_summary.csv')
            summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
            
            # 计算统计信息
            stats = {
                'total_events': len(results),
                'successful_events': len([r for r in results.values() if not np.isnan(r.get('mae', np.nan))]),
                'mean_mae': summary_df['MAE'].mean(),
                'mean_rmse': summary_df['RMSE'].mean(),
                'mean_nse': summary_df['NSE'].mean(),
                'mean_correlation': summary_df['Correlation'].mean(),
                'total_processing_time': summary_df['Processing_Time'].sum(),
                'results': results
            }
            
            logger.info(f"汇总报告已保存: {summary_file}")
            logger.info(f"平均MAE: {stats['mean_mae']:.3f}")
            logger.info(f"平均RMSE: {stats['mean_rmse']:.3f}")
            logger.info(f"平均NSE: {stats['mean_nse']:.3f}")
            
            return stats
            
        except Exception as e:
            logger.error(f"生成汇总报告失败: {e}")
            return {}
    
    def _generate_batch_visualizations(self, results: Dict, output_dir: str):
        """生成批量可视化"""
        try:
            if not self.visualizer or not results:
                return
            
            # 设置可视化输出目录
            viz_output_dir = os.path.join(output_dir, 'visualizations')
            self.visualizer.output_dir = viz_output_dir
            
            # 生成指标汇总图
            self.visualizer.plot_metrics_summary(results)
            
            logger.info("批量可视化生成完成")
            
        except Exception as e:
            logger.error(f"生成批量可视化失败: {e}")

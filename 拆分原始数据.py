import os
import pandas as pd

def split_and_save_data(input_file_path, output_dir):
    try:
        # 检查文件是否存在
        if not os.path.exists(input_file_path):
            print(f"文件 {input_file_path} 不存在，跳过处理。")
            return

        # 读取 Excel 文件
        excel_file = pd.ExcelFile(input_file_path)
        # 获取指定工作表中的数据
        df = excel_file.parse('JSON')

        if df.empty:
            print(f"{input_file_path} 数据为空，跳过处理。")
            return

        # 处理列名，先转换为字符串类型，再去除空格并转换为小写
        new_columns = []
        for col in df.columns:
            if isinstance(col, str):
                new_columns.append(col.strip().lower())
            else:
                new_columns.append(str(col).strip().lower())
        df.columns = new_columns

        # 检查列名是否有重复
        if len(set(df.columns[:-1])) != len(df.columns[:-1]):
            print(f"{input_file_path} 存在重复的站点列名，请检查数据。")
            return

        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 按站点名拆分数据并保存为 CSV 文件
        for column in df.columns[:-1]:
            single_site_df = df[['tm', column]]
            if not single_site_df[column].isna().all():
                # 修改列名
                single_site_df.columns = ['时间', '雨量']
                output_file_path = os.path.join(output_dir, f'{column}.csv')
                single_site_df.to_csv(output_file_path, index=False)

    except Exception as e:
        print(f"处理 {input_file_path} 时遇到错误: {e}")


base_path = r"D:/pythondata/spline/全年"
for year_folder in os.listdir(base_path):
    year_folder_path = os.path.join(base_path, year_folder)
    if os.path.isdir(year_folder_path):
        print(f"发现年份文件夹: {year_folder_path}")
        # 创建每个年份文件夹下的“太平点雨量”文件夹
        output_base_dir = os.path.join(year_folder_path, "点雨量")
        if not os.path.exists(output_base_dir):
            os.makedirs(output_base_dir)

        location_filenames = ['太平点雨量.xlsx','大化点雨量.xlsx','水晏点雨量.xlsx']
        for filename in location_filenames:
            input_file_path = os.path.join(year_folder_path, filename)
            print(f"检查文件: {input_file_path} 是否存在")
            if os.path.exists(input_file_path):
                print(f"开始处理 {input_file_path}")
                split_and_save_data(input_file_path, output_base_dir)
                print(f"完成处理 {input_file_path}")
            else:
                print(f"文件 {input_file_path} 不存在，跳过处理")
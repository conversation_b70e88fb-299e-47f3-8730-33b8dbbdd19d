# -*- coding: utf-8 -*-
"""
最优插值法(OI)空间插值程序 - 修复版

本程序实现了基于最优插值法的点雨量到面雨量的空间插值，
使用Delaunay三角网进行站点筛选，并应用留一法进行验证。

Author: 水文学教授
Date: 2023-05-22
"""

# 导入必要的库
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay
from scipy.interpolate import LinearNDInterpolator
import multiprocessing as mp
from datetime import datetime
import time
import re
import logging
import glob
import argparse
from tqdm import tqdm
import sys
import traceback

# 设置pandas显示选项，防止警告
pd.set_option('mode.chained_assignment', None)

def setup_logging(output_dir):
    """
    配置日志系统
    
    参数:
    output_dir: 输出目录，日志文件将保存在此目录下的logs子目录中
    
    返回:
    str: 日志文件路径
    """
    # 创建日志目录
    log_dir = os.path.join(output_dir, "logs")
    os.makedirs(log_dir, exist_ok=True)
    
    # 设置日志文件名（使用当前时间）
    log_file = os.path.join(log_dir, f"interpolation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志系统
    logging.basicConfig(
        level=logging.INFO,  # 设置日志级别为INFO
        format='%(asctime)s - %(levelname)s - %(message)s',  # 设置日志格式
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),  # 将日志写入文件
            logging.StreamHandler()  # 同时在控制台显示日志
        ]
    )
    
    logging.info("日志系统已配置")
    return log_file

def safe_filename(filename):
    """
    生成安全的文件名，替换不允许的字符
    
    参数:
    filename: 原始文件名（如时间戳）
    
    返回:
    str: 安全的文件名
    """
    # 替换常见的不允许字符
    safe_name = re.sub(r'[\\/*?:"<>|]', "_", str(filename))
    # 替换冒号和空格
    safe_name = safe_name.replace(":", "-").replace(" ", "_")
    return safe_name

def read_stations(stations_file):
    """
    读取站点信息文件
    
    参数:
    stations_file: 站点信息文件路径，包含站点ID、经度和纬度
    
    返回:
    DataFrame: 包含站点ID、经度和纬度的DataFrame
    """
    try:
        # 读取站点信息文件，假设列名为"站点"、"经度"、"纬度"
        stations = pd.read_csv(stations_file, encoding='utf-8')
        
        # 检查必要的列是否存在
        required_columns = ["站点", "经度", "纬度"]
        for col in required_columns:
            if col not in stations.columns:
                raise ValueError(f"站点信息文件缺少必要的列: {col}")
        
        # 将站点ID转换为字符串类型
        stations["站点"] = stations["站点"].astype(str)
        
        # 重命名列以便后续处理
        stations = stations.rename(columns={"站点": "station_id", "经度": "longitude", "纬度": "latitude"})
        
        logging.info(f"成功读取站点信息，共 {len(stations)} 个站点")
        return stations
    
    except Exception as e:
        logging.error(f"读取站点信息文件时出错: {e}")
        raise

def read_rainfall_data(input_dir, station_ids):
    """
    读取所有站点的降雨数据
    
    参数:
    input_dir: 输入数据目录，包含各站点的降雨数据CSV文件
    station_ids: 站点ID列表
    
    返回:
    DataFrame: 包含所有站点降雨数据的DataFrame，索引为时间，列为站点ID
    """
    try:
        # 创建一个空字典来存储每个站点的数据
        all_data = {}
        
        # 查找输入目录中的所有CSV文件
        csv_files = glob.glob(os.path.join(input_dir, "*.csv"))
        
        # 如果没有找到CSV文件，抛出错误
        if not csv_files:
            raise ValueError(f"在目录 {input_dir} 中没有找到CSV文件")
        
        # 遍历每个站点
        for station_id in station_ids:
            file_path = os.path.join(input_dir, f"{station_id}.csv")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logging.warning(f"站点 {station_id} 的数据文件不存在: {file_path}")
                continue
            
            # 读取降雨数据，尝试不同编码方式
            try:
                station_data = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                # 如果utf-8编码失败，尝试其他编码
                try:
                    station_data = pd.read_csv(file_path, encoding='gbk')
                except UnicodeDecodeError:
                    station_data = pd.read_csv(file_path, encoding='latin1')
            
            # 检查必要的列是否存在
            required_columns = ["时间", "雨量"]
            for col in required_columns:
                if col not in station_data.columns:
                    raise ValueError(f"站点 {station_id} 的数据文件缺少必要的列: {col}")
            
            # 将时间列转换为datetime类型
            station_data["时间"] = pd.to_datetime(station_data["时间"])
            
            # 将数据添加到字典中
            all_data[station_id] = station_data.set_index("时间")["雨量"]
        
        # 将所有站点数据合并为一个DataFrame
        rainfall_data = pd.DataFrame(all_data)
        
        # 检查是否有数据
        if rainfall_data.empty:
            raise ValueError("没有读取到任何降雨数据")
        
        logging.info(f"成功读取降雨数据，时间范围: {rainfall_data.index.min()} 到 {rainfall_data.index.max()}")
        logging.info(f"共读取了 {len(rainfall_data)} 个时间步，{len(rainfall_data.columns)} 个站点的数据")
        
        return rainfall_data
    
    except Exception as e:
        logging.error(f"读取降雨数据时出错: {e}")
        raise

def read_mask(mask_file):
    """
    读取流域掩膜文件(ASC格式)
    
    参数:
    mask_file: 掩膜文件路径
    
    返回:
    tuple: (mask_array, header)，mask_array为掩膜数组，header为ASC文件头信息
    """
    try:
        # 读取ASC文件头信息
        header = {}
        mask_data = []
        
        with open(mask_file, 'r') as f:
            # 读取前6行作为头信息
            for i in range(6):
                line = f.readline().strip()
                key, value = line.split()
                header[key.lower()] = float(value)
            
            # 读取数据部分
            for line in f:
                row = [float(x) for x in line.strip().split()]
                mask_data.append(row)
        
        # 将数据转换为NumPy数组
        mask_array = np.array(mask_data)
        
        # 检查数组维度是否与头信息一致
        nrows = int(header['nrows'])
        ncols = int(header['ncols'])
        if mask_array.shape != (nrows, ncols):
            raise ValueError(f"掩膜数组维度 {mask_array.shape} 与头信息中的维度 ({nrows}, {ncols}) 不一致")
        
        logging.info(f"成功读取掩膜文件，维度: {mask_array.shape}")
        return mask_array, header
    
    except Exception as e:
        logging.error(f"读取掩膜文件时出错: {e}")
        raise

def create_delaunay_triangulation(stations):
    """
    创建Delaunay三角网
    
    参数:
    stations: 包含站点经纬度的DataFrame
    
    返回:
    Delaunay: Delaunay三角网对象
    """
    try:
        # 提取站点坐标
        points = stations[['longitude', 'latitude']].values
        
        # 创建Delaunay三角网
        tri = Delaunay(points)
        
        logging.info("成功创建Delaunay三角网")
        return tri
    
    except Exception as e:
        logging.error(f"创建Delaunay三角网时出错: {e}")
        raise

def find_neighbor_stations(station_id, stations, tri):
    """
    使用Delaunay三角网寻找指定站点的邻近站点
    
    参数:
    station_id: 目标站点ID
    stations: 包含站点经纬度的DataFrame
    tri: Delaunay三角网对象
    
    返回:
    list: 邻近站点ID列表（不包含目标站点）
    """
    try:
        # 获取站点在DataFrame中的索引
        station_index = stations.index[stations['station_id'] == station_id].tolist()
        
        if not station_index:
            raise ValueError(f"找不到站点ID: {station_id}")
        
        station_index = station_index[0]
        
        # 查找包含该站点的所有三角形并提取邻近站点的索引
        neighbor_indices = set()
        for triangle in tri.simplices:
            if station_index in triangle:
                for idx in triangle:
                    if idx != station_index:  # 确保目标站点不被包含在邻近站点中
                        neighbor_indices.add(idx)
        
        # 转换为站点ID
        neighbor_stations = stations.iloc[list(neighbor_indices)]['station_id'].tolist()
        
        # 创建表格显示指定站点和邻近站点的关系
        relationship_table = {
            "station_id": station_id,
            "neighbor_stations": neighbor_stations
        }
        
        # 如果没有找到邻近站点，使用最近的3个站点
        if not neighbor_stations:
            logging.warning(f"站点 {station_id} 没有找到通过Delaunay三角网的邻近站点，使用最近的3个站点")
            station_coords = stations[stations['station_id'] == station_id][['longitude', 'latitude']].values[0]
            other_stations = stations[stations['station_id'] != station_id]
            
            # 计算距离
            distances = np.sqrt(
                (other_stations['longitude'].values - station_coords[0])**2 +
                (other_stations['latitude'].values - station_coords[1])**2
            )
            
            # 获取最近的3个站点
            nearest_indices = np.argsort(distances)[:3]
            neighbor_stations = other_stations.iloc[nearest_indices]['station_id'].tolist()
            
            # 更新关系表
            relationship_table["neighbor_stations"] = neighbor_stations
        
        # 打印或返回关系表格
        print(f"关系表格：\n{relationship_table}")
        
        return neighbor_stations
    
    except Exception as e:
        logging.error(f"查找邻近站点时出错: {e}")
        # 出错时返回空列表，让程序继续运行
        return []
    
def calculate_covariance_matrix(stations, rainfall_data):
    """
    计算所有站点之间的降雨量协方差矩阵
    
    参数:
    stations: 包含站点经纬度的DataFrame
    rainfall_data: 降雨数据DataFrame
    
    返回:
    numpy.ndarray: 协方差矩阵
    """
    try:
        n_stations = len(stations)
        station_ids = stations['station_id'].tolist()
        
        # 创建协方差矩阵
        cov_matrix = np.zeros((n_stations, n_stations))
        
        # 计算所有站点对的协方差
        for i in range(n_stations):
            for j in range(i, n_stations):
                station1 = station_ids[i]
                station2 = station_ids[j]
                
                # 检查站点是否在降雨数据中
                if station1 not in rainfall_data.columns or station2 not in rainfall_data.columns:
                    cov_matrix[i, j] = cov_matrix[j, i] = 0
                    continue
                
                # 提取两个站点的降雨数据
                data1 = rainfall_data[station1].values
                data2 = rainfall_data[station2].values
                
                # 计算协方差
                valid_indices = ~np.isnan(data1) & ~np.isnan(data2)
                if np.sum(valid_indices) > 1:
                    cov_value = np.cov(data1[valid_indices], data2[valid_indices])[0, 1]
                else:
                    cov_value = 0
                
                cov_matrix[i, j] = cov_matrix[j, i] = cov_value
        
        # 确保协方差矩阵正定（避免数值不稳定）
        min_eig = np.min(np.linalg.eigvals(cov_matrix))
        if min_eig < 0:
            logging.warning("协方差矩阵不是正定的，添加微小的对角线元素使其正定")
            cov_matrix += np.eye(n_stations) * (abs(min_eig) + 1e-5)
        
        logging.info("成功计算站点协方差矩阵")
        return cov_matrix
    
    except Exception as e:
        logging.error(f"计算协方差矩阵时出错: {e}")
        # 出错时返回单位矩阵，让程序继续运行
        return np.eye(len(stations))

def optimal_interpolation(target_point, observed_points, observed_values, cov_matrix, stations):
    try:
        # 观测点数量
        n = len(observed_points)
        
        # 如果没有观测点，返回0
        if n == 0:
            return 0.0
        
        # 如果只有一个观测点，直接返回该观测点的值
        if n == 1:
            return observed_values[0]
        
        # 观测误差
        obs_error = 0.01  # 略微调低
        
        # 计算目标点到各观测点的距离
        distances = []
        for point in observed_points:
            dist = np.sqrt((target_point[0] - point[0])**2 + (target_point[1] - point[1])**2)
            distances.append(dist)
        distances = np.array(distances)
        
        # 基于距离的权重调整
        use_dist_weights = True  # 设置为False可禁用距离权重
        
        # 构建观测点之间的误差协方差矩阵R
        R = np.zeros((n, n))
        
        # 获取观测点在stations中的索引
        obs_indices = []
        for i, point in enumerate(observed_points):
            # 查找最接近的站点
            dists = np.sqrt((stations['longitude'].values - point[0])**2 + 
                           (stations['latitude'].values - point[1])**2)
            obs_indices.append(np.argmin(dists))
        
        # 填充误差协方差矩阵
        for i in range(n):
            for j in range(n):
                if i == j:
                    # 对角线上加入观测误差，可基于距离微调
                    if use_dist_weights and distances[i] > 0:
                        # 距离越远，观测误差略微增加
                        dist_factor = min(1.0, distances[i] / 10.0)  # 距离影响因子
                        adjusted_error = obs_error * (1 + 0.2 * dist_factor)  # 最多增加20%
                        R[i, j] = cov_matrix[obs_indices[i], obs_indices[j]] + adjusted_error
                    else:
                        R[i, j] = cov_matrix[obs_indices[i], obs_indices[j]] + obs_error
                else:
                    R[i, j] = cov_matrix[obs_indices[i], obs_indices[j]]
        
        # 构建目标点与观测点之间的协方差向量
        p = np.zeros(n)
        
        # 查找目标点最接近的站点
        target_dists = np.sqrt((stations['longitude'].values - target_point[0])**2 + 
                             (stations['latitude'].values - target_point[1])**2)
        target_index = np.argmin(target_dists)
        
        # 填充协方差向量
        for i in range(n):
            p[i] = cov_matrix[target_index, obs_indices[i]]
        
        # 计算权重
        try:
            # 添加微小的对角线元素，增强数值稳定性
            R += np.eye(n) * 1e-5
            # 求解线性方程组 R*w = p，得到权重w
            weights = np.linalg.solve(R, p)
        except np.linalg.LinAlgError:
            # 如果矩阵奇异，使用伪逆
            logging.debug("使用伪逆计算权重")
            weights = np.linalg.lstsq(R, p, rcond=None)[0]
        
        # 计算插值结果: 观测值的加权和
        result = np.dot(weights, observed_values)
        
        # 确保结果非负（降雨量不能为负）
        result = max(0.0, result)
        
        return result
    
    except Exception as e:
        logging.error(f"最优插值计算时出错: {e}")
        # 出错时返回0，让程序继续运行
        return 0.0


def leave_one_out_validation(stations, rainfall_data, tri, time_index, cov_matrix):
    try:
        results = []
        
        # 提取当前时间步的降雨数据
        current_rainfall = rainfall_data.loc[time_index]
        
        # 检查是否大部分站点降雨为零
        valid_values = current_rainfall.dropna().values
        zero_count = np.sum(valid_values == 0)
        if len(valid_values) > 0 and zero_count / len(valid_values) > 0.8:
            # 如果超过80%的站点降雨为零，采用简化策略
            logging.debug(f"时间步 {time_index} 大部分站点无降雨，采用简化验证")
            
            for i, row in stations.iterrows():
                station_id = row['station_id']
                
                # 跳过缺失数据的站点
                if station_id not in current_rainfall or np.isnan(current_rainfall[station_id]):
                    continue
                
                # 获取实际降雨量
                actual_value = current_rainfall[station_id]
                
                # 对于无降雨区域，直接使用0作为插值结果
                if zero_count / len(valid_values) > 0.9:  # 超过90%站点无降雨
                    interpolated_value = 0.0
                else:
                    # 使用常规插值
                    neighbor_stations = find_neighbor_stations(station_id, stations, tri)
                    observed_points = []
                    observed_values = []
                    
                    for neighbor_id in neighbor_stations:
                        if neighbor_id in current_rainfall and not np.isnan(current_rainfall[neighbor_id]):
                            neighbor_row = stations[stations['station_id'] == neighbor_id].iloc[0]
                            observed_points.append([neighbor_row['longitude'], neighbor_row['latitude']])
                            observed_values.append(current_rainfall[neighbor_id])
                    
                    target_point = [row['longitude'], row['latitude']]
                    interpolated_value = optimal_interpolation(target_point, observed_points, observed_values, cov_matrix, stations)
                
                # 记录结果
                results.append({
                    'station_id': station_id,
                    'actual_value': actual_value,
                    'interpolated_value': interpolated_value,
                    'error': interpolated_value - actual_value
                })
        else:
            # 常规留一法验证 (原有代码)
            for i, row in stations.iterrows():
                station_id = row['station_id']
                
                # 跳过缺失数据的站点
                if station_id not in current_rainfall or np.isnan(current_rainfall[station_id]):
                    continue
                
                # 获取实际降雨量
                actual_value = current_rainfall[station_id]
                
                # 找到邻近站点
                neighbor_stations = find_neighbor_stations(station_id, stations, tri)
                
                # 提取邻近站点的坐标和降雨量
                observed_points = []
                observed_values = []
                
                for neighbor_id in neighbor_stations:
                    if neighbor_id in current_rainfall and not np.isnan(current_rainfall[neighbor_id]):
                        neighbor_row = stations[stations['station_id'] == neighbor_id].iloc[0]
                        observed_points.append([neighbor_row['longitude'], neighbor_row['latitude']])
                        observed_values.append(current_rainfall[neighbor_id])
                
                # 使用最优插值法计算该站点的降雨量
                target_point = [row['longitude'], row['latitude']]
                interpolated_value = optimal_interpolation(target_point, observed_points, observed_values, cov_matrix, stations)
                
                # 记录结果
                results.append({
                    'station_id': station_id,
                    'actual_value': actual_value,
                    'interpolated_value': interpolated_value,
                    'error': interpolated_value - actual_value
                })
        
        # 转换为DataFrame
        validation_df = pd.DataFrame(results)
        
        # 计算评价指标
        if not validation_df.empty:
            mae = np.mean(np.abs(validation_df['error']))
            rmse = np.sqrt(np.mean(validation_df['error']**2))
            
            logging.info(f"留一法验证结果 - 时间: {time_index}, MAE: {mae:.4f}, RMSE: {rmse:.4f}")
        
        return validation_df
    
    except Exception as e:
        logging.error(f"留一法验证时出错: {e}")
        # 出错时返回空DataFrame，让程序继续运行
        return pd.DataFrame()





# 定义为全局函数，修复多进程问题
def init_worker():
    """
    初始化工作进程的函数
    """
    import signal
    signal.signal(signal.SIGINT, signal.SIG_IGN)

def process_time_step(args):
    """
    处理单个时间步的插值 - 优化版本
    
    参数:
    args: 包含处理所需所有参数的元组
    
    返回:
    dict: 包含处理结果的字典
    """
    time_index, stations, rainfall_data_slice, mask_array, header, output_dir, tri, cov_matrix = args
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 提取当前时间步的降雨数据
        current_rainfall = rainfall_data_slice.loc[time_index]
        
        # 添加调试信息
        print(f"开始处理时间步: {time_index}")
        
        # 进行留一法验证
        validation_df = leave_one_out_validation(stations, rainfall_data_slice, tri, time_index, cov_matrix)
        
         
        # 生成安全的文件名
        safe_time = safe_filename(str(time_index))
               
        # 记录结束时间
        end_time = time.time()
        
        # 计算统计指标
        if not validation_df.empty:
            mae = np.mean(np.abs(validation_df['error']))
            rmse = np.sqrt(np.mean(validation_df['error']**2))
        else:
            mae = np.nan
            rmse = np.nan
        
        # 返回结果
        result = {
            'time_index': time_index,
            'validation': validation_df,
            'mae': mae,
            'rmse': rmse,
            'processing_time': end_time - start_time
        }
        
        print(f"完成时间步 {time_index} 的处理，耗时: {end_time - start_time:.2f}秒")
        
        return result
    
    except Exception as e:
        print(f"处理时间步 {time_index} 时出错: {str(e)}")
        print(traceback.format_exc())
        return {
            'time_index': time_index,
            'error': str(e)
        }

def process_in_batches(time_indices, stations, rainfall_data, mask_array, header, output_dir, tri, cov_matrix, num_processes, batch_size):
    """
    分批处理时间步，避免内存溢出 - 修复版本
    """
    all_results = []
    num_batches = (len(time_indices) + batch_size - 1) // batch_size
    
    # 处理每个批次
    for i in range(num_batches):
        print(f"处理批次 {i+1}/{num_batches}")
        
        # 获取当前批次的时间索引
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, len(time_indices))
        batch_time_indices = time_indices[start_idx:end_idx]
        
        # 只提取需要的时间步数据，减少内存使用
        batch_rainfall_data = rainfall_data.loc[batch_time_indices].copy()
        
        # 准备参数列表
        args_list = [(time_index, stations, batch_rainfall_data, mask_array, header, output_dir, tri, cov_matrix) 
                     for time_index in batch_time_indices]
        
        # 判断是否使用并行处理
        use_parallel = num_processes > 1 and len(batch_time_indices) > 1
        
        if use_parallel:
            # 使用多进程处理
            try:
                # 使用更安全的多进程方法
                ctx = mp.get_context('spawn')  # 在Windows上使用spawn方法更可靠
                batch_results = []
                
                with ctx.Pool(processes=num_processes, initializer=init_worker) as pool:
                    # 使用map_async以避免阻塞
                    async_result = pool.map_async(process_time_step, args_list)
                    
                    # 添加进度显示
                    while not async_result.ready():
                        print(f"等待批次 {i+1}/{num_batches} 完成处理... ({len(batch_results)}/{len(batch_time_indices)})")
                        time.sleep(5)  # 每5秒更新一次状态
                    
                    # 获取处理结果
                    batch_results = async_result.get()
            
            except Exception as e:
                logging.error(f"多进程处理失败，切换到串行处理: {e}")
                print(f"多进程处理失败: {e}")
                use_parallel = False
        
        if not use_parallel:
            # 串行处理
            batch_results = []
            for args in args_list:
                result = process_time_step(args)
                batch_results.append(result)
        
        # 收集结果
        all_results.extend(batch_results)
        
        # 释放内存
        del batch_results
        del batch_rainfall_data
        del args_list
        import gc
        gc.collect()
        
        print(f"批次 {i+1}/{num_batches} 处理完成")
    
    return all_results

def parse_arguments():
    """
    解析命令行参数
    
    返回:
    argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='最优插值法(OI)空间插值程序')
    
    parser.add_argument('--input_dir', type=str, default='D:/pythondata/spatial_interpolation/input/全年/2009-1/点雨量',
                        help='输入数据目录')
    parser.add_argument('--stations_file', type=str, default='D:/pythondata/spatial_interpolation/stations.csv',
                        help='站点信息文件路径')
    parser.add_argument('--mask_file', type=str, default='D:/pythondata/spatial_interpolation/terrain/90/mask.asc',
                        help='掩膜文件路径')
    parser.add_argument('--output_dir', type=str, default='D:/pythondata/spatial_interpolation/output/OI/2009-1',
                        help='输出目录')
    parser.add_argument('--num_processes', type=int, default=20,
                        help='并行进程数，默认使用4个CPU核心')
    parser.add_argument('--batch_size', type=int, default=100,
                        help='每批处理的时间步数，用于内存管理')
    parser.add_argument('--debug', action='store_true',
                        help='调试模式，只处理少量数据')
    
    return parser.parse_args()

def main():
    """
    主函数 - 修复版本
    """
    # 解析命令行参数
    args = parse_arguments()
    
    try:
        # 创建输出目录
        os.makedirs(args.output_dir, exist_ok=True)
        
        # 配置日志
        log_file = setup_logging(args.output_dir)
        
        logging.info("开始空间插值处理")
        logging.info(f"输入目录: {args.input_dir}")
        logging.info(f"站点文件: {args.stations_file}")
        logging.info(f"掩膜文件: {args.mask_file}")
        logging.info(f"输出目录: {args.output_dir}")
        
        # 读取站点信息
        stations = read_stations(args.stations_file)
        
        # 读取降雨数据
        station_ids = stations['station_id'].tolist()
        rainfall_data = read_rainfall_data(args.input_dir, station_ids)
        
        # 读取掩膜文件
        mask_array, header = read_mask(args.mask_file)
        
        # 创建Delaunay三角网
        tri = create_delaunay_triangulation(stations)
        
        # 计算协方差矩阵
        cov_matrix = calculate_covariance_matrix(stations, rainfall_data)
        
        # 设置并行进程数
        if args.num_processes is None:
            args.num_processes = max(1, mp.cpu_count() - 2)  # 留出两个核心给系统
        
        logging.info(f"使用 {args.num_processes} 个并行进程")
        
        # 准备时间索引列表
        time_indices = rainfall_data.index.tolist()
        logging.info(f"共有 {len(time_indices)} 个时间步")
        
        # 调试模式：只处理少量数据进行测试
        if args.debug:
            logging.info("调试模式：只处理前10个时间步")
            time_indices = time_indices[:10]
        
        # 单步调试：先处理一个时间步检查程序正确性
        do_single_step = True  # 设置为False跳过单步调试
        first_result = None
        
        if do_single_step:
            logging.info("单步调试：处理第一个时间步")
            first_time_index = time_indices[0]
            first_args = (first_time_index, 
                         stations, 
                         rainfall_data.loc[[first_time_index]], 
                         mask_array, 
                         header, 
                         args.output_dir, 
                         tri, 
                         cov_matrix)
            
            first_result = process_time_step(first_args)
            logging.info(f"单步调试完成，结果: {first_result}")
            
            # 如果单步调试成功，移除已处理的时间步
            if 'error' not in first_result:
                time_indices = time_indices[1:]
            else:
                logging.error(f"单步调试失败: {first_result['error']}")
                logging.error("程序终止")
                return
        
        # 如果没有剩余时间步，直接返回
        if not time_indices:
            logging.info("没有剩余时间步需要处理")
            return
        
        # 设置小的批次大小
        args.batch_size = min(20, args.batch_size)
        logging.info(f"使用批次大小: {args.batch_size}")
        
        # 分批处理时间步
        results = process_in_batches(
            time_indices, stations, rainfall_data, mask_array, 
            header, args.output_dir, tri, cov_matrix, 
            args.num_processes, args.batch_size
        )
        
        # 如果单步调试开启并成功，添加第一个时间步的结果
        if do_single_step and first_result and 'error' not in first_result:
            results = [first_result] + results
        
        # 处理结果
        all_interpolated_values = {}
        validation_results = []
        
        for result in results:
            if 'error' in result:
                logging.error(f"时间步 {result['time_index']} 处理失败: {result['error']}")
                continue
            
            time_index = result['time_index']
            validation_df = result['validation']
            
            # 收集所有验证结果
            if not validation_df.empty:
                validation_df['time_index'] = time_index
                validation_results.append(validation_df)
            
            # 收集所有插值结果
            if not validation_df.empty:
                interpolated_values = {}
                for _, row in validation_df.iterrows():
                    interpolated_values[row['station_id']] = row['interpolated_value']
                all_interpolated_values[time_index] = interpolated_values
        
        # 创建最终输出DataFrame
        if validation_results:
            all_validation = pd.concat(validation_results, ignore_index=True)
            validation_file = os.path.join(args.output_dir, "validation_results.csv")
            all_validation.to_csv(validation_file, index=False)
            logging.info(f"验证结果已保存到: {validation_file}")
            
            # 计算总体评价指标
            mae = np.mean(np.abs(all_validation['error']))
            rmse = np.sqrt(np.mean(all_validation['error']**2))

            # 计算相关系数(CC)
            actual = all_validation['actual_value'].values
            predicted = all_validation['interpolated_value'].values
            valid_indices = ~np.isnan(actual) & ~np.isnan(predicted)
            actual = actual[valid_indices]
            predicted = predicted[valid_indices]

            cc = np.corrcoef(actual, predicted)[0, 1] if (np.std(actual) > 0 and np.std(predicted) > 0) else np.nan

            # 计算纳什系数(NSE)
            nse = 1 - np.sum((predicted - actual)**2) / np.sum((actual - np.mean(actual))**2) if np.std(actual) > 0 else np.nan

            logging.info(f"总体评价指标 - MAE: {mae:.4f}, RMSE: {rmse:.4f}, CC: {cc:.4f}, NSE: {nse:.4f}")

        
        # 创建插值结果DataFrame
        if all_interpolated_values:
            interpolated_df = pd.DataFrame.from_dict(all_interpolated_values, orient='index')
            interpolated_file = os.path.join(args.output_dir, "interpolated_rainfall.csv")
            interpolated_df.to_csv(interpolated_file)
            logging.info(f"插值结果已保存到: {interpolated_file}")
        # 创建与输入格式一致的点雨量输出文件夹
            point_output_dir = os.path.join(args.output_dir, "point_rainfall")
            os.makedirs(point_output_dir, exist_ok=True)
            logging.info(f"开始生成点雨量输出文件，保存到: {point_output_dir}")
             # 为每个站点创建CSV文件
            for station_id in stations['station_id']:
                # 检查站点是否在插值结果中
                if station_id in interpolated_df.columns:
                    # 提取该站点的所有时间步的雨量
                    station_data = interpolated_df[station_id].reset_index()
                    station_data.columns = ["时间", "雨量"]  # 重命名列以匹配输入格式
            
                    # 保存为CSV文件
                    station_file = os.path.join(point_output_dir, f"{station_id}.csv")
                    station_data.to_csv(station_file, index=False, encoding='utf-8')
                    logging.info(f"已生成站点 {station_id} 的点雨量文件")
    
            logging.info(f"点雨量输出文件生成完成，共 {len(stations)} 个站点")
        logging.info("空间插值处理完成")
        
        # 显示日志文件位置
        print(f"处理完成，日志文件保存在: {log_file}")
    
    except Exception as e:
        logging.error(f"主程序执行时出错: {e}")
        logging.error(traceback.format_exc())

if __name__ == "__main__":
    main()

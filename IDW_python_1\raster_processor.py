"""
IDW空间插值栅格处理模块

主要功能：
1. 生成插值栅格
2. 应用流域掩膜
3. 计算面雨量
4. 栅格文件输出

作者：空间插值系统
版本：1.0
"""

import os
import numpy as np
import pandas as pd
import rasterio
from rasterio.transform import from_bounds
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class RasterProcessor:
    """栅格处理器
    
    负责栅格生成、掩膜应用和面雨量计算
    """
    
    def __init__(self, mask_file: str = None, resolution: float = 0.01):
        """
        初始化栅格处理器
        
        参数:
            mask_file: 流域掩膜文件路径
            resolution: 栅格分辨率（度）
        """
        self.mask_file = mask_file
        self.resolution = resolution
        self.mask_data = None
        self.mask_transform = None
        self.mask_bounds = None
        
        # 加载掩膜文件
        if mask_file and os.path.exists(mask_file):
            self._load_mask()
    
    def _load_mask(self):
        """加载流域掩膜文件"""
        try:
            logger.info(f"加载流域掩膜: {self.mask_file}")
            
            with rasterio.open(self.mask_file) as src:
                self.mask_data = src.read(1)
                self.mask_transform = src.transform
                self.mask_bounds = src.bounds
                
                logger.info(f"掩膜尺寸: {self.mask_data.shape}")
                logger.info(f"掩膜范围: {self.mask_bounds}")
                
                # 统计有效像元数量
                valid_pixels = np.sum(self.mask_data != -9999)
                total_pixels = self.mask_data.size
                logger.info(f"有效像元: {valid_pixels}/{total_pixels} ({valid_pixels/total_pixels*100:.1f}%)")
                
        except Exception as e:
            logger.error(f"加载掩膜文件失败: {e}")
            self.mask_data = None
    
    def generate_interpolation_grid(self, interpolator, rainfall_data: Dict[str, float],
                                  bounds: Tuple[float, float, float, float] = None,
                                  output_file: str = None) -> Optional[np.ndarray]:
        """
        生成插值栅格
        
        参数:
            interpolator: IDW插值器
            rainfall_data: 降雨数据字典
            bounds: 栅格范围 (min_lon, min_lat, max_lon, max_lat)
            output_file: 输出文件路径
            
        返回:
            np.ndarray: 插值栅格数据
        """
        try:
            # 确定栅格范围
            if bounds is None:
                if self.mask_bounds:
                    bounds = self.mask_bounds
                else:
                    # 根据站点坐标确定范围
                    coords = interpolator.station_coords
                    margin = 0.1  # 10%边界
                    min_lon = coords[:, 0].min() - margin
                    max_lon = coords[:, 0].max() + margin
                    min_lat = coords[:, 1].min() - margin
                    max_lat = coords[:, 1].max() + margin
                    bounds = (min_lon, min_lat, max_lon, max_lat)
            
            min_lon, min_lat, max_lon, max_lat = bounds
            
            # 计算栅格尺寸
            width = int((max_lon - min_lon) / self.resolution)
            height = int((max_lat - min_lat) / self.resolution)
            
            logger.info(f"生成插值栅格: {width}x{height}")
            logger.info(f"栅格范围: {bounds}")
            
            # 创建坐标网格
            x_coords = np.linspace(min_lon, max_lon, width)
            y_coords = np.linspace(min_lat, max_lat, height)
            
            # 初始化栅格
            rainfall_grid = np.full((height, width), np.nan)
            
            # 对每个像元进行插值
            total_pixels = width * height
            processed_pixels = 0
            
            for i, y in enumerate(y_coords):
                for j, x in enumerate(x_coords):
                    # 检查是否在掩膜范围内
                    if self.mask_data is not None:
                        # 转换到掩膜坐标系
                        mask_col = int((x - self.mask_bounds[0]) / 
                                     (self.mask_bounds[2] - self.mask_bounds[0]) * 
                                     self.mask_data.shape[1])
                        mask_row = int((self.mask_bounds[3] - y) / 
                                     (self.mask_bounds[3] - self.mask_bounds[1]) * 
                                     self.mask_data.shape[0])
                        
                        # 检查是否在掩膜内
                        if (0 <= mask_row < self.mask_data.shape[0] and 
                            0 <= mask_col < self.mask_data.shape[1]):
                            if self.mask_data[mask_row, mask_col] == -9999:
                                continue  # 跳过掩膜外的像元
                        else:
                            continue
                    
                    # 进行插值
                    target_point = np.array([x, y])
                    result = interpolator.interpolate_point(target_point, rainfall_data)
                    
                    if not np.isnan(result['interpolated_value']):
                        rainfall_grid[i, j] = result['interpolated_value']
                    
                    processed_pixels += 1
                    
                    # 显示进度
                    if processed_pixels % 1000 == 0:
                        progress = processed_pixels / total_pixels * 100
                        logger.info(f"插值进度: {progress:.1f}%")
            
            # 保存栅格文件
            if output_file:
                self._save_raster(rainfall_grid, bounds, output_file)
            
            logger.info("插值栅格生成完成")
            return rainfall_grid
            
        except Exception as e:
            logger.error(f"生成插值栅格失败: {e}")
            return None
    
    def _save_raster(self, data: np.ndarray, bounds: Tuple[float, float, float, float],
                    output_file: str):
        """保存栅格文件"""
        try:
            min_lon, min_lat, max_lon, max_lat = bounds
            height, width = data.shape
            
            # 创建仿射变换
            transform = from_bounds(min_lon, min_lat, max_lon, max_lat, width, height)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            # 处理数据
            output_data = data.copy()
            output_data[np.isnan(output_data)] = -9999
            
            # 保存文件
            with rasterio.open(
                output_file,
                'w',
                driver='AAIGrid',
                height=height,
                width=width,
                count=1,
                dtype=output_data.dtype,
                crs='EPSG:4326',
                transform=transform,
                nodata=-9999
            ) as dst:
                dst.write(output_data, 1)
            
            logger.info(f"栅格文件已保存: {output_file}")
            
        except Exception as e:
            logger.error(f"保存栅格文件失败: {e}")
    
    def calculate_areal_rainfall(self, rainfall_grid: np.ndarray,
                               method: str = 'mean') -> Dict:
        """
        计算面雨量
        
        参数:
            rainfall_grid: 降雨栅格数据
            method: 计算方法 ('mean', 'weighted_mean')
            
        返回:
            Dict: 面雨量统计结果
        """
        try:
            if self.mask_data is None:
                logger.warning("没有掩膜数据，无法计算面雨量")
                return {}
            
            # 将栅格数据重采样到掩膜分辨率
            resampled_grid = self._resample_to_mask(rainfall_grid)
            
            if resampled_grid is None:
                return {}
            
            # 应用掩膜
            valid_mask = (self.mask_data != -9999) & (~np.isnan(resampled_grid))
            valid_rainfall = resampled_grid[valid_mask]
            
            if len(valid_rainfall) == 0:
                logger.warning("没有有效的降雨数据")
                return {}
            
            # 计算面雨量统计
            areal_stats = {
                'mean_rainfall': np.mean(valid_rainfall),
                'total_rainfall': np.sum(valid_rainfall),
                'max_rainfall': np.max(valid_rainfall),
                'min_rainfall': np.min(valid_rainfall),
                'std_rainfall': np.std(valid_rainfall),
                'valid_pixels': len(valid_rainfall),
                'total_pixels': np.sum(self.mask_data != -9999),
                'coverage_ratio': len(valid_rainfall) / np.sum(self.mask_data != -9999)
            }
            
            # 根据方法计算面雨量
            if method == 'mean':
                areal_rainfall = areal_stats['mean_rainfall']
            elif method == 'weighted_mean':
                # 简单的面积加权（假设每个像元面积相等）
                areal_rainfall = areal_stats['mean_rainfall']
            else:
                areal_rainfall = areal_stats['mean_rainfall']
            
            areal_stats['areal_rainfall'] = areal_rainfall
            
            logger.info(f"面雨量计算完成: {areal_rainfall:.2f}mm")
            logger.info(f"有效像元覆盖率: {areal_stats['coverage_ratio']*100:.1f}%")
            
            return areal_stats
            
        except Exception as e:
            logger.error(f"计算面雨量失败: {e}")
            return {}
    
    def _resample_to_mask(self, rainfall_grid: np.ndarray) -> Optional[np.ndarray]:
        """将降雨栅格重采样到掩膜分辨率"""
        try:
            if self.mask_data is None:
                return None
            
            # 简单的最近邻重采样
            mask_height, mask_width = self.mask_data.shape
            grid_height, grid_width = rainfall_grid.shape
            
            # 创建重采样后的栅格
            resampled = np.full((mask_height, mask_width), np.nan)
            
            for i in range(mask_height):
                for j in range(mask_width):
                    # 计算对应的原始栅格坐标
                    grid_i = int(i * grid_height / mask_height)
                    grid_j = int(j * grid_width / mask_width)
                    
                    # 边界检查
                    if 0 <= grid_i < grid_height and 0 <= grid_j < grid_width:
                        resampled[i, j] = rainfall_grid[grid_i, grid_j]
            
            return resampled
            
        except Exception as e:
            logger.error(f"重采样失败: {e}")
            return None
    
    def get_mask_info(self) -> Dict:
        """获取掩膜信息"""
        if self.mask_data is None:
            return {}
        
        return {
            'shape': self.mask_data.shape,
            'bounds': self.mask_bounds,
            'transform': self.mask_transform,
            'valid_pixels': np.sum(self.mask_data != -9999),
            'total_pixels': self.mask_data.size,
            'nodata_value': -9999
        }

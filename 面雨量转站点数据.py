import os 
import re 
import numpy as np 
import pandas as pd 
from datetime import datetime 
 
# 路径配置（根据实际修改）
asc_folder = 'D:/pythondata/spatial_interpolation/output/PRISM/all'
stations_file = 'D:/pythondata/spatial_interpolation/stations.csv' 
output_file = 'output.csv' 
 
# 读取站点信息 
stations = pd.read_csv(stations_file) 
station_coords = stations[['经度', '纬度']].values  # 优化为数组读取 
 
# 初始化结果DataFrame 
result = pd.DataFrame(columns=['时间'] + stations['站点'].tolist())
 
# 遍历ASC文件 
for asc_file in os.listdir(asc_folder): 
    if not asc_file.endswith('.asc'): 
        continue 
    
    # 提取时间并格式化 
    time_match = re.search(r'\d{4}-\d{2}-\d{2}_\d{2}_\d{2}',  asc_file)
    if not time_match:
        print(f"跳过文件 {asc_file}：未匹配到时间")
        continue 
    
    try:
        dt = datetime.strptime(time_match.group(),  "%Y-%m-%d_%H_%M")
        formatted_time = dt.strftime("%Y-%m-%d  %H:%M")
    except ValueError:
        print(f"时间解析失败：{asc_file}")
        continue 
    
    # 读取ASC文件（优化头部解析）
    with open(os.path.join(asc_folder,  asc_file), 'r') as f:
        header = [f.readline().strip() for _ in range(6)]  # 读取前6行头部 
        
    ncols = int(header[0].split()[1])
    nrows = int(header[1].split()[1])
    xllcorner = float(header[2].split()[1])
    yllcorner = float(header[3].split()[1])
    cellsize = float(header[4].split()[1])
    nodata = float(header[5].split()[1])
    
    # 读取数据（优化内存效率）
    data = np.loadtxt(os.path.join(asc_folder,  asc_file), skiprows=6)
    
    # 提取站点数据 
    rainfall_values = [formatted_time]
    for lon, lat in station_coords:
        col = int((lon - xllcorner) / cellsize)
        row = int((yllcorner + nrows * cellsize - lat) / cellsize )
        
        if 0 <= row < nrows and 0 <= col < ncols:
            val = data[row, col]
            rainfall_values.append(val  if val != nodata else np.nan) 
        else:
            rainfall_values.append(np.nan) 
    
    # 添加至结果 
    result.loc[len(result)]  = rainfall_values 
 
# 保存结果（按时间排序）
result.sort_values('时间').to_csv(output_file, index=False)
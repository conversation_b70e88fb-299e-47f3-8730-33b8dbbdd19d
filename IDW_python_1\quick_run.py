"""
IDW空间插值系统快速运行脚本

这是一个简化的运行脚本，新手用户可以直接运行此脚本。
脚本会自动检查环境、配置参数，并提供交互式选项。

使用方法：
python quick_run.py

作者：空间插值系统
版本：1.0
"""

import os
import sys
import subprocess
import time

def print_banner():
    """打印欢迎横幅"""
    print("="*80)
    print("IDW空间插值系统 - 快速运行脚本")
    print("基于Delaunay三角剖分的反距离权重插值")
    print("="*80)
    print()

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        return False
    else:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要的包
    required_packages = ['numpy', 'pandas', 'scipy', 'matplotlib', 'rasterio']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}: 未安装")
    
    if missing_packages:
        print(f"\n⚠️  缺少以下包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_data_files():
    """检查数据文件"""
    print("\n🔍 检查数据文件...")
    
    required_files = [
        ("../input_another", "洪水事件数据目录"),
        ("../stations.csv", "站点信息文件"),
        ("../output/Delaunay/delaunay_analysis_summary.csv", "Delaunay权重文件"),
        ("../terrain/90/mask.asc", "流域掩膜文件（可选）")
    ]
    
    all_exist = True
    for file_path, description in required_files:
        if os.path.exists(file_path):
            print(f"✅ {description}: {file_path}")
        else:
            if "可选" in description:
                print(f"⚠️  {description}: {file_path} (文件不存在，但不影响运行)")
            else:
                print(f"❌ {description}: {file_path} (文件不存在)")
                all_exist = False
    
    return all_exist

def show_config_options():
    """显示配置选项"""
    print("\n⚙️  配置选项:")
    print("1. 快速运行 (使用默认参数)")
    print("2. 自定义参数运行")
    print("3. 单事件演示")
    print("4. 查看当前配置")
    print("5. 退出")
    
    while True:
        try:
            choice = input("\n请选择运行模式 (1-5): ").strip()
            if choice in ['1', '2', '3', '4', '5']:
                return int(choice)
            else:
                print("请输入1-5之间的数字")
        except KeyboardInterrupt:
            print("\n\n用户取消操作")
            return 5

def show_current_config():
    """显示当前配置"""
    try:
        import config
        print("\n📋 当前配置:")
        print(f"IDW权重指数: {config.IDW_POWER}")
        print(f"最大站点数: {config.MAX_STATIONS}")
        print(f"搜索半径: {config.SEARCH_RADIUS}度")
        print(f"生成栅格: {'是' if config.GENERATE_RASTER else '否'}")
        print(f"并行计算: {'是' if config.USE_PARALLEL else '否'}")
        print(f"生成图表: {'是' if config.GENERATE_PLOTS else '否'}")
    except Exception as e:
        print(f"读取配置失败: {e}")

def customize_parameters():
    """自定义参数"""
    print("\n⚙️  参数自定义 (按回车使用默认值):")
    
    # 读取当前配置
    try:
        import config
        current_power = config.IDW_POWER
        current_max_stations = config.MAX_STATIONS
        current_radius = config.SEARCH_RADIUS
        current_raster = config.GENERATE_RASTER
        current_parallel = config.USE_PARALLEL
    except:
        current_power = 2.0
        current_max_stations = 10
        current_radius = 0.5
        current_raster = False
        current_parallel = True
    
    # 获取用户输入
    try:
        power_input = input(f"IDW权重指数 (当前: {current_power}): ").strip()
        power = float(power_input) if power_input else current_power
        
        stations_input = input(f"最大站点数 (当前: {current_max_stations}): ").strip()
        max_stations = int(stations_input) if stations_input else current_max_stations
        
        radius_input = input(f"搜索半径/度 (当前: {current_radius}): ").strip()
        radius = float(radius_input) if radius_input else current_radius
        
        raster_input = input(f"生成栅格 y/n (当前: {'y' if current_raster else 'n'}): ").strip().lower()
        generate_raster = raster_input == 'y' if raster_input else current_raster
        
        parallel_input = input(f"并行计算 y/n (当前: {'y' if current_parallel else 'n'}): ").strip().lower()
        use_parallel = parallel_input == 'y' if parallel_input else current_parallel
        
        # 更新配置文件
        update_config_file(power, max_stations, radius, generate_raster, use_parallel)
        print("✅ 配置已更新")
        
    except ValueError:
        print("❌ 输入格式错误，使用默认配置")
    except KeyboardInterrupt:
        print("\n用户取消配置")

def update_config_file(power, max_stations, radius, generate_raster, use_parallel):
    """更新配置文件"""
    try:
        # 读取配置文件
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新参数
        content = content.replace(f'IDW_POWER = {power}', f'IDW_POWER = {power}')
        content = content.replace(f'MAX_STATIONS = {max_stations}', f'MAX_STATIONS = {max_stations}')
        content = content.replace(f'SEARCH_RADIUS = {radius}', f'SEARCH_RADIUS = {radius}')
        content = content.replace(f'GENERATE_RASTER = {generate_raster}', f'GENERATE_RASTER = {generate_raster}')
        content = content.replace(f'USE_PARALLEL = {use_parallel}', f'USE_PARALLEL = {use_parallel}')
        
        # 写回文件
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(content)
            
    except Exception as e:
        print(f"更新配置文件失败: {e}")

def run_system(mode='batch'):
    """运行系统"""
    print(f"\n🚀 开始运行IDW插值系统 ({mode}模式)...")
    
    start_time = time.time()
    
    try:
        if mode == 'demo':
            result = subprocess.run([sys.executable, 'main.py', '--demo'], 
                                  capture_output=True, text=True, encoding='utf-8')
        else:
            result = subprocess.run([sys.executable, 'main.py'], 
                                  capture_output=True, text=True, encoding='utf-8')
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ 系统运行成功！耗时: {duration:.1f}秒")
            print("\n📊 运行结果:")
            # 显示最后几行输出
            output_lines = result.stdout.split('\n')
            for line in output_lines[-10:]:
                if line.strip():
                    print(line)
        else:
            print(f"❌ 系统运行失败！")
            print("错误信息:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 运行出错: {e}")

def generate_plots():
    """生成汇总图表"""
    print("\n📈 生成汇总图表...")
    
    try:
        result = subprocess.run([sys.executable, 'generate_summary_plots.py'], 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 汇总图表生成成功！")
            print("图表保存位置: ../output/IDW/plots/")
        else:
            print("❌ 图表生成失败")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 生成图表出错: {e}")

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return
    
    # 检查数据文件
    if not check_data_files():
        print("\n❌ 数据文件检查失败，请确保必要文件存在")
        return
    
    print("\n✅ 环境和数据检查通过！")
    
    # 主循环
    while True:
        choice = show_config_options()
        
        if choice == 1:
            # 快速运行
            run_system('batch')
            generate_plots()
            break
            
        elif choice == 2:
            # 自定义参数运行
            customize_parameters()
            run_system('batch')
            generate_plots()
            break
            
        elif choice == 3:
            # 单事件演示
            run_system('demo')
            break
            
        elif choice == 4:
            # 查看当前配置
            show_current_config()
            continue
            
        elif choice == 5:
            # 退出
            print("👋 再见！")
            break
    
    print("\n🎉 感谢使用IDW空间插值系统！")
    print("结果文件保存在: ../output/IDW/")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断程序，再见！")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        print("请检查环境配置或联系技术支持")

import rawpy
import imageio


def cr2_to_jpg(cr2_path, jpg_path):
    try:
        # 打开 CR2 文件
        with rawpy.imread(cr2_path) as raw:
            # 处理 RAW 图像，不进行降采样
            rgb = raw.postprocess()

        # 保存为 JPG 文件
        imageio.imsave(jpg_path, rgb)
        print(f"成功将 {cr2_path} 转换为 {jpg_path}")
    except Exception as e:
        print(f"转换过程中出现错误: {e}")


if __name__ == "__main__":
    # 请替换为实际的 CR2 文件路径
    cr2_file = 'E:\\DCIM\\100CANON\\IMG_7307.CR2'
    # 请替换为你想要保存的 JPG 文件路径
    jpg_file = 'C:\\Users\\<USER>\\Desktop\\example.jpg'
    cr2_to_jpg(cr2_file, jpg_file)
    
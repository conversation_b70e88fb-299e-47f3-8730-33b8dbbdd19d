"""
运行所有洪水批次并优化表现较差的事件
"""

import os
import sys
import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from config import Config
from batch_processing import BatchProcessor

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("prism_batch_optimization.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def run_all_floods_with_optimization(nse_threshold: float = 0.5,
                                   enable_optimization: bool = True):
    """运行所有洪水批次并优化表现较差的事件
    
    Args:
        nse_threshold: NSE阈值，低于此值的事件将被优化
        enable_optimization: 是否启用参数优化
    """
    try:
        print("="*80)
        print("PRISM空间插值系统 - 批量处理与优化")
        print("="*80)
        
        # 创建批量处理配置
        config = Config(
            enable_batch_processing=True,
            batch_input_root="D:/pythondata/spatial_interpolation/input_another",
            batch_output_root="D:/pythondata/spatial_interpolation/output/PRISM",
            num_cores=12,
            memory_efficient=True,
            output_raster=False,  # 批量处理时关闭栅格输出以节省时间
            output_delaunay_plot=True,
            output_evaluation=True,
            verbose_logging=True
        )
        
        print(f"配置信息:")
        print(f"  输入根目录: {config.batch_input_root}")
        print(f"  输出根目录: {config.batch_output_root}")
        print(f"  并行核心数: {config.num_cores}")
        print(f"  NSE优化阈值: {nse_threshold}")
        print(f"  启用参数优化: {enable_optimization}")
        print("-"*80)
        
        # 第一阶段：批量处理所有洪水事件
        print("第一阶段：批量处理所有洪水事件")
        print("-"*40)
        
        batch_processor = BatchProcessor(config)
        batch_results = batch_processor.run_batch_processing()
        
        if not batch_results:
            print("❌ 批量处理失败，程序退出")
            return
        
        print(f"✅ 批量处理完成，成功处理 {len(batch_results)} 个事件")
        
        # 分析批量处理结果
        print("\n批量处理结果分析:")
        print("-"*40)
        
        successful_events = []
        failed_events = []
        poor_performance_events = []
        
        for event_name, result in batch_results.items():
            if isinstance(result, dict):
                if result.get('status') == 'success':
                    successful_events.append(event_name)
                    nse = result.get('overall_nse', 0)
                    if nse < nse_threshold:
                        poor_performance_events.append((event_name, nse))
                elif result.get('status') == 'failed':
                    failed_events.append(event_name)
        
        print(f"  成功处理事件: {len(successful_events)}")
        print(f"  处理失败事件: {len(failed_events)}")
        print(f"  表现较差事件 (NSE < {nse_threshold}): {len(poor_performance_events)}")
        
        if failed_events:
            print(f"  失败事件列表: {', '.join(failed_events[:10])}")
            if len(failed_events) > 10:
                print(f"    ... 还有 {len(failed_events) - 10} 个")
        
        if poor_performance_events:
            print(f"  表现较差事件:")
            for event_name, nse in sorted(poor_performance_events, key=lambda x: x[1])[:10]:
                print(f"    {event_name}: NSE = {nse:.4f}")
            if len(poor_performance_events) > 10:
                print(f"    ... 还有 {len(poor_performance_events) - 10} 个")
        
        # 第二阶段：参数优化
        if enable_optimization and poor_performance_events:
            print(f"\n第二阶段：参数优化 (优化 {len(poor_performance_events)} 个表现较差的事件)")
            print("-"*40)
            
            optimization_results = optimize_poor_events(poor_performance_events, config)
            
            if optimization_results:
                print(f"✅ 参数优化完成，优化了 {len(optimization_results)} 个事件")
                
                # 分析优化效果
                improved_count = 0
                total_improvement = 0
                
                for event_name, opt_result in optimization_results.items():
                    if 'improvement' in opt_result and opt_result['improvement'] > 0:
                        improved_count += 1
                        total_improvement += opt_result['improvement']
                
                if improved_count > 0:
                    avg_improvement = total_improvement / improved_count
                    print(f"  成功改善事件: {improved_count}")
                    print(f"  平均NSE提升: {avg_improvement:.4f}")
                
                # 保存优化结果汇总
                save_optimization_summary(optimization_results, config.batch_output_root)
            else:
                print("❌ 参数优化失败")
        elif not enable_optimization:
            print("\n跳过参数优化阶段（已禁用）")
        else:
            print(f"\n无需参数优化（所有事件NSE >= {nse_threshold}）")
        
        # 生成最终报告
        print(f"\n生成最终报告")
        print("-"*40)
        
        generate_final_report(batch_results, 
                            optimization_results if enable_optimization else {},
                            config.batch_output_root,
                            nse_threshold)
        
        print("="*80)
        print("批量处理与优化完成！")
        print(f"详细结果请查看: {config.batch_output_root}")
        print("="*80)
        
    except Exception as e:
        logger.error(f"批量处理与优化过程中出错: {e}")
        print(f"❌ 程序执行失败: {e}")


def save_optimization_summary(optimization_results: Dict, output_root: str):
    """保存优化结果汇总"""
    try:
        summary_dir = os.path.join(output_root, "optimization_summary")
        os.makedirs(summary_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建优化汇总DataFrame
        summary_data = []
        for event_name, result in optimization_results.items():
            summary_data.append({
                'Event': event_name,
                'Original_NSE': result.get('original_nse', 0),
                'Optimized_NSE': result.get('optimized_nse', 0),
                'Improvement': result.get('improvement', 0),
                'Optimization_Success': not result.get('optimization_failed', False)
            })
        
        summary_df = pd.DataFrame(summary_data)
        
        # 保存CSV文件
        csv_file = os.path.join(summary_dir, f"optimization_summary_{timestamp}.csv")
        summary_df.to_csv(csv_file, index=False, encoding='utf-8')
        
        # 保存JSON文件
        json_file = os.path.join(summary_dir, f"optimization_results_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(optimization_results, f, indent=4, ensure_ascii=False)
        
        logger.info(f"优化汇总已保存到: {summary_dir}")
        
    except Exception as e:
        logger.error(f"保存优化汇总失败: {e}")


def generate_final_report(batch_results: Dict, optimization_results: Dict,
                         output_root: str, nse_threshold: float):
    """生成最终报告"""
    try:
        report_dir = os.path.join(output_root, "final_report")
        os.makedirs(report_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(report_dir, f"final_report_{timestamp}.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("PRISM空间插值系统 - 批量处理与优化最终报告\n")
            f.write("="*60 + "\n\n")
            
            # 基本统计
            total_events = len(batch_results)
            successful_events = sum(1 for r in batch_results.values() 
                                  if isinstance(r, dict) and r.get('status') == 'success')
            failed_events = total_events - successful_events
            
            f.write(f"处理统计:\n")
            f.write(f"  总事件数: {total_events}\n")
            f.write(f"  成功处理: {successful_events}\n")
            f.write(f"  处理失败: {failed_events}\n")
            f.write(f"  成功率: {successful_events/total_events*100:.1f}%\n\n")
            
            # NSE分布统计
            nse_values = []
            for result in batch_results.values():
                if isinstance(result, dict) and 'overall_nse' in result:
                    nse_values.append(result['overall_nse'])
            
            if nse_values:
                f.write(f"NSE指标统计:\n")
                f.write(f"  平均NSE: {np.mean(nse_values):.4f}\n")
                f.write(f"  最大NSE: {np.max(nse_values):.4f}\n")
                f.write(f"  最小NSE: {np.min(nse_values):.4f}\n")
                f.write(f"  标准差: {np.std(nse_values):.4f}\n")
                
                excellent_count = sum(1 for nse in nse_values if nse > 0.75)
                good_count = sum(1 for nse in nse_values if 0.5 <= nse <= 0.75)
                poor_count = sum(1 for nse in nse_values if nse < 0.5)
                
                f.write(f"  优秀 (NSE > 0.75): {excellent_count} ({excellent_count/len(nse_values)*100:.1f}%)\n")
                f.write(f"  良好 (0.5 ≤ NSE ≤ 0.75): {good_count} ({good_count/len(nse_values)*100:.1f}%)\n")
                f.write(f"  较差 (NSE < 0.5): {poor_count} ({poor_count/len(nse_values)*100:.1f}%)\n\n")
            
            # 优化结果统计
            if optimization_results:
                f.write(f"参数优化结果:\n")
                f.write(f"  优化事件数: {len(optimization_results)}\n")
                
                improved_events = [r for r in optimization_results.values() 
                                 if 'improvement' in r and r['improvement'] > 0]
                
                if improved_events:
                    improvements = [r['improvement'] for r in improved_events]
                    f.write(f"  成功改善事件: {len(improved_events)}\n")
                    f.write(f"  平均NSE提升: {np.mean(improvements):.4f}\n")
                    f.write(f"  最大NSE提升: {np.max(improvements):.4f}\n")
                    f.write(f"  最小NSE提升: {np.min(improvements):.4f}\n")
                
                f.write(f"\n")
            
            # 详细事件列表
            f.write("详细事件结果:\n")
            f.write("-"*40 + "\n")
            
            for event_name, result in sorted(batch_results.items()):
                if isinstance(result, dict) and result.get('status') == 'success':
                    nse = result.get('overall_nse', 0)
                    rmse = result.get('overall_rmse', 0)
                    
                    status = "优秀" if nse > 0.75 else "良好" if nse >= 0.5 else "较差"
                    
                    f.write(f"{event_name}: NSE={nse:.4f}, RMSE={rmse:.2f}mm ({status})")
                    
                    # 添加优化信息
                    if event_name in optimization_results:
                        opt_result = optimization_results[event_name]
                        if 'improvement' in opt_result:
                            improvement = opt_result['improvement']
                            f.write(f" → 优化后NSE={opt_result['optimized_nse']:.4f} (+{improvement:.4f})")
                    
                    f.write("\n")
                else:
                    f.write(f"{event_name}: 处理失败\n")
        
        logger.info(f"最终报告已保存到: {report_file}")
        
    except Exception as e:
        logger.error(f"生成最终报告失败: {e}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='PRISM批量处理与优化')
    parser.add_argument('--nse-threshold', type=float, default=0.5,
                       help='NSE优化阈值 (默认: 0.5)')
    parser.add_argument('--no-optimization', action='store_true',
                       help='禁用参数优化')
    parser.add_argument('--quick-test', action='store_true',
                       help='快速测试模式（只处理前5个事件）')
    
    args = parser.parse_args()
    
    # 运行批量处理与优化
    run_all_floods_with_optimization(
        nse_threshold=args.nse_threshold,
        enable_optimization=not args.no_optimization
    )


if __name__ == "__main__":
    main()

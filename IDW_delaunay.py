# -*- coding: utf-8 -*-
"""
基于Delaunay三角网和IDW方法的水文点雨量转面雨量空间插值（内存优化版）
"""

import os
import time
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay
import rasterio
from rasterio.transform import from_origin
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
import multiprocessing as mp
from datetime import datetime
import warnings
import math
import re
from tqdm import tqdm
import matplotlib.colors as mcolors
import gc  # 用于垃圾回收

# 忽略警告
warnings.filterwarnings('ignore')

# 设置工作路径
base_path = 'D:/pythondata/spatial_interpolation/'
input_path = os.path.join(base_path, 'input/2017-2/点雨量')
terrain_path = os.path.join(base_path, 'terrain')
output_path = os.path.join(base_path, 'output/IDW/2017-2')
stations_file = os.path.join(base_path, 'stations.csv')

# 创建输出目录（如果不存在）
os.makedirs(output_path, exist_ok=True)

# 定义一个函数用于安全地创建文件名（移除特殊字符）
def clean_filename(filename):
    """
    清理文件名，移除非法字符
    
    参数:
        filename: 原始文件名
    
    返回:
        清理后的文件名
    """
    # 替换冒号和空格
    filename = filename.replace(':', '_').replace(' ', '_')
    # 移除其他不允许的字符
    filename = re.sub(r'[\\/*?:"<>|]', '', filename)
    return filename

# 加载站点信息
def load_stations():
    """
    加载站点信息数据
    
    返回:
        包含站点ID、经纬度信息的DataFrame
    """
    try:
        # 读取站点信息文件
        stations_df = pd.read_csv(stations_file, encoding='utf-8')
        print(f"站点信息表头: {stations_df.columns.tolist()}")
        
        # 检查必要列是否存在
        required_columns = ['站点', '经度', '纬度']
        for col in required_columns:
            if col not in stations_df.columns:
                raise ValueError(f"站点信息缺少必要列：{col}")
        
        print(f"成功加载{len(stations_df)}个站点信息")
        return stations_df
    except Exception as e:
        print(f"加载站点信息时出错：{e}")
        raise

# 从DEM中提取站点高程信息
def extract_elevation_from_dem(stations_df, dem_file=None):
    """
    从DEM文件中提取站点的高程信息
    
    参数:
        stations_df: 站点信息DataFrame
        dem_file: DEM文件路径，如果为None则自动查找
    
    返回:
        添加了高程信息的站点DataFrame
    """
    # 如果未指定DEM文件，则在terrain目录下寻找
    if dem_file is None:
        dem_files = [f for f in os.listdir(terrain_path) if f.lower().endswith('.asc') and 'dem' in f.lower()]
        if dem_files:
            dem_file = os.path.join(terrain_path, dem_files[0])
        else:
            print("警告：未找到DEM文件，将无法提取高程信息")
            stations_df['高程'] = 0
            return stations_df
    
    try:
        # 打开DEM文件
        with rasterio.open(dem_file) as dem:
            # 获取DEM的坐标系统和变换
            transform = dem.transform
            
            # 为每个站点提取高程
            elevations = []
            for idx, station in stations_df.iterrows():
                # 将经纬度转换为像素坐标
                x, y = station['经度'], station['纬度']
                row, col = ~transform * (x, y)
                
                # 确保坐标在栅格范围内
                if 0 <= row < dem.height and 0 <= col < dem.width:
                    # 读取该位置的高程值
                    elevation = dem.read(1)[int(row), int(col)]
                    elevations.append(elevation)
                else:
                    print(f"警告：站点{station['站点']}的坐标({x}, {y})超出DEM范围")
                    elevations.append(np.nan)
            
            # 将高程添加到站点信息中
            stations_df['高程'] = elevations
            
            # 处理可能的NaN值
            if stations_df['高程'].isna().any():
                print(f"警告：有{stations_df['高程'].isna().sum()}个站点无法获取高程信息，使用平均高程代替")
                stations_df['高程'].fillna(stations_df['高程'].mean(), inplace=True)
            
            return stations_df
    except Exception as e:
        print(f"从DEM提取高程时出错：{e}")
        stations_df['高程'] = 0
        return stations_df

# 加载雨量站点数据
def load_rainfall_data(station_ids):
    """
    加载所有站点的雨量数据
    
    参数:
        station_ids: 站点ID列表
    
    返回:
        包含所有站点雨量数据的字典，格式为{station_id: dataframe}
    """
    rainfall_data = {}
    
    for station_id in station_ids:
        try:
            file_path = os.path.join(input_path, f"{station_id}.csv")
            if os.path.exists(file_path):
                # 读取雨量数据文件
                df = pd.read_csv(file_path, encoding='utf-8')
                
                # 检查必要列是否存在
                if '时间' not in df.columns or '雨量' not in df.columns:
                    print(f"警告：站点{station_id}的数据缺少必要列（时间或雨量）")
                    continue
                
                # 确保时间列的格式正确
                try:
                    df['时间'] = pd.to_datetime(df['时间'])
                except:
                    print(f"警告：站点{station_id}的时间列格式不正确，尝试其他解析方式")
                    # 尝试常见的时间格式
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S', 
                                '%Y-%m-%d %H:%M', '%Y/%m/%d %H:%M']:
                        try:
                            df['时间'] = pd.to_datetime(df['时间'], format=fmt)
                            break
                        except:
                            continue
                
                # 确保雨量列是数值类型
                df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce')
                
                # 处理缺失值
                df.dropna(subset=['雨量'], inplace=True)
                
                rainfall_data[station_id] = df
                print(f"成功加载站点{station_id}的雨量数据，共{len(df)}条记录")
            else:
                print(f"警告：未找到站点{station_id}的雨量数据文件")
        except Exception as e:
            print(f"加载站点{station_id}的雨量数据时出错：{e}")
    
    return rainfall_data

# 构建Delaunay三角网
def build_delaunay_triangulation(stations_df):
    """
    基于站点坐标构建Delaunay三角网
    
    参数:
        stations_df: 包含站点坐标的DataFrame
    
    返回:
        Delaunay三角网对象
    """
    # 提取站点坐标
    points = stations_df[['经度', '纬度']].values
    
    # 构建Delaunay三角网
    try:
        tri = Delaunay(points)
        print(f"成功构建Delaunay三角网，包含{len(tri.simplices)}个三角形")
        return tri
    except Exception as e:
        print(f"构建Delaunay三角网时出错：{e}")
        raise

# 可视化所有Delaunay三角网（单张图）
def visualize_all_delaunay_triangulations(stations_df, output_file=None):
    """
    可视化所有站点的Delaunay三角网，每个三角网包含3个顶点和1个验证点
    
    参数:
        stations_df: 站点信息DataFrame
        output_file: 输出文件路径
    """
    plt.figure(figsize=(15, 12))
    
    # 生成随机颜色
    colors = list(mcolors.TABLEAU_COLORS.values())
    
    # 保存三角网信息的列表，用于后续生成表格
    triangulation_info = []
    
    # 为每个站点创建验证三角网
    for i, test_station in stations_df.iterrows():
        test_station_id = test_station['站点']
        test_point = [test_station['经度'], test_station['纬度']]
        
        # 构建不包含测试站点的临时站点集
        temp_stations = stations_df[stations_df['站点'] != test_station_id].copy()
        temp_points = temp_stations[['经度', '纬度']].values
        
        try:
            # 构建Delaunay三角网
            temp_tri = Delaunay(temp_points)
            
            # 找到包含测试点的三角形或最近的三个点
            # 首先尝试找到包含该点的三角形
            simplex_idx = temp_tri.find_simplex(test_point)
            
            if simplex_idx >= 0:
                # 找到包含测试点的三角形
                vertices_idx = temp_tri.simplices[simplex_idx]
                neighbor_points = temp_points[vertices_idx]
                neighbor_stations = temp_stations.iloc[vertices_idx]
            else:
                # 找不到包含测试点的三角形，使用最近的三个点
                dists = np.sqrt(np.sum((temp_points - test_point)**2, axis=1))
                nearest_idx = np.argsort(dists)[:3]
                neighbor_points = temp_points[nearest_idx]
                neighbor_stations = temp_stations.iloc[nearest_idx]
            
            # 随机选择一种颜色
            color = colors[i % len(colors)]
            
            # 绘制三角形
            triangle = plt.Polygon(neighbor_points, fill=True, alpha=0.2, color=color)
            plt.gca().add_patch(triangle)
            
            # 连接测试点与三个顶点
            for j in range(3):
                plt.plot([test_point[0], neighbor_points[j, 0]], 
                         [test_point[1], neighbor_points[j, 1]], 
                         '--', color=color, alpha=0.7)
            
            # 计算IDW权重（使用修复后的IDW函数）
            distances = np.sqrt(np.sum((neighbor_points - test_point)**2, axis=1))
            
            # 处理距离为0的情况
            if np.any(distances == 0):
                weights = np.zeros_like(distances)
                weights[distances == 0] = 1.0
            else:
                # 使用IDW计算权重
                weights = 1.0 / (distances ** 2)  # 使用平方反比
                # 归一化权重
                weights = weights / np.sum(weights)
            
            # 保存三角网信息
            for j in range(3):
                triangulation_info.append({
                    '验证站点': test_station_id,
                    '顶点站点': neighbor_stations.iloc[j]['站点'],
                    '权重': weights[j],
                    '经度': neighbor_points[j, 0],
                    '纬度': neighbor_points[j, 1]
                })
            
            # 绘制测试站点
            plt.scatter(test_point[0], test_point[1], c='red', s=80, 
                        edgecolor='black', alpha=0.7)
            
            # 标注测试站点
            plt.text(test_point[0], test_point[1] + 0.01, 
                     f"{test_station_id}", ha='center', va='bottom', fontsize=8)
            
            # 绘制顶点站点
            for j in range(3):
                plt.scatter(neighbor_points[j, 0], neighbor_points[j, 1], 
                            c='blue', s=60, edgecolor='black', alpha=0.7)
                
                # 标注顶点站点
                plt.text(neighbor_points[j, 0], neighbor_points[j, 1] + 0.01, 
                         f"{neighbor_stations.iloc[j]['站点']}", 
                         ha='center', va='bottom', fontsize=8)
        
        except Exception as e:
            print(f"为站点{test_station_id}创建验证三角网时出错: {e}")
    
    # 设置图表标题和标签
    plt.title('所有站点的Delaunay验证三角网')
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 创建图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='red', edgecolor='black', alpha=0.7, label='验证站点'),
        Patch(facecolor='blue', edgecolor='black', alpha=0.7, label='顶点站点')
    ]
    plt.legend(handles=legend_elements, loc='best')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"所有验证三角网已保存至: {output_file}")
    
    plt.close()
    
    # 将三角网信息保存为表格
    triangulation_df = pd.DataFrame(triangulation_info)
    return triangulation_df

# 找到包含指定点的三角形
def find_containing_triangle(point, tri, points):
    """
    找到包含指定点的三角形
    
    参数:
        point: 待查找的点坐标 [x, y]
        tri: Delaunay三角网对象
        points: 三角网中的所有点坐标
    
    返回:
        包含该点的三角形的三个顶点索引，如果没有找到则返回最近的三个点
    """
    try:
        # 尝试找到包含该点的三角形
        simplex_idx = tri.find_simplex(point)
        
        if simplex_idx >= 0:
            # 如果找到了包含该点的三角形，返回三角形的顶点索引
            return tri.simplices[simplex_idx]
        else:
            # 如果没有找到包含该点的三角形，找到最近的三个点
            dists = np.sqrt(np.sum((points - point)**2, axis=1))
            nearest_indices = np.argsort(dists)[:3]
            return nearest_indices
    except Exception as e:
        print(f"查找包含点{point}的三角形时出错：{e}")
        # 找到最近的三个点
        dists = np.sqrt(np.sum((points - point)**2, axis=1))
        nearest_indices = np.argsort(dists)[:3]
        return nearest_indices

# 计算IDW权重（修复版）
def calculate_idw_weights(target_point, neighbors_points, p=2):
    """
    计算IDW(反距离加权)插值的权重（修复版）
    
    参数:
        target_point: 目标点坐标 [x, y]
        neighbors_points: 邻居点坐标数组
        p: 距离衰减的幂次，默认为2
    
    返回:
        各邻居点的权重数组
    """
    # 确保输入是numpy数组
    target_point = np.array(target_point)
    neighbors_points = np.array(neighbors_points)
    
    # 计算目标点到各邻居点的距离
    distances = np.sqrt(np.sum((neighbors_points - target_point)**2, axis=1))
    
    # 处理距离为0的情况（当目标点与某个邻居点重合时）
    if np.any(distances < 1e-10):
        weights = np.zeros_like(distances)
        weights[distances < 1e-10] = 1.0
        return weights
    
    # 计算权重：距离的负p次方
    weights = 1.0 / (distances ** p)
    
    # 归一化权重，使其总和为1
    weights = weights / np.sum(weights)
    
    return weights

# 计算莫兰指数权重（简化版）
def calculate_moran_weights(target_point, neighbors_points, neighbors_values):
    """
    计算基于莫兰指数的空间权重（简化版）
    
    参数:
        target_point: 目标点坐标 [x, y]
        neighbors_points: 邻居点坐标数组
        neighbors_values: 邻居点的降雨量值
    
    返回:
        莫兰指数调整后的权重
    """
    # 首先计算IDW权重作为基础
    idw_weights = calculate_idw_weights(target_point, neighbors_points)
    
    # 如果邻居点数据不足，直接返回IDW权重
    if len(neighbors_values) < 3:
        return idw_weights
    
    try:
        # 计算邻居点降雨量的均值
        mean_value = np.mean(neighbors_values)
        
        # 计算邻居点降雨量的方差
        var_value = np.var(neighbors_values)
        
        # 如果方差接近0，直接返回IDW权重
        if var_value < 1e-10:
            return idw_weights
        
        # 计算空间权重矩阵（简化版）
        n = len(neighbors_points)
        W = np.zeros((n, n))
        for i in range(n):
            for j in range(n):
                if i != j:
                    # 使用简单的距离倒数作为空间权重
                    dist = np.sqrt(np.sum((neighbors_points[i] - neighbors_points[j])**2))
                    W[i, j] = 1.0 / (dist + 1e-10)  # 添加小量避免除零
        
        # 标准化空间权重矩阵
        W_sum = W.sum(axis=1)
        W = W / W_sum[:, np.newaxis]
        
        # 计算莫兰指数
        z = neighbors_values - mean_value
        moran_i = np.sum(np.outer(z, z) * W) / np.sum(z**2)
        
        # 调整权重：如果莫兰指数为正，表示高度相关，增强IDW权重；反之减弱
        adjusted_weights = idw_weights * (1 + 0.2 * moran_i)  # 使用较小的调整因子
        
        # 再次归一化
        adjusted_weights = adjusted_weights / np.sum(adjusted_weights)
        
        return adjusted_weights
    except Exception as e:
        print(f"计算莫兰指数权重时出错：{e}，使用标准IDW权重")
        return idw_weights

# 留一法验证函数
def leave_one_out_validation(timestamp, stations_df, rainfall_data, tri):
    """
    使用留一法对特定时间戳的降雨数据进行插值和验证
    
    参数:
        timestamp: 时间戳
        stations_df: 站点信息DataFrame
        rainfall_data: 站点雨量数据字典
        tri: Delaunay三角网对象
    
    返回:
        包含真实值和预测值的DataFrame
    """
    results = []
    points = stations_df[['经度', '纬度']].values
    
    # 获取此时间戳下有降雨数据的站点
    available_stations = {}
    for station_id, df in rainfall_data.items():
        if timestamp in df['时间'].values:
            rain_value = df.loc[df['时间'] == timestamp, '雨量'].values[0]
            if not np.isnan(rain_value):
                available_stations[station_id] = rain_value
    
    # 如果可用站点不足，返回空结果
    if len(available_stations) < 4:  # 至少需要4个站点才能进行留一法
        print(f"时间戳{timestamp}下的可用站点不足4个，跳过")
        return pd.DataFrame()
    
    # 对每个有数据的站点进行留一法验证
    for test_idx, test_station_id in enumerate(available_stations.keys()):
        test_station_info = stations_df[stations_df['站点'] == test_station_id]
        if test_station_info.empty:
            print(f"警告：找不到站点{test_station_id}的信息，跳过")
            continue
            
        test_station_info = test_station_info.iloc[0]
        test_point = [test_station_info['经度'], test_station_info['纬度']]
        
        # 构建不包含测试站点的临时三角网
        temp_stations = stations_df[stations_df['站点'] != test_station_id].copy()
        temp_points = temp_stations[['经度', '纬度']].values
        
        try:
            temp_tri = Delaunay(temp_points)
            
            # 找到测试点所在的三角形或最近的三个点
            neighbors_indices = find_containing_triangle(test_point, temp_tri, temp_points)
            
            # 获取邻居站点的ID
            neighbor_stations = []
            for idx in neighbors_indices:
                neighbor_station_id = temp_stations.iloc[idx]['站点']
                if neighbor_station_id in available_stations:
                    neighbor_stations.append(neighbor_station_id)
            
            # 确保至少有3个邻居站点
            while len(neighbor_stations) < 3 and len(available_stations) > len(neighbor_stations) + 1:
                # 寻找下一个最近的站点
                distances = []
                for station_id in available_stations.keys():
                    if station_id != test_station_id and station_id not in neighbor_stations:
                        station_info = stations_df[stations_df['站点'] == station_id]
                        if not station_info.empty:
                            station_info = station_info.iloc[0]
                            station_point = [station_info['经度'], station_info['纬度']]
                            distance = np.sqrt(np.sum((np.array(test_point) - np.array(station_point))**2))
                            distances.append((station_id, distance))
                
                if distances:
                    # 选择最近的站点
                    next_nearest = min(distances, key=lambda x: x[1])[0]
                    neighbor_stations.append(next_nearest)
                else:
                    break
            
            # 如果邻居站点不足3个，跳过该测试站点
            if len(neighbor_stations) < 3:
                print(f"时间戳{timestamp}下，站点{test_station_id}的可用邻居站点不足3个，跳过")
                continue
            
            # 获取邻居站点的坐标和降雨量
            neighbors_points = []
            neighbors_values = []
            for station_id in neighbor_stations:
                station_info = stations_df[stations_df['站点'] == station_id].iloc[0]
                neighbors_points.append([station_info['经度'], station_info['纬度']])
                neighbors_values.append(available_stations[station_id])
            
            neighbors_points = np.array(neighbors_points)
            neighbors_values = np.array(neighbors_values)
            
            # 计算IDW权重（使用修复版函数）
            weights = calculate_idw_weights(test_point, neighbors_points)
            
            # 使用权重进行插值预测
            predicted_value = np.sum(weights * neighbors_values)
            
            # 获取真实值
            true_value = available_stations[test_station_id]
            
            # 保存结果
            results.append({
                '时间': timestamp,
                '站点': test_station_id,
                '真实值': true_value,
                '预测值': predicted_value,
                '邻居站点': ','.join(neighbor_stations),
                '权重': ','.join([f"{w:.4f}" for w in weights])
            })
            
        except Exception as e:
            print(f"站点{test_station_id}在时间戳{timestamp}的留一法验证时出错：{e}")
    
    return pd.DataFrame(results)

# 计算评价指标
def calculate_metrics(validation_results):
    """
    计算插值结果的评价指标
    
    参数:
        validation_results: 包含真实值和预测值的DataFrame
    
    返回:
        包含各种评价指标的字典
    """
    if validation_results.empty:
        return {
            'MAE': np.nan,
            'RMSE': np.nan,
            'R2': np.nan,
            'NSE': np.nan
        }
    
    true_values = validation_results['真实值'].values
    predicted_values = validation_results['预测值'].values
    
    # 平均绝对误差
    mae = mean_absolute_error(true_values, predicted_values)
    
    # 均方根误差
    rmse = np.sqrt(mean_squared_error(true_values, predicted_values))
    
    # 决定系数R²
    r2 = r2_score(true_values, predicted_values)
    
    # 纳什系数
    mean_true = np.mean(true_values)
    numerator = np.sum((true_values - predicted_values) ** 2)
    denominator = np.sum((true_values - mean_true) ** 2)
    nse = 1 - (numerator / denominator) if denominator != 0 else np.nan
    
    return {
        'MAE': mae,
        'RMSE': rmse,
        'R2': r2,
        'NSE': nse
    }

# 根据留一法结果生成每个站点的完整降雨量时间序列
def generate_complete_timeseries(all_validation_results, rainfall_data):
    """
    根据留一法验证结果，生成每个站点的完整降雨量时间序列
    
    参数:
        all_validation_results: 所有时间戳的留一法验证结果
        rainfall_data: 原始雨量数据字典
    
    返回:
        包含完整时间序列的字典，格式为{station_id: dataframe}
    """
    # 获取所有站点
    all_stations = set()
    for df in all_validation_results.values():
        if not df.empty:
            all_stations.update(df['站点'].unique())
    
    # 获取所有时间戳
    all_timestamps = set()
    for df in rainfall_data.values():
        all_timestamps.update(df['时间'].unique())
    all_timestamps = sorted(all_timestamps)
    
    # 为每个站点创建完整的时间序列
    complete_series = {}
    
    for station_id in all_stations:
        # 创建一个包含所有时间戳的DataFrame
        station_data = pd.DataFrame({'时间': all_timestamps})
        station_data['雨量'] = np.nan
        
        # 首先填入原始数据
        if station_id in rainfall_data:
            original_data = rainfall_data[station_id]
            for _, row in original_data.iterrows():
                station_data.loc[station_data['时间'] == row['时间'], '雨量'] = row['雨量']
        
        # 然后填入插值结果
        for timestamp, df in all_validation_results.items():
            if not df.empty:
                station_results = df[df['站点'] == station_id]
                if not station_results.empty:
                    for _, row in station_results.iterrows():
                        station_data.loc[station_data['时间'] == row['时间'], '雨量'] = row['预测值']
        
        # 保存完整的时间序列
        complete_series[station_id] = station_data
    
    return complete_series

# 创建流域雨量栅格
def create_rainfall_grid(timestamp, stations_df, rainfall_data, mask_file, output_file):
    """
    为特定时间戳创建流域雨量栅格
    
    参数:
        timestamp: 时间戳
        stations_df: 站点信息DataFrame
        rainfall_data: 雨量数据字典
        mask_file: 流域掩码文件路径
        output_file: 输出栅格文件路径
    """
    try:
        # 读取流域掩码
        with rasterio.open(mask_file) as mask_src:
            mask = mask_src.read(1)
            transform = mask_src.transform
            crs = mask_src.crs
            
            # 获取栅格的范围
            height, width = mask.shape
            
            # 创建一个空的雨量栅格
            rainfall_grid = np.zeros_like(mask, dtype=np.float32)
            rainfall_grid.fill(np.nan)
            
            # 获取此时间戳的可用站点雨量数据
            available_stations = {}
            for station_id, df in rainfall_data.items():
                if timestamp in df['时间'].values:
                    rain_value = df.loc[df['时间'] == timestamp, '雨量'].values[0]
                    if not np.isnan(rain_value):
                        station_info = stations_df[stations_df['站点'] == station_id].iloc[0]
                        available_stations[station_id] = {
                            'value': rain_value,
                            'coord': [station_info['经度'], station_info['纬度']]
                        }
            
            # 如果可用站点太少，则不生成栅格
            if len(available_stations) < 3:
                print(f"时间戳{timestamp}的可用站点不足3个，跳过栅格生成")
                return False
            
            # 提取站点坐标和雨量值
            points = np.array([available_stations[sid]['coord'] for sid in available_stations])
            values = np.array([available_stations[sid]['value'] for sid in available_stations])
            
            # 构建Delaunay三角网
            try:
                tri = Delaunay(points)
            except Exception as e:
                print(f"构建三角网时出错：{e}，尝试使用最近邻插值")
                for y in range(height):
                    for x in range(width):
                        if mask[y, x] > 0:  # 只处理流域内的像素
                            # 获取像素的地理坐标
                            lon, lat = transform * (x + 0.5, y + 0.5)
                            
                            # 计算到各站点的距离
                            dists = np.sqrt(np.sum((points - np.array([lon, lat]))**2, axis=1))
                            
                            # 找到最近的站点
                            nearest_idx = np.argmin(dists)
                            
                            # 赋值
                            rainfall_grid[y, x] = values[nearest_idx]
                
                # 写入栅格文件
                with rasterio.open(
                    output_file,
                    'w',
                    driver='AAIGrid',
                    height=height,
                    width=width,
                    count=1,
                    dtype=rainfall_grid.dtype,
                    crs=crs,
                    transform=transform,
                    nodata=-9999
                ) as dst:
                    # 将NaN替换为NoData值
                    rainfall_grid_out = rainfall_grid.copy()
                    rainfall_grid_out[np.isnan(rainfall_grid_out)] = -9999
                    dst.write(rainfall_grid_out, 1)
                
                return True
            
            # 对流域内的每个像素进行IDW插值
            chunk_size = 100  # 每次处理100行以减少内存使用
            for y_start in range(0, height, chunk_size):
                y_end = min(y_start + chunk_size, height)
                
                for y in range(y_start, y_end):
                    for x in range(width):
                        if mask[y, x] > 0:  # 只处理流域内的像素
                            # 获取像素的地理坐标
                            lon, lat = transform * (x + 0.5, y + 0.5)
                            pixel_point = np.array([lon, lat])
                            
                            # 找到包含该点的三角形或最近的点
                            simplex_idx = tri.find_simplex(pixel_point)
                            
                            if simplex_idx >= 0:
                                # 如果点在三角网内，使用三角形的三个顶点
                                vertices_idx = tri.simplices[simplex_idx]
                                neighbor_points = points[vertices_idx]
                                neighbor_values = values[vertices_idx]
                            else:
                                # 如果点在三角网外，使用最近的三个点
                                dists = np.sqrt(np.sum((points - pixel_point)**2, axis=1))
                                nearest_idx = np.argsort(dists)[:3]
                                neighbor_points = points[nearest_idx]
                                neighbor_values = values[nearest_idx]
                            
                            # 计算IDW权重
                            weights = calculate_idw_weights(pixel_point, neighbor_points)
                            
                            # 使用权重计算插值结果
                            rainfall_grid[y, x] = np.sum(weights * neighbor_values)
            
            # 写入栅格文件
            with rasterio.open(
                output_file,
                'w',
                driver='AAIGrid',
                height=height,
                width=width,
                count=1,
                dtype=rainfall_grid.dtype,
                crs=crs,
                transform=transform,
                nodata=-9999
            ) as dst:
                # 将NaN替换为NoData值
                rainfall_grid_out = rainfall_grid.copy()
                rainfall_grid_out[np.isnan(rainfall_grid_out)] = -9999
                dst.write(rainfall_grid_out, 1)
            
            print(f"时间戳{timestamp}的雨量栅格已保存至: {output_file}")
            return True
            
    except Exception as e:
        print(f"创建雨量栅格时出错：{e}")
        return False

# 处理单个时间戳的函数，用于并行计算
def process_timestamp(args):
    """
    处理单个时间戳的插值任务，用于并行计算
    
    参数:
        args: 包含以下参数的元组:
            timestamp: 时间戳
            stations_df: 站点信息DataFrame
            rainfall_data: 雨量数据字典
            tri: Delaunay三角网对象
            create_grid: 是否创建栅格
            mask_file: 流域掩码文件路径
            output_path: 输出路径
    
    返回:
        包含验证结果的DataFrame
    """
    timestamp, stations_df, rainfall_data, tri, create_grid, mask_file, output_path = args
    
    try:
        # 执行留一法验证
        validation_results = leave_one_out_validation(timestamp, stations_df, rainfall_data, tri)
        
        # 如果启用栅格创建且有验证结果
        if create_grid and not validation_results.empty:
            os.makedirs(os.path.join(output_path, 'grids'), exist_ok=True)
            grid_output_file = os.path.join(output_path, 'grids', f"rainfall_{clean_filename(str(timestamp))}.asc")
            create_rainfall_grid(timestamp, stations_df, rainfall_data, mask_file, grid_output_file)
        
        return timestamp, validation_results
    
    except Exception as e:
        print(f"处理时间戳{timestamp}时出错：{e}")
        return timestamp, pd.DataFrame()

# 分批处理时间序列
def process_batches(all_timestamps, stations_df, rainfall_data, tri, create_grid, mask_file, output_path, batch_size=100):
    """
    分批处理时间序列以减少内存占用
    
    参数:
        all_timestamps: 所有需要处理的时间戳
        stations_df: 站点信息DataFrame
        rainfall_data: 雨量数据字典
        tri: Delaunay三角网对象
        create_grid: 是否创建栅格
        mask_file: 流域掩码文件路径
        output_path: 输出路径
        batch_size: 每批处理的时间戳数量
    
    返回:
        所有验证结果的字典
    """
    # 获取CPU核心数，但最多使用12个
    num_cores = min(12, mp.cpu_count())
    print(f"使用{num_cores}个CPU核心进行并行计算")
    
    # 将时间戳分成多个批次
    total_batches = (len(all_timestamps) + batch_size - 1) // batch_size
    print(f"将{len(all_timestamps)}个时间戳分成{total_batches}个批次处理，每批{batch_size}个")
    
    all_validation_results = {}
    
    # 逐批处理
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(all_timestamps))
        batch_timestamps = all_timestamps[start_idx:end_idx]
        
        print(f"\n处理第{batch_idx+1}/{total_batches}批，共{len(batch_timestamps)}个时间戳")
        
        # 准备并行处理的参数
        process_args = [(timestamp, stations_df, rainfall_data, tri, create_grid, mask_file, output_path) 
                       for timestamp in batch_timestamps]
        
        # 使用并行处理当前批次
        batch_results = {}
        with mp.Pool(num_cores) as pool, tqdm(total=len(batch_timestamps)) as pbar:
            for timestamp, results in pool.imap_unordered(process_timestamp, process_args):
                batch_results[timestamp] = results
                pbar.update()
        
        # 合并当前批次的结果
        all_validation_results.update(batch_results)
        
        # 保存当前批次的结果
        batch_df = pd.concat(batch_results.values()) if batch_results else pd.DataFrame()
        if not batch_df.empty:
            batch_file = os.path.join(output_path, f'validation_results_batch_{batch_idx+1}.csv')
            batch_df.to_csv(batch_file, index=False, encoding='utf-8')
            print(f"第{batch_idx+1}批验证结果已保存至: {batch_file}")
        
        # 手动清理内存
        del batch_results, batch_df
        gc.collect()
    
    return all_validation_results

# 主函数
def main():
    """
    主函数，执行整个IDW空间插值流程
    """
    print("开始执行IDW空间插值...")
    start_time = time.time()
    
    try:
        # 加载站点信息
        stations_df = load_stations()
        
        # 提取站点高程信息
        dem_file = os.path.join(terrain_path, 'dem.asc')
        if os.path.exists(dem_file):
            stations_df = extract_elevation_from_dem(stations_df, dem_file)
        else:
            print("未找到DEM文件，将不提取高程信息")
        
        # 获取站点ID列表
        station_ids = stations_df['站点'].unique()
        
        # 加载雨量数据
        rainfall_data = load_rainfall_data(station_ids)
        
        # 检查是否成功加载数据
        if not rainfall_data:
            raise ValueError("未能加载任何雨量数据，请检查输入文件")
        
        # 构建Delaunay三角网
        tri = build_delaunay_triangulation(stations_df)
        
        # 生成所有站点的验证三角网并保存为一张图
        triangulation_output_file = os.path.join(output_path, 'all_validation_triangulations.png')
        triangulation_df = visualize_all_delaunay_triangulations(stations_df, triangulation_output_file)
        
        # 保存三角网信息表格
        triangulation_table_file = os.path.join(output_path, 'triangulation_info.csv')
        triangulation_df.to_csv(triangulation_table_file, index=False, encoding='utf-8')
        print(f"三角网信息表格已保存至: {triangulation_table_file}")
        
        # 获取所有唯一的时间戳
        all_timestamps = set()
        for df in rainfall_data.values():
            all_timestamps.update(df['时间'].unique())
        all_timestamps = sorted(all_timestamps)
        
        print(f"共有{len(all_timestamps)}个时间戳需要处理")
        
        # 询问用户是否创建栅格
        create_grid = input("是否为每个时间戳创建流域雨量栅格？(y/n): ").lower() == 'y'
        
        # 询问用户批次大小
        try:
            batch_size = int(input("请输入每批处理的时间戳数量（推荐值：50-200）: "))
            if batch_size <= 0:
                batch_size = 100
                print(f"输入无效，使用默认批次大小：{batch_size}")
        except:
            batch_size = 100
            print(f"输入无效，使用默认批次大小：{batch_size}")
        
        # 流域掩码文件路径
        mask_file = os.path.join(terrain_path, 'mask.asc')
        
        # 分批处理时间序列
        all_validation_results = process_batches(
            all_timestamps, stations_df, rainfall_data, tri, 
            create_grid, mask_file, output_path, batch_size
        )
        
        # 合并所有验证结果
        all_results_df = pd.concat([df for df in all_validation_results.values() if not df.empty])
        
        if not all_results_df.empty:
            # 保存所有验证结果
            all_results_file = os.path.join(output_path, 'all_validation_results.csv')
            all_results_df.to_csv(all_results_file, index=False, encoding='utf-8')
            print(f"所有验证结果已保存至: {all_results_file}")
            
            # 计算总体评价指标
            overall_metrics = calculate_metrics(all_results_df)
            print("\n总体评价指标:")
            for metric, value in overall_metrics.items():
                print(f"{metric}: {value:.4f}")
            
            # 计算每个站点的评价指标
            station_metrics = {}
            for station in all_results_df['站点'].unique():
                station_df = all_results_df[all_results_df['站点'] == station]
                station_metrics[station] = calculate_metrics(station_df)
            
            # 创建站点评价指标DataFrame
            station_metrics_df = pd.DataFrame.from_dict(station_metrics, orient='index')
            station_metrics_file = os.path.join(output_path, 'station_metrics.csv')
            station_metrics_df.to_csv(station_metrics_file, encoding='utf-8')
            print(f"站点评价指标已保存至: {station_metrics_file}")
            
            # 生成完整的时间序列
            print("生成完整的时间序列...")
            complete_series = generate_complete_timeseries(all_validation_results, rainfall_data)
            
            # 保存每个站点的完整时间序列
            for station_id, df in complete_series.items():
                output_file = os.path.join(output_path, f"{station_id}.csv")
                df.to_csv(output_file, index=False, encoding='utf-8')
                print(f"站点{station_id}的完整时间序列已保存至: {output_file}")
        else:
            print("没有生成任何验证结果，请检查输入数据")
        
        # 计算总运行时间
        end_time = time.time()
        print(f"\nIDW空间插值完成，总耗时: {(end_time - start_time)/60:.2f}分钟")
        
    except Exception as e:
        print(f"执行过程中出错：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

"""
Kriging插值系统栅格处理模块
支持栅格输出和可视化
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class RasterProcessor:
    """栅格处理器"""
    
    def __init__(self, config):
        """初始化栅格处理器"""
        self.config = config
        self.raster_data = {}
        
    def create_interpolation_grid(self, stations_df: pd.DataFrame, 
                                resolution: float = 0.01) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """创建插值网格"""
        try:
            logger.info("创建插值网格...")
            
            # 获取站点范围
            min_lon = stations_df['经度'].min()
            max_lon = stations_df['经度'].max()
            min_lat = stations_df['纬度'].min()
            max_lat = stations_df['纬度'].max()
            
            # 扩展边界
            lon_range = max_lon - min_lon
            lat_range = max_lat - min_lat
            
            buffer = max(lon_range, lat_range) * 0.1  # 10% 缓冲区
            
            min_lon -= buffer
            max_lon += buffer
            min_lat -= buffer
            max_lat += buffer
            
            # 创建网格
            lon_grid = np.arange(min_lon, max_lon + resolution, resolution)
            lat_grid = np.arange(min_lat, max_lat + resolution, resolution)
            
            lon_mesh, lat_mesh = np.meshgrid(lon_grid, lat_grid)
            
            # 创建网格点数组
            grid_points = np.column_stack([lon_mesh.ravel(), lat_mesh.ravel()])
            
            logger.info(f"网格创建完成: {len(lon_grid)} x {len(lat_grid)} = {len(grid_points)} 个点")
            
            return grid_points, lon_mesh, lat_mesh
            
        except Exception as e:
            logger.error(f"创建插值网格失败: {e}")
            return np.array([]), np.array([]), np.array([])
    
    def interpolate_to_raster(self, grid_points: np.ndarray,
                            lon_mesh: np.ndarray, lat_mesh: np.ndarray,
                            stations_df: pd.DataFrame,
                            rainfall_at_time: Dict[str, float],
                            kriging_interpolator) -> np.ndarray:
        """插值到栅格"""
        try:
            logger.info(f"开始栅格插值: {len(grid_points)} 个网格点...")
            
            # 使用并行处理（如果可用）
            if hasattr(self.config, 'num_cores') and self.config.num_cores > 1:
                try:
                    from parallel_processing import ParallelProcessor
                    parallel_processor = ParallelProcessor(self.config)
                    
                    interpolated_values = parallel_processor.parallel_raster_interpolation(
                        grid_points, stations_df, rainfall_at_time, kriging_interpolator
                    )
                    
                except Exception as e:
                    logger.warning(f"并行栅格插值失败，使用串行处理: {e}")
                    interpolated_values = self._serial_raster_interpolation(
                        grid_points, stations_df, rainfall_at_time, kriging_interpolator
                    )
            else:
                interpolated_values = self._serial_raster_interpolation(
                    grid_points, stations_df, rainfall_at_time, kriging_interpolator
                )
            
            # 重塑为网格形状
            raster_data = interpolated_values.reshape(lon_mesh.shape)
            
            logger.info("栅格插值完成")
            
            return raster_data
            
        except Exception as e:
            logger.error(f"栅格插值失败: {e}")
            return np.zeros_like(lon_mesh)
    
    def _serial_raster_interpolation(self, grid_points: np.ndarray,
                                   stations_df: pd.DataFrame,
                                   rainfall_at_time: Dict[str, float],
                                   kriging_interpolator) -> np.ndarray:
        """串行栅格插值"""
        try:
            from kriging_core import KrigingCore
            from delaunay_triangulation import DelaunayTriangulation
            
            # 创建插值器
            kriging_core = KrigingCore(self.config)
            delaunay_tri = DelaunayTriangulation(self.config)
            delaunay_tri.build_triangulation(stations_df)
            
            interpolated_values = []
            
            # 批处理以节省内存
            batch_size = min(1000, len(grid_points))
            
            for i in range(0, len(grid_points), batch_size):
                batch_points = grid_points[i:i+batch_size]
                batch_values = []
                
                for grid_point in batch_points:
                    try:
                        # 找到最近的站点
                        distances = np.sqrt(
                            (stations_df['经度'] - grid_point[0])**2 + 
                            (stations_df['纬度'] - grid_point[1])**2
                        )
                        
                        # 选择最近的几个站点
                        neighbor_indices = distances.nsmallest(self.config.neighbor_count).index.tolist()
                        
                        if len(neighbor_indices) == 0:
                            batch_values.append(0.0)
                            continue
                        
                        # 获取邻近站点信息
                        neighbor_stations = stations_df.iloc[neighbor_indices]
                        neighbor_values = []
                        neighbor_coords = []
                        
                        for _, neighbor_row in neighbor_stations.iterrows():
                            neighbor_name = neighbor_row['站点']
                            neighbor_value = rainfall_at_time.get(neighbor_name, 0.0)
                            
                            neighbor_values.append(neighbor_value)
                            neighbor_coords.append([neighbor_row['经度'], neighbor_row['纬度']])
                        
                        neighbor_values = np.array(neighbor_values)
                        neighbor_coords = np.array(neighbor_coords)
                        
                        # 执行Kriging插值
                        predicted_value, _ = kriging_core.ordinary_kriging(
                            grid_point, neighbor_coords, neighbor_values
                        )
                        
                        batch_values.append(max(0.0, predicted_value))
                        
                    except Exception:
                        batch_values.append(0.0)
                
                interpolated_values.extend(batch_values)
                
                # 显示进度
                if (i // batch_size + 1) % 10 == 0:
                    progress = min(100, (i + batch_size) / len(grid_points) * 100)
                    logger.info(f"栅格插值进度: {progress:.1f}%")
            
            return np.array(interpolated_values)
            
        except Exception as e:
            logger.error(f"串行栅格插值失败: {e}")
            return np.zeros(len(grid_points))
    
    def save_raster_to_asc(self, raster_data: np.ndarray,
                          lon_mesh: np.ndarray, lat_mesh: np.ndarray,
                          output_path: str = None) -> str:
        """保存栅格为ASC格式"""
        try:
            if output_path is None:
                output_dirs = self.config.get_output_dirs()
                output_path = os.path.join(output_dirs['rasters'], 'kriging_interpolation.asc')
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 获取栅格参数
            nrows, ncols = raster_data.shape
            xllcorner = lon_mesh.min()
            yllcorner = lat_mesh.min()
            cellsize = (lon_mesh.max() - lon_mesh.min()) / (ncols - 1)
            nodata_value = -9999
            
            # 替换NaN值
            raster_data_clean = np.where(np.isnan(raster_data), nodata_value, raster_data)
            
            # 写入ASC文件
            with open(output_path, 'w') as f:
                f.write(f"ncols         {ncols}\n")
                f.write(f"nrows         {nrows}\n")
                f.write(f"xllcorner     {xllcorner:.6f}\n")
                f.write(f"yllcorner     {yllcorner:.6f}\n")
                f.write(f"cellsize      {cellsize:.6f}\n")
                f.write(f"NODATA_value  {nodata_value}\n")
                
                # 写入数据（从上到下）
                for row in reversed(raster_data_clean):
                    f.write(' '.join([f"{val:.6f}" for val in row]) + '\n')
            
            logger.info(f"栅格文件已保存: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"保存ASC栅格文件失败: {e}")
            return ""
    
    def create_raster_visualization(self, raster_data: np.ndarray,
                                  lon_mesh: np.ndarray, lat_mesh: np.ndarray,
                                  stations_df: pd.DataFrame,
                                  title: str = "Kriging插值结果",
                                  output_path: str = None) -> str:
        """创建栅格可视化"""
        try:
            if output_path is None:
                output_dirs = self.config.get_output_dirs()
                output_path = os.path.join(output_dirs['plots'], 'kriging_raster.png')
            
            # 创建图形
            plt.figure(figsize=(12, 10))
            
            # 绘制栅格
            im = plt.contourf(lon_mesh, lat_mesh, raster_data, 
                            levels=20, cmap='Blues', alpha=0.8)
            
            # 添加等值线
            contours = plt.contour(lon_mesh, lat_mesh, raster_data, 
                                 levels=10, colors='black', alpha=0.4, linewidths=0.5)
            plt.clabel(contours, inline=True, fontsize=8, fmt='%.1f')
            
            # 添加站点
            plt.scatter(stations_df['经度'], stations_df['纬度'], 
                       c='red', s=50, alpha=0.8, zorder=5, 
                       edgecolors='black', linewidth=0.5)
            
            # 添加颜色条
            cbar = plt.colorbar(im, shrink=0.8)
            cbar.set_label('降雨量 (mm)', fontsize=12)
            
            # 设置标签和标题
            plt.xlabel('经度', fontsize=12)
            plt.ylabel('纬度', fontsize=12)
            plt.title(title, fontsize=14, fontweight='bold')
            
            # 添加网格
            plt.grid(True, alpha=0.3)
            
            # 设置坐标轴比例
            plt.axis('equal')
            
            # 保存图形
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"栅格可视化已保存: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"创建栅格可视化失败: {e}")
            return ""
    
    def process_time_series_rasters(self, stations_df: pd.DataFrame,
                                  rainfall_data: Dict[str, pd.DataFrame],
                                  kriging_interpolator,
                                  max_time_points: int = 10) -> List[str]:
        """处理时间序列栅格"""
        try:
            logger.info("开始处理时间序列栅格...")
            
            # 获取时间点
            first_station = list(rainfall_data.keys())[0]
            first_df = rainfall_data[first_station]
            
            time_indices = np.linspace(0, len(first_df)-1, 
                                     min(max_time_points, len(first_df)), dtype=int)
            
            # 创建网格
            grid_points, lon_mesh, lat_mesh = self.create_interpolation_grid(stations_df)
            
            if len(grid_points) == 0:
                return []
            
            output_files = []
            
            for i, time_idx in enumerate(time_indices):
                try:
                    time_point = first_df.iloc[time_idx]['时间']
                    
                    # 构建该时间点的降雨数据
                    rainfall_at_time = {}
                    for station_name in stations_df['站点']:
                        if str(station_name) in rainfall_data:
                            station_df = rainfall_data[str(station_name)]
                            matching_rows = station_df[station_df['时间'] == time_point]
                            
                            if len(matching_rows) > 0:
                                rainfall_at_time[station_name] = matching_rows['雨量'].iloc[0]
                            else:
                                rainfall_at_time[station_name] = 0.0
                        else:
                            rainfall_at_time[station_name] = 0.0
                    
                    # 插值到栅格
                    raster_data = self.interpolate_to_raster(
                        grid_points, lon_mesh, lat_mesh, 
                        stations_df, rainfall_at_time, kriging_interpolator
                    )
                    
                    # 保存栅格文件
                    output_dirs = self.config.get_output_dirs()
                    asc_path = os.path.join(output_dirs['rasters'], f'rainfall_{i+1:03d}.asc')
                    self.save_raster_to_asc(raster_data, lon_mesh, lat_mesh, asc_path)
                    
                    # 创建可视化
                    png_path = os.path.join(output_dirs['plots'], f'raster_{i+1:03d}.png')
                    self.create_raster_visualization(
                        raster_data, lon_mesh, lat_mesh, stations_df,
                        title=f"Kriging插值结果 - {time_point}", output_path=png_path
                    )
                    
                    output_files.extend([asc_path, png_path])
                    
                    logger.info(f"时间序列栅格处理进度: {i+1}/{len(time_indices)}")
                    
                except Exception as e:
                    logger.warning(f"处理时间点 {time_idx} 失败: {e}")
                    continue
            
            logger.info(f"时间序列栅格处理完成: {len(output_files)} 个文件")
            
            return output_files
            
        except Exception as e:
            logger.error(f"处理时间序列栅格失败: {e}")
            return []
    
    def get_raster_statistics(self, raster_data: np.ndarray) -> Dict:
        """获取栅格统计信息"""
        try:
            # 移除无效值
            valid_data = raster_data[~np.isnan(raster_data)]
            
            if len(valid_data) == 0:
                return {}
            
            stats = {
                'min': float(np.min(valid_data)),
                'max': float(np.max(valid_data)),
                'mean': float(np.mean(valid_data)),
                'std': float(np.std(valid_data)),
                'median': float(np.median(valid_data)),
                'total_pixels': int(raster_data.size),
                'valid_pixels': int(len(valid_data)),
                'zero_pixels': int(np.sum(valid_data == 0)),
                'zero_ratio': float(np.sum(valid_data == 0) / len(valid_data))
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取栅格统计信息失败: {e}")
            return {}
    
    def cleanup(self):
        """清理内存"""
        try:
            self.raster_data.clear()
            logger.debug("栅格处理器内存清理完成")
        except Exception as e:
            logger.warning(f"栅格处理器内存清理失败: {e}")

"""
简易运行脚本
为新手用户提供简单的运行接口
"""

import os
import sys
import logging
from typing import Optional

from config import Config, save_config_to_file, load_config_from_file
from prism_main import PRISMInterpolation
from batch_processing import BatchProcessor

logger = logging.getLogger(__name__)


def create_default_config_file(config_path: str = "prism_config.json"):
    """创建默认配置文件"""
    try:
        # 创建默认配置
        default_config = Config()
        
        # 保存到文件
        save_config_to_file(default_config, config_path)
        
        print(f"默认配置文件已创建: {config_path}")
        print("请根据您的数据路径修改配置文件中的路径设置")
        
        return config_path
        
    except Exception as e:
        print(f"创建配置文件失败: {e}")
        return None


def load_config_with_validation(config_path: str) -> Optional[Config]:
    """加载并验证配置文件"""
    try:
        if not os.path.exists(config_path):
            print(f"配置文件不存在: {config_path}")
            print("正在创建默认配置文件...")
            create_default_config_file(config_path)
            return None
        
        # 加载配置
        config = load_config_from_file(config_path)
        
        # 验证关键路径
        if not os.path.exists(config.input_dir):
            print(f"错误: 输入数据目录不存在: {config.input_dir}")
            return None
        
        if not os.path.exists(config.terrain_dir):
            print(f"错误: 地形数据目录不存在: {config.terrain_dir}")
            return None
        
        if not os.path.exists(config.stations_file):
            print(f"错误: 站点文件不存在: {config.stations_file}")
            return None
        
        print("配置文件验证通过")
        return config
        
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return None


def run_prism_interpolation(config_path: str = "prism_config.json", 
                          mode: str = "single") -> bool:
    """运行PRISM插值
    
    Args:
        config_path: 配置文件路径
        mode: 运行模式 ('single' 或 'batch')
    
    Returns:
        是否成功运行
    """
    try:
        print("="*60)
        print("PRISM空间插值系统")
        print("="*60)
        
        # 加载配置
        config = load_config_with_validation(config_path)
        if config is None:
            return False
        
        # 打印配置信息
        print(f"运行模式: {mode}")
        print(f"输入目录: {config.input_dir}")
        print(f"输出目录: {config.output_dir}")
        print(f"并行核心数: {config.num_cores}")
        print(f"是否输出栅格: {config.output_raster}")
        print("-"*60)
        
        if mode == "single":
            # 单文件夹处理
            prism_interpolation = PRISMInterpolation(config)
            evaluation_results = prism_interpolation.run_complete_workflow()
            
            if evaluation_results:
                print("插值处理成功完成！")
                return True
            else:
                print("插值处理完成，但无评价结果")
                return False
                
        elif mode == "batch":
            # 批量处理
            config.enable_batch_processing = True
            batch_processor = BatchProcessor(config)
            batch_results = batch_processor.run_batch_processing()
            
            if batch_results:
                print("批量处理成功完成！")
                return True
            else:
                print("批量处理失败")
                return False
        else:
            print(f"不支持的运行模式: {mode}")
            return False
            
    except Exception as e:
        print(f"运行PRISM插值失败: {e}")
        logger.error(f"运行PRISM插值失败: {e}", exc_info=True)
        return False


def interactive_setup():
    """交互式设置"""
    print("="*60)
    print("PRISM插值系统 - 交互式设置")
    print("="*60)
    
    try:
        # 获取基本路径信息
        print("请输入以下路径信息（按回车使用默认值）:")
        
        input_dir = input("输入数据目录 [D:/pythondata/spatial_interpolation/input_another/2009-1]: ").strip()
        if not input_dir:
            input_dir = "D:/pythondata/spatial_interpolation/input_another/2009-1"
        
        terrain_dir = input("地形数据目录 [D:/pythondata/spatial_interpolation/terrain/90]: ").strip()
        if not terrain_dir:
            terrain_dir = "D:/pythondata/spatial_interpolation/terrain/90"
        
        output_dir = input("输出目录 [D:/pythondata/spatial_interpolation/output/PRISM/2009-1]: ").strip()
        if not output_dir:
            output_dir = "D:/pythondata/spatial_interpolation/output/PRISM/2009-1"
        
        stations_file = input("站点文件 [D:/pythondata/spatial_interpolation/stations.csv]: ").strip()
        if not stations_file:
            stations_file = "D:/pythondata/spatial_interpolation/stations.csv"
        
        # 获取处理选项
        print("\n处理选项:")
        
        output_raster = input("是否输出栅格文件? (y/n) [y]: ").strip().lower()
        output_raster = output_raster != 'n'
        
        num_cores = input("并行核心数 [12]: ").strip()
        try:
            num_cores = int(num_cores) if num_cores else 12
        except ValueError:
            num_cores = 12
        
        # 创建配置
        config = Config(
            input_dir=input_dir,
            terrain_dir=terrain_dir,
            output_dir=output_dir,
            stations_file=stations_file,
            output_raster=output_raster,
            num_cores=num_cores
        )
        
        # 保存配置
        config_file = "prism_config_interactive.json"
        save_config_to_file(config, config_file)
        
        print(f"\n配置已保存到: {config_file}")
        
        # 询问是否立即运行
        run_now = input("是否立即运行插值? (y/n) [y]: ").strip().lower()
        if run_now != 'n':
            mode = input("选择运行模式 (single/batch) [single]: ").strip().lower()
            if mode not in ['single', 'batch']:
                mode = 'single'
            
            return run_prism_interpolation(config_file, mode)
        
        return True
        
    except KeyboardInterrupt:
        print("\n用户取消操作")
        return False
    except Exception as e:
        print(f"交互式设置失败: {e}")
        return False


def print_usage():
    """打印使用说明"""
    print("""
PRISM空间插值系统使用说明
========================

1. 基本使用:
   python -m PRISM_python.easy_run

2. 指定配置文件:
   python -m PRISM_python.easy_run --config my_config.json

3. 批量处理模式:
   python -m PRISM_python.easy_run --mode batch

4. 交互式设置:
   python -m PRISM_python.easy_run --setup

参数说明:
  --config: 配置文件路径 (默认: prism_config.json)
  --mode:   运行模式 (single/batch, 默认: single)
  --setup:  启动交互式设置
  --help:   显示此帮助信息

配置文件说明:
  配置文件为JSON格式，包含所有必要的路径和参数设置。
  首次运行时会自动创建默认配置文件。

数据要求:
  1. 输入数据: CSV格式的点雨量文件，包含"时间"和"雨量"列
  2. 地形数据: dem.asc, slope.asc, aspect.asc文件
  3. 站点文件: 包含"站点"、"经度"、"纬度"列的CSV文件

输出结果:
  1. points/: 各站点插值结果
  2. rasters/: 栅格插值结果 (如果启用)
  3. plots/: 可视化图表
  4. evaluation/: 评价指标和报告
""")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='PRISM空间插值系统')
    parser.add_argument('--config', default='prism_config.json', 
                       help='配置文件路径')
    parser.add_argument('--mode', choices=['single', 'batch'], default='single',
                       help='运行模式')
    parser.add_argument('--setup', action='store_true',
                       help='启动交互式设置')
    parser.add_argument('--help-usage', action='store_true',
                       help='显示详细使用说明')
    
    args = parser.parse_args()
    
    if args.help_usage:
        print_usage()
        return
    
    if args.setup:
        success = interactive_setup()
    else:
        success = run_prism_interpolation(args.config, args.mode)
    
    if success:
        print("\n程序执行成功！")
        sys.exit(0)
    else:
        print("\n程序执行失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()

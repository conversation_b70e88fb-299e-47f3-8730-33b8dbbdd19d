2025-06-10 18:23:56,827 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:23:56,827 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:23:56,827 - prism_main - INFO - ============================================================
2025-06-10 18:23:56,827 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:23:56,828 - prism_main - INFO - ============================================================
2025-06-10 18:23:56,828 - prism_main - INFO - 开始加载数据...
2025-06-10 18:23:56,828 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:23:56,830 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:23:56,831 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:23:57,241 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:23:57,243 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:23:57,249 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:23:57,249 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:23:57,292 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:23:57,336 - raster_processing - INFO - 地形栅格数据加载完成，栅格大小: (962, 800)
2025-06-10 18:23:57,336 - prism_main - INFO - 数据加载完成
2025-06-10 18:23:57,336 - prism_main - INFO - 构建空间结构...
2025-06-10 18:23:57,337 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:23:57,350 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:23:58,117 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM/2009-1\plots\delaunay_triangulation.png
2025-06-10 18:23:58,118 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:23:58,118 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:23:58,124 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:23:58,124 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:24:02,346 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:24:02,415 - prism_main - INFO - 插值计算完成
2025-06-10 18:24:02,415 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:24:02,416 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:24:02,459 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM/2009-1\evaluation\evaluation_report_20250610_182402.txt
2025-06-10 18:24:02,471 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM/2009-1\evaluation\detailed_metrics_20250610_182402.csv
2025-06-10 18:24:02,843 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM/2009-1\plots\scatter_plot_20250610_182402.png
2025-06-10 18:24:02,845 - prism_main - INFO - 保存插值结果...
2025-06-10 18:24:02,909 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM/2009-1\points
2025-06-10 18:24:02,909 - prism_main - INFO - 保存权重信息...
2025-06-10 18:24:02,968 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM/2009-1\evaluation\weights_20250610_182402.csv
2025-06-10 18:24:02,971 - prism_main - INFO - 处理栅格输出...
2025-06-10 18:24:02,972 - raster_processing - INFO - 开始处理时间序列栅格输出...
2025-06-10 18:24:02,972 - raster_processing - INFO - 创建插值栅格，原始大小: 962x800, 降采样大小: 97x81
2025-06-10 18:24:03,257 - raster_processing - INFO - 创建了 4395 个有效插值栅格点
2025-06-10 18:24:03,258 - raster_processing - INFO - 处理栅格批次 1/20 (20 个时间点)
2025-06-10 18:24:24,391 - raster_processing - INFO - 处理栅格批次 2/20 (20 个时间点)
2025-06-10 18:24:27,245 - raster_processing - INFO - 处理栅格批次 3/20 (20 个时间点)
2025-06-10 18:24:55,247 - raster_processing - INFO - 处理栅格批次 4/20 (20 个时间点)
2025-06-10 18:25:24,941 - raster_processing - INFO - 处理栅格批次 5/20 (20 个时间点)
2025-06-10 18:25:43,789 - raster_processing - INFO - 处理栅格批次 6/20 (20 个时间点)
2025-06-10 18:25:46,593 - raster_processing - INFO - 处理栅格批次 7/20 (20 个时间点)
2025-06-10 18:25:49,411 - raster_processing - INFO - 处理栅格批次 8/20 (20 个时间点)
2025-06-10 18:25:52,220 - raster_processing - INFO - 处理栅格批次 9/20 (20 个时间点)
2025-06-10 18:26:07,053 - raster_processing - INFO - 处理栅格批次 10/20 (20 个时间点)
2025-06-10 18:26:57,206 - raster_processing - INFO - 处理栅格批次 11/20 (20 个时间点)
2025-06-10 18:27:29,711 - raster_processing - INFO - 处理栅格批次 12/20 (20 个时间点)
2025-06-10 18:27:33,716 - raster_processing - INFO - 处理栅格批次 13/20 (20 个时间点)
2025-06-10 18:27:36,546 - raster_processing - INFO - 处理栅格批次 14/20 (20 个时间点)
2025-06-10 18:27:39,379 - raster_processing - INFO - 处理栅格批次 15/20 (20 个时间点)
2025-06-10 18:27:42,212 - raster_processing - INFO - 处理栅格批次 16/20 (20 个时间点)
2025-06-10 18:29:05,674 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:29:05,674 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:29:05,674 - prism_main - INFO - ============================================================
2025-06-10 18:29:05,675 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:29:05,675 - prism_main - INFO - ============================================================
2025-06-10 18:29:05,675 - prism_main - INFO - 开始加载数据...
2025-06-10 18:29:05,675 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:29:05,677 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:29:05,677 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:29:06,020 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:29:06,020 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:29:06,026 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:29:06,027 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:29:06,070 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:29:06,106 - prism_main - INFO - 数据加载完成
2025-06-10 18:29:06,106 - prism_main - INFO - 构建空间结构...
2025-06-10 18:29:06,106 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:29:06,110 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:29:06,819 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\plots\delaunay_triangulation.png
2025-06-10 18:29:06,819 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:29:06,820 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:29:06,826 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:29:06,826 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:29:10,901 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:29:10,969 - prism_main - INFO - 插值计算完成
2025-06-10 18:29:10,970 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:29:10,970 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:29:11,015 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\evaluation\evaluation_report_20250610_182911.txt
2025-06-10 18:29:11,026 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\evaluation\detailed_metrics_20250610_182911.csv
2025-06-10 18:29:11,403 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\plots\scatter_plot_20250610_182911.png
2025-06-10 18:29:11,405 - prism_main - INFO - 保存插值结果...
2025-06-10 18:29:11,497 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\points
2025-06-10 18:29:11,497 - prism_main - INFO - 保存权重信息...
2025-06-10 18:29:11,558 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\evaluation\weights_20250610_182911.csv
2025-06-10 18:29:11,749 - prism_main - INFO - 内存清理完成
2025-06-10 18:29:11,749 - prism_main - INFO - ============================================================
2025-06-10 18:29:11,749 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.08 秒
2025-06-10 18:29:11,750 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-1
2025-06-10 18:29:11,750 - prism_main - INFO - ============================================================
2025-06-10 18:29:11,761 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:29:11,761 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:29:11,764 - prism_main - INFO - ============================================================
2025-06-10 18:29:11,764 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:29:11,764 - prism_main - INFO - ============================================================
2025-06-10 18:29:11,765 - prism_main - INFO - 开始加载数据...
2025-06-10 18:29:11,765 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:29:11,768 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:29:11,768 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:29:12,123 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:29:12,123 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:29:12,130 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:29:12,130 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:29:12,178 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:29:12,232 - prism_main - INFO - 数据加载完成
2025-06-10 18:29:12,232 - prism_main - INFO - 构建空间结构...
2025-06-10 18:29:12,233 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:29:12,236 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:29:12,812 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\plots\delaunay_triangulation.png
2025-06-10 18:29:12,813 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:29:12,813 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:29:12,822 - prism_main - INFO - 共有 504 个时间点需要处理
2025-06-10 18:29:12,822 - parallel_processing - INFO - 开始并行插值处理，共 504 个时间点
2025-06-10 18:29:17,819 - parallel_processing - INFO - 并行插值完成，成功处理 504 个时间点
2025-06-10 18:29:17,892 - prism_main - INFO - 插值计算完成
2025-06-10 18:29:17,893 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:29:17,893 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:29:17,964 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\evaluation\evaluation_report_20250610_182917.txt
2025-06-10 18:29:17,973 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\evaluation\detailed_metrics_20250610_182917.csv
2025-06-10 18:29:18,334 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\plots\scatter_plot_20250610_182917.png
2025-06-10 18:29:18,336 - prism_main - INFO - 保存插值结果...
2025-06-10 18:29:18,412 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\points
2025-06-10 18:29:18,412 - prism_main - INFO - 保存权重信息...
2025-06-10 18:29:18,501 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\evaluation\weights_20250610_182918.csv
2025-06-10 18:29:18,728 - prism_main - INFO - 内存清理完成
2025-06-10 18:29:18,728 - prism_main - INFO - ============================================================
2025-06-10 18:29:18,728 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.96 秒
2025-06-10 18:29:18,729 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-2
2025-06-10 18:29:18,729 - prism_main - INFO - ============================================================
2025-06-10 18:29:18,730 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:29:18,730 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:29:18,735 - prism_main - INFO - ============================================================
2025-06-10 18:29:18,735 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:29:18,735 - prism_main - INFO - ============================================================
2025-06-10 18:29:18,735 - prism_main - INFO - 开始加载数据...
2025-06-10 18:29:18,735 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:29:18,736 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:29:18,736 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:29:19,074 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:29:19,075 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:29:19,081 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:29:19,081 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:29:19,125 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:29:19,184 - prism_main - INFO - 数据加载完成
2025-06-10 18:29:19,184 - prism_main - INFO - 构建空间结构...
2025-06-10 18:29:19,184 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:29:19,187 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:29:19,733 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\plots\delaunay_triangulation.png
2025-06-10 18:29:19,733 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:29:19,734 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:29:19,740 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:29:19,740 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:29:24,004 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:29:24,106 - prism_main - INFO - 插值计算完成
2025-06-10 18:29:24,107 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:29:24,107 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:29:24,159 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\evaluation\evaluation_report_20250610_182924.txt
2025-06-10 18:29:24,166 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\evaluation\detailed_metrics_20250610_182924.csv
2025-06-10 18:29:24,520 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\plots\scatter_plot_20250610_182924.png
2025-06-10 18:29:24,522 - prism_main - INFO - 保存插值结果...
2025-06-10 18:29:24,585 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\points
2025-06-10 18:29:24,585 - prism_main - INFO - 保存权重信息...
2025-06-10 18:29:24,652 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\evaluation\weights_20250610_182924.csv
2025-06-10 18:29:24,855 - prism_main - INFO - 内存清理完成
2025-06-10 18:29:24,855 - prism_main - INFO - ============================================================
2025-06-10 18:29:24,855 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.12 秒
2025-06-10 18:29:24,855 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-3
2025-06-10 18:29:24,855 - prism_main - INFO - ============================================================
2025-06-10 18:29:24,857 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:29:24,857 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:29:24,860 - prism_main - INFO - ============================================================
2025-06-10 18:29:24,860 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:29:24,860 - prism_main - INFO - ============================================================
2025-06-10 18:29:24,860 - prism_main - INFO - 开始加载数据...
2025-06-10 18:29:24,861 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:29:24,862 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:29:24,862 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:29:25,201 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:29:25,202 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:29:25,208 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:29:25,208 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:29:25,255 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:29:25,318 - prism_main - INFO - 数据加载完成
2025-06-10 18:29:25,318 - prism_main - INFO - 构建空间结构...
2025-06-10 18:29:25,318 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:29:25,321 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:29:25,847 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\plots\delaunay_triangulation.png
2025-06-10 18:29:25,847 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:29:25,847 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:29:25,856 - prism_main - INFO - 共有 552 个时间点需要处理
2025-06-10 18:29:25,856 - parallel_processing - INFO - 开始并行插值处理，共 552 个时间点
2025-06-10 18:29:31,118 - parallel_processing - INFO - 并行插值完成，成功处理 552 个时间点
2025-06-10 18:29:31,211 - prism_main - INFO - 插值计算完成
2025-06-10 18:29:31,211 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:29:31,212 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:29:31,288 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\evaluation\evaluation_report_20250610_182931.txt
2025-06-10 18:29:31,297 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\evaluation\detailed_metrics_20250610_182931.csv
2025-06-10 18:29:31,693 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\plots\scatter_plot_20250610_182931.png
2025-06-10 18:29:31,695 - prism_main - INFO - 保存插值结果...
2025-06-10 18:29:31,773 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\points
2025-06-10 18:29:31,774 - prism_main - INFO - 保存权重信息...
2025-06-10 18:29:31,869 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\evaluation\weights_20250610_182931.csv
2025-06-10 18:29:32,128 - prism_main - INFO - 内存清理完成
2025-06-10 18:29:32,128 - prism_main - INFO - ============================================================
2025-06-10 18:29:32,128 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.27 秒
2025-06-10 18:29:32,128 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-4
2025-06-10 18:29:32,128 - prism_main - INFO - ============================================================
2025-06-10 18:29:32,129 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:29:32,129 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:29:32,134 - prism_main - INFO - ============================================================
2025-06-10 18:29:32,134 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:29:32,134 - prism_main - INFO - ============================================================
2025-06-10 18:29:32,134 - prism_main - INFO - 开始加载数据...
2025-06-10 18:29:32,134 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:29:32,136 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:29:32,136 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:29:32,474 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:29:32,474 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:29:32,481 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:29:32,481 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:29:32,522 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:29:32,589 - prism_main - INFO - 数据加载完成
2025-06-10 18:29:32,589 - prism_main - INFO - 构建空间结构...
2025-06-10 18:29:32,589 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:29:32,592 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:29:33,130 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\plots\delaunay_triangulation.png
2025-06-10 18:29:33,130 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:29:33,130 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:29:33,133 - prism_main - INFO - 共有 144 个时间点需要处理
2025-06-10 18:29:33,133 - parallel_processing - INFO - 开始并行插值处理，共 144 个时间点
2025-06-10 18:29:36,070 - parallel_processing - INFO - 并行插值完成，成功处理 144 个时间点
2025-06-10 18:29:36,164 - prism_main - INFO - 插值计算完成
2025-06-10 18:29:36,164 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:29:36,165 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:29:36,190 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\evaluation\evaluation_report_20250610_182936.txt
2025-06-10 18:29:36,193 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\evaluation\detailed_metrics_20250610_182936.csv
2025-06-10 18:29:36,481 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\plots\scatter_plot_20250610_182936.png
2025-06-10 18:29:36,483 - prism_main - INFO - 保存插值结果...
2025-06-10 18:29:36,525 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\points
2025-06-10 18:29:36,525 - prism_main - INFO - 保存权重信息...
2025-06-10 18:29:36,552 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\evaluation\weights_20250610_182936.csv
2025-06-10 18:29:36,803 - prism_main - INFO - 内存清理完成
2025-06-10 18:29:36,803 - prism_main - INFO - ============================================================
2025-06-10 18:29:36,803 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 4.67 秒
2025-06-10 18:29:36,803 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-1
2025-06-10 18:29:36,803 - prism_main - INFO - ============================================================
2025-06-10 18:29:36,805 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:29:36,805 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:29:36,807 - prism_main - INFO - ============================================================
2025-06-10 18:29:36,807 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:29:36,807 - prism_main - INFO - ============================================================
2025-06-10 18:29:36,807 - prism_main - INFO - 开始加载数据...
2025-06-10 18:29:36,808 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:29:36,809 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:29:36,810 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:29:37,147 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:29:37,147 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:29:37,154 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:29:37,154 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:29:37,196 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:29:37,274 - prism_main - INFO - 数据加载完成
2025-06-10 18:29:37,274 - prism_main - INFO - 构建空间结构...
2025-06-10 18:29:37,274 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:29:37,277 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:29:37,824 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\plots\delaunay_triangulation.png
2025-06-10 18:29:37,825 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:29:37,825 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:29:37,828 - prism_main - INFO - 共有 144 个时间点需要处理
2025-06-10 18:29:37,828 - parallel_processing - INFO - 开始并行插值处理，共 144 个时间点
2025-06-10 18:29:40,694 - parallel_processing - INFO - 并行插值完成，成功处理 144 个时间点
2025-06-10 18:29:40,791 - prism_main - INFO - 插值计算完成
2025-06-10 18:29:40,792 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:29:40,792 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:29:40,812 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\evaluation\evaluation_report_20250610_182940.txt
2025-06-10 18:29:40,816 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\evaluation\detailed_metrics_20250610_182940.csv
2025-06-10 18:29:41,129 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\plots\scatter_plot_20250610_182940.png
2025-06-10 18:29:41,131 - prism_main - INFO - 保存插值结果...
2025-06-10 18:29:41,173 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\points
2025-06-10 18:29:41,173 - prism_main - INFO - 保存权重信息...
2025-06-10 18:29:41,198 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\evaluation\weights_20250610_182941.csv
2025-06-10 18:29:41,462 - prism_main - INFO - 内存清理完成
2025-06-10 18:29:41,463 - prism_main - INFO - ============================================================
2025-06-10 18:29:41,463 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 4.66 秒
2025-06-10 18:29:41,463 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-2
2025-06-10 18:29:41,463 - prism_main - INFO - ============================================================
2025-06-10 18:29:41,472 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:29:41,472 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:29:41,474 - prism_main - INFO - ============================================================
2025-06-10 18:29:41,474 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:29:41,474 - prism_main - INFO - ============================================================
2025-06-10 18:29:41,474 - prism_main - INFO - 开始加载数据...
2025-06-10 18:29:41,474 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:29:41,476 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:29:41,476 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:29:41,818 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:29:41,818 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:29:41,824 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:29:41,824 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:29:41,867 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:29:41,949 - prism_main - INFO - 数据加载完成
2025-06-10 18:29:41,950 - prism_main - INFO - 构建空间结构...
2025-06-10 18:29:41,950 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:29:41,952 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:29:42,494 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\plots\delaunay_triangulation.png
2025-06-10 18:29:42,494 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:29:42,494 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:29:42,499 - prism_main - INFO - 共有 264 个时间点需要处理
2025-06-10 18:29:42,499 - parallel_processing - INFO - 开始并行插值处理，共 264 个时间点
2025-06-10 18:29:46,078 - parallel_processing - INFO - 并行插值完成，成功处理 264 个时间点
2025-06-10 18:29:46,182 - prism_main - INFO - 插值计算完成
2025-06-10 18:29:46,183 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:29:46,184 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:29:46,222 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\evaluation\evaluation_report_20250610_182946.txt
2025-06-10 18:29:46,227 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\evaluation\detailed_metrics_20250610_182946.csv
2025-06-10 18:29:46,551 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\plots\scatter_plot_20250610_182946.png
2025-06-10 18:29:46,552 - prism_main - INFO - 保存插值结果...
2025-06-10 18:29:46,605 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\points
2025-06-10 18:29:46,606 - prism_main - INFO - 保存权重信息...
2025-06-10 18:29:46,651 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\evaluation\weights_20250610_182946.csv
2025-06-10 18:29:46,948 - prism_main - INFO - 内存清理完成
2025-06-10 18:29:46,948 - prism_main - INFO - ============================================================
2025-06-10 18:29:46,948 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.47 秒
2025-06-10 18:29:46,949 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-3
2025-06-10 18:29:46,949 - prism_main - INFO - ============================================================
2025-06-10 18:29:46,950 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:29:46,950 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:29:46,953 - prism_main - INFO - ============================================================
2025-06-10 18:29:46,953 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:29:46,953 - prism_main - INFO - ============================================================
2025-06-10 18:29:46,953 - prism_main - INFO - 开始加载数据...
2025-06-10 18:29:46,953 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:29:46,955 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:29:46,955 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:29:47,288 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:29:47,288 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:29:47,294 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:29:47,294 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:29:47,334 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:29:47,418 - prism_main - INFO - 数据加载完成
2025-06-10 18:29:47,418 - prism_main - INFO - 构建空间结构...
2025-06-10 18:29:47,418 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:29:47,421 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:29:47,965 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\plots\delaunay_triangulation.png
2025-06-10 18:29:47,965 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:29:47,967 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:29:47,969 - prism_main - INFO - 共有 144 个时间点需要处理
2025-06-10 18:29:47,969 - parallel_processing - INFO - 开始并行插值处理，共 144 个时间点
2025-06-10 18:29:50,960 - parallel_processing - INFO - 并行插值完成，成功处理 144 个时间点
2025-06-10 18:29:51,076 - prism_main - INFO - 插值计算完成
2025-06-10 18:29:51,077 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:29:51,077 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:29:51,101 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\evaluation\evaluation_report_20250610_182951.txt
2025-06-10 18:29:51,105 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\evaluation\detailed_metrics_20250610_182951.csv
2025-06-10 18:29:51,406 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\plots\scatter_plot_20250610_182951.png
2025-06-10 18:29:51,408 - prism_main - INFO - 保存插值结果...
2025-06-10 18:29:51,450 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\points
2025-06-10 18:29:51,450 - prism_main - INFO - 保存权重信息...
2025-06-10 18:29:51,481 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\evaluation\weights_20250610_182951.csv
2025-06-10 18:29:51,790 - prism_main - INFO - 内存清理完成
2025-06-10 18:29:51,790 - prism_main - INFO - ============================================================
2025-06-10 18:29:51,790 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 4.84 秒
2025-06-10 18:29:51,790 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-4
2025-06-10 18:29:51,790 - prism_main - INFO - ============================================================
2025-06-10 18:29:51,792 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:29:51,792 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:29:51,794 - prism_main - INFO - ============================================================
2025-06-10 18:29:51,794 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:29:51,794 - prism_main - INFO - ============================================================
2025-06-10 18:29:51,794 - prism_main - INFO - 开始加载数据...
2025-06-10 18:29:51,795 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:29:51,796 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:29:51,797 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:29:52,135 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:29:52,135 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:29:52,141 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:29:52,141 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:29:52,184 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:29:52,276 - prism_main - INFO - 数据加载完成
2025-06-10 18:29:52,276 - prism_main - INFO - 构建空间结构...
2025-06-10 18:29:52,276 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:29:52,279 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:29:52,818 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\plots\delaunay_triangulation.png
2025-06-10 18:29:52,819 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:29:52,819 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:29:52,822 - prism_main - INFO - 共有 168 个时间点需要处理
2025-06-10 18:29:52,822 - parallel_processing - INFO - 开始并行插值处理，共 168 个时间点
2025-06-10 18:29:55,896 - parallel_processing - INFO - 并行插值完成，成功处理 168 个时间点
2025-06-10 18:29:56,014 - prism_main - INFO - 插值计算完成
2025-06-10 18:29:56,014 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:29:56,015 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:29:56,039 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\evaluation\evaluation_report_20250610_182956.txt
2025-06-10 18:29:56,043 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\evaluation\detailed_metrics_20250610_182956.csv
2025-06-10 18:29:56,349 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\plots\scatter_plot_20250610_182956.png
2025-06-10 18:29:56,351 - prism_main - INFO - 保存插值结果...
2025-06-10 18:29:56,394 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\points
2025-06-10 18:29:56,394 - prism_main - INFO - 保存权重信息...
2025-06-10 18:29:56,428 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\evaluation\weights_20250610_182956.csv
2025-06-10 18:29:56,753 - prism_main - INFO - 内存清理完成
2025-06-10 18:29:56,753 - prism_main - INFO - ============================================================
2025-06-10 18:29:56,753 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 4.96 秒
2025-06-10 18:29:56,753 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-5
2025-06-10 18:29:56,753 - prism_main - INFO - ============================================================
2025-06-10 18:29:56,754 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:29:56,755 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:29:56,757 - prism_main - INFO - ============================================================
2025-06-10 18:29:56,757 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:29:56,757 - prism_main - INFO - ============================================================
2025-06-10 18:29:56,757 - prism_main - INFO - 开始加载数据...
2025-06-10 18:29:56,758 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:29:56,759 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:29:56,759 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:29:57,096 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:29:57,097 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:29:57,103 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:29:57,103 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:29:57,142 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:29:57,246 - prism_main - INFO - 数据加载完成
2025-06-10 18:29:57,246 - prism_main - INFO - 构建空间结构...
2025-06-10 18:29:57,246 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:29:57,248 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:29:57,800 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\plots\delaunay_triangulation.png
2025-06-10 18:29:57,801 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:29:57,801 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:29:57,803 - prism_main - INFO - 共有 96 个时间点需要处理
2025-06-10 18:29:57,803 - parallel_processing - INFO - 开始并行插值处理，共 96 个时间点
2025-06-10 18:30:00,430 - parallel_processing - INFO - 并行插值完成，成功处理 96 个时间点
2025-06-10 18:30:00,558 - prism_main - INFO - 插值计算完成
2025-06-10 18:30:00,559 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:30:00,559 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:30:00,573 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\evaluation\evaluation_report_20250610_183000.txt
2025-06-10 18:30:00,577 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\evaluation\detailed_metrics_20250610_183000.csv
2025-06-10 18:30:00,870 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\plots\scatter_plot_20250610_183000.png
2025-06-10 18:30:00,871 - prism_main - INFO - 保存插值结果...
2025-06-10 18:30:00,910 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\points
2025-06-10 18:30:00,910 - prism_main - INFO - 保存权重信息...
2025-06-10 18:30:00,931 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\evaluation\weights_20250610_183000.csv
2025-06-10 18:30:01,259 - prism_main - INFO - 内存清理完成
2025-06-10 18:30:01,259 - prism_main - INFO - ============================================================
2025-06-10 18:30:01,259 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 4.50 秒
2025-06-10 18:30:01,259 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2011-1
2025-06-10 18:30:01,260 - prism_main - INFO - ============================================================
2025-06-10 18:30:01,261 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:30:01,261 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:30:01,262 - prism_main - INFO - ============================================================
2025-06-10 18:30:01,262 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:30:01,262 - prism_main - INFO - ============================================================
2025-06-10 18:30:01,263 - prism_main - INFO - 开始加载数据...
2025-06-10 18:30:01,263 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:30:01,264 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:30:01,264 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:30:01,615 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:30:01,616 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:30:01,622 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:30:01,623 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:30:01,667 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:30:01,795 - prism_main - INFO - 数据加载完成
2025-06-10 18:30:01,795 - prism_main - INFO - 构建空间结构...
2025-06-10 18:30:01,795 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:30:01,798 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:30:02,345 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\plots\delaunay_triangulation.png
2025-06-10 18:30:02,346 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:30:02,346 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:30:02,352 - prism_main - INFO - 共有 360 个时间点需要处理
2025-06-10 18:30:02,353 - parallel_processing - INFO - 开始并行插值处理，共 360 个时间点
2025-06-10 18:30:06,411 - parallel_processing - INFO - 并行插值完成，成功处理 360 个时间点
2025-06-10 18:30:06,550 - prism_main - INFO - 插值计算完成
2025-06-10 18:30:06,551 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:30:06,552 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:30:06,604 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\evaluation\evaluation_report_20250610_183006.txt
2025-06-10 18:30:06,608 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\evaluation\detailed_metrics_20250610_183006.csv
2025-06-10 18:30:06,948 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\plots\scatter_plot_20250610_183006.png
2025-06-10 18:30:06,950 - prism_main - INFO - 保存插值结果...
2025-06-10 18:30:07,013 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\points
2025-06-10 18:30:07,013 - prism_main - INFO - 保存权重信息...
2025-06-10 18:30:07,069 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\evaluation\weights_20250610_183007.csv
2025-06-10 18:30:07,435 - prism_main - INFO - 内存清理完成
2025-06-10 18:30:07,436 - prism_main - INFO - ============================================================
2025-06-10 18:30:07,436 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.17 秒
2025-06-10 18:30:07,436 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2011-2
2025-06-10 18:30:07,436 - prism_main - INFO - ============================================================
2025-06-10 18:30:07,438 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:30:07,438 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:30:07,441 - prism_main - INFO - ============================================================
2025-06-10 18:30:07,441 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:30:07,441 - prism_main - INFO - ============================================================
2025-06-10 18:30:07,441 - prism_main - INFO - 开始加载数据...
2025-06-10 18:30:07,441 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:30:07,443 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:30:07,443 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:30:07,778 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:30:07,779 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:30:07,785 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:30:07,785 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:30:07,827 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:30:07,946 - prism_main - INFO - 数据加载完成
2025-06-10 18:30:07,946 - prism_main - INFO - 构建空间结构...
2025-06-10 18:30:07,946 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:30:07,949 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:30:08,488 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\plots\delaunay_triangulation.png
2025-06-10 18:30:08,489 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:30:08,489 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:30:08,495 - prism_main - INFO - 共有 312 个时间点需要处理
2025-06-10 18:30:08,496 - parallel_processing - INFO - 开始并行插值处理，共 312 个时间点
2025-06-10 18:30:12,267 - parallel_processing - INFO - 并行插值完成，成功处理 312 个时间点
2025-06-10 18:30:12,412 - prism_main - INFO - 插值计算完成
2025-06-10 18:30:12,412 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:30:12,412 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:30:12,461 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\evaluation\evaluation_report_20250610_183012.txt
2025-06-10 18:30:12,466 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\evaluation\detailed_metrics_20250610_183012.csv
2025-06-10 18:30:12,805 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\plots\scatter_plot_20250610_183012.png
2025-06-10 18:30:12,805 - prism_main - INFO - 保存插值结果...
2025-06-10 18:30:12,863 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\points
2025-06-10 18:30:12,863 - prism_main - INFO - 保存权重信息...
2025-06-10 18:30:12,914 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\evaluation\weights_20250610_183012.csv
2025-06-10 18:30:13,311 - prism_main - INFO - 内存清理完成
2025-06-10 18:30:13,311 - prism_main - INFO - ============================================================
2025-06-10 18:30:13,312 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.87 秒
2025-06-10 18:30:13,312 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2011-3
2025-06-10 18:30:13,312 - prism_main - INFO - ============================================================
2025-06-10 18:30:13,313 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:30:13,313 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:30:13,316 - prism_main - INFO - ============================================================
2025-06-10 18:30:13,316 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:30:13,316 - prism_main - INFO - ============================================================
2025-06-10 18:30:13,316 - prism_main - INFO - 开始加载数据...
2025-06-10 18:30:13,316 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:30:13,318 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:30:13,318 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:30:13,656 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:30:13,656 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:30:13,662 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:30:13,662 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:30:13,705 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:30:13,829 - prism_main - INFO - 数据加载完成
2025-06-10 18:30:13,829 - prism_main - INFO - 构建空间结构...
2025-06-10 18:30:13,829 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:30:13,832 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:30:14,369 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\plots\delaunay_triangulation.png
2025-06-10 18:30:14,370 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:30:14,370 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:30:14,375 - prism_main - INFO - 共有 288 个时间点需要处理
2025-06-10 18:30:14,375 - parallel_processing - INFO - 开始并行插值处理，共 288 个时间点
2025-06-10 18:30:18,062 - parallel_processing - INFO - 并行插值完成，成功处理 288 个时间点
2025-06-10 18:30:18,213 - prism_main - INFO - 插值计算完成
2025-06-10 18:30:18,213 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:30:18,214 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:30:18,261 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\evaluation\evaluation_report_20250610_183018.txt
2025-06-10 18:30:18,265 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\evaluation\detailed_metrics_20250610_183018.csv
2025-06-10 18:30:18,638 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\plots\scatter_plot_20250610_183018.png
2025-06-10 18:30:18,639 - prism_main - INFO - 保存插值结果...
2025-06-10 18:30:18,694 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\points
2025-06-10 18:30:18,694 - prism_main - INFO - 保存权重信息...
2025-06-10 18:30:18,744 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\evaluation\weights_20250610_183018.csv
2025-06-10 18:30:19,178 - prism_main - INFO - 内存清理完成
2025-06-10 18:30:19,178 - prism_main - INFO - ============================================================
2025-06-10 18:30:19,178 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.86 秒
2025-06-10 18:30:19,178 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2011-4
2025-06-10 18:30:19,179 - prism_main - INFO - ============================================================
2025-06-10 18:30:19,180 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:30:19,181 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:30:19,183 - prism_main - INFO - ============================================================
2025-06-10 18:30:19,183 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:30:19,183 - prism_main - INFO - ============================================================
2025-06-10 18:30:19,183 - prism_main - INFO - 开始加载数据...
2025-06-10 18:30:19,183 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:30:19,186 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:30:19,186 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:30:19,521 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:30:19,521 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:30:19,527 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:30:19,527 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:30:19,572 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:30:19,698 - prism_main - INFO - 数据加载完成
2025-06-10 18:30:19,699 - prism_main - INFO - 构建空间结构...
2025-06-10 18:30:19,699 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:30:19,702 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:30:20,234 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\plots\delaunay_triangulation.png
2025-06-10 18:30:20,235 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:30:20,235 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:30:20,240 - prism_main - INFO - 共有 312 个时间点需要处理
2025-06-10 18:30:20,240 - parallel_processing - INFO - 开始并行插值处理，共 312 个时间点
2025-06-10 18:30:24,084 - parallel_processing - INFO - 并行插值完成，成功处理 312 个时间点
2025-06-10 18:30:24,254 - prism_main - INFO - 插值计算完成
2025-06-10 18:30:24,255 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:30:24,256 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:30:24,296 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\evaluation\evaluation_report_20250610_183024.txt
2025-06-10 18:30:24,301 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\evaluation\detailed_metrics_20250610_183024.csv
2025-06-10 18:30:24,649 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\plots\scatter_plot_20250610_183024.png
2025-06-10 18:30:24,650 - prism_main - INFO - 保存插值结果...
2025-06-10 18:30:24,708 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\points
2025-06-10 18:30:24,708 - prism_main - INFO - 保存权重信息...
2025-06-10 18:30:24,765 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\evaluation\weights_20250610_183024.csv
2025-06-10 18:30:25,219 - prism_main - INFO - 内存清理完成
2025-06-10 18:30:25,220 - prism_main - INFO - ============================================================
2025-06-10 18:30:25,220 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.04 秒
2025-06-10 18:30:25,220 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2012-1
2025-06-10 18:30:25,220 - prism_main - INFO - ============================================================
2025-06-10 18:30:25,222 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:30:25,222 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:30:25,225 - prism_main - INFO - ============================================================
2025-06-10 18:30:25,225 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:30:25,225 - prism_main - INFO - ============================================================
2025-06-10 18:30:25,225 - prism_main - INFO - 开始加载数据...
2025-06-10 18:30:25,226 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:30:25,227 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:30:25,227 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:30:25,566 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:30:25,566 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:30:25,572 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:30:25,572 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:30:25,618 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:30:25,746 - prism_main - INFO - 数据加载完成
2025-06-10 18:30:25,746 - prism_main - INFO - 构建空间结构...
2025-06-10 18:30:25,746 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:30:25,749 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:30:26,272 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\plots\delaunay_triangulation.png
2025-06-10 18:30:26,272 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:30:26,273 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:30:26,280 - prism_main - INFO - 共有 480 个时间点需要处理
2025-06-10 18:30:26,280 - parallel_processing - INFO - 开始并行插值处理，共 480 个时间点
2025-06-10 18:30:31,164 - parallel_processing - INFO - 并行插值完成，成功处理 480 个时间点
2025-06-10 18:30:31,340 - prism_main - INFO - 插值计算完成
2025-06-10 18:30:31,340 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:30:31,341 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:30:31,417 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\evaluation\evaluation_report_20250610_183031.txt
2025-06-10 18:30:31,425 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\evaluation\detailed_metrics_20250610_183031.csv
2025-06-10 18:30:31,803 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\plots\scatter_plot_20250610_183031.png
2025-06-10 18:30:31,805 - prism_main - INFO - 保存插值结果...
2025-06-10 18:30:31,877 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\points
2025-06-10 18:30:31,877 - prism_main - INFO - 保存权重信息...
2025-06-10 18:30:31,964 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\evaluation\weights_20250610_183031.csv
2025-06-10 18:30:32,455 - prism_main - INFO - 内存清理完成
2025-06-10 18:30:32,455 - prism_main - INFO - ============================================================
2025-06-10 18:30:32,456 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.23 秒
2025-06-10 18:30:32,456 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2012-2
2025-06-10 18:30:32,456 - prism_main - INFO - ============================================================
2025-06-10 18:30:32,467 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:30:32,467 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:30:32,472 - prism_main - INFO - ============================================================
2025-06-10 18:30:32,472 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:30:32,472 - prism_main - INFO - ============================================================
2025-06-10 18:30:32,472 - prism_main - INFO - 开始加载数据...
2025-06-10 18:30:32,472 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:30:32,474 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:30:32,474 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:30:32,806 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:30:32,807 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:30:32,813 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:30:32,813 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:30:32,855 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:30:32,990 - prism_main - INFO - 数据加载完成
2025-06-10 18:30:32,991 - prism_main - INFO - 构建空间结构...
2025-06-10 18:30:32,991 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:30:32,994 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:30:33,536 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\plots\delaunay_triangulation.png
2025-06-10 18:30:33,537 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:30:33,537 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:30:33,543 - prism_main - INFO - 共有 336 个时间点需要处理
2025-06-10 18:30:33,543 - parallel_processing - INFO - 开始并行插值处理，共 336 个时间点
2025-06-10 18:30:37,537 - parallel_processing - INFO - 并行插值完成，成功处理 336 个时间点
2025-06-10 18:30:37,719 - prism_main - INFO - 插值计算完成
2025-06-10 18:30:37,719 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:30:37,720 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:30:37,761 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\evaluation\evaluation_report_20250610_183037.txt
2025-06-10 18:30:37,766 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\evaluation\detailed_metrics_20250610_183037.csv
2025-06-10 18:30:38,115 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\plots\scatter_plot_20250610_183037.png
2025-06-10 18:30:38,116 - prism_main - INFO - 保存插值结果...
2025-06-10 18:30:38,177 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\points
2025-06-10 18:30:38,177 - prism_main - INFO - 保存权重信息...
2025-06-10 18:30:38,240 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\evaluation\weights_20250610_183038.csv
2025-06-10 18:30:38,750 - prism_main - INFO - 内存清理完成
2025-06-10 18:30:38,750 - prism_main - INFO - ============================================================
2025-06-10 18:30:38,750 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.28 秒
2025-06-10 18:30:38,750 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2013-1
2025-06-10 18:30:38,750 - prism_main - INFO - ============================================================
2025-06-10 18:30:38,752 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:30:38,752 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:30:38,756 - prism_main - INFO - ============================================================
2025-06-10 18:30:38,756 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:30:38,756 - prism_main - INFO - ============================================================
2025-06-10 18:30:38,756 - prism_main - INFO - 开始加载数据...
2025-06-10 18:30:38,756 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:30:38,758 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:30:38,758 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:30:39,097 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:30:39,097 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:30:39,104 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:30:39,104 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:30:39,149 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:30:39,297 - prism_main - INFO - 数据加载完成
2025-06-10 18:30:39,297 - prism_main - INFO - 构建空间结构...
2025-06-10 18:30:39,297 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:30:39,301 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:30:39,857 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\plots\delaunay_triangulation.png
2025-06-10 18:30:39,857 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:30:39,858 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:30:39,865 - prism_main - INFO - 共有 409 个时间点需要处理
2025-06-10 18:30:39,865 - parallel_processing - INFO - 开始并行插值处理，共 409 个时间点
2025-06-10 18:30:44,457 - parallel_processing - INFO - 并行插值完成，成功处理 409 个时间点
2025-06-10 18:30:44,645 - prism_main - INFO - 插值计算完成
2025-06-10 18:30:44,646 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:30:44,646 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:30:44,708 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\evaluation\evaluation_report_20250610_183044.txt
2025-06-10 18:30:44,713 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\evaluation\detailed_metrics_20250610_183044.csv
2025-06-10 18:30:45,095 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\plots\scatter_plot_20250610_183044.png
2025-06-10 18:30:45,096 - prism_main - INFO - 保存插值结果...
2025-06-10 18:30:45,169 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\points
2025-06-10 18:30:45,169 - prism_main - INFO - 保存权重信息...
2025-06-10 18:30:45,248 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\evaluation\weights_20250610_183045.csv
2025-06-10 18:30:45,774 - prism_main - INFO - 内存清理完成
2025-06-10 18:30:45,774 - prism_main - INFO - ============================================================
2025-06-10 18:30:45,774 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.02 秒
2025-06-10 18:30:45,775 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2013-2
2025-06-10 18:30:45,775 - prism_main - INFO - ============================================================
2025-06-10 18:30:45,785 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:30:45,785 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:30:45,790 - prism_main - INFO - ============================================================
2025-06-10 18:30:45,790 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:30:45,790 - prism_main - INFO - ============================================================
2025-06-10 18:30:45,790 - prism_main - INFO - 开始加载数据...
2025-06-10 18:30:45,790 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:30:45,792 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:30:45,792 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:30:46,126 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:30:46,126 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:30:46,133 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:30:46,133 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:30:46,176 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:30:46,329 - prism_main - INFO - 数据加载完成
2025-06-10 18:30:46,330 - prism_main - INFO - 构建空间结构...
2025-06-10 18:30:46,330 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:30:46,333 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:30:46,874 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\plots\delaunay_triangulation.png
2025-06-10 18:30:46,874 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:30:46,875 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:30:46,880 - prism_main - INFO - 共有 297 个时间点需要处理
2025-06-10 18:30:46,880 - parallel_processing - INFO - 开始并行插值处理，共 297 个时间点
2025-06-10 18:30:50,630 - parallel_processing - INFO - 并行插值完成，成功处理 297 个时间点
2025-06-10 18:30:50,826 - prism_main - INFO - 插值计算完成
2025-06-10 18:30:50,826 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:30:50,826 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:30:50,870 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\evaluation\evaluation_report_20250610_183050.txt
2025-06-10 18:30:50,875 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\evaluation\detailed_metrics_20250610_183050.csv
2025-06-10 18:30:51,207 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\plots\scatter_plot_20250610_183050.png
2025-06-10 18:30:51,209 - prism_main - INFO - 保存插值结果...
2025-06-10 18:30:51,264 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\points
2025-06-10 18:30:51,264 - prism_main - INFO - 保存权重信息...
2025-06-10 18:30:51,320 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\evaluation\weights_20250610_183051.csv
2025-06-10 18:30:51,863 - prism_main - INFO - 内存清理完成
2025-06-10 18:30:51,863 - prism_main - INFO - ============================================================
2025-06-10 18:30:51,863 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.07 秒
2025-06-10 18:30:51,863 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2014-1
2025-06-10 18:30:51,864 - prism_main - INFO - ============================================================
2025-06-10 18:30:51,864 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:30:51,864 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:30:51,869 - prism_main - INFO - ============================================================
2025-06-10 18:30:51,869 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:30:51,869 - prism_main - INFO - ============================================================
2025-06-10 18:30:51,869 - prism_main - INFO - 开始加载数据...
2025-06-10 18:30:51,869 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:30:51,871 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:30:51,871 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:30:52,205 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:30:52,205 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:30:52,211 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:30:52,211 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:30:52,253 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:30:52,418 - prism_main - INFO - 数据加载完成
2025-06-10 18:30:52,418 - prism_main - INFO - 构建空间结构...
2025-06-10 18:30:52,419 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:30:52,422 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:30:52,954 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\plots\delaunay_triangulation.png
2025-06-10 18:30:52,954 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:30:52,955 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:30:52,960 - prism_main - INFO - 共有 283 个时间点需要处理
2025-06-10 18:30:52,960 - parallel_processing - INFO - 开始并行插值处理，共 283 个时间点
2025-06-10 18:30:56,650 - parallel_processing - INFO - 并行插值完成，成功处理 283 个时间点
2025-06-10 18:30:56,854 - prism_main - INFO - 插值计算完成
2025-06-10 18:30:56,854 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:30:56,855 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:30:56,892 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\evaluation\evaluation_report_20250610_183056.txt
2025-06-10 18:30:56,897 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\evaluation\detailed_metrics_20250610_183056.csv
2025-06-10 18:30:57,226 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\plots\scatter_plot_20250610_183056.png
2025-06-10 18:30:57,227 - prism_main - INFO - 保存插值结果...
2025-06-10 18:30:57,281 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\points
2025-06-10 18:30:57,281 - prism_main - INFO - 保存权重信息...
2025-06-10 18:30:57,332 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\evaluation\weights_20250610_183057.csv
2025-06-10 18:30:57,885 - prism_main - INFO - 内存清理完成
2025-06-10 18:30:57,886 - prism_main - INFO - ============================================================
2025-06-10 18:30:57,886 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.02 秒
2025-06-10 18:30:57,886 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2014-2
2025-06-10 18:30:57,886 - prism_main - INFO - ============================================================
2025-06-10 18:30:57,897 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:30:57,897 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:30:57,901 - prism_main - INFO - ============================================================
2025-06-10 18:30:57,901 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:30:57,902 - prism_main - INFO - ============================================================
2025-06-10 18:30:57,902 - prism_main - INFO - 开始加载数据...
2025-06-10 18:30:57,902 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:30:57,903 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:30:57,903 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:30:58,241 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:30:58,241 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:30:58,247 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:30:58,247 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:30:58,289 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:30:58,464 - prism_main - INFO - 数据加载完成
2025-06-10 18:30:58,464 - prism_main - INFO - 构建空间结构...
2025-06-10 18:30:58,464 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:30:58,467 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:30:59,001 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\plots\delaunay_triangulation.png
2025-06-10 18:30:59,002 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:30:59,002 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:30:59,007 - prism_main - INFO - 共有 264 个时间点需要处理
2025-06-10 18:30:59,007 - parallel_processing - INFO - 开始并行插值处理，共 264 个时间点
2025-06-10 18:31:02,620 - parallel_processing - INFO - 并行插值完成，成功处理 264 个时间点
2025-06-10 18:31:02,843 - prism_main - INFO - 插值计算完成
2025-06-10 18:31:02,843 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:31:02,843 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:31:02,885 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\evaluation\evaluation_report_20250610_183102.txt
2025-06-10 18:31:02,890 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\evaluation\detailed_metrics_20250610_183102.csv
2025-06-10 18:31:03,206 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\plots\scatter_plot_20250610_183102.png
2025-06-10 18:31:03,211 - prism_main - INFO - 保存插值结果...
2025-06-10 18:31:03,264 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\points
2025-06-10 18:31:03,264 - prism_main - INFO - 保存权重信息...
2025-06-10 18:31:03,314 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\evaluation\weights_20250610_183103.csv
2025-06-10 18:31:03,892 - prism_main - INFO - 内存清理完成
2025-06-10 18:31:03,892 - prism_main - INFO - ============================================================
2025-06-10 18:31:03,892 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.99 秒
2025-06-10 18:31:03,892 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2014-3
2025-06-10 18:31:03,892 - prism_main - INFO - ============================================================
2025-06-10 18:31:03,893 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:31:03,893 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:31:03,897 - prism_main - INFO - ============================================================
2025-06-10 18:31:03,897 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:31:03,897 - prism_main - INFO - ============================================================
2025-06-10 18:31:03,897 - prism_main - INFO - 开始加载数据...
2025-06-10 18:31:03,897 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:31:03,899 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:31:03,899 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:31:04,231 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:31:04,231 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:31:04,237 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:31:04,237 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:31:04,283 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:31:04,466 - prism_main - INFO - 数据加载完成
2025-06-10 18:31:04,466 - prism_main - INFO - 构建空间结构...
2025-06-10 18:31:04,466 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:31:04,469 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:31:04,997 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\plots\delaunay_triangulation.png
2025-06-10 18:31:04,997 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:31:04,998 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:31:05,005 - prism_main - INFO - 共有 401 个时间点需要处理
2025-06-10 18:31:05,005 - parallel_processing - INFO - 开始并行插值处理，共 401 个时间点
2025-06-10 18:31:09,400 - parallel_processing - INFO - 并行插值完成，成功处理 401 个时间点
2025-06-10 18:31:09,624 - prism_main - INFO - 插值计算完成
2025-06-10 18:31:09,625 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:31:09,625 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:31:09,682 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\evaluation\evaluation_report_20250610_183109.txt
2025-06-10 18:31:09,689 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\evaluation\detailed_metrics_20250610_183109.csv
2025-06-10 18:31:10,043 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\plots\scatter_plot_20250610_183109.png
2025-06-10 18:31:10,045 - prism_main - INFO - 保存插值结果...
2025-06-10 18:31:10,111 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\points
2025-06-10 18:31:10,112 - prism_main - INFO - 保存权重信息...
2025-06-10 18:31:10,184 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\evaluation\weights_20250610_183110.csv
2025-06-10 18:31:10,831 - prism_main - INFO - 内存清理完成
2025-06-10 18:31:10,831 - prism_main - INFO - ============================================================
2025-06-10 18:31:10,831 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.93 秒
2025-06-10 18:31:10,831 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2014-4
2025-06-10 18:31:10,831 - prism_main - INFO - ============================================================
2025-06-10 18:31:10,833 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:31:10,833 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:31:10,839 - prism_main - INFO - ============================================================
2025-06-10 18:31:10,839 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:31:10,839 - prism_main - INFO - ============================================================
2025-06-10 18:31:10,839 - prism_main - INFO - 开始加载数据...
2025-06-10 18:31:10,839 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:31:10,840 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:31:10,840 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:31:11,176 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:31:11,176 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:31:11,183 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:31:11,183 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:31:11,224 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:31:11,407 - prism_main - INFO - 数据加载完成
2025-06-10 18:31:11,408 - prism_main - INFO - 构建空间结构...
2025-06-10 18:31:11,408 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:31:11,410 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:31:11,950 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\plots\delaunay_triangulation.png
2025-06-10 18:31:11,951 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:31:11,951 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:31:11,956 - prism_main - INFO - 共有 232 个时间点需要处理
2025-06-10 18:31:11,956 - parallel_processing - INFO - 开始并行插值处理，共 232 个时间点
2025-06-10 18:31:15,443 - parallel_processing - INFO - 并行插值完成，成功处理 232 个时间点
2025-06-10 18:31:15,674 - prism_main - INFO - 插值计算完成
2025-06-10 18:31:15,674 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:31:15,675 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:31:15,710 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\evaluation\evaluation_report_20250610_183115.txt
2025-06-10 18:31:15,715 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\evaluation\detailed_metrics_20250610_183115.csv
2025-06-10 18:31:16,034 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\plots\scatter_plot_20250610_183115.png
2025-06-10 18:31:16,035 - prism_main - INFO - 保存插值结果...
2025-06-10 18:31:16,085 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\points
2025-06-10 18:31:16,085 - prism_main - INFO - 保存权重信息...
2025-06-10 18:31:16,132 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\evaluation\weights_20250610_183116.csv
2025-06-10 18:31:16,768 - prism_main - INFO - 内存清理完成
2025-06-10 18:31:16,770 - prism_main - INFO - ============================================================
2025-06-10 18:31:16,770 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.93 秒
2025-06-10 18:31:16,770 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2014-5
2025-06-10 18:31:16,770 - prism_main - INFO - ============================================================
2025-06-10 18:31:16,771 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:31:16,771 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:31:16,774 - prism_main - INFO - ============================================================
2025-06-10 18:31:16,774 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:31:16,774 - prism_main - INFO - ============================================================
2025-06-10 18:31:16,775 - prism_main - INFO - 开始加载数据...
2025-06-10 18:31:16,775 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:31:16,776 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:31:16,776 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:31:17,113 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:31:17,113 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:31:17,120 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:31:17,120 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:31:17,168 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:31:17,359 - prism_main - INFO - 数据加载完成
2025-06-10 18:31:17,360 - prism_main - INFO - 构建空间结构...
2025-06-10 18:31:17,360 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:31:17,362 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:31:17,910 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\plots\delaunay_triangulation.png
2025-06-10 18:31:17,910 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:31:17,910 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:31:17,920 - prism_main - INFO - 共有 547 个时间点需要处理
2025-06-10 18:31:17,920 - parallel_processing - INFO - 开始并行插值处理，共 547 个时间点
2025-06-10 18:31:22,940 - parallel_processing - INFO - 并行插值完成，成功处理 547 个时间点
2025-06-10 18:31:23,175 - prism_main - INFO - 插值计算完成
2025-06-10 18:31:23,177 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:31:23,177 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:31:23,247 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\evaluation\evaluation_report_20250610_183123.txt
2025-06-10 18:31:23,254 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\evaluation\detailed_metrics_20250610_183123.csv
2025-06-10 18:31:23,656 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\plots\scatter_plot_20250610_183123.png
2025-06-10 18:31:23,658 - prism_main - INFO - 保存插值结果...
2025-06-10 18:31:23,737 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\points
2025-06-10 18:31:23,737 - prism_main - INFO - 保存权重信息...
2025-06-10 18:31:23,826 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\evaluation\weights_20250610_183123.csv
2025-06-10 18:31:24,481 - prism_main - INFO - 内存清理完成
2025-06-10 18:31:24,481 - prism_main - INFO - ============================================================
2025-06-10 18:31:24,482 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.71 秒
2025-06-10 18:31:24,482 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2015-1
2025-06-10 18:31:24,482 - prism_main - INFO - ============================================================
2025-06-10 18:31:24,493 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:31:24,493 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:31:24,497 - prism_main - INFO - ============================================================
2025-06-10 18:31:24,497 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:31:24,497 - prism_main - INFO - ============================================================
2025-06-10 18:31:24,497 - prism_main - INFO - 开始加载数据...
2025-06-10 18:31:24,498 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:31:24,499 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:31:24,499 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:31:24,834 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:31:24,834 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:31:24,840 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:31:24,840 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:31:24,885 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:31:25,084 - prism_main - INFO - 数据加载完成
2025-06-10 18:31:25,085 - prism_main - INFO - 构建空间结构...
2025-06-10 18:31:25,085 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:31:25,087 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:31:25,617 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\plots\delaunay_triangulation.png
2025-06-10 18:31:25,618 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:31:25,618 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:31:25,625 - prism_main - INFO - 共有 401 个时间点需要处理
2025-06-10 18:31:25,625 - parallel_processing - INFO - 开始并行插值处理，共 401 个时间点
2025-06-10 18:31:30,033 - parallel_processing - INFO - 并行插值完成，成功处理 401 个时间点
2025-06-10 18:31:30,274 - prism_main - INFO - 插值计算完成
2025-06-10 18:31:30,274 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:31:30,274 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:31:30,323 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\evaluation\evaluation_report_20250610_183130.txt
2025-06-10 18:31:30,329 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\evaluation\detailed_metrics_20250610_183130.csv
2025-06-10 18:31:30,690 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\plots\scatter_plot_20250610_183130.png
2025-06-10 18:31:30,691 - prism_main - INFO - 保存插值结果...
2025-06-10 18:31:30,756 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\points
2025-06-10 18:31:30,756 - prism_main - INFO - 保存权重信息...
2025-06-10 18:31:30,829 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\evaluation\weights_20250610_183130.csv
2025-06-10 18:31:31,496 - prism_main - INFO - 内存清理完成
2025-06-10 18:31:31,496 - prism_main - INFO - ============================================================
2025-06-10 18:31:31,497 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.00 秒
2025-06-10 18:31:31,497 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2015-2
2025-06-10 18:31:31,497 - prism_main - INFO - ============================================================
2025-06-10 18:31:31,499 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:31:31,499 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:31:31,503 - prism_main - INFO - ============================================================
2025-06-10 18:31:31,503 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:31:31,503 - prism_main - INFO - ============================================================
2025-06-10 18:31:31,503 - prism_main - INFO - 开始加载数据...
2025-06-10 18:31:31,504 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:31:31,505 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:31:31,505 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:31:31,845 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:31:31,845 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:31:31,852 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:31:31,852 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:31:31,895 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:31:32,106 - prism_main - INFO - 数据加载完成
2025-06-10 18:31:32,106 - prism_main - INFO - 构建空间结构...
2025-06-10 18:31:32,107 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:31:32,110 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:31:32,640 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\plots\delaunay_triangulation.png
2025-06-10 18:31:32,640 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:31:32,641 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:31:32,646 - prism_main - INFO - 共有 326 个时间点需要处理
2025-06-10 18:31:32,646 - parallel_processing - INFO - 开始并行插值处理，共 326 个时间点
2025-06-10 18:31:36,478 - parallel_processing - INFO - 并行插值完成，成功处理 326 个时间点
2025-06-10 18:31:36,724 - prism_main - INFO - 插值计算完成
2025-06-10 18:31:36,724 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:31:36,724 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:31:36,769 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\evaluation\evaluation_report_20250610_183136.txt
2025-06-10 18:31:36,774 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\evaluation\detailed_metrics_20250610_183136.csv
2025-06-10 18:31:37,136 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\plots\scatter_plot_20250610_183136.png
2025-06-10 18:31:37,137 - prism_main - INFO - 保存插值结果...
2025-06-10 18:31:37,196 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\points
2025-06-10 18:31:37,196 - prism_main - INFO - 保存权重信息...
2025-06-10 18:31:37,246 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\evaluation\weights_20250610_183137.csv
2025-06-10 18:31:37,937 - prism_main - INFO - 内存清理完成
2025-06-10 18:31:37,937 - prism_main - INFO - ============================================================
2025-06-10 18:31:37,938 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.43 秒
2025-06-10 18:31:37,938 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2015-3
2025-06-10 18:31:37,938 - prism_main - INFO - ============================================================
2025-06-10 18:31:37,949 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:31:37,949 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:31:37,952 - prism_main - INFO - ============================================================
2025-06-10 18:31:37,952 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:31:37,953 - prism_main - INFO - ============================================================
2025-06-10 18:31:37,953 - prism_main - INFO - 开始加载数据...
2025-06-10 18:31:37,953 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:31:37,956 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:31:37,956 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:31:38,288 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:31:38,288 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:31:38,294 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:31:38,294 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:31:38,338 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:31:38,550 - prism_main - INFO - 数据加载完成
2025-06-10 18:31:38,550 - prism_main - INFO - 构建空间结构...
2025-06-10 18:31:38,550 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:31:38,553 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:31:39,082 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\plots\delaunay_triangulation.png
2025-06-10 18:31:39,083 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:31:39,083 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:31:39,089 - prism_main - INFO - 共有 361 个时间点需要处理
2025-06-10 18:31:39,089 - parallel_processing - INFO - 开始并行插值处理，共 361 个时间点
2025-06-10 18:31:43,198 - parallel_processing - INFO - 并行插值完成，成功处理 361 个时间点
2025-06-10 18:31:43,458 - prism_main - INFO - 插值计算完成
2025-06-10 18:31:43,459 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:31:43,459 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:31:43,508 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\evaluation\evaluation_report_20250610_183143.txt
2025-06-10 18:31:43,515 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\evaluation\detailed_metrics_20250610_183143.csv
2025-06-10 18:31:43,858 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\plots\scatter_plot_20250610_183143.png
2025-06-10 18:31:43,859 - prism_main - INFO - 保存插值结果...
2025-06-10 18:31:43,920 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\points
2025-06-10 18:31:43,921 - prism_main - INFO - 保存权重信息...
2025-06-10 18:31:43,983 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\evaluation\weights_20250610_183143.csv
2025-06-10 18:31:44,693 - prism_main - INFO - 内存清理完成
2025-06-10 18:31:44,693 - prism_main - INFO - ============================================================
2025-06-10 18:31:44,693 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.74 秒
2025-06-10 18:31:44,693 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2015-4
2025-06-10 18:31:44,693 - prism_main - INFO - ============================================================
2025-06-10 18:31:44,695 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:31:44,695 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:31:44,698 - prism_main - INFO - ============================================================
2025-06-10 18:31:44,698 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:31:44,698 - prism_main - INFO - ============================================================
2025-06-10 18:31:44,698 - prism_main - INFO - 开始加载数据...
2025-06-10 18:31:44,699 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:31:44,700 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:31:44,700 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:31:45,033 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:31:45,033 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:31:45,040 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:31:45,040 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:31:45,078 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:31:45,301 - prism_main - INFO - 数据加载完成
2025-06-10 18:31:45,302 - prism_main - INFO - 构建空间结构...
2025-06-10 18:31:45,302 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:31:45,304 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:31:45,839 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\plots\delaunay_triangulation.png
2025-06-10 18:31:45,840 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:31:45,840 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:31:45,843 - prism_main - INFO - 共有 85 个时间点需要处理
2025-06-10 18:31:45,843 - parallel_processing - INFO - 开始并行插值处理，共 85 个时间点
2025-06-10 18:31:48,403 - parallel_processing - INFO - 并行插值完成，成功处理 85 个时间点
2025-06-10 18:31:48,649 - prism_main - INFO - 插值计算完成
2025-06-10 18:31:48,650 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:31:48,650 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:31:48,664 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\evaluation\evaluation_report_20250610_183148.txt
2025-06-10 18:31:48,668 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\evaluation\detailed_metrics_20250610_183148.csv
2025-06-10 18:31:48,947 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\plots\scatter_plot_20250610_183148.png
2025-06-10 18:31:48,948 - prism_main - INFO - 保存插值结果...
2025-06-10 18:31:48,987 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\points
2025-06-10 18:31:48,987 - prism_main - INFO - 保存权重信息...
2025-06-10 18:31:49,006 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\evaluation\weights_20250610_183148.csv
2025-06-10 18:31:49,707 - prism_main - INFO - 内存清理完成
2025-06-10 18:31:49,707 - prism_main - INFO - ============================================================
2025-06-10 18:31:49,707 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.01 秒
2025-06-10 18:31:49,707 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2017-1
2025-06-10 18:31:49,707 - prism_main - INFO - ============================================================
2025-06-10 18:31:49,719 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:31:49,719 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:31:49,721 - prism_main - INFO - ============================================================
2025-06-10 18:31:49,721 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:31:49,721 - prism_main - INFO - ============================================================
2025-06-10 18:31:49,721 - prism_main - INFO - 开始加载数据...
2025-06-10 18:31:49,721 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:31:49,724 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:31:49,724 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:31:50,058 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:31:50,059 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:31:50,064 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:31:50,064 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:31:50,110 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:31:50,339 - prism_main - INFO - 数据加载完成
2025-06-10 18:31:50,339 - prism_main - INFO - 构建空间结构...
2025-06-10 18:31:50,339 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:31:50,343 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:31:50,886 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\plots\delaunay_triangulation.png
2025-06-10 18:31:50,887 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:31:50,887 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:31:50,895 - prism_main - INFO - 共有 443 个时间点需要处理
2025-06-10 18:31:50,895 - parallel_processing - INFO - 开始并行插值处理，共 443 个时间点
2025-06-10 18:31:55,676 - parallel_processing - INFO - 并行插值完成，成功处理 443 个时间点
2025-06-10 18:31:55,950 - prism_main - INFO - 插值计算完成
2025-06-10 18:31:55,950 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:31:55,951 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:31:56,017 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\evaluation\evaluation_report_20250610_183156.txt
2025-06-10 18:31:56,025 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\evaluation\detailed_metrics_20250610_183156.csv
2025-06-10 18:31:56,403 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\plots\scatter_plot_20250610_183156.png
2025-06-10 18:31:56,405 - prism_main - INFO - 保存插值结果...
2025-06-10 18:31:56,477 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\points
2025-06-10 18:31:56,477 - prism_main - INFO - 保存权重信息...
2025-06-10 18:31:56,565 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\evaluation\weights_20250610_183156.csv
2025-06-10 18:31:57,358 - prism_main - INFO - 内存清理完成
2025-06-10 18:31:57,359 - prism_main - INFO - ============================================================
2025-06-10 18:31:57,359 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.64 秒
2025-06-10 18:31:57,359 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2017-2
2025-06-10 18:31:57,359 - prism_main - INFO - ============================================================
2025-06-10 18:31:57,360 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:31:57,361 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:31:57,365 - prism_main - INFO - ============================================================
2025-06-10 18:31:57,365 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:31:57,366 - prism_main - INFO - ============================================================
2025-06-10 18:31:57,366 - prism_main - INFO - 开始加载数据...
2025-06-10 18:31:57,366 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:31:57,367 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:31:57,368 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:31:57,707 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:31:57,708 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:31:57,713 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:31:57,714 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:31:57,759 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:31:58,001 - prism_main - INFO - 数据加载完成
2025-06-10 18:31:58,001 - prism_main - INFO - 构建空间结构...
2025-06-10 18:31:58,001 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:31:58,004 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:31:58,533 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\plots\delaunay_triangulation.png
2025-06-10 18:31:58,533 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:31:58,533 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:31:58,539 - prism_main - INFO - 共有 349 个时间点需要处理
2025-06-10 18:31:58,540 - parallel_processing - INFO - 开始并行插值处理，共 349 个时间点
2025-06-10 18:32:02,518 - parallel_processing - INFO - 并行插值完成，成功处理 349 个时间点
2025-06-10 18:32:02,798 - prism_main - INFO - 插值计算完成
2025-06-10 18:32:02,799 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:32:02,799 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:32:02,853 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\evaluation\evaluation_report_20250610_183202.txt
2025-06-10 18:32:02,859 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\evaluation\detailed_metrics_20250610_183202.csv
2025-06-10 18:32:03,234 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\plots\scatter_plot_20250610_183202.png
2025-06-10 18:32:03,235 - prism_main - INFO - 保存插值结果...
2025-06-10 18:32:03,298 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\points
2025-06-10 18:32:03,299 - prism_main - INFO - 保存权重信息...
2025-06-10 18:32:03,355 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\evaluation\weights_20250610_183203.csv
2025-06-10 18:32:04,137 - prism_main - INFO - 内存清理完成
2025-06-10 18:32:04,138 - prism_main - INFO - ============================================================
2025-06-10 18:32:04,138 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.77 秒
2025-06-10 18:32:04,138 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2018-1
2025-06-10 18:32:04,138 - prism_main - INFO - ============================================================
2025-06-10 18:32:04,140 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:32:04,140 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:32:04,143 - prism_main - INFO - ============================================================
2025-06-10 18:32:04,143 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:32:04,143 - prism_main - INFO - ============================================================
2025-06-10 18:32:04,143 - prism_main - INFO - 开始加载数据...
2025-06-10 18:32:04,144 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:32:04,145 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:32:04,146 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:32:04,476 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:32:04,476 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:32:04,483 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:32:04,483 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:32:04,524 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:32:04,768 - prism_main - INFO - 数据加载完成
2025-06-10 18:32:04,768 - prism_main - INFO - 构建空间结构...
2025-06-10 18:32:04,768 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:32:04,771 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:32:05,306 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\plots\delaunay_triangulation.png
2025-06-10 18:32:05,307 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:32:05,307 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:32:05,312 - prism_main - INFO - 共有 235 个时间点需要处理
2025-06-10 18:32:05,312 - parallel_processing - INFO - 开始并行插值处理，共 235 个时间点
2025-06-10 18:32:08,636 - parallel_processing - INFO - 并行插值完成，成功处理 235 个时间点
2025-06-10 18:32:08,914 - prism_main - INFO - 插值计算完成
2025-06-10 18:32:08,915 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:32:08,915 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:32:08,940 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\evaluation\evaluation_report_20250610_183208.txt
2025-06-10 18:32:08,945 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\evaluation\detailed_metrics_20250610_183208.csv
2025-06-10 18:32:09,262 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\plots\scatter_plot_20250610_183208.png
2025-06-10 18:32:09,264 - prism_main - INFO - 保存插值结果...
2025-06-10 18:32:09,314 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\points
2025-06-10 18:32:09,314 - prism_main - INFO - 保存权重信息...
2025-06-10 18:32:09,351 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\evaluation\weights_20250610_183209.csv
2025-06-10 18:32:10,142 - prism_main - INFO - 内存清理完成
2025-06-10 18:32:10,142 - prism_main - INFO - ============================================================
2025-06-10 18:32:10,142 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.00 秒
2025-06-10 18:32:10,142 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2019-1
2025-06-10 18:32:10,142 - prism_main - INFO - ============================================================
2025-06-10 18:32:10,143 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:32:10,143 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:32:10,146 - prism_main - INFO - ============================================================
2025-06-10 18:32:10,146 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:32:10,146 - prism_main - INFO - ============================================================
2025-06-10 18:32:10,147 - prism_main - INFO - 开始加载数据...
2025-06-10 18:32:10,147 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:32:10,148 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:32:10,149 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:32:10,485 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:32:10,485 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:32:10,491 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:32:10,491 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:32:10,532 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:32:10,786 - prism_main - INFO - 数据加载完成
2025-06-10 18:32:10,786 - prism_main - INFO - 构建空间结构...
2025-06-10 18:32:10,787 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:32:10,790 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:32:11,320 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\plots\delaunay_triangulation.png
2025-06-10 18:32:11,320 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:32:11,320 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:32:11,323 - prism_main - INFO - 共有 153 个时间点需要处理
2025-06-10 18:32:11,324 - parallel_processing - INFO - 开始并行插值处理，共 153 个时间点
2025-06-10 18:32:14,351 - parallel_processing - INFO - 并行插值完成，成功处理 153 个时间点
2025-06-10 18:32:14,658 - prism_main - INFO - 插值计算完成
2025-06-10 18:32:14,659 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:32:14,659 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:32:14,691 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\evaluation\evaluation_report_20250610_183214.txt
2025-06-10 18:32:14,695 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\evaluation\detailed_metrics_20250610_183214.csv
2025-06-10 18:32:15,007 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\plots\scatter_plot_20250610_183214.png
2025-06-10 18:32:15,008 - prism_main - INFO - 保存插值结果...
2025-06-10 18:32:15,052 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\points
2025-06-10 18:32:15,053 - prism_main - INFO - 保存权重信息...
2025-06-10 18:32:15,082 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\evaluation\weights_20250610_183215.csv
2025-06-10 18:32:15,885 - prism_main - INFO - 内存清理完成
2025-06-10 18:32:15,885 - prism_main - INFO - ============================================================
2025-06-10 18:32:15,885 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.74 秒
2025-06-10 18:32:15,885 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2019-2
2025-06-10 18:32:15,885 - prism_main - INFO - ============================================================
2025-06-10 18:32:15,887 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:32:15,888 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:32:15,889 - prism_main - INFO - ============================================================
2025-06-10 18:32:15,890 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:32:15,890 - prism_main - INFO - ============================================================
2025-06-10 18:32:15,890 - prism_main - INFO - 开始加载数据...
2025-06-10 18:32:15,890 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:32:15,892 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:32:15,892 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:32:16,227 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:32:16,227 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:32:16,234 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:32:16,234 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:32:16,276 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:32:16,531 - prism_main - INFO - 数据加载完成
2025-06-10 18:32:16,532 - prism_main - INFO - 构建空间结构...
2025-06-10 18:32:16,532 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:32:16,534 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:32:17,072 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\plots\delaunay_triangulation.png
2025-06-10 18:32:17,072 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:32:17,072 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:32:17,078 - prism_main - INFO - 共有 284 个时间点需要处理
2025-06-10 18:32:17,078 - parallel_processing - INFO - 开始并行插值处理，共 284 个时间点
2025-06-10 18:32:20,847 - parallel_processing - INFO - 并行插值完成，成功处理 284 个时间点
2025-06-10 18:32:21,153 - prism_main - INFO - 插值计算完成
2025-06-10 18:32:21,153 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:32:21,153 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:32:21,193 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\evaluation\evaluation_report_20250610_183221.txt
2025-06-10 18:32:21,199 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\evaluation\detailed_metrics_20250610_183221.csv
2025-06-10 18:32:21,522 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\plots\scatter_plot_20250610_183221.png
2025-06-10 18:32:21,523 - prism_main - INFO - 保存插值结果...
2025-06-10 18:32:21,577 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\points
2025-06-10 18:32:21,579 - prism_main - INFO - 保存权重信息...
2025-06-10 18:32:21,634 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\evaluation\weights_20250610_183221.csv
2025-06-10 18:32:22,537 - prism_main - INFO - 内存清理完成
2025-06-10 18:32:22,538 - prism_main - INFO - ============================================================
2025-06-10 18:32:22,538 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.65 秒
2025-06-10 18:32:22,538 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2019-3
2025-06-10 18:32:22,538 - prism_main - INFO - ============================================================
2025-06-10 18:32:22,540 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:32:22,540 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:32:22,543 - prism_main - INFO - ============================================================
2025-06-10 18:32:22,543 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:32:22,543 - prism_main - INFO - ============================================================
2025-06-10 18:32:22,543 - prism_main - INFO - 开始加载数据...
2025-06-10 18:32:22,543 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:32:22,545 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:32:22,545 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:32:22,889 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:32:22,889 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:32:22,895 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:32:22,895 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:32:22,939 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:32:23,209 - prism_main - INFO - 数据加载完成
2025-06-10 18:32:23,209 - prism_main - INFO - 构建空间结构...
2025-06-10 18:32:23,209 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:32:23,212 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:32:23,754 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\plots\delaunay_triangulation.png
2025-06-10 18:32:23,754 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:32:23,754 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:32:23,760 - prism_main - INFO - 共有 353 个时间点需要处理
2025-06-10 18:32:23,760 - parallel_processing - INFO - 开始并行插值处理，共 353 个时间点
2025-06-10 18:32:27,651 - parallel_processing - INFO - 并行插值完成，成功处理 353 个时间点
2025-06-10 18:32:27,996 - prism_main - INFO - 插值计算完成
2025-06-10 18:32:27,996 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:32:27,997 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:32:28,051 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\evaluation\evaluation_report_20250610_183228.txt
2025-06-10 18:32:28,057 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\evaluation\detailed_metrics_20250610_183228.csv
2025-06-10 18:32:28,415 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\plots\scatter_plot_20250610_183228.png
2025-06-10 18:32:28,418 - prism_main - INFO - 保存插值结果...
2025-06-10 18:32:28,486 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\points
2025-06-10 18:32:28,486 - prism_main - INFO - 保存权重信息...
2025-06-10 18:32:28,545 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\evaluation\weights_20250610_183228.csv
2025-06-10 18:32:29,431 - prism_main - INFO - 内存清理完成
2025-06-10 18:32:29,431 - prism_main - INFO - ============================================================
2025-06-10 18:32:29,432 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.89 秒
2025-06-10 18:32:29,432 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2019-4
2025-06-10 18:32:29,432 - prism_main - INFO - ============================================================
2025-06-10 18:32:29,434 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:32:29,434 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:32:29,436 - prism_main - INFO - ============================================================
2025-06-10 18:32:29,436 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:32:29,437 - prism_main - INFO - ============================================================
2025-06-10 18:32:29,437 - prism_main - INFO - 开始加载数据...
2025-06-10 18:32:29,437 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:32:29,438 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:32:29,439 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:32:29,789 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:32:29,789 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:32:29,795 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:32:29,796 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:32:29,837 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:32:30,117 - prism_main - INFO - 数据加载完成
2025-06-10 18:32:30,117 - prism_main - INFO - 构建空间结构...
2025-06-10 18:32:30,117 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:32:30,120 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:32:30,650 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\plots\delaunay_triangulation.png
2025-06-10 18:32:30,651 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:32:30,651 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:32:30,656 - prism_main - INFO - 共有 254 个时间点需要处理
2025-06-10 18:32:30,656 - parallel_processing - INFO - 开始并行插值处理，共 254 个时间点
2025-06-10 18:32:34,052 - parallel_processing - INFO - 并行插值完成，成功处理 254 个时间点
2025-06-10 18:32:34,361 - prism_main - INFO - 插值计算完成
2025-06-10 18:32:34,361 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:32:34,361 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:32:34,387 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\evaluation\evaluation_report_20250610_183234.txt
2025-06-10 18:32:34,391 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\evaluation\detailed_metrics_20250610_183234.csv
2025-06-10 18:32:34,729 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\plots\scatter_plot_20250610_183234.png
2025-06-10 18:32:34,731 - prism_main - INFO - 保存插值结果...
2025-06-10 18:32:34,782 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\points
2025-06-10 18:32:34,782 - prism_main - INFO - 保存权重信息...
2025-06-10 18:32:34,821 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\evaluation\weights_20250610_183234.csv
2025-06-10 18:32:35,740 - prism_main - INFO - 内存清理完成
2025-06-10 18:32:35,741 - prism_main - INFO - ============================================================
2025-06-10 18:32:35,741 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.30 秒
2025-06-10 18:32:35,741 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2020-1
2025-06-10 18:32:35,742 - prism_main - INFO - ============================================================
2025-06-10 18:32:35,743 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:32:35,743 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:32:35,745 - prism_main - INFO - ============================================================
2025-06-10 18:32:35,745 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:32:35,745 - prism_main - INFO - ============================================================
2025-06-10 18:32:35,745 - prism_main - INFO - 开始加载数据...
2025-06-10 18:32:35,745 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:32:35,747 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:32:35,747 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:32:36,087 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:32:36,087 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:32:36,094 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:32:36,094 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:32:36,136 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:32:36,424 - prism_main - INFO - 数据加载完成
2025-06-10 18:32:36,424 - prism_main - INFO - 构建空间结构...
2025-06-10 18:32:36,424 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:32:36,427 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:32:36,965 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\plots\delaunay_triangulation.png
2025-06-10 18:32:36,966 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:32:36,966 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:32:36,971 - prism_main - INFO - 共有 337 个时间点需要处理
2025-06-10 18:32:36,972 - parallel_processing - INFO - 开始并行插值处理，共 337 个时间点
2025-06-10 18:32:40,984 - parallel_processing - INFO - 并行插值完成，成功处理 337 个时间点
2025-06-10 18:32:41,310 - prism_main - INFO - 插值计算完成
2025-06-10 18:32:41,310 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:32:41,311 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:32:41,361 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\evaluation\evaluation_report_20250610_183241.txt
2025-06-10 18:32:41,367 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\evaluation\detailed_metrics_20250610_183241.csv
2025-06-10 18:32:41,703 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\plots\scatter_plot_20250610_183241.png
2025-06-10 18:32:41,705 - prism_main - INFO - 保存插值结果...
2025-06-10 18:32:41,766 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\points
2025-06-10 18:32:41,766 - prism_main - INFO - 保存权重信息...
2025-06-10 18:32:41,826 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\evaluation\weights_20250610_183241.csv
2025-06-10 18:32:42,734 - prism_main - INFO - 内存清理完成
2025-06-10 18:32:42,734 - prism_main - INFO - ============================================================
2025-06-10 18:32:42,734 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.99 秒
2025-06-10 18:32:42,734 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2020-2
2025-06-10 18:32:42,734 - prism_main - INFO - ============================================================
2025-06-10 18:32:42,735 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:32:42,735 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:32:42,738 - prism_main - INFO - ============================================================
2025-06-10 18:32:42,738 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:32:42,739 - prism_main - INFO - ============================================================
2025-06-10 18:32:42,739 - prism_main - INFO - 开始加载数据...
2025-06-10 18:32:42,739 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:32:42,741 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:32:42,741 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:32:43,076 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:32:43,076 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:32:43,082 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:32:43,082 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:32:43,125 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:32:43,416 - prism_main - INFO - 数据加载完成
2025-06-10 18:32:43,416 - prism_main - INFO - 构建空间结构...
2025-06-10 18:32:43,416 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:32:43,419 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:32:43,953 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\plots\delaunay_triangulation.png
2025-06-10 18:32:43,954 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:32:43,954 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:32:43,960 - prism_main - INFO - 共有 390 个时间点需要处理
2025-06-10 18:32:43,960 - parallel_processing - INFO - 开始并行插值处理，共 390 个时间点
2025-06-10 18:32:48,168 - parallel_processing - INFO - 并行插值完成，成功处理 390 个时间点
2025-06-10 18:32:48,489 - prism_main - INFO - 插值计算完成
2025-06-10 18:32:48,489 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:32:48,489 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:32:48,538 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\evaluation\evaluation_report_20250610_183248.txt
2025-06-10 18:32:48,544 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\evaluation\detailed_metrics_20250610_183248.csv
2025-06-10 18:32:48,900 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\plots\scatter_plot_20250610_183248.png
2025-06-10 18:32:48,902 - prism_main - INFO - 保存插值结果...
2025-06-10 18:32:48,965 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\points
2025-06-10 18:32:48,966 - prism_main - INFO - 保存权重信息...
2025-06-10 18:32:49,030 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\evaluation\weights_20250610_183248.csv
2025-06-10 18:32:49,966 - prism_main - INFO - 内存清理完成
2025-06-10 18:32:49,966 - prism_main - INFO - ============================================================
2025-06-10 18:32:49,966 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.23 秒
2025-06-10 18:32:49,966 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2020-3
2025-06-10 18:32:49,966 - prism_main - INFO - ============================================================
2025-06-10 18:32:49,968 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:32:49,968 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:32:49,971 - prism_main - INFO - ============================================================
2025-06-10 18:32:49,971 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:32:49,971 - prism_main - INFO - ============================================================
2025-06-10 18:32:49,971 - prism_main - INFO - 开始加载数据...
2025-06-10 18:32:49,972 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:32:49,973 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:32:49,973 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:32:50,317 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:32:50,317 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:32:50,323 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:32:50,323 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:32:50,369 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:32:50,665 - prism_main - INFO - 数据加载完成
2025-06-10 18:32:50,665 - prism_main - INFO - 构建空间结构...
2025-06-10 18:32:50,665 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:32:50,668 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:32:51,210 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\plots\delaunay_triangulation.png
2025-06-10 18:32:51,210 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:32:51,211 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:32:51,217 - prism_main - INFO - 共有 429 个时间点需要处理
2025-06-10 18:32:51,218 - parallel_processing - INFO - 开始并行插值处理，共 429 个时间点
2025-06-10 18:32:55,608 - parallel_processing - INFO - 并行插值完成，成功处理 429 个时间点
2025-06-10 18:32:55,983 - prism_main - INFO - 插值计算完成
2025-06-10 18:32:55,983 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:32:55,984 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:32:56,034 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\evaluation\evaluation_report_20250610_183256.txt
2025-06-10 18:32:56,042 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\evaluation\detailed_metrics_20250610_183256.csv
2025-06-10 18:32:56,452 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\plots\scatter_plot_20250610_183256.png
2025-06-10 18:32:56,453 - prism_main - INFO - 保存插值结果...
2025-06-10 18:32:56,521 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\points
2025-06-10 18:32:56,522 - prism_main - INFO - 保存权重信息...
2025-06-10 18:32:56,586 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\evaluation\weights_20250610_183256.csv
2025-06-10 18:32:57,573 - prism_main - INFO - 内存清理完成
2025-06-10 18:32:57,574 - prism_main - INFO - ============================================================
2025-06-10 18:32:57,574 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.60 秒
2025-06-10 18:32:57,574 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2020-4
2025-06-10 18:32:57,574 - prism_main - INFO - ============================================================
2025-06-10 18:32:57,585 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:32:57,585 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:32:57,589 - prism_main - INFO - ============================================================
2025-06-10 18:32:57,589 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:32:57,590 - prism_main - INFO - ============================================================
2025-06-10 18:32:57,590 - prism_main - INFO - 开始加载数据...
2025-06-10 18:32:57,590 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:32:57,592 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:32:57,592 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:32:57,925 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:32:57,925 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:32:57,931 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:32:57,931 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:32:57,970 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:32:58,286 - prism_main - INFO - 数据加载完成
2025-06-10 18:32:58,287 - prism_main - INFO - 构建空间结构...
2025-06-10 18:32:58,287 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:32:58,290 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:32:58,837 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\plots\delaunay_triangulation.png
2025-06-10 18:32:58,840 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:32:58,840 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:32:58,844 - prism_main - INFO - 共有 143 个时间点需要处理
2025-06-10 18:32:58,845 - parallel_processing - INFO - 开始并行插值处理，共 143 个时间点
2025-06-10 18:33:01,916 - parallel_processing - INFO - 并行插值完成，成功处理 143 个时间点
2025-06-10 18:33:02,266 - prism_main - INFO - 插值计算完成
2025-06-10 18:33:02,266 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:33:02,267 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:33:02,284 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\evaluation\evaluation_report_20250610_183302.txt
2025-06-10 18:33:02,287 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\evaluation\detailed_metrics_20250610_183302.csv
2025-06-10 18:33:02,597 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\plots\scatter_plot_20250610_183302.png
2025-06-10 18:33:02,599 - prism_main - INFO - 保存插值结果...
2025-06-10 18:33:02,640 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\points
2025-06-10 18:33:02,640 - prism_main - INFO - 保存权重信息...
2025-06-10 18:33:02,666 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\evaluation\weights_20250610_183302.csv
2025-06-10 18:33:03,617 - prism_main - INFO - 内存清理完成
2025-06-10 18:33:03,617 - prism_main - INFO - ============================================================
2025-06-10 18:33:03,618 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.03 秒
2025-06-10 18:33:03,618 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2021-1
2025-06-10 18:33:03,618 - prism_main - INFO - ============================================================
2025-06-10 18:33:03,619 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:33:03,619 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:33:03,621 - prism_main - INFO - ============================================================
2025-06-10 18:33:03,621 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:33:03,621 - prism_main - INFO - ============================================================
2025-06-10 18:33:03,621 - prism_main - INFO - 开始加载数据...
2025-06-10 18:33:03,621 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:33:03,623 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:33:03,623 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:33:03,956 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:33:03,956 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:33:03,962 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:33:03,962 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:33:04,006 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:33:04,324 - prism_main - INFO - 数据加载完成
2025-06-10 18:33:04,324 - prism_main - INFO - 构建空间结构...
2025-06-10 18:33:04,324 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:33:04,327 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:33:04,873 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\plots\delaunay_triangulation.png
2025-06-10 18:33:04,873 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:33:04,873 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:33:04,879 - prism_main - INFO - 共有 334 个时间点需要处理
2025-06-10 18:33:04,879 - parallel_processing - INFO - 开始并行插值处理，共 334 个时间点
2025-06-10 18:33:08,898 - parallel_processing - INFO - 并行插值完成，成功处理 334 个时间点
2025-06-10 18:33:09,322 - prism_main - INFO - 插值计算完成
2025-06-10 18:33:09,322 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:33:09,322 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:33:09,363 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\evaluation\evaluation_report_20250610_183309.txt
2025-06-10 18:33:09,370 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\evaluation\detailed_metrics_20250610_183309.csv
2025-06-10 18:33:09,711 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\plots\scatter_plot_20250610_183309.png
2025-06-10 18:33:09,712 - prism_main - INFO - 保存插值结果...
2025-06-10 18:33:09,772 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\points
2025-06-10 18:33:09,773 - prism_main - INFO - 保存权重信息...
2025-06-10 18:33:09,826 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\evaluation\weights_20250610_183309.csv
2025-06-10 18:33:09,956 - parallel_processing - INFO - 并行处理器初始化，使用 4 个核心
2025-06-10 18:33:09,956 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:33:09,956 - prism_main - INFO - 开始加载数据...
2025-06-10 18:33:09,957 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:33:09,959 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:33:09,959 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:33:10,308 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:33:10,308 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:33:10,315 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:33:10,316 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:33:10,361 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:33:10,405 - prism_main - INFO - 数据加载完成
2025-06-10 18:33:10,835 - prism_main - INFO - 内存清理完成
2025-06-10 18:33:10,835 - prism_main - INFO - ============================================================
2025-06-10 18:33:10,835 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.21 秒
2025-06-10 18:33:10,835 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2021-2
2025-06-10 18:33:10,835 - prism_main - INFO - ============================================================
2025-06-10 18:33:10,846 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:33:10,846 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:33:10,849 - prism_main - INFO - ============================================================
2025-06-10 18:33:10,849 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:33:10,849 - prism_main - INFO - ============================================================
2025-06-10 18:33:10,849 - prism_main - INFO - 开始加载数据...
2025-06-10 18:33:10,849 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:33:10,851 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:33:10,851 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:33:11,191 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:33:11,191 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:33:11,197 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:33:11,197 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:33:11,241 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:33:11,564 - prism_main - INFO - 数据加载完成
2025-06-10 18:33:11,565 - prism_main - INFO - 构建空间结构...
2025-06-10 18:33:11,565 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:33:11,568 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:33:12,104 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\plots\delaunay_triangulation.png
2025-06-10 18:33:12,104 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:33:12,104 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:33:12,110 - prism_main - INFO - 共有 355 个时间点需要处理
2025-06-10 18:33:12,110 - parallel_processing - INFO - 开始并行插值处理，共 355 个时间点
2025-06-10 18:33:16,454 - parallel_processing - INFO - 并行插值完成，成功处理 355 个时间点
2025-06-10 18:33:16,815 - prism_main - INFO - 插值计算完成
2025-06-10 18:33:16,815 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:33:16,816 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:33:16,879 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\evaluation\evaluation_report_20250610_183316.txt
2025-06-10 18:33:16,884 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\evaluation\detailed_metrics_20250610_183316.csv
2025-06-10 18:33:17,254 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\plots\scatter_plot_20250610_183316.png
2025-06-10 18:33:17,255 - prism_main - INFO - 保存插值结果...
2025-06-10 18:33:17,319 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\points
2025-06-10 18:33:17,320 - prism_main - INFO - 保存权重信息...
2025-06-10 18:33:17,392 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\evaluation\weights_20250610_183317.csv
2025-06-10 18:33:18,450 - prism_main - INFO - 内存清理完成
2025-06-10 18:33:18,451 - prism_main - INFO - ============================================================
2025-06-10 18:33:18,451 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.60 秒
2025-06-10 18:33:18,451 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2022-1
2025-06-10 18:33:18,451 - prism_main - INFO - ============================================================
2025-06-10 18:33:18,462 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:33:18,462 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:33:18,467 - prism_main - INFO - ============================================================
2025-06-10 18:33:18,467 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:33:18,467 - prism_main - INFO - ============================================================
2025-06-10 18:33:18,468 - prism_main - INFO - 开始加载数据...
2025-06-10 18:33:18,468 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:33:18,469 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:33:18,469 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:33:18,797 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:33:18,797 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:33:18,803 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:33:18,803 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:33:18,848 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:33:19,166 - prism_main - INFO - 数据加载完成
2025-06-10 18:33:19,166 - prism_main - INFO - 构建空间结构...
2025-06-10 18:33:19,167 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:33:19,170 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:33:19,692 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\plots\delaunay_triangulation.png
2025-06-10 18:33:19,692 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:33:19,692 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:33:19,700 - prism_main - INFO - 共有 541 个时间点需要处理
2025-06-10 18:33:19,700 - parallel_processing - INFO - 开始并行插值处理，共 541 个时间点
2025-06-10 18:33:25,161 - parallel_processing - INFO - 并行插值完成，成功处理 541 个时间点
2025-06-10 18:33:25,541 - prism_main - INFO - 插值计算完成
2025-06-10 18:33:25,542 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:33:25,542 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:33:25,627 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\evaluation\evaluation_report_20250610_183325.txt
2025-06-10 18:33:25,635 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\evaluation\detailed_metrics_20250610_183325.csv
2025-06-10 18:33:26,036 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\plots\scatter_plot_20250610_183325.png
2025-06-10 18:33:26,037 - prism_main - INFO - 保存插值结果...
2025-06-10 18:33:26,121 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\points
2025-06-10 18:33:26,121 - prism_main - INFO - 保存权重信息...
2025-06-10 18:33:26,230 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\evaluation\weights_20250610_183326.csv
2025-06-10 18:33:27,339 - prism_main - INFO - 内存清理完成
2025-06-10 18:33:27,340 - prism_main - INFO - ============================================================
2025-06-10 18:33:27,340 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 8.87 秒
2025-06-10 18:33:27,340 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2022-2
2025-06-10 18:33:27,340 - prism_main - INFO - ============================================================
2025-06-10 18:33:27,341 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:33:27,341 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:33:27,346 - prism_main - INFO - ============================================================
2025-06-10 18:33:27,346 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:33:27,347 - prism_main - INFO - ============================================================
2025-06-10 18:33:27,347 - prism_main - INFO - 开始加载数据...
2025-06-10 18:33:27,347 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:33:27,348 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:33:27,349 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:33:27,681 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:33:27,681 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:33:27,687 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:33:27,688 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:33:27,731 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:33:28,059 - prism_main - INFO - 数据加载完成
2025-06-10 18:33:28,060 - prism_main - INFO - 构建空间结构...
2025-06-10 18:33:28,063 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:33:28,066 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:33:28,660 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\plots\delaunay_triangulation.png
2025-06-10 18:33:28,660 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:33:28,660 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:33:28,668 - prism_main - INFO - 共有 432 个时间点需要处理
2025-06-10 18:33:28,669 - parallel_processing - INFO - 开始并行插值处理，共 432 个时间点
2025-06-10 18:33:33,529 - parallel_processing - INFO - 并行插值完成，成功处理 432 个时间点
2025-06-10 18:33:33,902 - prism_main - INFO - 插值计算完成
2025-06-10 18:33:33,903 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:33:33,903 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:33:33,964 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\evaluation\evaluation_report_20250610_183333.txt
2025-06-10 18:33:33,970 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\evaluation\detailed_metrics_20250610_183333.csv
2025-06-10 18:33:34,361 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\plots\scatter_plot_20250610_183333.png
2025-06-10 18:33:34,363 - prism_main - INFO - 保存插值结果...
2025-06-10 18:33:34,432 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\points
2025-06-10 18:33:34,432 - prism_main - INFO - 保存权重信息...
2025-06-10 18:33:34,507 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\evaluation\weights_20250610_183334.csv
2025-06-10 18:33:35,574 - prism_main - INFO - 内存清理完成
2025-06-10 18:33:35,575 - prism_main - INFO - ============================================================
2025-06-10 18:33:35,575 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 8.23 秒
2025-06-10 18:33:35,575 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2022-3
2025-06-10 18:33:35,575 - prism_main - INFO - ============================================================
2025-06-10 18:33:35,586 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:33:35,586 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:33:35,591 - prism_main - INFO - ============================================================
2025-06-10 18:33:35,591 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:33:35,591 - prism_main - INFO - ============================================================
2025-06-10 18:33:35,591 - prism_main - INFO - 开始加载数据...
2025-06-10 18:33:35,591 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:33:35,593 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:33:35,593 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:33:35,925 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:33:35,925 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:33:35,931 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:33:35,932 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:33:35,976 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:33:36,315 - prism_main - INFO - 数据加载完成
2025-06-10 18:33:36,315 - prism_main - INFO - 构建空间结构...
2025-06-10 18:33:36,315 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:33:36,319 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:33:36,847 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\plots\delaunay_triangulation.png
2025-06-10 18:33:36,848 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:33:36,848 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:33:36,856 - prism_main - INFO - 共有 463 个时间点需要处理
2025-06-10 18:33:36,856 - parallel_processing - INFO - 开始并行插值处理，共 463 个时间点
2025-06-10 18:33:41,585 - parallel_processing - INFO - 并行插值完成，成功处理 463 个时间点
2025-06-10 18:33:41,973 - prism_main - INFO - 插值计算完成
2025-06-10 18:33:41,973 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:33:41,974 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:33:42,040 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\evaluation\evaluation_report_20250610_183342.txt
2025-06-10 18:33:42,047 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\evaluation\detailed_metrics_20250610_183342.csv
2025-06-10 18:33:42,450 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\plots\scatter_plot_20250610_183342.png
2025-06-10 18:33:42,451 - prism_main - INFO - 保存插值结果...
2025-06-10 18:33:42,522 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\points
2025-06-10 18:33:42,523 - prism_main - INFO - 保存权重信息...
2025-06-10 18:33:42,609 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\evaluation\weights_20250610_183342.csv
2025-06-10 18:33:43,724 - prism_main - INFO - 内存清理完成
2025-06-10 18:33:43,724 - prism_main - INFO - ============================================================
2025-06-10 18:33:43,725 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 8.13 秒
2025-06-10 18:33:43,725 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2023-1
2025-06-10 18:33:43,725 - prism_main - INFO - ============================================================
2025-06-10 18:33:43,741 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:33:43,741 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:33:43,741 - prism_main - INFO - ============================================================
2025-06-10 18:33:43,741 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:33:43,741 - prism_main - INFO - ============================================================
2025-06-10 18:33:43,742 - prism_main - INFO - 开始加载数据...
2025-06-10 18:33:43,742 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:33:43,743 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:33:43,743 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:33:44,083 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:33:44,083 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:33:44,089 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:33:44,090 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:33:44,132 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:33:44,504 - prism_main - INFO - 数据加载完成
2025-06-10 18:33:44,504 - prism_main - INFO - 构建空间结构...
2025-06-10 18:33:44,504 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:33:44,507 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:33:44,508 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:33:44,508 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:33:44,513 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:33:44,514 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:33:48,635 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:33:49,028 - prism_main - INFO - 插值计算完成
2025-06-10 18:33:49,028 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:33:49,029 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:33:49,066 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1\evaluation\evaluation_report_20250610_183349.txt
2025-06-10 18:33:49,071 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1\evaluation\detailed_metrics_20250610_183349.csv
2025-06-10 18:33:49,434 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1\plots\scatter_plot_20250610_183349.png
2025-06-10 18:33:49,436 - prism_main - INFO - 保存插值结果...
2025-06-10 18:33:49,499 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1\points
2025-06-10 18:33:49,500 - prism_main - INFO - 保存权重信息...
2025-06-10 18:33:49,572 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1\evaluation\weights_20250610_183349.csv
2025-06-10 18:33:50,693 - prism_main - INFO - 内存清理完成
2025-06-10 18:33:50,694 - prism_main - INFO - ============================================================
2025-06-10 18:33:50,694 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.95 秒
2025-06-10 18:33:50,694 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1
2025-06-10 18:33:50,694 - prism_main - INFO - ============================================================
2025-06-10 18:33:50,709 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:33:50,709 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:33:50,713 - prism_main - INFO - ============================================================
2025-06-10 18:33:50,713 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:33:50,713 - prism_main - INFO - ============================================================
2025-06-10 18:33:50,714 - prism_main - INFO - 开始加载数据...
2025-06-10 18:33:50,714 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:33:50,716 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:33:50,716 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:33:51,049 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:33:51,049 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:33:51,055 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:33:51,055 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:33:51,096 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:33:51,460 - prism_main - INFO - 数据加载完成
2025-06-10 18:33:51,460 - prism_main - INFO - 构建空间结构...
2025-06-10 18:33:51,461 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:33:51,464 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:33:51,464 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:33:51,464 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:33:51,470 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:33:51,470 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:33:55,578 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:33:55,973 - prism_main - INFO - 插值计算完成
2025-06-10 18:33:55,973 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:33:55,974 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:33:56,013 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2\evaluation\evaluation_report_20250610_183356.txt
2025-06-10 18:33:56,020 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2\evaluation\detailed_metrics_20250610_183356.csv
2025-06-10 18:33:56,373 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2\plots\scatter_plot_20250610_183356.png
2025-06-10 18:33:56,374 - prism_main - INFO - 保存插值结果...
2025-06-10 18:33:56,440 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2\points
2025-06-10 18:33:56,440 - prism_main - INFO - 保存权重信息...
2025-06-10 18:33:56,504 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2\evaluation\weights_20250610_183356.csv
2025-06-10 18:33:57,636 - prism_main - INFO - 内存清理完成
2025-06-10 18:33:57,636 - prism_main - INFO - ============================================================
2025-06-10 18:33:57,636 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.92 秒
2025-06-10 18:33:57,636 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2
2025-06-10 18:33:57,637 - prism_main - INFO - ============================================================
2025-06-10 18:33:57,641 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:33:57,641 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:33:57,643 - prism_main - INFO - ============================================================
2025-06-10 18:33:57,643 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:33:57,643 - prism_main - INFO - ============================================================
2025-06-10 18:33:57,644 - prism_main - INFO - 开始加载数据...
2025-06-10 18:33:57,644 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:33:57,645 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:33:57,646 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:33:57,979 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:33:57,979 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:33:57,985 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:33:57,986 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:33:58,030 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:33:58,398 - prism_main - INFO - 数据加载完成
2025-06-10 18:33:58,398 - prism_main - INFO - 构建空间结构...
2025-06-10 18:33:58,398 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:33:58,401 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:33:58,401 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:33:58,401 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:33:58,410 - prism_main - INFO - 共有 504 个时间点需要处理
2025-06-10 18:33:58,410 - parallel_processing - INFO - 开始并行插值处理，共 504 个时间点
2025-06-10 18:34:03,464 - parallel_processing - INFO - 并行插值完成，成功处理 504 个时间点
2025-06-10 18:34:03,867 - prism_main - INFO - 插值计算完成
2025-06-10 18:34:03,868 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:34:03,868 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:34:03,933 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1\evaluation\evaluation_report_20250610_183403.txt
2025-06-10 18:34:03,938 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1\evaluation\detailed_metrics_20250610_183403.csv
2025-06-10 18:34:04,326 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1\plots\scatter_plot_20250610_183403.png
2025-06-10 18:34:04,327 - prism_main - INFO - 保存插值结果...
2025-06-10 18:34:04,402 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1\points
2025-06-10 18:34:04,403 - prism_main - INFO - 保存权重信息...
2025-06-10 18:34:04,520 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1\evaluation\weights_20250610_183404.csv
2025-06-10 18:34:05,679 - prism_main - INFO - 内存清理完成
2025-06-10 18:34:05,680 - prism_main - INFO - ============================================================
2025-06-10 18:34:05,680 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 8.04 秒
2025-06-10 18:34:05,680 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1
2025-06-10 18:34:05,680 - prism_main - INFO - ============================================================
2025-06-10 18:34:05,685 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:34:05,685 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:34:05,690 - prism_main - INFO - ============================================================
2025-06-10 18:34:05,690 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:34:05,690 - prism_main - INFO - ============================================================
2025-06-10 18:34:05,690 - prism_main - INFO - 开始加载数据...
2025-06-10 18:34:05,690 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:34:05,692 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:34:05,692 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:34:06,028 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:34:06,028 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:34:06,035 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:34:06,035 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:34:06,078 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:34:06,452 - prism_main - INFO - 数据加载完成
2025-06-10 18:34:06,453 - prism_main - INFO - 构建空间结构...
2025-06-10 18:34:06,453 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:34:06,456 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:34:06,457 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:34:06,457 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:34:06,464 - prism_main - INFO - 共有 504 个时间点需要处理
2025-06-10 18:34:06,464 - parallel_processing - INFO - 开始并行插值处理，共 504 个时间点
2025-06-10 18:34:11,486 - parallel_processing - INFO - 并行插值完成，成功处理 504 个时间点
2025-06-10 18:34:11,888 - prism_main - INFO - 插值计算完成
2025-06-10 18:34:11,888 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:34:11,889 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:34:11,959 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2\evaluation\evaluation_report_20250610_183411.txt
2025-06-10 18:34:11,967 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2\evaluation\detailed_metrics_20250610_183411.csv
2025-06-10 18:34:12,351 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2\plots\scatter_plot_20250610_183411.png
2025-06-10 18:34:12,353 - prism_main - INFO - 保存插值结果...
2025-06-10 18:34:12,429 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2\points
2025-06-10 18:34:12,429 - prism_main - INFO - 保存权重信息...
2025-06-10 18:34:12,531 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2\evaluation\weights_20250610_183412.csv
2025-06-10 18:34:13,693 - prism_main - INFO - 内存清理完成
2025-06-10 18:34:13,693 - prism_main - INFO - ============================================================
2025-06-10 18:34:13,694 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 8.00 秒
2025-06-10 18:34:13,694 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2
2025-06-10 18:34:13,694 - prism_main - INFO - ============================================================
2025-06-10 18:34:13,699 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:34:13,699 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:34:13,704 - prism_main - INFO - ============================================================
2025-06-10 18:34:13,704 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:34:13,704 - prism_main - INFO - ============================================================
2025-06-10 18:34:13,704 - prism_main - INFO - 开始加载数据...
2025-06-10 18:34:13,705 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:34:13,706 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:34:13,706 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:34:14,042 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:34:14,042 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:34:14,048 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:34:14,048 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:34:14,090 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:34:14,461 - prism_main - INFO - 数据加载完成
2025-06-10 18:34:14,462 - prism_main - INFO - 构建空间结构...
2025-06-10 18:34:14,462 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:34:14,465 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:34:14,465 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:34:14,465 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:34:14,471 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:34:14,472 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:34:18,736 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:34:19,142 - prism_main - INFO - 插值计算完成
2025-06-10 18:34:19,142 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:34:19,142 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:34:19,199 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1\evaluation\evaluation_report_20250610_183419.txt
2025-06-10 18:34:19,205 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1\evaluation\detailed_metrics_20250610_183419.csv
2025-06-10 18:34:19,554 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1\plots\scatter_plot_20250610_183419.png
2025-06-10 18:34:19,556 - prism_main - INFO - 保存插值结果...
2025-06-10 18:34:19,618 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1\points
2025-06-10 18:34:19,619 - prism_main - INFO - 保存权重信息...
2025-06-10 18:34:19,705 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1\evaluation\weights_20250610_183419.csv
2025-06-10 18:34:20,860 - prism_main - INFO - 内存清理完成
2025-06-10 18:34:20,860 - prism_main - INFO - ============================================================
2025-06-10 18:34:20,860 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.16 秒
2025-06-10 18:34:20,860 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1
2025-06-10 18:34:20,861 - prism_main - INFO - ============================================================
2025-06-10 18:34:20,865 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:34:20,865 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:34:20,869 - prism_main - INFO - ============================================================
2025-06-10 18:34:20,869 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:34:20,869 - prism_main - INFO - ============================================================
2025-06-10 18:34:20,869 - prism_main - INFO - 开始加载数据...
2025-06-10 18:34:20,870 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:34:20,871 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:34:20,871 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:34:21,204 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:34:21,204 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:34:21,210 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:34:21,210 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:34:21,253 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:34:21,627 - prism_main - INFO - 数据加载完成
2025-06-10 18:34:21,627 - prism_main - INFO - 构建空间结构...
2025-06-10 18:34:21,628 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:34:21,631 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:34:21,631 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:34:21,631 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:34:21,637 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:34:21,637 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:34:25,866 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:34:26,272 - prism_main - INFO - 插值计算完成
2025-06-10 18:34:26,273 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:34:26,273 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:34:26,325 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2\evaluation\evaluation_report_20250610_183426.txt
2025-06-10 18:34:26,332 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2\evaluation\detailed_metrics_20250610_183426.csv
2025-06-10 18:34:26,679 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2\plots\scatter_plot_20250610_183426.png
2025-06-10 18:34:26,680 - prism_main - INFO - 保存插值结果...
2025-06-10 18:34:26,742 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2\points
2025-06-10 18:34:26,742 - prism_main - INFO - 保存权重信息...
2025-06-10 18:34:26,818 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2\evaluation\weights_20250610_183426.csv
2025-06-10 18:34:27,991 - prism_main - INFO - 内存清理完成
2025-06-10 18:34:27,991 - prism_main - INFO - ============================================================
2025-06-10 18:34:27,991 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.12 秒
2025-06-10 18:34:27,992 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2
2025-06-10 18:34:27,992 - prism_main - INFO - ============================================================
2025-06-10 18:34:27,997 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:34:27,997 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:34:28,001 - prism_main - INFO - ============================================================
2025-06-10 18:34:28,001 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:34:28,002 - prism_main - INFO - ============================================================
2025-06-10 18:34:28,002 - prism_main - INFO - 开始加载数据...
2025-06-10 18:34:28,002 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:34:28,004 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:34:28,004 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:34:28,347 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:34:28,347 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:34:28,353 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:34:28,354 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:34:28,401 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:34:28,775 - prism_main - INFO - 数据加载完成
2025-06-10 18:34:28,775 - prism_main - INFO - 构建空间结构...
2025-06-10 18:34:28,775 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:34:28,778 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:34:28,778 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:34:28,778 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:34:28,786 - prism_main - INFO - 共有 552 个时间点需要处理
2025-06-10 18:34:28,786 - parallel_processing - INFO - 开始并行插值处理，共 552 个时间点
2025-06-10 18:34:33,996 - parallel_processing - INFO - 并行插值完成，成功处理 552 个时间点
2025-06-10 18:34:34,410 - prism_main - INFO - 插值计算完成
2025-06-10 18:34:34,411 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:34:34,411 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:34:34,485 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1\evaluation\evaluation_report_20250610_183434.txt
2025-06-10 18:34:34,494 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1\evaluation\detailed_metrics_20250610_183434.csv
2025-06-10 18:34:34,901 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1\plots\scatter_plot_20250610_183434.png
2025-06-10 18:34:34,903 - prism_main - INFO - 保存插值结果...
2025-06-10 18:34:34,981 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1\points
2025-06-10 18:34:34,981 - prism_main - INFO - 保存权重信息...
2025-06-10 18:34:35,101 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1\evaluation\weights_20250610_183435.csv
2025-06-10 18:34:36,283 - prism_main - INFO - 内存清理完成
2025-06-10 18:34:36,283 - prism_main - INFO - ============================================================
2025-06-10 18:34:36,283 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 8.28 秒
2025-06-10 18:34:36,283 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1
2025-06-10 18:34:36,284 - prism_main - INFO - ============================================================
2025-06-10 18:34:36,288 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:34:36,288 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:34:36,293 - prism_main - INFO - ============================================================
2025-06-10 18:34:36,293 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:34:36,293 - prism_main - INFO - ============================================================
2025-06-10 18:34:36,294 - prism_main - INFO - 开始加载数据...
2025-06-10 18:34:36,294 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:34:36,295 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:34:36,295 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:34:36,635 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:34:36,636 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:34:36,641 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:34:36,642 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:34:36,689 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:34:37,064 - prism_main - INFO - 数据加载完成
2025-06-10 18:34:37,064 - prism_main - INFO - 构建空间结构...
2025-06-10 18:34:37,065 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:34:37,068 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:34:37,068 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:34:37,068 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:34:37,077 - prism_main - INFO - 共有 552 个时间点需要处理
2025-06-10 18:34:37,077 - parallel_processing - INFO - 开始并行插值处理，共 552 个时间点
2025-06-10 18:34:42,246 - parallel_processing - INFO - 并行插值完成，成功处理 552 个时间点
2025-06-10 18:34:42,672 - prism_main - INFO - 插值计算完成
2025-06-10 18:34:42,672 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:34:42,673 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:34:42,744 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2\evaluation\evaluation_report_20250610_183442.txt
2025-06-10 18:34:42,753 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2\evaluation\detailed_metrics_20250610_183442.csv
2025-06-10 18:34:43,138 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2\plots\scatter_plot_20250610_183442.png
2025-06-10 18:34:43,139 - prism_main - INFO - 保存插值结果...
2025-06-10 18:34:43,215 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2\points
2025-06-10 18:34:43,215 - prism_main - INFO - 保存权重信息...
2025-06-10 18:34:43,319 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2\evaluation\weights_20250610_183443.csv
2025-06-10 18:34:44,521 - prism_main - INFO - 内存清理完成
2025-06-10 18:34:44,521 - prism_main - INFO - ============================================================
2025-06-10 18:34:44,521 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 8.23 秒
2025-06-10 18:34:44,521 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2
2025-06-10 18:34:44,521 - prism_main - INFO - ============================================================
2025-06-10 18:34:44,526 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:34:44,526 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:34:44,532 - prism_main - INFO - ============================================================
2025-06-10 18:34:44,532 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:34:44,532 - prism_main - INFO - ============================================================
2025-06-10 18:34:44,532 - prism_main - INFO - 开始加载数据...
2025-06-10 18:34:44,532 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:34:44,534 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:34:44,534 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:34:44,880 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:34:44,880 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:34:44,886 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:34:44,886 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:34:44,926 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:34:45,301 - prism_main - INFO - 数据加载完成
2025-06-10 18:34:45,301 - prism_main - INFO - 构建空间结构...
2025-06-10 18:34:45,301 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:34:45,304 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:34:45,305 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:34:45,305 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:34:45,307 - prism_main - INFO - 共有 144 个时间点需要处理
2025-06-10 18:34:45,308 - parallel_processing - INFO - 开始并行插值处理，共 144 个时间点
2025-06-10 18:34:48,285 - parallel_processing - INFO - 并行插值完成，成功处理 144 个时间点
2025-06-10 18:34:48,689 - prism_main - INFO - 插值计算完成
2025-06-10 18:34:48,689 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:34:48,689 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:34:48,709 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1\evaluation\evaluation_report_20250610_183448.txt
2025-06-10 18:34:48,714 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1\evaluation\detailed_metrics_20250610_183448.csv
2025-06-10 18:34:48,998 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1\plots\scatter_plot_20250610_183448.png
2025-06-10 18:34:48,999 - prism_main - INFO - 保存插值结果...
2025-06-10 18:34:49,037 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1\points
2025-06-10 18:34:49,037 - prism_main - INFO - 保存权重信息...
2025-06-10 18:34:49,073 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1\evaluation\weights_20250610_183449.csv
2025-06-10 18:34:50,217 - prism_main - INFO - 内存清理完成
2025-06-10 18:34:50,218 - prism_main - INFO - ============================================================
2025-06-10 18:34:50,218 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.69 秒
2025-06-10 18:34:50,218 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1
2025-06-10 18:34:50,218 - prism_main - INFO - ============================================================
2025-06-10 18:34:50,222 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:34:50,222 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:34:50,225 - prism_main - INFO - ============================================================
2025-06-10 18:34:50,225 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:34:50,225 - prism_main - INFO - ============================================================
2025-06-10 18:34:50,225 - prism_main - INFO - 开始加载数据...
2025-06-10 18:34:50,225 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:34:50,227 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:34:50,227 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:34:50,565 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:34:50,565 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:34:50,571 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:34:50,571 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:34:50,610 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:34:50,988 - prism_main - INFO - 数据加载完成
2025-06-10 18:34:50,989 - prism_main - INFO - 构建空间结构...
2025-06-10 18:34:50,989 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:34:50,992 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:34:50,992 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:34:50,992 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:34:50,995 - prism_main - INFO - 共有 144 个时间点需要处理
2025-06-10 18:34:50,995 - parallel_processing - INFO - 开始并行插值处理，共 144 个时间点
2025-06-10 18:34:53,881 - parallel_processing - INFO - 并行插值完成，成功处理 144 个时间点
2025-06-10 18:34:54,300 - prism_main - INFO - 插值计算完成
2025-06-10 18:34:54,301 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:34:54,301 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:34:54,320 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2\evaluation\evaluation_report_20250610_183454.txt
2025-06-10 18:34:54,324 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2\evaluation\detailed_metrics_20250610_183454.csv
2025-06-10 18:34:54,607 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2\plots\scatter_plot_20250610_183454.png
2025-06-10 18:34:54,609 - prism_main - INFO - 保存插值结果...
2025-06-10 18:34:54,648 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2\points
2025-06-10 18:34:54,648 - prism_main - INFO - 保存权重信息...
2025-06-10 18:34:54,680 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2\evaluation\weights_20250610_183454.csv
2025-06-10 18:34:55,844 - prism_main - INFO - 内存清理完成
2025-06-10 18:34:55,844 - prism_main - INFO - ============================================================
2025-06-10 18:34:55,844 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.62 秒
2025-06-10 18:34:55,844 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2
2025-06-10 18:34:55,844 - prism_main - INFO - ============================================================
2025-06-10 18:49:00,391 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:00,391 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:00,391 - prism_main - INFO - ============================================================
2025-06-10 18:49:00,391 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:00,391 - prism_main - INFO - ============================================================
2025-06-10 18:49:00,391 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:00,391 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:00,393 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:00,393 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:00,729 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:00,730 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:00,736 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:00,736 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:00,781 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:00,814 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:00,814 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:00,814 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:00,817 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:01,504 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\plots\delaunay_triangulation.png
2025-06-10 18:49:01,504 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:01,504 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:01,512 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:49:01,512 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:49:05,390 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:49:05,449 - prism_main - INFO - 插值计算完成
2025-06-10 18:49:05,449 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:49:05,449 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:49:05,496 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\evaluation\evaluation_report_20250610_184905.txt
2025-06-10 18:49:05,508 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\evaluation\detailed_metrics_20250610_184905.csv
2025-06-10 18:49:05,849 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\plots\scatter_plot_20250610_184905.png
2025-06-10 18:49:05,850 - prism_main - INFO - 保存插值结果...
2025-06-10 18:49:05,909 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\points
2025-06-10 18:49:05,909 - prism_main - INFO - 保存权重信息...
2025-06-10 18:49:05,965 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1\evaluation\weights_20250610_184905.csv
2025-06-10 18:49:06,076 - prism_main - INFO - 内存清理完成
2025-06-10 18:49:06,076 - prism_main - INFO - ============================================================
2025-06-10 18:49:06,076 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.69 秒
2025-06-10 18:49:06,076 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-1
2025-06-10 18:49:06,078 - prism_main - INFO - ============================================================
2025-06-10 18:49:06,078 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:06,079 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:06,080 - prism_main - INFO - ============================================================
2025-06-10 18:49:06,080 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:06,080 - prism_main - INFO - ============================================================
2025-06-10 18:49:06,081 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:06,081 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:06,083 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:06,083 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:06,420 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:06,420 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:06,426 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:06,426 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:06,473 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:06,519 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:06,519 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:06,520 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:06,522 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:07,044 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\plots\delaunay_triangulation.png
2025-06-10 18:49:07,045 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:07,045 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:07,054 - prism_main - INFO - 共有 504 个时间点需要处理
2025-06-10 18:49:07,054 - parallel_processing - INFO - 开始并行插值处理，共 504 个时间点
2025-06-10 18:49:11,860 - parallel_processing - INFO - 并行插值完成，成功处理 504 个时间点
2025-06-10 18:49:11,930 - prism_main - INFO - 插值计算完成
2025-06-10 18:49:11,930 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:49:11,931 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:49:12,000 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\evaluation\evaluation_report_20250610_184911.txt
2025-06-10 18:49:12,008 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\evaluation\detailed_metrics_20250610_184912.csv
2025-06-10 18:49:12,378 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\plots\scatter_plot_20250610_184912.png
2025-06-10 18:49:12,379 - prism_main - INFO - 保存插值结果...
2025-06-10 18:49:12,452 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\points
2025-06-10 18:49:12,452 - prism_main - INFO - 保存权重信息...
2025-06-10 18:49:12,539 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2\evaluation\weights_20250610_184912.csv
2025-06-10 18:49:12,698 - prism_main - INFO - 内存清理完成
2025-06-10 18:49:12,698 - prism_main - INFO - ============================================================
2025-06-10 18:49:12,699 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.62 秒
2025-06-10 18:49:12,699 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-2
2025-06-10 18:49:12,699 - prism_main - INFO - ============================================================
2025-06-10 18:49:12,700 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:12,700 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:12,703 - prism_main - INFO - ============================================================
2025-06-10 18:49:12,703 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:12,703 - prism_main - INFO - ============================================================
2025-06-10 18:49:12,704 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:12,704 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:12,705 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:12,705 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:13,043 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:13,043 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:13,050 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:13,050 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:13,092 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:13,143 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:13,143 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:13,143 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:13,146 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:13,661 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\plots\delaunay_triangulation.png
2025-06-10 18:49:13,661 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:13,661 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:13,668 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:49:13,668 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:49:17,700 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:49:17,775 - prism_main - INFO - 插值计算完成
2025-06-10 18:49:17,775 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:49:17,776 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:49:17,824 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\evaluation\evaluation_report_20250610_184917.txt
2025-06-10 18:49:17,830 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\evaluation\detailed_metrics_20250610_184917.csv
2025-06-10 18:49:18,188 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\plots\scatter_plot_20250610_184917.png
2025-06-10 18:49:18,190 - prism_main - INFO - 保存插值结果...
2025-06-10 18:49:18,249 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\points
2025-06-10 18:49:18,250 - prism_main - INFO - 保存权重信息...
2025-06-10 18:49:18,313 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3\evaluation\weights_20250610_184918.csv
2025-06-10 18:49:18,488 - prism_main - INFO - 内存清理完成
2025-06-10 18:49:18,488 - prism_main - INFO - ============================================================
2025-06-10 18:49:18,488 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.78 秒
2025-06-10 18:49:18,488 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-3
2025-06-10 18:49:18,488 - prism_main - INFO - ============================================================
2025-06-10 18:49:18,489 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:18,489 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:18,492 - prism_main - INFO - ============================================================
2025-06-10 18:49:18,492 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:18,492 - prism_main - INFO - ============================================================
2025-06-10 18:49:18,493 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:18,493 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:18,494 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:18,494 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:18,834 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:18,834 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:18,840 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:18,840 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:18,886 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:18,943 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:18,943 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:18,943 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:18,945 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:19,471 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\plots\delaunay_triangulation.png
2025-06-10 18:49:19,473 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:19,473 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:19,482 - prism_main - INFO - 共有 552 个时间点需要处理
2025-06-10 18:49:19,482 - parallel_processing - INFO - 开始并行插值处理，共 552 个时间点
2025-06-10 18:49:24,433 - parallel_processing - INFO - 并行插值完成，成功处理 552 个时间点
2025-06-10 18:49:24,517 - prism_main - INFO - 插值计算完成
2025-06-10 18:49:24,517 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:49:24,518 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:49:24,582 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\evaluation\evaluation_report_20250610_184924.txt
2025-06-10 18:49:24,592 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\evaluation\detailed_metrics_20250610_184924.csv
2025-06-10 18:49:24,978 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\plots\scatter_plot_20250610_184924.png
2025-06-10 18:49:24,980 - prism_main - INFO - 保存插值结果...
2025-06-10 18:49:25,054 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\points
2025-06-10 18:49:25,054 - prism_main - INFO - 保存权重信息...
2025-06-10 18:49:25,145 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4\evaluation\weights_20250610_184925.csv
2025-06-10 18:49:25,354 - prism_main - INFO - 内存清理完成
2025-06-10 18:49:25,355 - prism_main - INFO - ============================================================
2025-06-10 18:49:25,355 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.86 秒
2025-06-10 18:49:25,355 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-4
2025-06-10 18:49:25,355 - prism_main - INFO - ============================================================
2025-06-10 18:49:25,356 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:25,356 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:25,361 - prism_main - INFO - ============================================================
2025-06-10 18:49:25,361 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:25,361 - prism_main - INFO - ============================================================
2025-06-10 18:49:25,361 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:25,361 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:25,363 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:25,363 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:25,702 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:25,702 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:25,708 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:25,708 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:25,748 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:25,809 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:25,810 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:25,810 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:25,813 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:26,327 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\plots\delaunay_triangulation.png
2025-06-10 18:49:26,328 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:26,328 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:26,331 - prism_main - INFO - 共有 144 个时间点需要处理
2025-06-10 18:49:26,331 - parallel_processing - INFO - 开始并行插值处理，共 144 个时间点
2025-06-10 18:49:29,062 - parallel_processing - INFO - 并行插值完成，成功处理 144 个时间点
2025-06-10 18:49:29,136 - prism_main - INFO - 插值计算完成
2025-06-10 18:49:29,136 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:49:29,137 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:49:29,156 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\evaluation\evaluation_report_20250610_184929.txt
2025-06-10 18:49:29,160 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\evaluation\detailed_metrics_20250610_184929.csv
2025-06-10 18:49:29,431 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\plots\scatter_plot_20250610_184929.png
2025-06-10 18:49:29,433 - prism_main - INFO - 保存插值结果...
2025-06-10 18:49:29,471 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\points
2025-06-10 18:49:29,471 - prism_main - INFO - 保存权重信息...
2025-06-10 18:49:29,497 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1\evaluation\weights_20250610_184929.csv
2025-06-10 18:49:29,702 - prism_main - INFO - 内存清理完成
2025-06-10 18:49:29,703 - prism_main - INFO - ============================================================
2025-06-10 18:49:29,703 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 4.34 秒
2025-06-10 18:49:29,703 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-1
2025-06-10 18:49:29,703 - prism_main - INFO - ============================================================
2025-06-10 18:49:29,704 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:29,704 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:29,706 - prism_main - INFO - ============================================================
2025-06-10 18:49:29,706 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:29,706 - prism_main - INFO - ============================================================
2025-06-10 18:49:29,706 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:29,706 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:29,708 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:29,708 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:30,048 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:30,049 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:30,055 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:30,055 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:30,094 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:30,159 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:30,159 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:30,160 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:30,162 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:30,687 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\plots\delaunay_triangulation.png
2025-06-10 18:49:30,688 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:30,688 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:30,691 - prism_main - INFO - 共有 144 个时间点需要处理
2025-06-10 18:49:30,691 - parallel_processing - INFO - 开始并行插值处理，共 144 个时间点
2025-06-10 18:49:33,385 - parallel_processing - INFO - 并行插值完成，成功处理 144 个时间点
2025-06-10 18:49:33,463 - prism_main - INFO - 插值计算完成
2025-06-10 18:49:33,464 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:49:33,464 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:49:33,481 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\evaluation\evaluation_report_20250610_184933.txt
2025-06-10 18:49:33,485 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\evaluation\detailed_metrics_20250610_184933.csv
2025-06-10 18:49:33,786 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\plots\scatter_plot_20250610_184933.png
2025-06-10 18:49:33,787 - prism_main - INFO - 保存插值结果...
2025-06-10 18:49:33,825 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\points
2025-06-10 18:49:33,825 - prism_main - INFO - 保存权重信息...
2025-06-10 18:49:33,849 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-2\evaluation\weights_20250610_184933.csv
2025-06-10 18:49:34,060 - prism_main - INFO - 内存清理完成
2025-06-10 18:49:34,060 - prism_main - INFO - ============================================================
2025-06-10 18:49:34,061 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 4.35 秒
2025-06-10 18:49:34,061 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-2
2025-06-10 18:49:34,061 - prism_main - INFO - ============================================================
2025-06-10 18:49:34,062 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:34,062 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:34,065 - prism_main - INFO - ============================================================
2025-06-10 18:49:34,065 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:34,065 - prism_main - INFO - ============================================================
2025-06-10 18:49:34,065 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:34,065 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:34,066 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:34,067 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:34,401 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:34,401 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:34,408 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:34,408 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:34,448 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:34,520 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:34,520 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:34,520 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:34,523 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:35,034 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\plots\delaunay_triangulation.png
2025-06-10 18:49:35,034 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:35,035 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:35,040 - prism_main - INFO - 共有 264 个时间点需要处理
2025-06-10 18:49:35,040 - parallel_processing - INFO - 开始并行插值处理，共 264 个时间点
2025-06-10 18:49:38,408 - parallel_processing - INFO - 并行插值完成，成功处理 264 个时间点
2025-06-10 18:49:38,502 - prism_main - INFO - 插值计算完成
2025-06-10 18:49:38,502 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:49:38,503 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:49:38,533 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\evaluation\evaluation_report_20250610_184938.txt
2025-06-10 18:49:38,538 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\evaluation\detailed_metrics_20250610_184938.csv
2025-06-10 18:49:38,873 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\plots\scatter_plot_20250610_184938.png
2025-06-10 18:49:38,875 - prism_main - INFO - 保存插值结果...
2025-06-10 18:49:38,923 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\points
2025-06-10 18:49:38,923 - prism_main - INFO - 保存权重信息...
2025-06-10 18:49:38,967 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-3\evaluation\weights_20250610_184938.csv
2025-06-10 18:49:39,204 - prism_main - INFO - 内存清理完成
2025-06-10 18:49:39,205 - prism_main - INFO - ============================================================
2025-06-10 18:49:39,205 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.14 秒
2025-06-10 18:49:39,205 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-3
2025-06-10 18:49:39,205 - prism_main - INFO - ============================================================
2025-06-10 18:49:39,206 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:39,206 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:39,208 - prism_main - INFO - ============================================================
2025-06-10 18:49:39,208 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:39,208 - prism_main - INFO - ============================================================
2025-06-10 18:49:39,208 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:39,209 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:39,210 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:39,210 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:39,544 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:39,544 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:39,550 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:39,551 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:39,589 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:39,669 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:39,669 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:39,669 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:39,673 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:40,184 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\plots\delaunay_triangulation.png
2025-06-10 18:49:40,185 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:40,185 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:40,188 - prism_main - INFO - 共有 144 个时间点需要处理
2025-06-10 18:49:40,188 - parallel_processing - INFO - 开始并行插值处理，共 144 个时间点
2025-06-10 18:49:42,969 - parallel_processing - INFO - 并行插值完成，成功处理 144 个时间点
2025-06-10 18:49:43,069 - prism_main - INFO - 插值计算完成
2025-06-10 18:49:43,069 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:49:43,069 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:49:43,099 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\evaluation\evaluation_report_20250610_184943.txt
2025-06-10 18:49:43,105 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\evaluation\detailed_metrics_20250610_184943.csv
2025-06-10 18:49:43,393 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\plots\scatter_plot_20250610_184943.png
2025-06-10 18:49:43,395 - prism_main - INFO - 保存插值结果...
2025-06-10 18:49:43,432 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\points
2025-06-10 18:49:43,432 - prism_main - INFO - 保存权重信息...
2025-06-10 18:49:43,462 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-4\evaluation\weights_20250610_184943.csv
2025-06-10 18:49:43,708 - prism_main - INFO - 内存清理完成
2025-06-10 18:49:43,708 - prism_main - INFO - ============================================================
2025-06-10 18:49:43,708 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 4.50 秒
2025-06-10 18:49:43,708 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-4
2025-06-10 18:49:43,708 - prism_main - INFO - ============================================================
2025-06-10 18:49:43,709 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:43,709 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:43,713 - prism_main - INFO - ============================================================
2025-06-10 18:49:43,713 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:43,713 - prism_main - INFO - ============================================================
2025-06-10 18:49:43,713 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:43,713 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:43,715 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:43,715 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:44,046 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:44,046 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:44,052 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:44,052 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:44,092 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:44,176 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:44,176 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:44,176 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:44,179 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:44,699 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\plots\delaunay_triangulation.png
2025-06-10 18:49:44,699 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:44,699 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:44,703 - prism_main - INFO - 共有 168 个时间点需要处理
2025-06-10 18:49:44,703 - parallel_processing - INFO - 开始并行插值处理，共 168 个时间点
2025-06-10 18:49:47,582 - parallel_processing - INFO - 并行插值完成，成功处理 168 个时间点
2025-06-10 18:49:47,712 - prism_main - INFO - 插值计算完成
2025-06-10 18:49:47,713 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:49:47,713 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:49:47,736 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\evaluation\evaluation_report_20250610_184947.txt
2025-06-10 18:49:47,741 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\evaluation\detailed_metrics_20250610_184947.csv
2025-06-10 18:49:48,041 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\plots\scatter_plot_20250610_184947.png
2025-06-10 18:49:48,043 - prism_main - INFO - 保存插值结果...
2025-06-10 18:49:48,083 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\points
2025-06-10 18:49:48,083 - prism_main - INFO - 保存权重信息...
2025-06-10 18:49:48,116 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-5\evaluation\weights_20250610_184948.csv
2025-06-10 18:49:48,380 - prism_main - INFO - 内存清理完成
2025-06-10 18:49:48,380 - prism_main - INFO - ============================================================
2025-06-10 18:49:48,381 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 4.67 秒
2025-06-10 18:49:48,381 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-5
2025-06-10 18:49:48,381 - prism_main - INFO - ============================================================
2025-06-10 18:49:48,382 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:48,382 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:48,384 - prism_main - INFO - ============================================================
2025-06-10 18:49:48,384 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:48,384 - prism_main - INFO - ============================================================
2025-06-10 18:49:48,384 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:48,384 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:48,385 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:48,385 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:48,728 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:48,728 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:48,734 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:48,734 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:48,774 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:48,860 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:48,861 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:48,861 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:48,863 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:49,378 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\plots\delaunay_triangulation.png
2025-06-10 18:49:49,379 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:49,379 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:49,381 - prism_main - INFO - 共有 96 个时间点需要处理
2025-06-10 18:49:49,381 - parallel_processing - INFO - 开始并行插值处理，共 96 个时间点
2025-06-10 18:49:51,827 - parallel_processing - INFO - 并行插值完成，成功处理 96 个时间点
2025-06-10 18:49:51,931 - prism_main - INFO - 插值计算完成
2025-06-10 18:49:51,931 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:49:51,932 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:49:51,946 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\evaluation\evaluation_report_20250610_184951.txt
2025-06-10 18:49:51,949 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\evaluation\detailed_metrics_20250610_184951.csv
2025-06-10 18:49:52,228 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\plots\scatter_plot_20250610_184951.png
2025-06-10 18:49:52,230 - prism_main - INFO - 保存插值结果...
2025-06-10 18:49:52,264 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\points
2025-06-10 18:49:52,264 - prism_main - INFO - 保存权重信息...
2025-06-10 18:49:52,283 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-1\evaluation\weights_20250610_184952.csv
2025-06-10 18:49:52,556 - prism_main - INFO - 内存清理完成
2025-06-10 18:49:52,558 - prism_main - INFO - ============================================================
2025-06-10 18:49:52,558 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 4.17 秒
2025-06-10 18:49:52,558 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2011-1
2025-06-10 18:49:52,559 - prism_main - INFO - ============================================================
2025-06-10 18:49:52,560 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:52,560 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:52,561 - prism_main - INFO - ============================================================
2025-06-10 18:49:52,561 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:52,561 - prism_main - INFO - ============================================================
2025-06-10 18:49:52,561 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:52,561 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:52,563 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:52,563 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:52,901 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:52,901 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:52,908 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:52,908 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:52,953 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:53,057 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:53,057 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:53,057 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:53,060 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:53,574 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\plots\delaunay_triangulation.png
2025-06-10 18:49:53,575 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:53,575 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:53,581 - prism_main - INFO - 共有 360 个时间点需要处理
2025-06-10 18:49:53,581 - parallel_processing - INFO - 开始并行插值处理，共 360 个时间点
2025-06-10 18:49:57,418 - parallel_processing - INFO - 并行插值完成，成功处理 360 个时间点
2025-06-10 18:49:57,533 - prism_main - INFO - 插值计算完成
2025-06-10 18:49:57,534 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:49:57,534 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:49:57,580 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\evaluation\evaluation_report_20250610_184957.txt
2025-06-10 18:49:57,586 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\evaluation\detailed_metrics_20250610_184957.csv
2025-06-10 18:49:57,938 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\plots\scatter_plot_20250610_184957.png
2025-06-10 18:49:57,939 - prism_main - INFO - 保存插值结果...
2025-06-10 18:49:57,997 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\points
2025-06-10 18:49:57,997 - prism_main - INFO - 保存权重信息...
2025-06-10 18:49:58,054 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-2\evaluation\weights_20250610_184958.csv
2025-06-10 18:49:58,370 - prism_main - INFO - 内存清理完成
2025-06-10 18:49:58,370 - prism_main - INFO - ============================================================
2025-06-10 18:49:58,371 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.81 秒
2025-06-10 18:49:58,371 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2011-2
2025-06-10 18:49:58,371 - prism_main - INFO - ============================================================
2025-06-10 18:49:58,372 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:49:58,372 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:49:58,374 - prism_main - INFO - ============================================================
2025-06-10 18:49:58,374 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:49:58,374 - prism_main - INFO - ============================================================
2025-06-10 18:49:58,374 - prism_main - INFO - 开始加载数据...
2025-06-10 18:49:58,374 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:49:58,376 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:49:58,376 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:49:58,707 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:49:58,707 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:49:58,713 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:49:58,714 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:49:58,755 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:49:58,851 - prism_main - INFO - 数据加载完成
2025-06-10 18:49:58,852 - prism_main - INFO - 构建空间结构...
2025-06-10 18:49:58,852 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:49:58,854 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:49:59,364 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\plots\delaunay_triangulation.png
2025-06-10 18:49:59,364 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:49:59,365 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:49:59,370 - prism_main - INFO - 共有 312 个时间点需要处理
2025-06-10 18:49:59,370 - parallel_processing - INFO - 开始并行插值处理，共 312 个时间点
2025-06-10 18:50:02,950 - parallel_processing - INFO - 并行插值完成，成功处理 312 个时间点
2025-06-10 18:50:03,075 - prism_main - INFO - 插值计算完成
2025-06-10 18:50:03,075 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:50:03,076 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:50:03,118 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\evaluation\evaluation_report_20250610_185003.txt
2025-06-10 18:50:03,124 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\evaluation\detailed_metrics_20250610_185003.csv
2025-06-10 18:50:03,447 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\plots\scatter_plot_20250610_185003.png
2025-06-10 18:50:03,449 - prism_main - INFO - 保存插值结果...
2025-06-10 18:50:03,501 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\points
2025-06-10 18:50:03,501 - prism_main - INFO - 保存权重信息...
2025-06-10 18:50:03,552 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-3\evaluation\weights_20250610_185003.csv
2025-06-10 18:50:03,883 - prism_main - INFO - 内存清理完成
2025-06-10 18:50:03,883 - prism_main - INFO - ============================================================
2025-06-10 18:50:03,883 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.51 秒
2025-06-10 18:50:03,883 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2011-3
2025-06-10 18:50:03,883 - prism_main - INFO - ============================================================
2025-06-10 18:50:03,885 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:50:03,885 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:50:03,887 - prism_main - INFO - ============================================================
2025-06-10 18:50:03,887 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:50:03,888 - prism_main - INFO - ============================================================
2025-06-10 18:50:03,888 - prism_main - INFO - 开始加载数据...
2025-06-10 18:50:03,888 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:50:03,889 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:50:03,889 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:50:04,228 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:50:04,229 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:50:04,235 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:50:04,235 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:50:04,276 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:50:04,383 - prism_main - INFO - 数据加载完成
2025-06-10 18:50:04,383 - prism_main - INFO - 构建空间结构...
2025-06-10 18:50:04,383 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:50:04,386 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:50:04,900 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\plots\delaunay_triangulation.png
2025-06-10 18:50:04,901 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:50:04,901 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:50:04,906 - prism_main - INFO - 共有 288 个时间点需要处理
2025-06-10 18:50:04,906 - parallel_processing - INFO - 开始并行插值处理，共 288 个时间点
2025-06-10 18:50:08,417 - parallel_processing - INFO - 并行插值完成，成功处理 288 个时间点
2025-06-10 18:50:08,544 - prism_main - INFO - 插值计算完成
2025-06-10 18:50:08,545 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:50:08,545 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:50:08,580 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\evaluation\evaluation_report_20250610_185008.txt
2025-06-10 18:50:08,587 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\evaluation\detailed_metrics_20250610_185008.csv
2025-06-10 18:50:08,901 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\plots\scatter_plot_20250610_185008.png
2025-06-10 18:50:08,902 - prism_main - INFO - 保存插值结果...
2025-06-10 18:50:08,953 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\points
2025-06-10 18:50:08,953 - prism_main - INFO - 保存权重信息...
2025-06-10 18:50:09,002 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2011-4\evaluation\weights_20250610_185008.csv
2025-06-10 18:50:09,357 - prism_main - INFO - 内存清理完成
2025-06-10 18:50:09,358 - prism_main - INFO - ============================================================
2025-06-10 18:50:09,358 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.47 秒
2025-06-10 18:50:09,358 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2011-4
2025-06-10 18:50:09,358 - prism_main - INFO - ============================================================
2025-06-10 18:50:09,359 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:50:09,359 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:50:09,362 - prism_main - INFO - ============================================================
2025-06-10 18:50:09,362 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:50:09,362 - prism_main - INFO - ============================================================
2025-06-10 18:50:09,363 - prism_main - INFO - 开始加载数据...
2025-06-10 18:50:09,363 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:50:09,364 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:50:09,365 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:50:09,696 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:50:09,696 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:50:09,702 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:50:09,702 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:50:09,743 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:50:09,849 - prism_main - INFO - 数据加载完成
2025-06-10 18:50:09,849 - prism_main - INFO - 构建空间结构...
2025-06-10 18:50:09,849 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:50:09,852 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:50:10,356 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\plots\delaunay_triangulation.png
2025-06-10 18:50:10,356 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:50:10,357 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:50:10,362 - prism_main - INFO - 共有 312 个时间点需要处理
2025-06-10 18:50:10,362 - parallel_processing - INFO - 开始并行插值处理，共 312 个时间点
2025-06-10 18:50:14,036 - parallel_processing - INFO - 并行插值完成，成功处理 312 个时间点
2025-06-10 18:50:14,191 - prism_main - INFO - 插值计算完成
2025-06-10 18:50:14,191 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:50:14,192 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:50:14,235 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\evaluation\evaluation_report_20250610_185014.txt
2025-06-10 18:50:14,251 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\evaluation\detailed_metrics_20250610_185014.csv
2025-06-10 18:50:14,584 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\plots\scatter_plot_20250610_185014.png
2025-06-10 18:50:14,586 - prism_main - INFO - 保存插值结果...
2025-06-10 18:50:14,639 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\points
2025-06-10 18:50:14,639 - prism_main - INFO - 保存权重信息...
2025-06-10 18:50:14,694 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-1\evaluation\weights_20250610_185014.csv
2025-06-10 18:50:15,060 - prism_main - INFO - 内存清理完成
2025-06-10 18:50:15,061 - prism_main - INFO - ============================================================
2025-06-10 18:50:15,061 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.70 秒
2025-06-10 18:50:15,061 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2012-1
2025-06-10 18:50:15,061 - prism_main - INFO - ============================================================
2025-06-10 18:50:15,062 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:50:15,062 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:50:15,065 - prism_main - INFO - ============================================================
2025-06-10 18:50:15,065 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:50:15,065 - prism_main - INFO - ============================================================
2025-06-10 18:50:15,065 - prism_main - INFO - 开始加载数据...
2025-06-10 18:50:15,065 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:50:15,067 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:50:15,067 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:50:15,402 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:50:15,402 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:50:15,408 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:50:15,408 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:50:15,454 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:50:15,565 - prism_main - INFO - 数据加载完成
2025-06-10 18:50:15,565 - prism_main - INFO - 构建空间结构...
2025-06-10 18:50:15,565 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:50:15,568 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:50:16,077 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\plots\delaunay_triangulation.png
2025-06-10 18:50:16,077 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:50:16,078 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:50:16,085 - prism_main - INFO - 共有 480 个时间点需要处理
2025-06-10 18:50:16,085 - parallel_processing - INFO - 开始并行插值处理，共 480 个时间点
2025-06-10 18:50:20,716 - parallel_processing - INFO - 并行插值完成，成功处理 480 个时间点
2025-06-10 18:50:20,865 - prism_main - INFO - 插值计算完成
2025-06-10 18:50:20,865 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:50:20,865 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:50:20,928 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\evaluation\evaluation_report_20250610_185020.txt
2025-06-10 18:50:20,944 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\evaluation\detailed_metrics_20250610_185020.csv
2025-06-10 18:50:21,325 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\plots\scatter_plot_20250610_185020.png
2025-06-10 18:50:21,326 - prism_main - INFO - 保存插值结果...
2025-06-10 18:50:21,395 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\points
2025-06-10 18:50:21,395 - prism_main - INFO - 保存权重信息...
2025-06-10 18:50:21,483 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2012-2\evaluation\weights_20250610_185021.csv
2025-06-10 18:50:21,913 - prism_main - INFO - 内存清理完成
2025-06-10 18:50:21,913 - prism_main - INFO - ============================================================
2025-06-10 18:50:21,913 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.85 秒
2025-06-10 18:50:21,914 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2012-2
2025-06-10 18:50:21,914 - prism_main - INFO - ============================================================
2025-06-10 18:50:21,914 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:50:21,915 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:50:21,919 - prism_main - INFO - ============================================================
2025-06-10 18:50:21,919 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:50:21,919 - prism_main - INFO - ============================================================
2025-06-10 18:50:21,919 - prism_main - INFO - 开始加载数据...
2025-06-10 18:50:21,919 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:50:21,921 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:50:21,921 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:50:22,257 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:50:22,257 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:50:22,263 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:50:22,263 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:50:22,305 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:50:22,421 - prism_main - INFO - 数据加载完成
2025-06-10 18:50:22,421 - prism_main - INFO - 构建空间结构...
2025-06-10 18:50:22,421 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:50:22,424 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:50:22,941 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\plots\delaunay_triangulation.png
2025-06-10 18:50:22,942 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:50:22,942 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:50:22,948 - prism_main - INFO - 共有 336 个时间点需要处理
2025-06-10 18:50:22,948 - parallel_processing - INFO - 开始并行插值处理，共 336 个时间点
2025-06-10 18:50:26,704 - parallel_processing - INFO - 并行插值完成，成功处理 336 个时间点
2025-06-10 18:50:26,855 - prism_main - INFO - 插值计算完成
2025-06-10 18:50:26,855 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:50:26,856 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:50:26,898 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\evaluation\evaluation_report_20250610_185026.txt
2025-06-10 18:50:26,913 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\evaluation\detailed_metrics_20250610_185026.csv
2025-06-10 18:50:27,263 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\plots\scatter_plot_20250610_185026.png
2025-06-10 18:50:27,265 - prism_main - INFO - 保存插值结果...
2025-06-10 18:50:27,319 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\points
2025-06-10 18:50:27,319 - prism_main - INFO - 保存权重信息...
2025-06-10 18:50:27,377 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-1\evaluation\weights_20250610_185027.csv
2025-06-10 18:50:27,790 - prism_main - INFO - 内存清理完成
2025-06-10 18:50:27,791 - prism_main - INFO - ============================================================
2025-06-10 18:50:27,791 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.87 秒
2025-06-10 18:50:27,791 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2013-1
2025-06-10 18:50:27,791 - prism_main - INFO - ============================================================
2025-06-10 18:50:27,792 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:50:27,792 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:50:27,795 - prism_main - INFO - ============================================================
2025-06-10 18:50:27,795 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:50:27,795 - prism_main - INFO - ============================================================
2025-06-10 18:50:27,795 - prism_main - INFO - 开始加载数据...
2025-06-10 18:50:27,796 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:50:27,797 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:50:27,797 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:50:28,132 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:50:28,133 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:50:28,138 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:50:28,138 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:50:28,184 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:50:28,309 - prism_main - INFO - 数据加载完成
2025-06-10 18:50:28,309 - prism_main - INFO - 构建空间结构...
2025-06-10 18:50:28,310 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:50:28,312 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:50:28,825 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\plots\delaunay_triangulation.png
2025-06-10 18:50:28,826 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:50:28,826 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:50:28,833 - prism_main - INFO - 共有 409 个时间点需要处理
2025-06-10 18:50:28,833 - parallel_processing - INFO - 开始并行插值处理，共 409 个时间点
2025-06-10 18:50:33,139 - parallel_processing - INFO - 并行插值完成，成功处理 409 个时间点
2025-06-10 18:50:33,303 - prism_main - INFO - 插值计算完成
2025-06-10 18:50:33,304 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:50:33,304 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:50:33,357 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\evaluation\evaluation_report_20250610_185033.txt
2025-06-10 18:50:33,373 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\evaluation\detailed_metrics_20250610_185033.csv
2025-06-10 18:50:33,723 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\plots\scatter_plot_20250610_185033.png
2025-06-10 18:50:33,725 - prism_main - INFO - 保存插值结果...
2025-06-10 18:50:33,788 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\points
2025-06-10 18:50:33,788 - prism_main - INFO - 保存权重信息...
2025-06-10 18:50:33,863 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2013-2\evaluation\weights_20250610_185033.csv
2025-06-10 18:50:34,332 - prism_main - INFO - 内存清理完成
2025-06-10 18:50:34,332 - prism_main - INFO - ============================================================
2025-06-10 18:50:34,332 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.54 秒
2025-06-10 18:50:34,333 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2013-2
2025-06-10 18:50:34,333 - prism_main - INFO - ============================================================
2025-06-10 18:50:34,334 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:50:34,334 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:50:34,337 - prism_main - INFO - ============================================================
2025-06-10 18:50:34,337 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:50:34,337 - prism_main - INFO - ============================================================
2025-06-10 18:50:34,337 - prism_main - INFO - 开始加载数据...
2025-06-10 18:50:34,338 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:50:34,339 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:50:34,341 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:50:34,675 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:50:34,675 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:50:34,681 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:50:34,681 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:50:34,722 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:50:34,857 - prism_main - INFO - 数据加载完成
2025-06-10 18:50:34,858 - prism_main - INFO - 构建空间结构...
2025-06-10 18:50:34,858 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:50:34,861 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:50:35,377 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\plots\delaunay_triangulation.png
2025-06-10 18:50:35,378 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:50:35,378 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:50:35,383 - prism_main - INFO - 共有 297 个时间点需要处理
2025-06-10 18:50:35,383 - parallel_processing - INFO - 开始并行插值处理，共 297 个时间点
2025-06-10 18:50:38,931 - parallel_processing - INFO - 并行插值完成，成功处理 297 个时间点
2025-06-10 18:50:39,099 - prism_main - INFO - 插值计算完成
2025-06-10 18:50:39,100 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:50:39,100 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:50:39,136 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\evaluation\evaluation_report_20250610_185039.txt
2025-06-10 18:50:39,151 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\evaluation\detailed_metrics_20250610_185039.csv
2025-06-10 18:50:39,471 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\plots\scatter_plot_20250610_185039.png
2025-06-10 18:50:39,472 - prism_main - INFO - 保存插值结果...
2025-06-10 18:50:39,523 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\points
2025-06-10 18:50:39,523 - prism_main - INFO - 保存权重信息...
2025-06-10 18:50:39,576 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-1\evaluation\weights_20250610_185039.csv
2025-06-10 18:50:40,034 - prism_main - INFO - 内存清理完成
2025-06-10 18:50:40,034 - prism_main - INFO - ============================================================
2025-06-10 18:50:40,034 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.70 秒
2025-06-10 18:50:40,034 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2014-1
2025-06-10 18:50:40,034 - prism_main - INFO - ============================================================
2025-06-10 18:50:40,036 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:50:40,036 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:50:40,039 - prism_main - INFO - ============================================================
2025-06-10 18:50:40,039 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:50:40,039 - prism_main - INFO - ============================================================
2025-06-10 18:50:40,040 - prism_main - INFO - 开始加载数据...
2025-06-10 18:50:40,040 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:50:40,041 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:50:40,042 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:50:40,374 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:50:40,374 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:50:40,380 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:50:40,380 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:50:40,421 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:50:40,565 - prism_main - INFO - 数据加载完成
2025-06-10 18:50:40,565 - prism_main - INFO - 构建空间结构...
2025-06-10 18:50:40,565 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:50:40,568 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:50:41,073 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\plots\delaunay_triangulation.png
2025-06-10 18:50:41,074 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:50:41,074 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:50:41,079 - prism_main - INFO - 共有 283 个时间点需要处理
2025-06-10 18:50:41,079 - parallel_processing - INFO - 开始并行插值处理，共 283 个时间点
2025-06-10 18:50:44,515 - parallel_processing - INFO - 并行插值完成，成功处理 283 个时间点
2025-06-10 18:50:44,697 - prism_main - INFO - 插值计算完成
2025-06-10 18:50:44,698 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:50:44,698 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:50:44,737 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\evaluation\evaluation_report_20250610_185044.txt
2025-06-10 18:50:44,751 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\evaluation\detailed_metrics_20250610_185044.csv
2025-06-10 18:50:45,067 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\plots\scatter_plot_20250610_185044.png
2025-06-10 18:50:45,068 - prism_main - INFO - 保存插值结果...
2025-06-10 18:50:45,116 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\points
2025-06-10 18:50:45,116 - prism_main - INFO - 保存权重信息...
2025-06-10 18:50:45,165 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-2\evaluation\weights_20250610_185045.csv
2025-06-10 18:50:45,629 - prism_main - INFO - 内存清理完成
2025-06-10 18:50:45,630 - prism_main - INFO - ============================================================
2025-06-10 18:50:45,630 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.59 秒
2025-06-10 18:50:45,630 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2014-2
2025-06-10 18:50:45,630 - prism_main - INFO - ============================================================
2025-06-10 18:50:45,631 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:50:45,631 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:50:45,634 - prism_main - INFO - ============================================================
2025-06-10 18:50:45,634 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:50:45,634 - prism_main - INFO - ============================================================
2025-06-10 18:50:45,634 - prism_main - INFO - 开始加载数据...
2025-06-10 18:50:45,635 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:50:45,637 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:50:45,637 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:50:45,966 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:50:45,966 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:50:45,972 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:50:45,972 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:50:46,013 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:50:46,153 - prism_main - INFO - 数据加载完成
2025-06-10 18:50:46,153 - prism_main - INFO - 构建空间结构...
2025-06-10 18:50:46,154 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:50:46,156 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:50:46,661 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\plots\delaunay_triangulation.png
2025-06-10 18:50:46,662 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:50:46,662 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:50:46,667 - prism_main - INFO - 共有 264 个时间点需要处理
2025-06-10 18:50:46,667 - parallel_processing - INFO - 开始并行插值处理，共 264 个时间点
2025-06-10 18:50:50,033 - parallel_processing - INFO - 并行插值完成，成功处理 264 个时间点
2025-06-10 18:50:50,225 - prism_main - INFO - 插值计算完成
2025-06-10 18:50:50,225 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:50:50,225 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:50:50,263 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\evaluation\evaluation_report_20250610_185050.txt
2025-06-10 18:50:50,279 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\evaluation\detailed_metrics_20250610_185050.csv
2025-06-10 18:50:50,590 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\plots\scatter_plot_20250610_185050.png
2025-06-10 18:50:50,592 - prism_main - INFO - 保存插值结果...
2025-06-10 18:50:50,639 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\points
2025-06-10 18:50:50,639 - prism_main - INFO - 保存权重信息...
2025-06-10 18:50:50,687 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-3\evaluation\weights_20250610_185050.csv
2025-06-10 18:50:51,169 - prism_main - INFO - 内存清理完成
2025-06-10 18:50:51,169 - prism_main - INFO - ============================================================
2025-06-10 18:50:51,169 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.53 秒
2025-06-10 18:50:51,169 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2014-3
2025-06-10 18:50:51,169 - prism_main - INFO - ============================================================
2025-06-10 18:50:51,170 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:50:51,170 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:50:51,173 - prism_main - INFO - ============================================================
2025-06-10 18:50:51,173 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:50:51,173 - prism_main - INFO - ============================================================
2025-06-10 18:50:51,173 - prism_main - INFO - 开始加载数据...
2025-06-10 18:50:51,173 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:50:51,175 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:50:51,175 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:50:51,507 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:50:51,507 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:50:51,513 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:50:51,514 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:50:51,558 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:50:51,713 - prism_main - INFO - 数据加载完成
2025-06-10 18:50:51,713 - prism_main - INFO - 构建空间结构...
2025-06-10 18:50:51,714 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:50:51,717 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:50:52,227 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\plots\delaunay_triangulation.png
2025-06-10 18:50:52,228 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:50:52,229 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:50:52,236 - prism_main - INFO - 共有 401 个时间点需要处理
2025-06-10 18:50:52,236 - parallel_processing - INFO - 开始并行插值处理，共 401 个时间点
2025-06-10 18:50:56,352 - parallel_processing - INFO - 并行插值完成，成功处理 401 个时间点
2025-06-10 18:50:56,536 - prism_main - INFO - 插值计算完成
2025-06-10 18:50:56,540 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:50:56,540 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:50:56,592 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\evaluation\evaluation_report_20250610_185056.txt
2025-06-10 18:50:56,607 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\evaluation\detailed_metrics_20250610_185056.csv
2025-06-10 18:50:56,971 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\plots\scatter_plot_20250610_185056.png
2025-06-10 18:50:56,972 - prism_main - INFO - 保存插值结果...
2025-06-10 18:50:57,031 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\points
2025-06-10 18:50:57,032 - prism_main - INFO - 保存权重信息...
2025-06-10 18:50:57,102 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-4\evaluation\weights_20250610_185057.csv
2025-06-10 18:50:57,637 - prism_main - INFO - 内存清理完成
2025-06-10 18:50:57,637 - prism_main - INFO - ============================================================
2025-06-10 18:50:57,638 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.46 秒
2025-06-10 18:50:57,638 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2014-4
2025-06-10 18:50:57,638 - prism_main - INFO - ============================================================
2025-06-10 18:50:57,639 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:50:57,639 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:50:57,643 - prism_main - INFO - ============================================================
2025-06-10 18:50:57,643 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:50:57,643 - prism_main - INFO - ============================================================
2025-06-10 18:50:57,643 - prism_main - INFO - 开始加载数据...
2025-06-10 18:50:57,643 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:50:57,645 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:50:57,645 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:50:57,974 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:50:57,976 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:50:57,981 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:50:57,982 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:50:58,022 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:50:58,184 - prism_main - INFO - 数据加载完成
2025-06-10 18:50:58,185 - prism_main - INFO - 构建空间结构...
2025-06-10 18:50:58,185 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:50:58,188 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:50:58,690 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\plots\delaunay_triangulation.png
2025-06-10 18:50:58,691 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:50:58,691 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:50:58,695 - prism_main - INFO - 共有 232 个时间点需要处理
2025-06-10 18:50:58,695 - parallel_processing - INFO - 开始并行插值处理，共 232 个时间点
2025-06-10 18:51:01,950 - parallel_processing - INFO - 并行插值完成，成功处理 232 个时间点
2025-06-10 18:51:02,148 - prism_main - INFO - 插值计算完成
2025-06-10 18:51:02,148 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:51:02,149 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:51:02,182 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\evaluation\evaluation_report_20250610_185102.txt
2025-06-10 18:51:02,187 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\evaluation\detailed_metrics_20250610_185102.csv
2025-06-10 18:51:02,486 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\plots\scatter_plot_20250610_185102.png
2025-06-10 18:51:02,487 - prism_main - INFO - 保存插值结果...
2025-06-10 18:51:02,533 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\points
2025-06-10 18:51:02,533 - prism_main - INFO - 保存权重信息...
2025-06-10 18:51:02,582 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2014-5\evaluation\weights_20250610_185102.csv
2025-06-10 18:51:03,109 - prism_main - INFO - 内存清理完成
2025-06-10 18:51:03,109 - prism_main - INFO - ============================================================
2025-06-10 18:51:03,110 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.47 秒
2025-06-10 18:51:03,110 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2014-5
2025-06-10 18:51:03,110 - prism_main - INFO - ============================================================
2025-06-10 18:51:03,111 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:51:03,111 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:51:03,115 - prism_main - INFO - ============================================================
2025-06-10 18:51:03,115 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:51:03,115 - prism_main - INFO - ============================================================
2025-06-10 18:51:03,116 - prism_main - INFO - 开始加载数据...
2025-06-10 18:51:03,116 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:51:03,118 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:51:03,118 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:51:03,455 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:51:03,456 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:51:03,462 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:51:03,462 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:51:03,507 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:51:03,683 - prism_main - INFO - 数据加载完成
2025-06-10 18:51:03,683 - prism_main - INFO - 构建空间结构...
2025-06-10 18:51:03,683 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:51:03,686 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:51:04,199 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\plots\delaunay_triangulation.png
2025-06-10 18:51:04,200 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:51:04,200 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:51:04,210 - prism_main - INFO - 共有 547 个时间点需要处理
2025-06-10 18:51:04,210 - parallel_processing - INFO - 开始并行插值处理，共 547 个时间点
2025-06-10 18:51:09,038 - parallel_processing - INFO - 并行插值完成，成功处理 547 个时间点
2025-06-10 18:51:09,238 - prism_main - INFO - 插值计算完成
2025-06-10 18:51:09,239 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:51:09,239 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:51:09,297 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\evaluation\evaluation_report_20250610_185109.txt
2025-06-10 18:51:09,315 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\evaluation\detailed_metrics_20250610_185109.csv
2025-06-10 18:51:09,686 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\plots\scatter_plot_20250610_185109.png
2025-06-10 18:51:09,688 - prism_main - INFO - 保存插值结果...
2025-06-10 18:51:09,761 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\points
2025-06-10 18:51:09,761 - prism_main - INFO - 保存权重信息...
2025-06-10 18:51:09,844 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-1\evaluation\weights_20250610_185109.csv
2025-06-10 18:51:10,419 - prism_main - INFO - 内存清理完成
2025-06-10 18:51:10,419 - prism_main - INFO - ============================================================
2025-06-10 18:51:10,419 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.30 秒
2025-06-10 18:51:10,419 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2015-1
2025-06-10 18:51:10,420 - prism_main - INFO - ============================================================
2025-06-10 18:51:10,421 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:51:10,421 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:51:10,425 - prism_main - INFO - ============================================================
2025-06-10 18:51:10,425 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:51:10,425 - prism_main - INFO - ============================================================
2025-06-10 18:51:10,426 - prism_main - INFO - 开始加载数据...
2025-06-10 18:51:10,426 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:51:10,427 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:51:10,427 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:51:10,757 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:51:10,757 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:51:10,763 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:51:10,764 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:51:10,807 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:51:10,982 - prism_main - INFO - 数据加载完成
2025-06-10 18:51:10,982 - prism_main - INFO - 构建空间结构...
2025-06-10 18:51:10,983 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:51:10,986 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:51:11,490 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\plots\delaunay_triangulation.png
2025-06-10 18:51:11,491 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:51:11,491 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:51:11,498 - prism_main - INFO - 共有 401 个时间点需要处理
2025-06-10 18:51:11,498 - parallel_processing - INFO - 开始并行插值处理，共 401 个时间点
2025-06-10 18:51:15,627 - parallel_processing - INFO - 并行插值完成，成功处理 401 个时间点
2025-06-10 18:51:15,843 - prism_main - INFO - 插值计算完成
2025-06-10 18:51:15,843 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:51:15,844 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:51:15,897 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\evaluation\evaluation_report_20250610_185115.txt
2025-06-10 18:51:15,906 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\evaluation\detailed_metrics_20250610_185115.csv
2025-06-10 18:51:16,269 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\plots\scatter_plot_20250610_185115.png
2025-06-10 18:51:16,270 - prism_main - INFO - 保存插值结果...
2025-06-10 18:51:16,332 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\points
2025-06-10 18:51:16,332 - prism_main - INFO - 保存权重信息...
2025-06-10 18:51:16,403 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-2\evaluation\weights_20250610_185116.csv
2025-06-10 18:51:16,993 - prism_main - INFO - 内存清理完成
2025-06-10 18:51:16,993 - prism_main - INFO - ============================================================
2025-06-10 18:51:16,993 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.57 秒
2025-06-10 18:51:16,993 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2015-2
2025-06-10 18:51:16,993 - prism_main - INFO - ============================================================
2025-06-10 18:51:16,994 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:51:16,994 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:51:16,998 - prism_main - INFO - ============================================================
2025-06-10 18:51:16,998 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:51:16,999 - prism_main - INFO - ============================================================
2025-06-10 18:51:16,999 - prism_main - INFO - 开始加载数据...
2025-06-10 18:51:16,999 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:51:17,001 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:51:17,001 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:51:17,328 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:51:17,328 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:51:17,334 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:51:17,334 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:51:17,376 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:51:17,551 - prism_main - INFO - 数据加载完成
2025-06-10 18:51:17,551 - prism_main - INFO - 构建空间结构...
2025-06-10 18:51:17,552 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:51:17,554 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:51:18,060 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\plots\delaunay_triangulation.png
2025-06-10 18:51:18,061 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:51:18,061 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:51:18,066 - prism_main - INFO - 共有 326 个时间点需要处理
2025-06-10 18:51:18,066 - parallel_processing - INFO - 开始并行插值处理，共 326 个时间点
2025-06-10 18:51:21,626 - parallel_processing - INFO - 并行插值完成，成功处理 326 个时间点
2025-06-10 18:51:21,831 - prism_main - INFO - 插值计算完成
2025-06-10 18:51:21,831 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:51:21,832 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:51:21,863 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\evaluation\evaluation_report_20250610_185121.txt
2025-06-10 18:51:21,878 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\evaluation\detailed_metrics_20250610_185121.csv
2025-06-10 18:51:22,203 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\plots\scatter_plot_20250610_185121.png
2025-06-10 18:51:22,205 - prism_main - INFO - 保存插值结果...
2025-06-10 18:51:22,259 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\points
2025-06-10 18:51:22,259 - prism_main - INFO - 保存权重信息...
2025-06-10 18:51:22,310 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-3\evaluation\weights_20250610_185122.csv
2025-06-10 18:51:22,903 - prism_main - INFO - 内存清理完成
2025-06-10 18:51:22,904 - prism_main - INFO - ============================================================
2025-06-10 18:51:22,904 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.91 秒
2025-06-10 18:51:22,904 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2015-3
2025-06-10 18:51:22,904 - prism_main - INFO - ============================================================
2025-06-10 18:51:22,905 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:51:22,905 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:51:22,907 - prism_main - INFO - ============================================================
2025-06-10 18:51:22,908 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:51:22,908 - prism_main - INFO - ============================================================
2025-06-10 18:51:22,908 - prism_main - INFO - 开始加载数据...
2025-06-10 18:51:22,908 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:51:22,909 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:51:22,909 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:51:23,239 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:51:23,239 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:51:23,245 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:51:23,245 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:51:23,287 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:51:23,481 - prism_main - INFO - 数据加载完成
2025-06-10 18:51:23,481 - prism_main - INFO - 构建空间结构...
2025-06-10 18:51:23,482 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:51:23,484 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:51:23,997 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\plots\delaunay_triangulation.png
2025-06-10 18:51:23,998 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:51:23,998 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:51:24,005 - prism_main - INFO - 共有 361 个时间点需要处理
2025-06-10 18:51:24,005 - parallel_processing - INFO - 开始并行插值处理，共 361 个时间点
2025-06-10 18:51:27,837 - parallel_processing - INFO - 并行插值完成，成功处理 361 个时间点
2025-06-10 18:51:28,064 - prism_main - INFO - 插值计算完成
2025-06-10 18:51:28,064 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:51:28,064 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:51:28,103 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\evaluation\evaluation_report_20250610_185128.txt
2025-06-10 18:51:28,110 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\evaluation\detailed_metrics_20250610_185128.csv
2025-06-10 18:51:28,467 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\plots\scatter_plot_20250610_185128.png
2025-06-10 18:51:28,468 - prism_main - INFO - 保存插值结果...
2025-06-10 18:51:28,525 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\points
2025-06-10 18:51:28,525 - prism_main - INFO - 保存权重信息...
2025-06-10 18:51:28,585 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2015-4\evaluation\weights_20250610_185128.csv
2025-06-10 18:51:29,229 - prism_main - INFO - 内存清理完成
2025-06-10 18:51:29,230 - prism_main - INFO - ============================================================
2025-06-10 18:51:29,230 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.32 秒
2025-06-10 18:51:29,230 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2015-4
2025-06-10 18:51:29,230 - prism_main - INFO - ============================================================
2025-06-10 18:51:29,231 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:51:29,231 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:51:29,234 - prism_main - INFO - ============================================================
2025-06-10 18:51:29,235 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:51:29,235 - prism_main - INFO - ============================================================
2025-06-10 18:51:29,235 - prism_main - INFO - 开始加载数据...
2025-06-10 18:51:29,235 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:51:29,236 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:51:29,236 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:51:29,573 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:51:29,573 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:51:29,579 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:51:29,579 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:51:29,617 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:51:29,817 - prism_main - INFO - 数据加载完成
2025-06-10 18:51:29,818 - prism_main - INFO - 构建空间结构...
2025-06-10 18:51:29,818 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:51:29,820 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:51:30,324 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\plots\delaunay_triangulation.png
2025-06-10 18:51:30,325 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:51:30,325 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:51:30,328 - prism_main - INFO - 共有 85 个时间点需要处理
2025-06-10 18:51:30,328 - parallel_processing - INFO - 开始并行插值处理，共 85 个时间点
2025-06-10 18:51:32,685 - parallel_processing - INFO - 并行插值完成，成功处理 85 个时间点
2025-06-10 18:51:32,927 - prism_main - INFO - 插值计算完成
2025-06-10 18:51:32,927 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:51:32,927 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:51:32,940 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\evaluation\evaluation_report_20250610_185132.txt
2025-06-10 18:51:32,953 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\evaluation\detailed_metrics_20250610_185132.csv
2025-06-10 18:51:33,251 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\plots\scatter_plot_20250610_185132.png
2025-06-10 18:51:33,253 - prism_main - INFO - 保存插值结果...
2025-06-10 18:51:33,286 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\points
2025-06-10 18:51:33,286 - prism_main - INFO - 保存权重信息...
2025-06-10 18:51:33,304 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-1\evaluation\weights_20250610_185133.csv
2025-06-10 18:51:33,947 - prism_main - INFO - 内存清理完成
2025-06-10 18:51:33,947 - prism_main - INFO - ============================================================
2025-06-10 18:51:33,947 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 4.71 秒
2025-06-10 18:51:33,947 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2017-1
2025-06-10 18:51:33,948 - prism_main - INFO - ============================================================
2025-06-10 18:51:33,948 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:51:33,949 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:51:33,950 - prism_main - INFO - ============================================================
2025-06-10 18:51:33,950 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:51:33,950 - prism_main - INFO - ============================================================
2025-06-10 18:51:33,950 - prism_main - INFO - 开始加载数据...
2025-06-10 18:51:33,951 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:51:33,952 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:51:33,952 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:51:34,284 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:51:34,284 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:51:34,290 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:51:34,290 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:51:34,335 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:51:34,530 - prism_main - INFO - 数据加载完成
2025-06-10 18:51:34,530 - prism_main - INFO - 构建空间结构...
2025-06-10 18:51:34,530 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:51:34,533 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:51:35,041 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\plots\delaunay_triangulation.png
2025-06-10 18:51:35,041 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:51:35,041 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:51:35,049 - prism_main - INFO - 共有 443 个时间点需要处理
2025-06-10 18:51:35,049 - parallel_processing - INFO - 开始并行插值处理，共 443 个时间点
2025-06-10 18:51:39,552 - parallel_processing - INFO - 并行插值完成，成功处理 443 个时间点
2025-06-10 18:51:39,815 - prism_main - INFO - 插值计算完成
2025-06-10 18:51:39,815 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:51:39,815 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:51:39,875 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\evaluation\evaluation_report_20250610_185139.txt
2025-06-10 18:51:39,882 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\evaluation\detailed_metrics_20250610_185139.csv
2025-06-10 18:51:40,246 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\plots\scatter_plot_20250610_185139.png
2025-06-10 18:51:40,247 - prism_main - INFO - 保存插值结果...
2025-06-10 18:51:40,312 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\points
2025-06-10 18:51:40,312 - prism_main - INFO - 保存权重信息...
2025-06-10 18:51:40,403 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2017-2\evaluation\weights_20250610_185140.csv
2025-06-10 18:51:41,173 - prism_main - INFO - 内存清理完成
2025-06-10 18:51:41,173 - prism_main - INFO - ============================================================
2025-06-10 18:51:41,173 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.22 秒
2025-06-10 18:51:41,174 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2017-2
2025-06-10 18:51:41,174 - prism_main - INFO - ============================================================
2025-06-10 18:51:41,175 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:51:41,175 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:51:41,178 - prism_main - INFO - ============================================================
2025-06-10 18:51:41,178 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:51:41,179 - prism_main - INFO - ============================================================
2025-06-10 18:51:41,179 - prism_main - INFO - 开始加载数据...
2025-06-10 18:51:41,179 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:51:41,180 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:51:41,181 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:51:41,519 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:51:41,519 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:51:41,525 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:51:41,525 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:51:41,567 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:51:41,814 - prism_main - INFO - 数据加载完成
2025-06-10 18:51:41,814 - prism_main - INFO - 构建空间结构...
2025-06-10 18:51:41,814 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:51:41,818 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:51:42,337 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\plots\delaunay_triangulation.png
2025-06-10 18:51:42,338 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:51:42,338 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:51:42,345 - prism_main - INFO - 共有 349 个时间点需要处理
2025-06-10 18:51:42,345 - parallel_processing - INFO - 开始并行插值处理，共 349 个时间点
2025-06-10 18:51:46,107 - parallel_processing - INFO - 并行插值完成，成功处理 349 个时间点
2025-06-10 18:51:46,360 - prism_main - INFO - 插值计算完成
2025-06-10 18:51:46,360 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:51:46,360 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:51:46,396 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\evaluation\evaluation_report_20250610_185146.txt
2025-06-10 18:51:46,411 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\evaluation\detailed_metrics_20250610_185146.csv
2025-06-10 18:51:46,746 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\plots\scatter_plot_20250610_185146.png
2025-06-10 18:51:46,748 - prism_main - INFO - 保存插值结果...
2025-06-10 18:51:46,802 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\points
2025-06-10 18:51:46,803 - prism_main - INFO - 保存权重信息...
2025-06-10 18:51:46,860 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2018-1\evaluation\weights_20250610_185146.csv
2025-06-10 18:51:47,562 - prism_main - INFO - 内存清理完成
2025-06-10 18:51:47,562 - prism_main - INFO - ============================================================
2025-06-10 18:51:47,562 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.38 秒
2025-06-10 18:51:47,562 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2018-1
2025-06-10 18:51:47,562 - prism_main - INFO - ============================================================
2025-06-10 18:51:47,563 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:51:47,563 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:51:47,566 - prism_main - INFO - ============================================================
2025-06-10 18:51:47,566 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:51:47,566 - prism_main - INFO - ============================================================
2025-06-10 18:51:47,566 - prism_main - INFO - 开始加载数据...
2025-06-10 18:51:47,567 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:51:47,568 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:51:47,568 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:51:47,903 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:51:47,903 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:51:47,909 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:51:47,910 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:51:47,949 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:51:48,161 - prism_main - INFO - 数据加载完成
2025-06-10 18:51:48,161 - prism_main - INFO - 构建空间结构...
2025-06-10 18:51:48,161 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:51:48,164 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:51:48,671 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\plots\delaunay_triangulation.png
2025-06-10 18:51:48,671 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:51:48,672 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:51:48,676 - prism_main - INFO - 共有 235 个时间点需要处理
2025-06-10 18:51:48,676 - parallel_processing - INFO - 开始并行插值处理，共 235 个时间点
2025-06-10 18:51:51,754 - parallel_processing - INFO - 并行插值完成，成功处理 235 个时间点
2025-06-10 18:51:52,002 - prism_main - INFO - 插值计算完成
2025-06-10 18:51:52,002 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:51:52,003 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:51:52,027 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\evaluation\evaluation_report_20250610_185152.txt
2025-06-10 18:51:52,041 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\evaluation\detailed_metrics_20250610_185152.csv
2025-06-10 18:51:52,357 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\plots\scatter_plot_20250610_185152.png
2025-06-10 18:51:52,359 - prism_main - INFO - 保存插值结果...
2025-06-10 18:51:52,403 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\points
2025-06-10 18:51:52,404 - prism_main - INFO - 保存权重信息...
2025-06-10 18:51:52,442 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-1\evaluation\weights_20250610_185152.csv
2025-06-10 18:51:53,135 - prism_main - INFO - 内存清理完成
2025-06-10 18:51:53,136 - prism_main - INFO - ============================================================
2025-06-10 18:51:53,136 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.57 秒
2025-06-10 18:51:53,136 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2019-1
2025-06-10 18:51:53,136 - prism_main - INFO - ============================================================
2025-06-10 18:51:53,137 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:51:53,137 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:51:53,139 - prism_main - INFO - ============================================================
2025-06-10 18:51:53,139 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:51:53,139 - prism_main - INFO - ============================================================
2025-06-10 18:51:53,139 - prism_main - INFO - 开始加载数据...
2025-06-10 18:51:53,139 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:51:53,141 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:51:53,141 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:51:53,472 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:51:53,473 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:51:53,478 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:51:53,479 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:51:53,517 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:51:53,738 - prism_main - INFO - 数据加载完成
2025-06-10 18:51:53,738 - prism_main - INFO - 构建空间结构...
2025-06-10 18:51:53,738 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:51:53,741 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:51:54,244 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\plots\delaunay_triangulation.png
2025-06-10 18:51:54,244 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:51:54,245 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:51:54,248 - prism_main - INFO - 共有 153 个时间点需要处理
2025-06-10 18:51:54,248 - parallel_processing - INFO - 开始并行插值处理，共 153 个时间点
2025-06-10 18:51:56,952 - parallel_processing - INFO - 并行插值完成，成功处理 153 个时间点
2025-06-10 18:51:57,195 - prism_main - INFO - 插值计算完成
2025-06-10 18:51:57,196 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:51:57,196 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:51:57,216 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\evaluation\evaluation_report_20250610_185157.txt
2025-06-10 18:51:57,220 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\evaluation\detailed_metrics_20250610_185157.csv
2025-06-10 18:51:57,497 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\plots\scatter_plot_20250610_185157.png
2025-06-10 18:51:57,498 - prism_main - INFO - 保存插值结果...
2025-06-10 18:51:57,536 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\points
2025-06-10 18:51:57,536 - prism_main - INFO - 保存权重信息...
2025-06-10 18:51:57,564 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-2\evaluation\weights_20250610_185157.csv
2025-06-10 18:51:58,259 - prism_main - INFO - 内存清理完成
2025-06-10 18:51:58,259 - prism_main - INFO - ============================================================
2025-06-10 18:51:58,260 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.12 秒
2025-06-10 18:51:58,260 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2019-2
2025-06-10 18:51:58,260 - prism_main - INFO - ============================================================
2025-06-10 18:51:58,261 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:51:58,261 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:51:58,264 - prism_main - INFO - ============================================================
2025-06-10 18:51:58,264 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:51:58,264 - prism_main - INFO - ============================================================
2025-06-10 18:51:58,264 - prism_main - INFO - 开始加载数据...
2025-06-10 18:51:58,264 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:51:58,266 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:51:58,266 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:51:58,602 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:51:58,603 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:51:58,608 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:51:58,609 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:51:58,650 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:51:58,871 - prism_main - INFO - 数据加载完成
2025-06-10 18:51:58,871 - prism_main - INFO - 构建空间结构...
2025-06-10 18:51:58,871 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:51:58,874 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:51:59,382 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\plots\delaunay_triangulation.png
2025-06-10 18:51:59,383 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:51:59,383 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:51:59,389 - prism_main - INFO - 共有 284 个时间点需要处理
2025-06-10 18:51:59,389 - parallel_processing - INFO - 开始并行插值处理，共 284 个时间点
2025-06-10 18:52:02,827 - parallel_processing - INFO - 并行插值完成，成功处理 284 个时间点
2025-06-10 18:52:03,094 - prism_main - INFO - 插值计算完成
2025-06-10 18:52:03,095 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:52:03,095 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:52:03,130 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\evaluation\evaluation_report_20250610_185203.txt
2025-06-10 18:52:03,145 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\evaluation\detailed_metrics_20250610_185203.csv
2025-06-10 18:52:03,464 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\plots\scatter_plot_20250610_185203.png
2025-06-10 18:52:03,465 - prism_main - INFO - 保存插值结果...
2025-06-10 18:52:03,517 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\points
2025-06-10 18:52:03,518 - prism_main - INFO - 保存权重信息...
2025-06-10 18:52:03,568 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-3\evaluation\weights_20250610_185203.csv
2025-06-10 18:52:04,300 - prism_main - INFO - 内存清理完成
2025-06-10 18:52:04,301 - prism_main - INFO - ============================================================
2025-06-10 18:52:04,301 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.04 秒
2025-06-10 18:52:04,301 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2019-3
2025-06-10 18:52:04,301 - prism_main - INFO - ============================================================
2025-06-10 18:52:04,302 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:52:04,302 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:52:04,305 - prism_main - INFO - ============================================================
2025-06-10 18:52:04,305 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:52:04,305 - prism_main - INFO - ============================================================
2025-06-10 18:52:04,305 - prism_main - INFO - 开始加载数据...
2025-06-10 18:52:04,305 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:52:04,308 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:52:04,308 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:52:04,637 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:52:04,637 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:52:04,643 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:52:04,643 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:52:04,685 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:52:04,922 - prism_main - INFO - 数据加载完成
2025-06-10 18:52:04,922 - prism_main - INFO - 构建空间结构...
2025-06-10 18:52:04,922 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:52:04,926 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:52:05,434 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\plots\delaunay_triangulation.png
2025-06-10 18:52:05,434 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:52:05,435 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:52:05,441 - prism_main - INFO - 共有 353 个时间点需要处理
2025-06-10 18:52:05,441 - parallel_processing - INFO - 开始并行插值处理，共 353 个时间点
2025-06-10 18:52:09,088 - parallel_processing - INFO - 并行插值完成，成功处理 353 个时间点
2025-06-10 18:52:09,355 - prism_main - INFO - 插值计算完成
2025-06-10 18:52:09,355 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:52:09,356 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:52:09,389 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\evaluation\evaluation_report_20250610_185209.txt
2025-06-10 18:52:09,395 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\evaluation\detailed_metrics_20250610_185209.csv
2025-06-10 18:52:09,726 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\plots\scatter_plot_20250610_185209.png
2025-06-10 18:52:09,727 - prism_main - INFO - 保存插值结果...
2025-06-10 18:52:09,783 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\points
2025-06-10 18:52:09,783 - prism_main - INFO - 保存权重信息...
2025-06-10 18:52:09,837 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2019-4\evaluation\weights_20250610_185209.csv
2025-06-10 18:52:10,603 - prism_main - INFO - 内存清理完成
2025-06-10 18:52:10,603 - prism_main - INFO - ============================================================
2025-06-10 18:52:10,603 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.30 秒
2025-06-10 18:52:10,603 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2019-4
2025-06-10 18:52:10,603 - prism_main - INFO - ============================================================
2025-06-10 18:52:10,605 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:52:10,605 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:52:10,607 - prism_main - INFO - ============================================================
2025-06-10 18:52:10,607 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:52:10,607 - prism_main - INFO - ============================================================
2025-06-10 18:52:10,607 - prism_main - INFO - 开始加载数据...
2025-06-10 18:52:10,607 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:52:10,609 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:52:10,609 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:52:10,940 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:52:10,941 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:52:10,947 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:52:10,947 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:52:10,987 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:52:11,225 - prism_main - INFO - 数据加载完成
2025-06-10 18:52:11,225 - prism_main - INFO - 构建空间结构...
2025-06-10 18:52:11,225 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:52:11,228 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:52:11,743 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\plots\delaunay_triangulation.png
2025-06-10 18:52:11,744 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:52:11,744 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:52:11,749 - prism_main - INFO - 共有 254 个时间点需要处理
2025-06-10 18:52:11,749 - parallel_processing - INFO - 开始并行插值处理，共 254 个时间点
2025-06-10 18:52:14,912 - parallel_processing - INFO - 并行插值完成，成功处理 254 个时间点
2025-06-10 18:52:15,191 - prism_main - INFO - 插值计算完成
2025-06-10 18:52:15,192 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:52:15,192 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:52:15,217 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\evaluation\evaluation_report_20250610_185215.txt
2025-06-10 18:52:15,230 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\evaluation\detailed_metrics_20250610_185215.csv
2025-06-10 18:52:15,539 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\plots\scatter_plot_20250610_185215.png
2025-06-10 18:52:15,540 - prism_main - INFO - 保存插值结果...
2025-06-10 18:52:15,588 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\points
2025-06-10 18:52:15,588 - prism_main - INFO - 保存权重信息...
2025-06-10 18:52:15,625 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-1\evaluation\weights_20250610_185215.csv
2025-06-10 18:52:16,382 - prism_main - INFO - 内存清理完成
2025-06-10 18:52:16,383 - prism_main - INFO - ============================================================
2025-06-10 18:52:16,383 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.78 秒
2025-06-10 18:52:16,383 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2020-1
2025-06-10 18:52:16,383 - prism_main - INFO - ============================================================
2025-06-10 18:52:16,384 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:52:16,384 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:52:16,386 - prism_main - INFO - ============================================================
2025-06-10 18:52:16,386 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:52:16,387 - prism_main - INFO - ============================================================
2025-06-10 18:52:16,387 - prism_main - INFO - 开始加载数据...
2025-06-10 18:52:16,387 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:52:16,389 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:52:16,389 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:52:16,725 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:52:16,725 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:52:16,730 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:52:16,731 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:52:16,772 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:52:17,024 - prism_main - INFO - 数据加载完成
2025-06-10 18:52:17,025 - prism_main - INFO - 构建空间结构...
2025-06-10 18:52:17,025 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:52:17,027 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:52:17,531 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\plots\delaunay_triangulation.png
2025-06-10 18:52:17,532 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:52:17,532 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:52:17,538 - prism_main - INFO - 共有 337 个时间点需要处理
2025-06-10 18:52:17,538 - parallel_processing - INFO - 开始并行插值处理，共 337 个时间点
2025-06-10 18:52:21,287 - parallel_processing - INFO - 并行插值完成，成功处理 337 个时间点
2025-06-10 18:52:21,585 - prism_main - INFO - 插值计算完成
2025-06-10 18:52:21,585 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:52:21,586 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:52:21,625 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\evaluation\evaluation_report_20250610_185221.txt
2025-06-10 18:52:21,640 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\evaluation\detailed_metrics_20250610_185221.csv
2025-06-10 18:52:21,970 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\plots\scatter_plot_20250610_185221.png
2025-06-10 18:52:21,972 - prism_main - INFO - 保存插值结果...
2025-06-10 18:52:22,028 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\points
2025-06-10 18:52:22,028 - prism_main - INFO - 保存权重信息...
2025-06-10 18:52:22,084 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-2\evaluation\weights_20250610_185222.csv
2025-06-10 18:52:22,899 - prism_main - INFO - 内存清理完成
2025-06-10 18:52:22,899 - prism_main - INFO - ============================================================
2025-06-10 18:52:22,900 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.51 秒
2025-06-10 18:52:22,900 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2020-2
2025-06-10 18:52:22,900 - prism_main - INFO - ============================================================
2025-06-10 18:52:22,901 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:52:22,901 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:52:22,904 - prism_main - INFO - ============================================================
2025-06-10 18:52:22,904 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:52:22,904 - prism_main - INFO - ============================================================
2025-06-10 18:52:22,904 - prism_main - INFO - 开始加载数据...
2025-06-10 18:52:22,904 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:52:22,906 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:52:22,906 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:52:23,242 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:52:23,242 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:52:23,247 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:52:23,247 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:52:23,291 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:52:23,554 - prism_main - INFO - 数据加载完成
2025-06-10 18:52:23,554 - prism_main - INFO - 构建空间结构...
2025-06-10 18:52:23,554 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:52:23,557 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:52:24,062 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\plots\delaunay_triangulation.png
2025-06-10 18:52:24,063 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:52:24,063 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:52:24,070 - prism_main - INFO - 共有 390 个时间点需要处理
2025-06-10 18:52:24,070 - parallel_processing - INFO - 开始并行插值处理，共 390 个时间点
2025-06-10 18:52:27,993 - parallel_processing - INFO - 并行插值完成，成功处理 390 个时间点
2025-06-10 18:52:28,284 - prism_main - INFO - 插值计算完成
2025-06-10 18:52:28,285 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:52:28,285 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:52:28,329 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\evaluation\evaluation_report_20250610_185228.txt
2025-06-10 18:52:28,343 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\evaluation\detailed_metrics_20250610_185228.csv
2025-06-10 18:52:28,694 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\plots\scatter_plot_20250610_185228.png
2025-06-10 18:52:28,695 - prism_main - INFO - 保存插值结果...
2025-06-10 18:52:28,755 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\points
2025-06-10 18:52:28,755 - prism_main - INFO - 保存权重信息...
2025-06-10 18:52:28,818 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-3\evaluation\weights_20250610_185228.csv
2025-06-10 18:52:29,668 - prism_main - INFO - 内存清理完成
2025-06-10 18:52:29,668 - prism_main - INFO - ============================================================
2025-06-10 18:52:29,669 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.76 秒
2025-06-10 18:52:29,669 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2020-3
2025-06-10 18:52:29,669 - prism_main - INFO - ============================================================
2025-06-10 18:52:29,669 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:52:29,669 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:52:29,672 - prism_main - INFO - ============================================================
2025-06-10 18:52:29,672 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:52:29,672 - prism_main - INFO - ============================================================
2025-06-10 18:52:29,672 - prism_main - INFO - 开始加载数据...
2025-06-10 18:52:29,673 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:52:29,674 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:52:29,674 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:52:30,004 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:52:30,004 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:52:30,010 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:52:30,010 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:52:30,053 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:52:30,312 - prism_main - INFO - 数据加载完成
2025-06-10 18:52:30,313 - prism_main - INFO - 构建空间结构...
2025-06-10 18:52:30,313 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:52:30,316 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:52:30,840 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\plots\delaunay_triangulation.png
2025-06-10 18:52:30,841 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:52:30,841 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:52:30,848 - prism_main - INFO - 共有 429 个时间点需要处理
2025-06-10 18:52:30,849 - parallel_processing - INFO - 开始并行插值处理，共 429 个时间点
2025-06-10 18:52:35,109 - parallel_processing - INFO - 并行插值完成，成功处理 429 个时间点
2025-06-10 18:52:35,423 - prism_main - INFO - 插值计算完成
2025-06-10 18:52:35,424 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:52:35,424 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:52:35,466 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\evaluation\evaluation_report_20250610_185235.txt
2025-06-10 18:52:35,472 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\evaluation\detailed_metrics_20250610_185235.csv
2025-06-10 18:52:35,847 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\plots\scatter_plot_20250610_185235.png
2025-06-10 18:52:35,848 - prism_main - INFO - 保存插值结果...
2025-06-10 18:52:35,909 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\points
2025-06-10 18:52:35,909 - prism_main - INFO - 保存权重信息...
2025-06-10 18:52:35,972 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2020-4\evaluation\weights_20250610_185235.csv
2025-06-10 18:52:36,814 - prism_main - INFO - 内存清理完成
2025-06-10 18:52:36,815 - prism_main - INFO - ============================================================
2025-06-10 18:52:36,815 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.14 秒
2025-06-10 18:52:36,815 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2020-4
2025-06-10 18:52:36,815 - prism_main - INFO - ============================================================
2025-06-10 18:52:36,816 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:52:36,816 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:52:36,819 - prism_main - INFO - ============================================================
2025-06-10 18:52:36,819 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:52:36,819 - prism_main - INFO - ============================================================
2025-06-10 18:52:36,819 - prism_main - INFO - 开始加载数据...
2025-06-10 18:52:36,819 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:52:36,821 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:52:36,821 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:52:37,148 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:52:37,148 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:52:37,153 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:52:37,155 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:52:37,193 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:52:37,457 - prism_main - INFO - 数据加载完成
2025-06-10 18:52:37,457 - prism_main - INFO - 构建空间结构...
2025-06-10 18:52:37,457 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:52:37,460 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:52:37,969 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\plots\delaunay_triangulation.png
2025-06-10 18:52:37,970 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:52:37,970 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:52:37,972 - prism_main - INFO - 共有 143 个时间点需要处理
2025-06-10 18:52:37,974 - parallel_processing - INFO - 开始并行插值处理，共 143 个时间点
2025-06-10 18:52:40,593 - parallel_processing - INFO - 并行插值完成，成功处理 143 个时间点
2025-06-10 18:52:40,888 - prism_main - INFO - 插值计算完成
2025-06-10 18:52:40,888 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:52:40,889 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:52:40,905 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\evaluation\evaluation_report_20250610_185240.txt
2025-06-10 18:52:40,909 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\evaluation\detailed_metrics_20250610_185240.csv
2025-06-10 18:52:41,202 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\plots\scatter_plot_20250610_185240.png
2025-06-10 18:52:41,204 - prism_main - INFO - 保存插值结果...
2025-06-10 18:52:41,240 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\points
2025-06-10 18:52:41,241 - prism_main - INFO - 保存权重信息...
2025-06-10 18:52:41,264 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-1\evaluation\weights_20250610_185241.csv
2025-06-10 18:52:42,090 - prism_main - INFO - 内存清理完成
2025-06-10 18:52:42,091 - prism_main - INFO - ============================================================
2025-06-10 18:52:42,091 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.27 秒
2025-06-10 18:52:42,091 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2021-1
2025-06-10 18:52:42,091 - prism_main - INFO - ============================================================
2025-06-10 18:52:42,092 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:52:42,092 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:52:42,094 - prism_main - INFO - ============================================================
2025-06-10 18:52:42,094 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:52:42,094 - prism_main - INFO - ============================================================
2025-06-10 18:52:42,094 - prism_main - INFO - 开始加载数据...
2025-06-10 18:52:42,094 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:52:42,096 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:52:42,096 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:52:42,427 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:52:42,427 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:52:42,432 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:52:42,434 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:52:42,475 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:52:42,760 - prism_main - INFO - 数据加载完成
2025-06-10 18:52:42,760 - prism_main - INFO - 构建空间结构...
2025-06-10 18:52:42,760 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:52:42,762 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:52:43,266 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\plots\delaunay_triangulation.png
2025-06-10 18:52:43,267 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:52:43,267 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:52:43,272 - prism_main - INFO - 共有 334 个时间点需要处理
2025-06-10 18:52:43,276 - parallel_processing - INFO - 开始并行插值处理，共 334 个时间点
2025-06-10 18:52:46,915 - parallel_processing - INFO - 并行插值完成，成功处理 334 个时间点
2025-06-10 18:52:47,231 - prism_main - INFO - 插值计算完成
2025-06-10 18:52:47,231 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:52:47,232 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:52:47,266 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\evaluation\evaluation_report_20250610_185247.txt
2025-06-10 18:52:47,280 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\evaluation\detailed_metrics_20250610_185247.csv
2025-06-10 18:52:47,610 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\plots\scatter_plot_20250610_185247.png
2025-06-10 18:52:47,613 - prism_main - INFO - 保存插值结果...
2025-06-10 18:52:47,666 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\points
2025-06-10 18:52:47,667 - prism_main - INFO - 保存权重信息...
2025-06-10 18:52:47,719 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2021-2\evaluation\weights_20250610_185247.csv
2025-06-10 18:52:48,590 - prism_main - INFO - 内存清理完成
2025-06-10 18:52:48,590 - prism_main - INFO - ============================================================
2025-06-10 18:52:48,590 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.50 秒
2025-06-10 18:52:48,590 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2021-2
2025-06-10 18:52:48,590 - prism_main - INFO - ============================================================
2025-06-10 18:52:48,591 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:52:48,591 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:52:48,593 - prism_main - INFO - ============================================================
2025-06-10 18:52:48,594 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:52:48,594 - prism_main - INFO - ============================================================
2025-06-10 18:52:48,594 - prism_main - INFO - 开始加载数据...
2025-06-10 18:52:48,594 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:52:48,595 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:52:48,596 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:52:48,930 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:52:48,930 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:52:48,936 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:52:48,936 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:52:48,981 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:52:49,254 - prism_main - INFO - 数据加载完成
2025-06-10 18:52:49,255 - prism_main - INFO - 构建空间结构...
2025-06-10 18:52:49,255 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:52:49,258 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:52:49,767 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\plots\delaunay_triangulation.png
2025-06-10 18:52:49,767 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:52:49,767 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:52:49,774 - prism_main - INFO - 共有 355 个时间点需要处理
2025-06-10 18:52:49,774 - parallel_processing - INFO - 开始并行插值处理，共 355 个时间点
2025-06-10 18:52:53,818 - parallel_processing - INFO - 并行插值完成，成功处理 355 个时间点
2025-06-10 18:52:54,138 - prism_main - INFO - 插值计算完成
2025-06-10 18:52:54,138 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:52:54,139 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:52:54,189 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\evaluation\evaluation_report_20250610_185254.txt
2025-06-10 18:52:54,205 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\evaluation\detailed_metrics_20250610_185254.csv
2025-06-10 18:52:54,544 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\plots\scatter_plot_20250610_185254.png
2025-06-10 18:52:54,546 - prism_main - INFO - 保存插值结果...
2025-06-10 18:52:54,604 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\points
2025-06-10 18:52:54,604 - prism_main - INFO - 保存权重信息...
2025-06-10 18:52:54,674 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-1\evaluation\weights_20250610_185254.csv
2025-06-10 18:52:55,600 - prism_main - INFO - 内存清理完成
2025-06-10 18:52:55,600 - prism_main - INFO - ============================================================
2025-06-10 18:52:55,600 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.01 秒
2025-06-10 18:52:55,600 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2022-1
2025-06-10 18:52:55,600 - prism_main - INFO - ============================================================
2025-06-10 18:52:55,601 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:52:55,601 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:52:55,605 - prism_main - INFO - ============================================================
2025-06-10 18:52:55,605 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:52:55,605 - prism_main - INFO - ============================================================
2025-06-10 18:52:55,605 - prism_main - INFO - 开始加载数据...
2025-06-10 18:52:55,605 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:52:55,607 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:52:55,607 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:52:55,939 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:52:55,939 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:52:55,945 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:52:55,945 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:52:55,993 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:52:56,282 - prism_main - INFO - 数据加载完成
2025-06-10 18:52:56,282 - prism_main - INFO - 构建空间结构...
2025-06-10 18:52:56,282 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:52:56,285 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:52:56,795 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\plots\delaunay_triangulation.png
2025-06-10 18:52:56,795 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:52:56,795 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:52:56,804 - prism_main - INFO - 共有 541 个时间点需要处理
2025-06-10 18:52:56,805 - parallel_processing - INFO - 开始并行插值处理，共 541 个时间点
2025-06-10 18:53:01,965 - parallel_processing - INFO - 并行插值完成，成功处理 541 个时间点
2025-06-10 18:53:02,309 - prism_main - INFO - 插值计算完成
2025-06-10 18:53:02,309 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:53:02,310 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:53:02,386 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\evaluation\evaluation_report_20250610_185302.txt
2025-06-10 18:53:02,402 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\evaluation\detailed_metrics_20250610_185302.csv
2025-06-10 18:53:02,785 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\plots\scatter_plot_20250610_185302.png
2025-06-10 18:53:02,786 - prism_main - INFO - 保存插值结果...
2025-06-10 18:53:02,859 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\points
2025-06-10 18:53:02,859 - prism_main - INFO - 保存权重信息...
2025-06-10 18:53:02,964 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-2\evaluation\weights_20250610_185302.csv
2025-06-10 18:53:03,928 - prism_main - INFO - 内存清理完成
2025-06-10 18:53:03,929 - prism_main - INFO - ============================================================
2025-06-10 18:53:03,929 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 8.32 秒
2025-06-10 18:53:03,929 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2022-2
2025-06-10 18:53:03,929 - prism_main - INFO - ============================================================
2025-06-10 18:53:03,930 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:53:03,930 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:53:03,935 - prism_main - INFO - ============================================================
2025-06-10 18:53:03,935 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:53:03,936 - prism_main - INFO - ============================================================
2025-06-10 18:53:03,936 - prism_main - INFO - 开始加载数据...
2025-06-10 18:53:03,936 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:53:03,938 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:53:03,938 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:53:04,266 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:53:04,266 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:53:04,273 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:53:04,273 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:53:04,316 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:53:04,604 - prism_main - INFO - 数据加载完成
2025-06-10 18:53:04,605 - prism_main - INFO - 构建空间结构...
2025-06-10 18:53:04,605 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:53:04,608 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:53:05,113 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\plots\delaunay_triangulation.png
2025-06-10 18:53:05,114 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:53:05,114 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:53:05,121 - prism_main - INFO - 共有 432 个时间点需要处理
2025-06-10 18:53:05,121 - parallel_processing - INFO - 开始并行插值处理，共 432 个时间点
2025-06-10 18:53:09,396 - parallel_processing - INFO - 并行插值完成，成功处理 432 个时间点
2025-06-10 18:53:09,728 - prism_main - INFO - 插值计算完成
2025-06-10 18:53:09,728 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:53:09,729 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:53:09,785 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\evaluation\evaluation_report_20250610_185309.txt
2025-06-10 18:53:09,792 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\evaluation\detailed_metrics_20250610_185309.csv
2025-06-10 18:53:10,141 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\plots\scatter_plot_20250610_185309.png
2025-06-10 18:53:10,142 - prism_main - INFO - 保存插值结果...
2025-06-10 18:53:10,206 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\points
2025-06-10 18:53:10,206 - prism_main - INFO - 保存权重信息...
2025-06-10 18:53:10,280 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2022-3\evaluation\weights_20250610_185310.csv
2025-06-10 18:53:11,245 - prism_main - INFO - 内存清理完成
2025-06-10 18:53:11,245 - prism_main - INFO - ============================================================
2025-06-10 18:53:11,245 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.31 秒
2025-06-10 18:53:11,246 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2022-3
2025-06-10 18:53:11,246 - prism_main - INFO - ============================================================
2025-06-10 18:53:11,247 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:53:11,247 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:53:11,250 - prism_main - INFO - ============================================================
2025-06-10 18:53:11,250 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:53:11,251 - prism_main - INFO - ============================================================
2025-06-10 18:53:11,251 - prism_main - INFO - 开始加载数据...
2025-06-10 18:53:11,251 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:53:11,253 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:53:11,253 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:53:11,587 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:53:11,587 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:53:11,593 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:53:11,593 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:53:11,635 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:53:11,935 - prism_main - INFO - 数据加载完成
2025-06-10 18:53:11,935 - prism_main - INFO - 构建空间结构...
2025-06-10 18:53:11,936 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:53:11,938 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:53:12,439 - delaunay_triangulation - INFO - Delaunay三角网可视化已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\plots\delaunay_triangulation.png
2025-06-10 18:53:12,440 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:53:12,440 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:53:12,448 - prism_main - INFO - 共有 463 个时间点需要处理
2025-06-10 18:53:12,448 - parallel_processing - INFO - 开始并行插值处理，共 463 个时间点
2025-06-10 18:53:16,999 - parallel_processing - INFO - 并行插值完成，成功处理 463 个时间点
2025-06-10 18:53:17,341 - prism_main - INFO - 插值计算完成
2025-06-10 18:53:17,342 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:53:17,342 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:53:17,399 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\evaluation\evaluation_report_20250610_185317.txt
2025-06-10 18:53:17,405 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\evaluation\detailed_metrics_20250610_185317.csv
2025-06-10 18:53:17,770 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\plots\scatter_plot_20250610_185317.png
2025-06-10 18:53:17,772 - prism_main - INFO - 保存插值结果...
2025-06-10 18:53:17,838 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\points
2025-06-10 18:53:17,839 - prism_main - INFO - 保存权重信息...
2025-06-10 18:53:17,923 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2023-1\evaluation\weights_20250610_185317.csv
2025-06-10 18:53:18,969 - prism_main - INFO - 内存清理完成
2025-06-10 18:53:18,969 - prism_main - INFO - ============================================================
2025-06-10 18:53:18,970 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.72 秒
2025-06-10 18:53:18,970 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2023-1
2025-06-10 18:53:18,970 - prism_main - INFO - ============================================================
2025-06-10 18:53:18,986 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:53:18,986 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:53:18,986 - prism_main - INFO - ============================================================
2025-06-10 18:53:18,986 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:53:18,987 - prism_main - INFO - ============================================================
2025-06-10 18:53:18,987 - prism_main - INFO - 开始加载数据...
2025-06-10 18:53:18,987 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:53:18,988 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:53:18,988 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:53:19,323 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:53:19,323 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:53:19,329 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:53:19,329 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:53:19,373 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:53:19,730 - prism_main - INFO - 数据加载完成
2025-06-10 18:53:19,730 - prism_main - INFO - 构建空间结构...
2025-06-10 18:53:19,730 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:53:19,733 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:53:19,733 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:53:19,733 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:53:19,738 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:53:19,738 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:53:23,686 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:53:24,047 - prism_main - INFO - 插值计算完成
2025-06-10 18:53:24,047 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:53:24,047 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:53:24,095 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1\evaluation\evaluation_report_20250610_185324.txt
2025-06-10 18:53:24,099 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1\evaluation\detailed_metrics_20250610_185324.csv
2025-06-10 18:53:24,437 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1\plots\scatter_plot_20250610_185324.png
2025-06-10 18:53:24,438 - prism_main - INFO - 保存插值结果...
2025-06-10 18:53:24,499 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1\points
2025-06-10 18:53:24,499 - prism_main - INFO - 保存权重信息...
2025-06-10 18:53:24,569 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1\evaluation\weights_20250610_185324.csv
2025-06-10 18:53:25,570 - prism_main - INFO - 内存清理完成
2025-06-10 18:53:25,571 - prism_main - INFO - ============================================================
2025-06-10 18:53:25,571 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.59 秒
2025-06-10 18:53:25,571 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_1
2025-06-10 18:53:25,571 - prism_main - INFO - ============================================================
2025-06-10 18:53:25,575 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:53:25,576 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:53:25,578 - prism_main - INFO - ============================================================
2025-06-10 18:53:25,578 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:53:25,578 - prism_main - INFO - ============================================================
2025-06-10 18:53:25,578 - prism_main - INFO - 开始加载数据...
2025-06-10 18:53:25,578 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:53:25,580 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:53:25,580 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:53:25,916 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:53:25,916 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:53:25,922 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:53:25,923 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:53:25,964 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:53:26,291 - prism_main - INFO - 数据加载完成
2025-06-10 18:53:26,291 - prism_main - INFO - 构建空间结构...
2025-06-10 18:53:26,292 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:53:26,295 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:53:26,295 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:53:26,295 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:53:26,301 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:53:26,301 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:53:30,175 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:53:30,540 - prism_main - INFO - 插值计算完成
2025-06-10 18:53:30,541 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:53:30,541 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:53:30,579 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2\evaluation\evaluation_report_20250610_185330.txt
2025-06-10 18:53:30,585 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2\evaluation\detailed_metrics_20250610_185330.csv
2025-06-10 18:53:30,920 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2\plots\scatter_plot_20250610_185330.png
2025-06-10 18:53:30,922 - prism_main - INFO - 保存插值结果...
2025-06-10 18:53:30,983 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2\points
2025-06-10 18:53:30,983 - prism_main - INFO - 保存权重信息...
2025-06-10 18:53:31,047 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2\evaluation\weights_20250610_185331.csv
2025-06-10 18:53:32,072 - prism_main - INFO - 内存清理完成
2025-06-10 18:53:32,072 - prism_main - INFO - ============================================================
2025-06-10 18:53:32,072 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.49 秒
2025-06-10 18:53:32,072 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-1_opt_2
2025-06-10 18:53:32,072 - prism_main - INFO - ============================================================
2025-06-10 18:53:32,076 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:53:32,076 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:53:32,079 - prism_main - INFO - ============================================================
2025-06-10 18:53:32,079 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:53:32,079 - prism_main - INFO - ============================================================
2025-06-10 18:53:32,079 - prism_main - INFO - 开始加载数据...
2025-06-10 18:53:32,079 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:53:32,081 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:53:32,081 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:53:32,410 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:53:32,410 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:53:32,416 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:53:32,417 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:53:32,462 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:53:32,785 - prism_main - INFO - 数据加载完成
2025-06-10 18:53:32,785 - prism_main - INFO - 构建空间结构...
2025-06-10 18:53:32,786 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:53:32,788 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:53:32,789 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:53:32,789 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:53:32,796 - prism_main - INFO - 共有 504 个时间点需要处理
2025-06-10 18:53:32,797 - parallel_processing - INFO - 开始并行插值处理，共 504 个时间点
2025-06-10 18:53:37,646 - parallel_processing - INFO - 并行插值完成，成功处理 504 个时间点
2025-06-10 18:53:38,016 - prism_main - INFO - 插值计算完成
2025-06-10 18:53:38,017 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:53:38,017 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:53:38,078 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1\evaluation\evaluation_report_20250610_185338.txt
2025-06-10 18:53:38,084 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1\evaluation\detailed_metrics_20250610_185338.csv
2025-06-10 18:53:38,460 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1\plots\scatter_plot_20250610_185338.png
2025-06-10 18:53:38,461 - prism_main - INFO - 保存插值结果...
2025-06-10 18:53:38,534 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1\points
2025-06-10 18:53:38,535 - prism_main - INFO - 保存权重信息...
2025-06-10 18:53:38,649 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1\evaluation\weights_20250610_185338.csv
2025-06-10 18:53:39,718 - prism_main - INFO - 内存清理完成
2025-06-10 18:53:39,718 - prism_main - INFO - ============================================================
2025-06-10 18:53:39,718 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.64 秒
2025-06-10 18:53:39,718 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_1
2025-06-10 18:53:39,718 - prism_main - INFO - ============================================================
2025-06-10 18:53:39,723 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:53:39,723 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:53:39,727 - prism_main - INFO - ============================================================
2025-06-10 18:53:39,728 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:53:39,728 - prism_main - INFO - ============================================================
2025-06-10 18:53:39,728 - prism_main - INFO - 开始加载数据...
2025-06-10 18:53:39,728 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:53:39,729 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:53:39,729 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:53:40,062 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:53:40,062 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:53:40,068 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:53:40,068 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:53:40,113 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:53:40,449 - prism_main - INFO - 数据加载完成
2025-06-10 18:53:40,450 - prism_main - INFO - 构建空间结构...
2025-06-10 18:53:40,450 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:53:40,452 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:53:40,453 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:53:40,453 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:53:40,461 - prism_main - INFO - 共有 504 个时间点需要处理
2025-06-10 18:53:40,461 - parallel_processing - INFO - 开始并行插值处理，共 504 个时间点
2025-06-10 18:53:45,267 - parallel_processing - INFO - 并行插值完成，成功处理 504 个时间点
2025-06-10 18:53:45,631 - prism_main - INFO - 插值计算完成
2025-06-10 18:53:45,632 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:53:45,632 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:53:45,698 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2\evaluation\evaluation_report_20250610_185345.txt
2025-06-10 18:53:45,706 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2\evaluation\detailed_metrics_20250610_185345.csv
2025-06-10 18:53:46,076 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2\plots\scatter_plot_20250610_185345.png
2025-06-10 18:53:46,078 - prism_main - INFO - 保存插值结果...
2025-06-10 18:53:46,148 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2\points
2025-06-10 18:53:46,149 - prism_main - INFO - 保存权重信息...
2025-06-10 18:53:46,248 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2\evaluation\weights_20250610_185346.csv
2025-06-10 18:53:47,319 - prism_main - INFO - 内存清理完成
2025-06-10 18:53:47,320 - prism_main - INFO - ============================================================
2025-06-10 18:53:47,320 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.59 秒
2025-06-10 18:53:47,320 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-2_opt_2
2025-06-10 18:53:47,320 - prism_main - INFO - ============================================================
2025-06-10 18:53:47,324 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:53:47,324 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:53:47,329 - prism_main - INFO - ============================================================
2025-06-10 18:53:47,329 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:53:47,329 - prism_main - INFO - ============================================================
2025-06-10 18:53:47,329 - prism_main - INFO - 开始加载数据...
2025-06-10 18:53:47,329 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:53:47,331 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:53:47,331 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:53:47,663 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:53:47,663 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:53:47,669 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:53:47,669 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:53:47,711 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:53:48,056 - prism_main - INFO - 数据加载完成
2025-06-10 18:53:48,056 - prism_main - INFO - 构建空间结构...
2025-06-10 18:53:48,056 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:53:48,059 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:53:48,059 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:53:48,060 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:53:48,065 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:53:48,066 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:53:52,187 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:53:52,548 - prism_main - INFO - 插值计算完成
2025-06-10 18:53:52,549 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:53:52,549 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:53:52,594 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1\evaluation\evaluation_report_20250610_185352.txt
2025-06-10 18:53:52,600 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1\evaluation\detailed_metrics_20250610_185352.csv
2025-06-10 18:53:52,929 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1\plots\scatter_plot_20250610_185352.png
2025-06-10 18:53:52,931 - prism_main - INFO - 保存插值结果...
2025-06-10 18:53:52,993 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1\points
2025-06-10 18:53:52,993 - prism_main - INFO - 保存权重信息...
2025-06-10 18:53:53,075 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1\evaluation\weights_20250610_185353.csv
2025-06-10 18:53:54,135 - prism_main - INFO - 内存清理完成
2025-06-10 18:53:54,136 - prism_main - INFO - ============================================================
2025-06-10 18:53:54,136 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.81 秒
2025-06-10 18:53:54,136 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_1
2025-06-10 18:53:54,136 - prism_main - INFO - ============================================================
2025-06-10 18:53:54,140 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:53:54,140 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:53:54,145 - prism_main - INFO - ============================================================
2025-06-10 18:53:54,146 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:53:54,146 - prism_main - INFO - ============================================================
2025-06-10 18:53:54,146 - prism_main - INFO - 开始加载数据...
2025-06-10 18:53:54,146 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:53:54,148 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:53:54,148 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:53:54,481 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:53:54,482 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:53:54,488 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:53:54,488 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:53:54,530 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:53:54,856 - prism_main - INFO - 数据加载完成
2025-06-10 18:53:54,856 - prism_main - INFO - 构建空间结构...
2025-06-10 18:53:54,857 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:53:54,859 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:53:54,860 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:53:54,860 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:53:54,866 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-10 18:53:54,866 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-10 18:53:58,880 - parallel_processing - INFO - 并行插值完成，成功处理 384 个时间点
2025-06-10 18:53:59,256 - prism_main - INFO - 插值计算完成
2025-06-10 18:53:59,256 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:53:59,256 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:53:59,302 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2\evaluation\evaluation_report_20250610_185359.txt
2025-06-10 18:53:59,308 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2\evaluation\detailed_metrics_20250610_185359.csv
2025-06-10 18:53:59,640 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2\plots\scatter_plot_20250610_185359.png
2025-06-10 18:53:59,642 - prism_main - INFO - 保存插值结果...
2025-06-10 18:53:59,702 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2\points
2025-06-10 18:53:59,702 - prism_main - INFO - 保存权重信息...
2025-06-10 18:53:59,778 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2\evaluation\weights_20250610_185359.csv
2025-06-10 18:54:00,829 - prism_main - INFO - 内存清理完成
2025-06-10 18:54:00,830 - prism_main - INFO - ============================================================
2025-06-10 18:54:00,830 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 6.69 秒
2025-06-10 18:54:00,830 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-3_opt_2
2025-06-10 18:54:00,830 - prism_main - INFO - ============================================================
2025-06-10 18:54:00,834 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:54:00,834 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:54:00,838 - prism_main - INFO - ============================================================
2025-06-10 18:54:00,838 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:54:00,838 - prism_main - INFO - ============================================================
2025-06-10 18:54:00,839 - prism_main - INFO - 开始加载数据...
2025-06-10 18:54:00,839 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:54:00,840 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:54:00,841 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:54:01,173 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:54:01,173 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:54:01,179 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:54:01,179 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:54:01,224 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:54:01,547 - prism_main - INFO - 数据加载完成
2025-06-10 18:54:01,547 - prism_main - INFO - 构建空间结构...
2025-06-10 18:54:01,547 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:54:01,550 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:54:01,550 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:54:01,551 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:54:01,559 - prism_main - INFO - 共有 552 个时间点需要处理
2025-06-10 18:54:01,559 - parallel_processing - INFO - 开始并行插值处理，共 552 个时间点
2025-06-10 18:54:06,560 - parallel_processing - INFO - 并行插值完成，成功处理 552 个时间点
2025-06-10 18:54:06,925 - prism_main - INFO - 插值计算完成
2025-06-10 18:54:06,925 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:54:06,926 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:54:07,005 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1\evaluation\evaluation_report_20250610_185407.txt
2025-06-10 18:54:07,015 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1\evaluation\detailed_metrics_20250610_185407.csv
2025-06-10 18:54:07,411 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1\plots\scatter_plot_20250610_185407.png
2025-06-10 18:54:07,412 - prism_main - INFO - 保存插值结果...
2025-06-10 18:54:07,488 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1\points
2025-06-10 18:54:07,488 - prism_main - INFO - 保存权重信息...
2025-06-10 18:54:07,605 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1\evaluation\weights_20250610_185407.csv
2025-06-10 18:54:08,675 - prism_main - INFO - 内存清理完成
2025-06-10 18:54:08,675 - prism_main - INFO - ============================================================
2025-06-10 18:54:08,675 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.84 秒
2025-06-10 18:54:08,675 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_1
2025-06-10 18:54:08,676 - prism_main - INFO - ============================================================
2025-06-10 18:54:08,680 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:54:08,680 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:54:08,685 - prism_main - INFO - ============================================================
2025-06-10 18:54:08,685 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:54:08,685 - prism_main - INFO - ============================================================
2025-06-10 18:54:08,685 - prism_main - INFO - 开始加载数据...
2025-06-10 18:54:08,685 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:54:08,687 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:54:08,687 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:54:09,021 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:54:09,022 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:54:09,028 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:54:09,028 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:54:09,074 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:54:09,405 - prism_main - INFO - 数据加载完成
2025-06-10 18:54:09,405 - prism_main - INFO - 构建空间结构...
2025-06-10 18:54:09,405 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:54:09,408 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:54:09,409 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:54:09,409 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:54:09,417 - prism_main - INFO - 共有 552 个时间点需要处理
2025-06-10 18:54:09,417 - parallel_processing - INFO - 开始并行插值处理，共 552 个时间点
2025-06-10 18:54:14,358 - parallel_processing - INFO - 并行插值完成，成功处理 552 个时间点
2025-06-10 18:54:14,728 - prism_main - INFO - 插值计算完成
2025-06-10 18:54:14,729 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:54:14,729 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:54:14,790 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2\evaluation\evaluation_report_20250610_185414.txt
2025-06-10 18:54:14,796 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2\evaluation\detailed_metrics_20250610_185414.csv
2025-06-10 18:54:15,166 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2\plots\scatter_plot_20250610_185414.png
2025-06-10 18:54:15,167 - prism_main - INFO - 保存插值结果...
2025-06-10 18:54:15,243 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2\points
2025-06-10 18:54:15,244 - prism_main - INFO - 保存权重信息...
2025-06-10 18:54:15,346 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2\evaluation\weights_20250610_185415.csv
2025-06-10 18:54:16,420 - prism_main - INFO - 内存清理完成
2025-06-10 18:54:16,420 - prism_main - INFO - ============================================================
2025-06-10 18:54:16,420 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 7.74 秒
2025-06-10 18:54:16,421 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2009-4_opt_2
2025-06-10 18:54:16,421 - prism_main - INFO - ============================================================
2025-06-10 18:54:16,425 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:54:16,426 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:54:16,431 - prism_main - INFO - ============================================================
2025-06-10 18:54:16,432 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:54:16,432 - prism_main - INFO - ============================================================
2025-06-10 18:54:16,432 - prism_main - INFO - 开始加载数据...
2025-06-10 18:54:16,432 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:54:16,433 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:54:16,433 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:54:16,763 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:54:16,763 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:54:16,769 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:54:16,769 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:54:16,808 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:54:17,152 - prism_main - INFO - 数据加载完成
2025-06-10 18:54:17,153 - prism_main - INFO - 构建空间结构...
2025-06-10 18:54:17,153 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:54:17,155 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:54:17,155 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:54:17,155 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:54:17,158 - prism_main - INFO - 共有 144 个时间点需要处理
2025-06-10 18:54:17,158 - parallel_processing - INFO - 开始并行插值处理，共 144 个时间点
2025-06-10 18:54:19,903 - parallel_processing - INFO - 并行插值完成，成功处理 144 个时间点
2025-06-10 18:54:20,281 - prism_main - INFO - 插值计算完成
2025-06-10 18:54:20,282 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:54:20,282 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:54:20,302 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1\evaluation\evaluation_report_20250610_185420.txt
2025-06-10 18:54:20,305 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1\evaluation\detailed_metrics_20250610_185420.csv
2025-06-10 18:54:20,598 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1\plots\scatter_plot_20250610_185420.png
2025-06-10 18:54:20,599 - prism_main - INFO - 保存插值结果...
2025-06-10 18:54:20,638 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1\points
2025-06-10 18:54:20,638 - prism_main - INFO - 保存权重信息...
2025-06-10 18:54:20,672 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1\evaluation\weights_20250610_185420.csv
2025-06-10 18:54:21,719 - prism_main - INFO - 内存清理完成
2025-06-10 18:54:21,719 - prism_main - INFO - ============================================================
2025-06-10 18:54:21,719 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.29 秒
2025-06-10 18:54:21,720 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_1
2025-06-10 18:54:21,720 - prism_main - INFO - ============================================================
2025-06-10 18:54:21,724 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-10 18:54:21,724 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-10 18:54:21,726 - prism_main - INFO - ============================================================
2025-06-10 18:54:21,726 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-10 18:54:21,727 - prism_main - INFO - ============================================================
2025-06-10 18:54:21,727 - prism_main - INFO - 开始加载数据...
2025-06-10 18:54:21,727 - data_processing - INFO - 正在加载站点信息...
2025-06-10 18:54:21,728 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-10 18:54:21,729 - data_processing - INFO - 正在加载地形数据...
2025-06-10 18:54:22,060 - data_processing - INFO - 地形数据加载完成
2025-06-10 18:54:22,060 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-10 18:54:22,067 - data_processing - INFO - 地形特征提取完成
2025-06-10 18:54:22,067 - data_processing - INFO - 正在加载降雨数据...
2025-06-10 18:54:22,107 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-10 18:54:22,441 - prism_main - INFO - 数据加载完成
2025-06-10 18:54:22,441 - prism_main - INFO - 构建空间结构...
2025-06-10 18:54:22,441 - delaunay_triangulation - INFO - 正在构建Delaunay三角网...
2025-06-10 18:54:22,444 - delaunay_triangulation - INFO - Delaunay三角网构建完成: 总三角形数 61, 高质量三角形 39, 低质量三角形 22
2025-06-10 18:54:22,444 - prism_main - INFO - 三角网统计: 总三角形数=61, 高质量三角形数=39
2025-06-10 18:54:22,444 - prism_main - INFO - 开始执行插值计算...
2025-06-10 18:54:22,447 - prism_main - INFO - 共有 144 个时间点需要处理
2025-06-10 18:54:22,456 - parallel_processing - INFO - 开始并行插值处理，共 144 个时间点
2025-06-10 18:54:25,176 - parallel_processing - INFO - 并行插值完成，成功处理 144 个时间点
2025-06-10 18:54:25,555 - prism_main - INFO - 插值计算完成
2025-06-10 18:54:25,555 - prism_main - INFO - 开始评价插值结果...
2025-06-10 18:54:25,555 - evaluation_metrics - INFO - 正在计算时间序列评价指标...
2025-06-10 18:54:25,574 - evaluation_metrics - INFO - 评价报告已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2\evaluation\evaluation_report_20250610_185425.txt
2025-06-10 18:54:25,577 - evaluation_metrics - INFO - 详细评价指标已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2\evaluation\detailed_metrics_20250610_185425.csv
2025-06-10 18:54:25,858 - evaluation_metrics - INFO - 散点图已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2\plots\scatter_plot_20250610_185425.png
2025-06-10 18:54:25,860 - prism_main - INFO - 保存插值结果...
2025-06-10 18:54:25,899 - prism_main - INFO - 插值结果已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2\points
2025-06-10 18:54:25,899 - prism_main - INFO - 保存权重信息...
2025-06-10 18:54:25,930 - prism_main - INFO - 权重信息已保存到: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2\evaluation\weights_20250610_185425.csv
2025-06-10 18:54:26,995 - prism_main - INFO - 内存清理完成
2025-06-10 18:54:26,995 - prism_main - INFO - ============================================================
2025-06-10 18:54:26,995 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 5.27 秒
2025-06-10 18:54:26,995 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM\2010-1_opt_2
2025-06-10 18:54:26,996 - prism_main - INFO - ============================================================
2025-06-11 16:08:20,841 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-11 16:08:20,852 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-11 16:08:20,852 - prism_main - INFO - ============================================================
2025-06-11 16:08:20,852 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-11 16:08:20,852 - prism_main - INFO - ============================================================
2025-06-11 16:08:20,852 - prism_main - INFO - 开始加载数据...
2025-06-11 16:08:20,853 - data_processing - INFO - 正在加载站点信息...
2025-06-11 16:08:20,855 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-11 16:08:20,855 - data_processing - INFO - 正在加载地形数据...
2025-06-11 16:08:21,288 - data_processing - INFO - 地形数据加载完成
2025-06-11 16:08:21,288 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-11 16:08:21,297 - data_processing - INFO - 地形特征提取完成
2025-06-11 16:08:21,297 - data_processing - INFO - 正在加载降雨数据...
2025-06-11 16:08:21,347 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-11 16:08:21,377 - prism_main - INFO - 数据加载完成
2025-06-11 16:08:21,377 - prism_main - INFO - 构建空间结构...
2025-06-11 16:08:21,377 - delaunay_triangulation - INFO - 构建标准化Delaunay三角网...
2025-06-11 16:08:21,381 - delaunay_triangulation - INFO - 三角网质量统计:
2025-06-11 16:08:21,381 - delaunay_triangulation - INFO -   最小角度: 1.37°
2025-06-11 16:08:21,381 - delaunay_triangulation - INFO -   平均角度: 60.00°
2025-06-11 16:08:21,383 - delaunay_triangulation - INFO -   角度标准差: 38.23°
2025-06-11 16:08:21,383 - delaunay_triangulation - WARNING - 存在过小的三角形角度 (1.37°)，可能影响插值质量
2025-06-11 16:08:21,383 - delaunay_triangulation - INFO - 成功构建标准化Delaunay三角网: 36 个站点, 61 个三角形
2025-06-11 16:08:21,383 - prism_main - ERROR - 构建空间结构失败: 'DelaunayTriangulation' object has no attribute 'visualize_triangulation'
2025-06-11 16:08:21,383 - prism_main - ERROR - PRISM插值工作流程失败: 'DelaunayTriangulation' object has no attribute 'visualize_triangulation'
2025-06-11 16:08:21,384 - __main__ - ERROR - 运行PRISM插值失败: 'DelaunayTriangulation' object has no attribute 'visualize_triangulation'
Traceback (most recent call last):
  File "D:\pythondata\spatial_interpolation\PRISM_python\easy_run.py", line 102, in run_prism_interpolation
    evaluation_results = prism_interpolation.run_complete_workflow()
  File "D:\pythondata\spatial_interpolation\PRISM_python\prism_main.py", line 323, in run_complete_workflow
    self.build_spatial_structure()
  File "D:\pythondata\spatial_interpolation\PRISM_python\prism_main.py", line 105, in build_spatial_structure
    self.delaunay_tri.visualize_triangulation(
AttributeError: 'DelaunayTriangulation' object has no attribute 'visualize_triangulation'
2025-06-11 16:09:50,141 - parallel_processing - INFO - 并行处理器初始化，使用 12 个核心
2025-06-11 16:09:50,141 - prism_main - INFO - PRISM插值系统初始化完成
2025-06-11 16:09:50,141 - prism_main - INFO - ============================================================
2025-06-11 16:09:50,141 - prism_main - INFO - 开始PRISM插值完整工作流程
2025-06-11 16:09:50,142 - prism_main - INFO - ============================================================
2025-06-11 16:09:50,142 - prism_main - INFO - 开始加载数据...
2025-06-11 16:09:50,142 - data_processing - INFO - 正在加载站点信息...
2025-06-11 16:09:50,145 - data_processing - INFO - 成功加载 36 个站点信息
2025-06-11 16:09:50,145 - data_processing - INFO - 正在加载地形数据...
2025-06-11 16:09:50,487 - data_processing - INFO - 地形数据加载完成
2025-06-11 16:09:50,497 - data_processing - INFO - 正在为站点提取地形特征...
2025-06-11 16:09:50,504 - data_processing - INFO - 地形特征提取完成
2025-06-11 16:09:50,505 - data_processing - INFO - 正在加载降雨数据...
2025-06-11 16:09:50,551 - data_processing - INFO - 成功加载 36 个站点的降雨数据
2025-06-11 16:09:50,577 - raster_processing - INFO - 地形栅格数据加载完成，栅格大小: (962, 800)
2025-06-11 16:09:50,577 - prism_main - INFO - 数据加载完成
2025-06-11 16:09:50,578 - prism_main - INFO - 构建空间结构...
2025-06-11 16:09:50,578 - delaunay_triangulation - INFO - 构建标准化Delaunay三角网...
2025-06-11 16:09:50,582 - delaunay_triangulation - INFO - 三角网质量统计:
2025-06-11 16:09:50,583 - delaunay_triangulation - INFO -   最小角度: 1.37°
2025-06-11 16:09:50,583 - delaunay_triangulation - INFO -   平均角度: 60.00°
2025-06-11 16:09:50,583 - delaunay_triangulation - INFO -   角度标准差: 38.23°
2025-06-11 16:09:50,583 - delaunay_triangulation - WARNING - 存在过小的三角形角度 (1.37°)，可能影响插值质量
2025-06-11 16:09:50,583 - delaunay_triangulation - INFO - 成功构建标准化Delaunay三角网: 36 个站点, 61 个三角形
2025-06-11 16:09:50,584 - delaunay_triangulation - INFO - 绘制标准化Delaunay三角网图...
2025-06-11 16:09:51,161 - delaunay_triangulation - INFO - 标准化Delaunay三角网图已保存: D:/pythondata/spatial_interpolation/output/PRISM/2009-1\plots\delaunay_triangulation.png
2025-06-11 16:09:51,161 - prism_main - INFO - 三角网统计: 总三角形数=0, 高质量三角形数=0
2025-06-11 16:09:51,161 - prism_main - INFO - 开始执行插值计算...
2025-06-11 16:09:51,167 - prism_main - INFO - 共有 384 个时间点需要处理
2025-06-11 16:09:51,167 - parallel_processing - INFO - 开始并行插值处理，共 384 个时间点
2025-06-11 16:09:51,167 - parallel_processing - ERROR - 并行插值处理失败: 'DelaunayTriangulation' object has no attribute 'kdtree'
2025-06-11 16:09:51,169 - prism_main - INFO - 插值计算完成
2025-06-11 16:09:51,169 - prism_main - INFO - 开始评价插值结果...
2025-06-11 16:09:51,169 - prism_main - WARNING - 没有插值结果可供评价
2025-06-11 16:09:51,169 - prism_main - INFO - 保存插值结果...
2025-06-11 16:09:51,169 - prism_main - WARNING - 没有插值结果可保存
2025-06-11 16:09:51,255 - prism_main - INFO - 内存清理完成
2025-06-11 16:09:51,255 - prism_main - INFO - ============================================================
2025-06-11 16:09:51,255 - prism_main - INFO - PRISM插值工作流程完成！总耗时: 1.11 秒
2025-06-11 16:09:51,255 - prism_main - INFO - 结果保存在: D:/pythondata/spatial_interpolation/output/PRISM/2009-1
2025-06-11 16:09:51,255 - prism_main - INFO - ============================================================

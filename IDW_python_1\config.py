"""
IDW空间插值系统配置文件

本配置文件包含所有可调参数，新手用户只需修改此文件即可运行系统。
所有参数都有详细的中文注释说明。

作者：空间插值系统
版本：1.0
"""

import os

# ==================== 基础路径配置 ====================
# 输入数据目录（包含各个洪水事件文件夹）
INPUT_DIR = "../input_another"

# 站点信息文件
STATIONS_FILE = "../stations.csv"

# Delaunay权重文件
DELAUNAY_WEIGHTS_FILE = "../output/Delaunay/delaunay_analysis_summary.csv"

# 输出目录
OUTPUT_DIR = "../output/IDW"

# 流域掩膜文件（用于栅格输出和面雨量计算）
MASK_FILE = "../terrain/90/mask.asc"

# ==================== IDW插值参数 ====================
# IDW权重指数（通常为2.0，值越大距离衰减越快）
IDW_POWER = 2.0

# 最少使用站点数（建议3-5个）
MIN_STATIONS = 3

# 最多使用站点数（建议8-15个）
MAX_STATIONS = 3

# 搜索半径（度，约0.5度≈55km）
SEARCH_RADIUS = 0.1

# ==================== 数据处理参数 ====================
# 是否启用数据质量检查
ENABLE_QUALITY_CHECK = False

# 异常值检测阈值（标准差倍数）
OUTLIER_THRESHOLD = 3.0

# 最小有效数据比例（0.0-1.0）
MIN_VALID_DATA_RATIO = 0.1

# 是否保留零值（True=保留，False=当作缺失值）
KEEP_ZERO_VALUES = True

# ==================== 栅格输出参数 ====================
# 是否生成栅格文件（设为False可节省时间和空间）
GENERATE_RASTER = False

# 栅格分辨率（度，0.01度约1km）
RASTER_RESOLUTION = 0.01

# 栅格输出格式（'ASC' 或 'TIFF'）
RASTER_FORMAT = 'ASC'

# ==================== 面雨量计算参数 ====================
# 是否计算面雨量
CALCULATE_AREAL_RAINFALL = True

# 面雨量计算方法（'mean', 'weighted_mean'）
AREAL_RAINFALL_METHOD = 'mean'

# ==================== 并行计算参数 ====================
# 是否启用并行计算
USE_PARALLEL = True

# 并行进程数（0=自动检测CPU核心数）
N_PROCESSES = 0

# ==================== 可视化参数 ====================
# 是否生成可视化图表
GENERATE_PLOTS = True

# 图表分辨率（DPI）
PLOT_DPI = 300

# 图表格式（'png', 'pdf', 'svg'）
PLOT_FORMAT = 'png'

# 是否显示中文标签
USE_CHINESE_LABELS = True

# ==================== 日志配置 ====================
# 日志级别（'DEBUG', 'INFO', 'WARNING', 'ERROR'）
LOG_LEVEL = 'INFO'

# 是否保存日志文件
SAVE_LOG_FILE = True

# 日志文件名
LOG_FILE = 'idw_interpolation.log'

# ==================== 验证参数 ====================
# 是否执行留一法验证
PERFORM_LEAVE_ONE_OUT = True

# 是否保存验证详细结果
SAVE_VALIDATION_DETAILS = True

# ==================== 输出控制 ====================
# 是否保存插值结果
SAVE_INTERPOLATION_RESULTS = True

# 是否保存评估指标
SAVE_EVALUATION_METRICS = True

# 是否保存站点权重信息
SAVE_STATION_WEIGHTS = True

# ==================== 高级参数 ====================
# Delaunay权重使用优先级（True=优先使用Delaunay权重）
PREFER_DELAUNAY_WEIGHTS = True

# 距离权重回退阈值（当Delaunay权重不可用时）
DISTANCE_FALLBACK_THRESHOLD = 0.1

# 内存优化模式（True=节省内存但可能较慢）
MEMORY_OPTIMIZATION = False

# ==================== 配置验证函数 ====================
def validate_config():
    """验证配置参数的合理性"""
    errors = []
    
    # 检查必要文件是否存在
    if not os.path.exists(INPUT_DIR):
        errors.append(f"输入目录不存在: {INPUT_DIR}")
    
    if not os.path.exists(STATIONS_FILE):
        errors.append(f"站点文件不存在: {STATIONS_FILE}")
    
    if not os.path.exists(DELAUNAY_WEIGHTS_FILE):
        errors.append(f"Delaunay权重文件不存在: {DELAUNAY_WEIGHTS_FILE}")
    
    # 检查参数范围
    if IDW_POWER <= 0:
        errors.append("IDW_POWER必须大于0")
    
    if MIN_STATIONS < 1:
        errors.append("MIN_STATIONS必须至少为1")
    
    if MAX_STATIONS < MIN_STATIONS:
        errors.append("MAX_STATIONS必须大于等于MIN_STATIONS")
    
    if SEARCH_RADIUS <= 0:
        errors.append("SEARCH_RADIUS必须大于0")
    
    if not 0 <= MIN_VALID_DATA_RATIO <= 1:
        errors.append("MIN_VALID_DATA_RATIO必须在0-1之间")
    
    if RASTER_RESOLUTION <= 0:
        errors.append("RASTER_RESOLUTION必须大于0")
    
    if errors:
        print("配置错误:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    return True

# ==================== 配置信息显示 ====================
def print_config():
    """打印当前配置信息"""
    print("="*60)
    print("IDW空间插值系统配置")
    print("="*60)
    print(f"输入目录: {INPUT_DIR}")
    print(f"输出目录: {OUTPUT_DIR}")
    print(f"IDW权重指数: {IDW_POWER}")
    print(f"使用站点数: {MIN_STATIONS}-{MAX_STATIONS}")
    print(f"搜索半径: {SEARCH_RADIUS}度")
    print(f"生成栅格: {'是' if GENERATE_RASTER else '否'}")
    print(f"计算面雨量: {'是' if CALCULATE_AREAL_RAINFALL else '否'}")
    print(f"并行计算: {'是' if USE_PARALLEL else '否'}")
    print(f"生成图表: {'是' if GENERATE_PLOTS else '否'}")
    print("="*60)

if __name__ == "__main__":
    # 验证配置
    if validate_config():
        print("配置验证通过！")
        print_config()
    else:
        print("配置验证失败，请检查配置参数。")

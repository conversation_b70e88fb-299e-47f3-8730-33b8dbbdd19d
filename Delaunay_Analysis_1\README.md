# Delaunay三角剖分分析工具

基于珠江流域实时监测雨量数据融合方法应用研究的Delaunay三角剖分方法，用于分析站点间的空间关系和权重计算。

## 功能特点

1. **Delaunay三角网构建**: 基于站点经纬度坐标构建标准Delaunay三角网
2. **站点包围关系分析**: 分析每个站点被哪些站点包围
3. **权重计算**: 基于IDW方法计算包围站点对验证站点的权重
4. **可视化输出**: 生成支持中文的Delaunay三角网图
5. **详细报告**: 输出完整的分析结果和报告

## 输入文件

需要准备`stations.csv`文件，包含以下列：
- `站点`: 站点编号或名称
- `经度`: 站点经度坐标
- `纬度`: 站点纬度坐标

示例格式：
```csv
站点,经度,纬度
80606500,110.606944,24.090833
80607800,110.513055,24.231666
80608500,110.5063,24.2308
```

## 使用方法

### 方法1: 直接运行
```bash
cd Delaunay_Analysis
python delaunay_analysis.py
```

### 方法2: 作为模块使用
```python
from delaunay_analysis import DelaunayAnalyzer

# 创建分析器
analyzer = DelaunayAnalyzer('stations.csv')

# 执行完整分析
analyzer.load_stations()
analyzer.build_triangulation()
results = analyzer.analyze_station_surroundings(power=2.0)

# 生成可视化
analyzer.plot_delaunay_triangulation(output_dir="output/Delaunay")

# 保存结果
analyzer.save_analysis_results(output_dir="output/Delaunay")
analyzer.generate_report(output_dir="output/Delaunay")
```

## 输出文件

所有输出文件保存在`output/Delaunay/`目录下：

1. **delaunay_triangulation.png**: Delaunay三角网可视化图
2. **delaunay_analysis_results.csv**: 详细分析结果（每个包围关系一行）
3. **delaunay_analysis_summary.csv**: 汇总分析结果（每个验证站点一行）
4. **delaunay_analysis_report.md**: 完整的分析报告

## 分析结果说明

### 包围关系分析
- 对每个站点执行"留一法"分析
- 找到包含该站点的Delaunay三角形
- 确定三角形的三个顶点作为包围站点

### 权重计算
- 使用IDW（反距离权重）方法
- 权重公式: w_i = (1/d_i^p) / Σ(1/d_j^p)
- 默认IDW指数p=2.0
- 权重归一化，总和为1

### 可视化特点
- 支持中文站点名称显示
- 自动调整标签位置避免重叠
- 可高亮显示特定站点及其包围关系
- 高分辨率输出（300 DPI）

## 参数配置

可以通过修改代码中的参数来调整分析：

- `power`: IDW权重指数，默认2.0
- `show_station_names`: 是否显示站点名称，默认True
- `highlight_station`: 高亮显示的站点，默认None

## 注意事项

1. 确保stations.csv文件格式正确，包含必要的列
2. 站点数量至少需要4个才能进行有效的三角剖分
3. 如果站点不在任何三角形内，会自动使用最近的3个站点作为备选
4. 输出目录会自动创建，无需手动创建

## 技术实现

- 基于scipy.spatial.Delaunay进行三角剖分
- 使用matplotlib进行可视化，支持中文字体
- 采用留一法（Leave-One-Out）进行验证分析
- IDW权重计算确保数值稳定性

## 应用场景

- 空间插值方法研究
- 雨量站网优化分析
- 站点影响范围评估
- 空间权重矩阵构建

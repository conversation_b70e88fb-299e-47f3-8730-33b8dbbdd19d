import os
import shutil
'''
input/
├── 2009-1/
│   └── 点雨量/
│       ├── 1234.csv
│       ├── 1456.csv
│       ├── 5631.csv
│       └── 122.csv
├── 2009-2/
│   └── 点雨量/
│       ├── 1234.csv
│       ├── 1456.csv
│       ├── 5631.csv
│       └── 122.csv
...
└── 2023-1/
    └── 点雨量/
        ├── 1234.csv
        ├── 1456.csv
        ├── 5631.csv
        └── 122.csv
————————————————————————————————————————————————————
       input_another/
├── 2009-1/
│   ├── 1234.csv
│   ├── 1456.csv
│   ├── 5631.csv
│   └── 122.csv
├── 2009-2/
│   ├── 1234.csv
│   ├── 1456.csv
│   ├── 5631.csv
│   └── 122.csv
...
└── 2023-1/
    ├── 1234.csv
    ├── 1456.csv
    ├── 5631.csv
    └── 122.csv
      '''


def copy_rainfall_files(src_root, dst_root):
    """
    将源目录中每个子文件夹下的“点雨量”文件夹中的所有CSV文件复制到目标目录中的对应子文件夹中

    参数:
        src_root (str): 源目录路径
        dst_root (str): 目标目录路径
    """
    # 遍历源目录
    for root, dirs, files in os.walk(src_root):
        # 检查当前目录是否包含“点雨量”子文件夹
        if '点雨量' in dirs:
            src_rainfall_dir = os.path.join(root, '点雨量')
            # 构建目标目录路径（不包含“点雨量”）
            relative_path = os.path.relpath(root, src_root)
            dst_dir = os.path.join(dst_root, relative_path)
            
            # 创建目标目录（如果不存在）
            os.makedirs(dst_dir, exist_ok=True)
            
            # 复制“点雨量”文件夹中的所有CSV文件
            for file in os.listdir(src_rainfall_dir):
                if file.endswith('.csv'):
                    src_file = os.path.join(src_rainfall_dir, file)
                    dst_file = os.path.join(dst_dir, file)
                    shutil.copy2(src_file, dst_file)
                    print(f"Copied: {src_file} -> {dst_file}")


if __name__ == "__main__":
    src_root = 'D:/pythondata/spline/全年_原'       # 源目录路径
    dst_root = 'input_another'  # 目标目录路径

    copy_rainfall_files(src_root, dst_root)
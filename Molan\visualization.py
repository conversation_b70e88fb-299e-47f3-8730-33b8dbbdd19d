# -*- coding: utf-8 -*-
"""
莫兰指数分析可视化模块

作者：空间插值系统
日期：2024年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MoranVisualization:
    """莫兰指数分析可视化类"""

    def __init__(self, results_file, excel_file, output_dir):
        """
        初始化可视化系统

        参数:
        results_file: 莫兰指数分析详细结果文件路径
        excel_file: 水晏泰森.xlsx文件路径（包含站点名称）
        output_dir: 可视化结果输出目录
        """
        self.results_file = results_file
        self.excel_file = excel_file
        self.output_dir = output_dir

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 加载数据
        self.load_data()
        
    def load_data(self):
        """加载分析结果数据"""
        print("正在加载莫兰指数分析结果...")
        self.df = pd.read_csv(self.results_file, encoding='utf-8-sig')
        print(f"加载了 {len(self.df)} 条分析记录")

        # 加载Excel文件获取站点名称映射
        print("正在加载站点名称映射...")
        excel_df = pd.read_excel(self.excel_file)
        self.station_name_map = {}
        for _, row in excel_df.iterrows():
            station_id = str(row['PSTCD']).strip().lower()
            station_name = str(row['NAME']).strip()
            self.station_name_map[station_id] = station_name
        print(f"加载了 {len(self.station_name_map)} 个站点名称映射")

        # 数据预处理
        self.preprocess_data()
        
    def preprocess_data(self):
        """数据预处理"""
        # 确保数值列为数值类型
        numeric_columns = ['top_1_moran', 'top_2_moran', 'top_3_moran', 
                          'top_1_p_value', 'top_2_p_value', 'top_3_p_value',
                          'avg_moran', 'max_moran', 'significant_count']
        
        for col in numeric_columns:
            if col in self.df.columns:
                self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
        
        # 创建年份列
        self.df['year'] = self.df['flood_event'].str.extract('(\d{4})').astype(int)
        
        print("数据预处理完成")

    def get_station_name(self, station_id):
        """获取站点的中文名称"""
        if station_id in self.station_name_map:
            return self.station_name_map[station_id]
        else:
            # 特殊处理一些已知的站点
            special_names = {
                '806d2942': '佛子坪2',  # 可能是806d2941的变体
                '806r2409': '未知站点1',
                '806r3724': '未知站点2'
            }

            if station_id in special_names:
                return special_names[station_id]
            else:
                # 如果找不到中文名称，返回站点ID的后6位
                return station_id[-6:] if len(station_id) > 6 else station_id
    
    def plot_moran_distribution_by_group(self):
        """绘制各分组的莫兰指数分布图"""
        print("正在生成分组莫兰指数分布图...")

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('各分组莫兰指数分布', fontsize=16, fontweight='bold')

        groups = ['整体', '大化', '太平', '水晏']
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']

        for i, group in enumerate(groups):
            row = i // 2
            col = i % 2
            ax = axes[row, col]

            group_data = self.df[self.df['group'] == group]
            if len(group_data) == 0:
                ax.text(0.5, 0.5, f'{group}组\n无数据', ha='center', va='center',
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'{group}组')
                continue

            # 提取所有莫兰指数值
            moran_values = []
            for _, row_data in group_data.iterrows():
                for j in range(1, 4):
                    moran_val = row_data.get(f'top_{j}_moran', 0)
                    if not pd.isna(moran_val) and moran_val != 0:
                        moran_values.append(moran_val)

            if moran_values:
                # 绘制直方图
                ax.hist(moran_values, bins=30, alpha=0.7, color=colors[i],
                       edgecolor='black', linewidth=0.5)

                # 添加统计信息
                mean_val = np.mean(moran_values)
                std_val = np.std(moran_values)
                ax.axvline(mean_val, color='red', linestyle='--', linewidth=2,
                          label=f'均值: {mean_val:.4f}')

                # 显示记录数和莫兰指数值数量
                record_count = len(group_data)
                value_count = len(moran_values)
                ax.set_title(f'{group}组\n记录数: {record_count}, 莫兰指数值: {value_count}')
                ax.set_xlabel('莫兰指数')
                ax.set_ylabel('频次')
                ax.legend()
                ax.grid(True, alpha=0.3)

                # 添加统计文本
                stats_text = f'记录数: {record_count}\n莫兰指数值: {value_count}\n均值: {mean_val:.4f}\n标准差: {std_val:.4f}\n最大值: {max(moran_values):.4f}'
                ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                       verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            else:
                ax.text(0.5, 0.5, f'{group}组\n记录数: {len(group_data)}\n无有效莫兰指数值',
                       ha='center', va='center', transform=ax.transAxes, fontsize=12)
                ax.set_title(f'{group}组')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, '分组莫兰指数分布图.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()
        print("分组莫兰指数分布图已保存")
    
    def plot_significance_summary(self):
        """绘制显著性检验汇总图"""
        print("正在生成显著性检验汇总图...")

        # 计算各组的显著性统计
        significance_stats = []

        for group in ['整体', '大化', '太平', '水晏']:
            group_data = self.df[self.df['group'] == group]
            if len(group_data) == 0:
                continue

            # 记录数和总的站点对数
            record_count = len(group_data)
            total_pairs = record_count * 3  # 每条记录有3个站点对
            sig_counts = {'***': 0, '**': 0, '*': 0, '.': 0, 'NS': 0}

            for _, row in group_data.iterrows():
                for level in ['top_1_significance', 'top_2_significance', 'top_3_significance']:
                    sig_level = row.get(level, 'NS')
                    if sig_level in sig_counts:
                        sig_counts[sig_level] += 1
                    else:
                        sig_counts['NS'] += 1

            # 计算百分比
            percentages = {k: (v / total_pairs * 100) if total_pairs > 0 else 0
                          for k, v in sig_counts.items()}

            significance_stats.append({
                'group': group,
                'records': record_count,
                'total_pairs': total_pairs,
                '极显著': percentages['***'],
                '高显著': percentages['**'],
                '显著': percentages['*'],
                '边际显著': percentages['.'],
                '不显著': percentages['NS']
            })

        if not significance_stats:
            print("无显著性数据可绘制")
            return

        # 创建DataFrame
        sig_df = pd.DataFrame(significance_stats)

        # 绘制堆叠柱状图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 6))

        # 左图：百分比堆叠柱状图
        categories = ['极显著', '高显著', '显著', '边际显著', '不显著']
        colors = ['#d62728', '#ff7f0e', '#2ca02c', '#1f77b4', '#9467bd']

        bottom = np.zeros(len(sig_df))
        for i, category in enumerate(categories):
            if category in sig_df.columns:
                ax1.bar(sig_df['group'], sig_df[category], bottom=bottom,
                       label=category, color=colors[i], alpha=0.8)
                bottom += sig_df[category]

        ax1.set_title('各分组显著性检验结果分布', fontsize=14, fontweight='bold')
        ax1.set_ylabel('百分比 (%)')
        ax1.set_xlabel('分组')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 添加记录数标签
        for i, (_, row) in enumerate(sig_df.iterrows()):
            ax1.text(i, 102, f'记录数: {row["records"]}\n站点对: {row["total_pairs"]}',
                    ha='center', va='bottom', fontsize=10, fontweight='bold')

        # 右图：显著站点对数量
        significant_counts = []
        for _, row in sig_df.iterrows():
            sig_count = row['极显著'] + row['高显著'] + row['显著']
            significant_counts.append(sig_count * row['total_pairs'] / 100)

        bars = ax2.bar(sig_df['group'], significant_counts, color='#2ca02c', alpha=0.7)
        ax2.set_title('各分组显著站点对数量', fontsize=14, fontweight='bold')
        ax2.set_ylabel('显著站点对数量')
        ax2.set_xlabel('分组')
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, count in zip(bars, significant_counts):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{int(count)}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, '显著性检验汇总图.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()
        print("显著性检验汇总图已保存")
    
    def plot_temporal_trends(self):
        """绘制时间趋势图（按洪水场次）"""
        print("正在生成时间趋势图...")

        # 创建两种视图：年际趋势和洪水场次趋势
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('莫兰指数时间趋势分析', fontsize=16, fontweight='bold')

        groups = ['整体', '大化', '太平', '水晏']
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']

        # 1. 按年份的平均莫兰指数趋势
        ax1 = axes[0, 0]
        yearly_stats = self.df.groupby(['year', 'group']).agg({
            'avg_moran': 'mean',
            'max_moran': 'mean',
            'significant_count': 'mean'
        }).reset_index()

        for i, group in enumerate(groups):
            group_data = yearly_stats[yearly_stats['group'] == group]
            if len(group_data) > 0:
                ax1.plot(group_data['year'], group_data['avg_moran'],
                        marker='o', label=group, color=colors[i], linewidth=2)

        ax1.set_title('平均莫兰指数年际变化')
        ax1.set_xlabel('年份')
        ax1.set_ylabel('平均莫兰指数')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 按洪水场次的莫兰指数趋势（整体组）
        ax2 = axes[0, 1]
        overall_data = self.df[self.df['group'] == '整体']
        if len(overall_data) > 0:
            flood_events = sorted(overall_data['flood_event'].unique())
            event_stats = overall_data.groupby('flood_event').agg({
                'avg_moran': 'mean',
                'max_moran': 'mean'
            }).reset_index()

            # 重新排序以匹配洪水事件顺序
            event_stats = event_stats.set_index('flood_event').reindex(flood_events).reset_index()

            ax2.plot(range(len(flood_events)), event_stats['avg_moran'],
                    marker='o', color='#1f77b4', linewidth=2, label='平均莫兰指数')
            ax2.plot(range(len(flood_events)), event_stats['max_moran'],
                    marker='s', color='#ff7f0e', linewidth=2, label='最大莫兰指数')

            # 设置x轴为洪水场次名称
            ax2.set_xticks(range(0, len(flood_events), max(1, len(flood_events)//10)))
            ax2.set_xticklabels([flood_events[i] for i in range(0, len(flood_events), max(1, len(flood_events)//10))],
                               rotation=45, ha='right')

        ax2.set_title('整体组莫兰指数按洪水场次变化')
        ax2.set_xlabel('洪水场次')
        ax2.set_ylabel('莫兰指数')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 显著性比例按洪水场次变化
        ax3 = axes[1, 0]
        if len(overall_data) > 0:
            significance_by_event = []
            for event in flood_events:
                event_data = overall_data[overall_data['flood_event'] == event]
                total_pairs = 0
                significant_pairs = 0

                for _, row in event_data.iterrows():
                    for j in range(1, 4):
                        sig_level = row.get(f'top_{j}_significance', 'NS')
                        if pd.notna(sig_level):
                            total_pairs += 1
                            if sig_level in ['*', '**', '***']:
                                significant_pairs += 1

                sig_ratio = significant_pairs / total_pairs * 100 if total_pairs > 0 else 0
                significance_by_event.append(sig_ratio)

            ax3.plot(range(len(flood_events)), significance_by_event,
                    marker='^', color='#2ca02c', linewidth=2)

            # 设置x轴为洪水场次名称
            ax3.set_xticks(range(0, len(flood_events), max(1, len(flood_events)//10)))
            ax3.set_xticklabels([flood_events[i] for i in range(0, len(flood_events), max(1, len(flood_events)//10))],
                               rotation=45, ha='right')

        ax3.set_title('显著性比例按洪水场次变化')
        ax3.set_xlabel('洪水场次')
        ax3.set_ylabel('显著性比例 (%)')
        ax3.grid(True, alpha=0.3)

        # 4. 各组显著性比例对比（最近10个洪水事件）
        ax4 = axes[1, 1]
        recent_events = sorted(self.df['flood_event'].unique())[-10:]  # 最近10个事件

        for i, group in enumerate(groups):
            group_sig_ratios = []
            for event in recent_events:
                event_group_data = self.df[(self.df['flood_event'] == event) & (self.df['group'] == group)]
                if len(event_group_data) > 0:
                    total_pairs = 0
                    significant_pairs = 0

                    for _, row in event_group_data.iterrows():
                        for j in range(1, 4):
                            sig_level = row.get(f'top_{j}_significance', 'NS')
                            if pd.notna(sig_level):
                                total_pairs += 1
                                if sig_level in ['*', '**', '***']:
                                    significant_pairs += 1

                    sig_ratio = significant_pairs / total_pairs * 100 if total_pairs > 0 else 0
                    group_sig_ratios.append(sig_ratio)
                else:
                    group_sig_ratios.append(0)

            ax4.plot(range(len(recent_events)), group_sig_ratios,
                    marker='o', label=group, color=colors[i], linewidth=2)

        ax4.set_xticks(range(len(recent_events)))
        ax4.set_xticklabels(recent_events, rotation=45, ha='right')
        ax4.set_title('各组显著性比例对比（最近10个洪水事件）')
        ax4.set_xlabel('洪水场次')
        ax4.set_ylabel('显著性比例 (%)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, '时间趋势分析图.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()
        print("时间趋势分析图已保存")

    def plot_correlation_heatmap(self):
        """绘制站点相关性热力图"""
        print("正在生成站点相关性热力图...")

        # 创建整体组的完整热力图和分组的简化热力图
        fig = plt.figure(figsize=(24, 18))

        # 整体组热力图（占据左侧大部分空间）
        ax_main = plt.subplot2grid((3, 4), (0, 0), colspan=3, rowspan=3)

        # 分组热力图（占据右侧空间）
        ax_dahua = plt.subplot2grid((3, 4), (0, 3))
        ax_taiping = plt.subplot2grid((3, 4), (1, 3))
        ax_shuiyan = plt.subplot2grid((3, 4), (2, 3))

        fig.suptitle('站点莫兰指数相关性热力图', fontsize=16, fontweight='bold')

        # 绘制整体组完整热力图
        overall_data = self.df[self.df['group'] == '整体']
        if len(overall_data) > 0:
            # 获取所有站点
            all_stations = sorted(list(set(overall_data['reference_station'].tolist())))
            n_stations = len(all_stations)

            print(f"整体组包含 {n_stations} 个站点")

            correlation_matrix = np.zeros((n_stations, n_stations))

            for _, row_data in overall_data.iterrows():
                ref_station = row_data['reference_station']
                if ref_station in all_stations:
                    ref_idx = all_stations.index(ref_station)

                    for j in range(1, 4):
                        target_station = row_data.get(f'top_{j}_station', '')
                        moran_val = row_data.get(f'top_{j}_moran', 0)

                        if target_station in all_stations and not pd.isna(moran_val):
                            target_idx = all_stations.index(target_station)
                            # 确保矩阵对称：同时设置(i,j)和(j,i)
                            current_val = max(correlation_matrix[ref_idx, target_idx], moran_val)
                            correlation_matrix[ref_idx, target_idx] = current_val
                            correlation_matrix[target_idx, ref_idx] = current_val

            # 设置对角线为1（站点与自身的相关性）
            np.fill_diagonal(correlation_matrix, 1.0)

            # 绘制整体组热力图
            im_main = ax_main.imshow(correlation_matrix, cmap='RdYlBu_r', aspect='auto', vmin=0, vmax=1)

            # 设置坐标轴
            ax_main.set_xticks(range(n_stations))
            ax_main.set_yticks(range(n_stations))
            ax_main.set_xticklabels([self.get_station_name(s) for s in all_stations],
                                   rotation=45, ha='right', fontsize=8)
            ax_main.set_yticklabels([self.get_station_name(s) for s in all_stations],
                                   fontsize=8)

            ax_main.set_title(f'整体组站点相关性矩阵 ({n_stations}个站点)', fontsize=14)
            ax_main.set_xlabel('目标站点')
            ax_main.set_ylabel('参考站点')

            # 添加颜色条
            plt.colorbar(im_main, ax=ax_main, shrink=0.8)

        # 绘制各分组的简化热力图
        subgroups = [('大化', ax_dahua), ('太平', ax_taiping), ('水晏', ax_shuiyan)]

        for group_name, ax in subgroups:
            group_data = self.df[self.df['group'] == group_name]
            if len(group_data) == 0:
                ax.text(0.5, 0.5, f'{group_name}组\n无数据', ha='center', va='center',
                       transform=ax.transAxes, fontsize=10)
                ax.set_title(f'{group_name}组', fontsize=12)
                continue

            # 创建站点对莫兰指数矩阵
            stations = sorted(list(set(group_data['reference_station'].tolist())))
            n_stations = len(stations)

            if n_stations < 2:
                ax.text(0.5, 0.5, f'{group_name}组\n站点数不足', ha='center', va='center',
                       transform=ax.transAxes, fontsize=10)
                ax.set_title(f'{group_name}组', fontsize=12)
                continue

            # 限制显示的站点数量（小图表）
            if n_stations > 15:
                stations = stations[:15]
                n_stations = 15

            correlation_matrix = np.zeros((n_stations, n_stations))

            for _, row_data in group_data.iterrows():
                ref_station = row_data['reference_station']
                if ref_station in stations:
                    ref_idx = stations.index(ref_station)

                    for j in range(1, 4):
                        target_station = row_data.get(f'top_{j}_station', '')
                        moran_val = row_data.get(f'top_{j}_moran', 0)

                        if target_station in stations and not pd.isna(moran_val):
                            target_idx = stations.index(target_station)
                            # 确保矩阵对称：同时设置(i,j)和(j,i)
                            current_val = max(correlation_matrix[ref_idx, target_idx], moran_val)
                            correlation_matrix[ref_idx, target_idx] = current_val
                            correlation_matrix[target_idx, ref_idx] = current_val

            # 设置对角线为1（站点与自身的相关性）
            np.fill_diagonal(correlation_matrix, 1.0)

            # 绘制热力图
            im = ax.imshow(correlation_matrix, cmap='RdYlBu_r', aspect='auto', vmin=0, vmax=1)

            # 设置坐标轴
            ax.set_xticks(range(n_stations))
            ax.set_yticks(range(n_stations))
            ax.set_xticklabels([self.get_station_name(s) for s in stations],
                              rotation=45, ha='right', fontsize=6)
            ax.set_yticklabels([self.get_station_name(s) for s in stations],
                              fontsize=6)

            ax.set_title(f'{group_name}组\n({n_stations}个站点)', fontsize=10)

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, '站点相关性热力图.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()
        print("站点相关性热力图已保存")

    def plot_moran_vs_pvalue_scatter(self):
        """绘制莫兰指数与p值的散点图（按洪水场次）"""
        print("正在生成莫兰指数与p值散点图（按洪水场次）...")

        # 创建按洪水事件的散点图
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('各组莫兰指数与p值关系（按洪水场次）', fontsize=16, fontweight='bold')

        groups = ['整体', '大化', '太平', '水晏']
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']

        for i, group in enumerate(groups):
            row = i // 2
            col = i % 2
            ax = axes[row, col]

            group_data = self.df[self.df['group'] == group]
            if len(group_data) == 0:
                ax.text(0.5, 0.5, f'{group}组\n无数据', ha='center', va='center',
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'{group}组')
                continue

            # 按洪水事件统计平均莫兰指数和p值
            flood_events = sorted(group_data['flood_event'].unique())
            avg_moran_by_event = []
            avg_pvalue_by_event = []
            significant_ratios = []

            for event in flood_events:
                event_data = group_data[group_data['flood_event'] == event]

                # 收集该事件的所有莫兰指数和p值
                moran_values = []
                p_values = []
                sig_count = 0
                total_count = 0

                for _, row_data in event_data.iterrows():
                    for j in range(1, 4):
                        moran_val = row_data.get(f'top_{j}_moran', 0)
                        p_val = row_data.get(f'top_{j}_p_value', 1)
                        sig_level = row_data.get(f'top_{j}_significance', 'NS')

                        if not pd.isna(moran_val) and not pd.isna(p_val) and moran_val != 0:
                            moran_values.append(moran_val)
                            p_values.append(p_val)
                            total_count += 1
                            if sig_level in ['*', '**', '***']:
                                sig_count += 1

                if moran_values:
                    avg_moran_by_event.append(np.mean(moran_values))
                    avg_pvalue_by_event.append(np.mean(p_values))
                    significant_ratios.append(sig_count / total_count * 100 if total_count > 0 else 0)
                else:
                    avg_moran_by_event.append(0)
                    avg_pvalue_by_event.append(1)
                    significant_ratios.append(0)

            if not avg_moran_by_event:
                ax.text(0.5, 0.5, f'{group}组\n无有效数据', ha='center', va='center',
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'{group}组')
                continue

            # 根据显著性比例设置颜色
            scatter = ax.scatter(range(len(flood_events)), avg_moran_by_event,
                               c=significant_ratios, cmap='RdYlGn', alpha=0.7, s=60)

            # 设置x轴为洪水场次名称
            ax.set_xticks(range(len(flood_events)))
            ax.set_xticklabels(flood_events, rotation=45, ha='right')

            ax.set_title(f'{group}组 - 各洪水场次平均莫兰指数')
            ax.set_xlabel('洪水场次')
            ax.set_ylabel('平均莫兰指数')
            ax.grid(True, alpha=0.3)

            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax)
            cbar.set_label('显著性比例 (%)')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, '莫兰指数与p值散点图.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()
        print("莫兰指数与p值散点图已保存")

    def plot_flood_event_pvalue_scatter(self):
        """绘制按洪水场次的莫兰指数与p值散点图"""
        print("正在生成按洪水场次的莫兰指数与p值散点图...")

        # 创建单独的散点图，横坐标为洪水场次
        fig, axes = plt.subplots(2, 2, figsize=(24, 16))
        fig.suptitle('各组莫兰指数与p值关系（按洪水场次详细分析）', fontsize=16, fontweight='bold')

        groups = ['整体', '大化', '太平', '水晏']
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']

        for i, group in enumerate(groups):
            row = i // 2
            col = i % 2
            ax = axes[row, col]

            group_data = self.df[self.df['group'] == group]
            if len(group_data) == 0:
                ax.text(0.5, 0.5, f'{group}组\n无数据', ha='center', va='center',
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'{group}组')
                continue

            # 按洪水事件收集数据
            flood_events = sorted(group_data['flood_event'].unique())
            event_positions = range(len(flood_events))

            moran_values = []
            p_values = []
            event_indices = []
            significance_levels = []

            for event_idx, event in enumerate(flood_events):
                event_data = group_data[group_data['flood_event'] == event]

                for _, row_data in event_data.iterrows():
                    for j in range(1, 4):
                        moran_val = row_data.get(f'top_{j}_moran', 0)
                        p_val = row_data.get(f'top_{j}_p_value', 1)
                        sig_level = row_data.get(f'top_{j}_significance', 'NS')

                        if not pd.isna(moran_val) and not pd.isna(p_val) and moran_val != 0:
                            moran_values.append(moran_val)
                            p_values.append(p_val)
                            event_indices.append(event_idx)
                            significance_levels.append(sig_level)

            if not moran_values:
                ax.text(0.5, 0.5, f'{group}组\n无有效数据', ha='center', va='center',
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'{group}组')
                continue

            # 根据显著性水平设置颜色
            color_map = {'***': '#d62728', '**': '#ff7f0e', '*': '#2ca02c',
                        '.': '#1f77b4', 'NS': '#9467bd'}
            point_colors = [color_map.get(sig, '#9467bd') for sig in significance_levels]

            # 绘制散点图 - 使用双y轴
            ax2 = ax.twinx()

            # 左y轴：莫兰指数
            scatter1 = ax.scatter(event_indices, moran_values, c=point_colors,
                                alpha=0.6, s=30, marker='o', label='莫兰指数')
            ax.set_ylabel('莫兰指数', color='blue')
            ax.tick_params(axis='y', labelcolor='blue')

            # 右y轴：p值（对数尺度）
            scatter2 = ax2.scatter(event_indices, p_values, c=point_colors,
                                 alpha=0.4, s=20, marker='^', label='p值')
            ax2.set_ylabel('p值 (对数尺度)', color='red')
            ax2.set_yscale('log')
            ax2.tick_params(axis='y', labelcolor='red')

            # 添加显著性水平线（在右y轴上）
            ax2.axhline(y=0.001, color='red', linestyle='--', alpha=0.7, linewidth=1)
            ax2.axhline(y=0.01, color='orange', linestyle='--', alpha=0.7, linewidth=1)
            ax2.axhline(y=0.05, color='green', linestyle='--', alpha=0.7, linewidth=1)
            ax2.axhline(y=0.1, color='blue', linestyle='--', alpha=0.7, linewidth=1)

            # 设置x轴为洪水场次名称
            ax.set_xticks(range(0, len(flood_events), max(1, len(flood_events)//8)))
            ax.set_xticklabels([flood_events[i] for i in range(0, len(flood_events), max(1, len(flood_events)//8))],
                              rotation=45, ha='right')

            ax.set_title(f'{group}组 - 莫兰指数与p值按洪水场次变化 (n={len(moran_values)})')
            ax.set_xlabel('洪水场次')
            ax.grid(True, alpha=0.3)

            # 添加统计信息
            significant_ratio = sum([1 for sig in significance_levels if sig in ['*', '**', '***']]) / len(significance_levels) * 100
            stats_text = f'显著比例: {significant_ratio:.1f}%\n平均莫兰指数: {np.mean(moran_values):.4f}\n平均p值: {np.mean(p_values):.4f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, '洪水场次莫兰指数p值散点图.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()
        print("洪水场次莫兰指数p值散点图已保存")

    def generate_all_visualizations(self):
        """生成所有可视化图表"""
        print("=" * 60)
        print("开始生成莫兰指数分析可视化图表")
        print("=" * 60)

        try:
            self.plot_moran_distribution_by_group()
            self.plot_significance_summary()
            self.plot_temporal_trends()
            self.plot_correlation_heatmap()
            self.plot_moran_vs_pvalue_scatter()
            self.plot_flood_event_pvalue_scatter()

            print("\n" + "=" * 60)
            print("所有可视化图表生成完成！")
            print(f"图表已保存到: {self.output_dir}")
            print("生成的图表包括：")
            print("1. 分组莫兰指数分布图.png")
            print("2. 显著性检验汇总图.png")
            print("3. 时间趋势分析图.png")
            print("4. 站点相关性热力图.png")
            print("5. 莫兰指数与p值散点图.png")
            print("6. 洪水场次莫兰指数p值散点图.png")
            print("=" * 60)

        except Exception as e:
            print(f"生成可视化图表时出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("莫兰指数分析可视化系统")
    print("=" * 40)

    # 文件路径配置
    results_file = "../output/molan/莫兰指数分析详细结果.csv"
    excel_file = "../水晏泰森.xlsx"
    output_dir = "../output/molan_visualization_with_names"

    try:
        # 检查结果文件是否存在
        if not os.path.exists(results_file):
            print(f"错误：找不到结果文件 {results_file}")
            print("请先运行 batch_molan_analysis.py 生成分析结果")
            return

        # 检查Excel文件是否存在
        if not os.path.exists(excel_file):
            print(f"错误：找不到Excel文件 {excel_file}")
            return

        # 创建可视化实例
        visualizer = MoranVisualization(results_file, excel_file, output_dir)

        # 生成所有可视化图表
        visualizer.generate_all_visualizations()

    except Exception as e:
        print(f"可视化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

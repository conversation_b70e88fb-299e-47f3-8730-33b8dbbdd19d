#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证中文字体显示测试脚本

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def test_chinese_display():
    """测试中文字体显示"""
    print("Testing Chinese font display...")
    
    # 尝试不同的中文字体设置方法
    font_methods = [
        {
            'name': 'Method 1: rcParams only',
            'setup': lambda: plt.rcParams.update({
                'font.sans-serif': ['SimHei', 'Microsoft YaHei', 'DejaVu Sans'],
                'axes.unicode_minus': False
            })
        },
        {
            'name': 'Method 2: matplotlib.rcParams',
            'setup': lambda: matplotlib.rcParams.update({
                'font.sans-serif': ['SimHei', 'Microsoft YaHei', 'DejaVu Sans'],
                'axes.unicode_minus': False
            })
        },
        {
            'name': 'Method 3: FontProperties',
            'setup': lambda: None
        }
    ]
    
    # 测试站点名称
    test_stations = [
        ('80606500', '大化'),
        ('80633800', '太平'),
        ('80634200', '水岩'),
        ('805g2300', '百龙滩'),
        ('80607500', '太平'),
        ('80609500', '水晏')
    ]
    
    for i, method in enumerate(font_methods):
        print(f"\n{method['name']}:")
        
        try:
            # 设置字体
            method['setup']()
            
            # 创建测试图
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 测试数据
            values = np.random.rand(len(test_stations))
            
            # 绘制条形图
            bars = ax.barh(range(len(test_stations)), values)
            
            # 设置y轴标签为中文名称
            chinese_names = [name for _, name in test_stations]
            ax.set_yticks(range(len(test_stations)))
            ax.set_yticklabels(chinese_names, fontsize=12)
            
            # 如果是方法3，使用FontProperties
            if i == 2:
                from matplotlib.font_manager import FontProperties
                try:
                    font_prop = FontProperties(fname='C:/Windows/Fonts/simhei.ttf')
                    ax.set_yticklabels(chinese_names, fontproperties=font_prop, fontsize=12)
                except:
                    try:
                        font_prop = FontProperties(family='SimHei')
                        ax.set_yticklabels(chinese_names, fontproperties=font_prop, fontsize=12)
                    except:
                        print("  ❌ FontProperties method failed")
                        plt.close()
                        continue
            
            ax.set_xlabel('Test Values', fontsize=12)
            ax.set_title('Chinese Station Names Test', fontsize=14, fontweight='bold')
            
            # 添加站点代码标注
            for j, (bar, (code, name)) in enumerate(zip(bars, test_stations)):
                ax.text(bar.get_width() + 0.01, j, f'({code})', 
                       va='center', fontsize=10)
            
            plt.tight_layout()
            
            # 保存测试图
            test_path = Path(f'chinese_test_method_{i+1}.png')
            plt.savefig(test_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"  ✅ Test image saved: {test_path}")
            
        except Exception as e:
            print(f"  ❌ Method failed: {e}")

def load_and_test_station_mapping():
    """加载并测试站点映射"""
    print("\nTesting station mapping from 水晏泰森.xlsx...")
    
    try:
        # 读取Excel文件
        df = pd.read_excel('水晏泰森.xlsx')
        print(f"✅ Loaded Excel file with {len(df)} rows")
        
        # 创建映射
        station_mapping = {}
        for _, row in df.iterrows():
            code = str(row['PSTCD']).strip()
            name = str(row['NAME']).strip()
            
            # 存储多种格式
            for variant in [code, code.upper(), code.lower()]:
                station_mapping[variant] = name
        
        print(f"✅ Created mapping with {len(set(station_mapping.values()))} unique names")
        
        # 显示所有映射
        print("\nAll station mappings:")
        unique_mappings = {}
        for code, name in station_mapping.items():
            base_code = code.upper()
            if base_code not in unique_mappings:
                unique_mappings[base_code] = name
                print(f"  {code} -> {name}")
        
        return station_mapping
        
    except Exception as e:
        print(f"❌ Failed to load station mapping: {e}")
        return {}

def test_actual_metrics_data():
    """测试实际的评价指标数据"""
    print("\nTesting actual metrics data...")
    
    try:
        # 加载评价指标文件
        metrics_dir = Path('output/Delaunay_interpolation/metrics')
        csv_files = list(metrics_dir.glob('*_metrics.csv'))
        
        if not csv_files:
            print("❌ No metrics files found")
            return
        
        # 读取第一个文件
        df = pd.read_csv(csv_files[0])
        stations = df['站点代码'].unique()
        
        print(f"✅ Found {len(stations)} stations in metrics")
        print("Stations in metrics:")
        for station in sorted(stations):
            print(f"  {station}")
        
        return stations
        
    except Exception as e:
        print(f"❌ Failed to load metrics data: {e}")
        return []

def create_real_test_with_mapping(station_mapping, metrics_stations):
    """使用真实映射创建测试图"""
    print("\nCreating real test with station mapping...")
    
    if not station_mapping or len(metrics_stations) == 0:
        print("❌ Missing data for real test")
        return
    
    # 设置字体 - 尝试多种方法
    font_setups = [
        lambda: plt.rcParams.update({
            'font.sans-serif': ['SimHei'],
            'axes.unicode_minus': False
        }),
        lambda: matplotlib.rcParams.update({
            'font.sans-serif': ['SimHei'],
            'axes.unicode_minus': False
        }),
        lambda: plt.rcParams.update({
            'font.family': ['sans-serif'],
            'font.sans-serif': ['SimHei', 'Microsoft YaHei'],
            'axes.unicode_minus': False
        })
    ]
    
    for i, setup_func in enumerate(font_setups):
        try:
            print(f"\nTrying font setup method {i+1}...")
            setup_func()
            
            # 选择前8个站点进行测试
            test_stations = metrics_stations[:8]
            test_values = np.random.rand(len(test_stations))
            
            # 获取中文名称
            chinese_names = []
            mapping_success = []
            
            for station in test_stations:
                chinese_name = None
                found = False
                
                # 尝试匹配
                for variant in [station, station.upper(), station.lower()]:
                    if variant in station_mapping:
                        chinese_name = station_mapping[variant]
                        found = True
                        break
                
                if found and chinese_name and chinese_name != 'nan':
                    chinese_names.append(chinese_name)
                    mapping_success.append(True)
                    print(f"  ✅ {station} -> {chinese_name}")
                else:
                    chinese_names.append(station)
                    mapping_success.append(False)
                    print(f"  ❌ {station} -> NOT FOUND")
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 10))
            
            # 绘制条形图
            colors = ['green' if success else 'red' for success in mapping_success]
            bars = ax.barh(range(len(test_stations)), test_values, color=colors, alpha=0.7)
            
            # 设置y轴标签
            ax.set_yticks(range(len(test_stations)))
            ax.set_yticklabels(chinese_names, fontsize=12)
            
            # 添加站点代码标注
            for j, (bar, station, success) in enumerate(zip(bars, test_stations, mapping_success)):
                status = "✅" if success else "❌"
                ax.text(bar.get_width() + 0.01, j, f'{status} ({station})', 
                       va='center', fontsize=10)
            
            ax.set_xlabel('Test Values', fontsize=12)
            ax.set_title(f'Real Station Mapping Test (Method {i+1})\nGreen=Mapped, Red=Not Mapped', 
                        fontsize=14, fontweight='bold')
            ax.grid(True, alpha=0.3, axis='x')
            
            plt.tight_layout()
            
            # 保存图片
            save_path = Path(f'real_mapping_test_method_{i+1}.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            print(f"  ✅ Real test image saved: {save_path}")
            
            # 统计结果
            success_count = sum(mapping_success)
            print(f"  📊 Mapping success: {success_count}/{len(test_stations)} ({success_count/len(test_stations)*100:.1f}%)")
            
        except Exception as e:
            print(f"  ❌ Method {i+1} failed: {e}")

def main():
    """主函数"""
    print("="*80)
    print("Chinese Font Display Verification Test")
    print("="*80)
    
    # 1. 基础中文字体测试
    test_chinese_display()
    
    # 2. 加载站点映射
    station_mapping = load_and_test_station_mapping()
    
    # 3. 测试实际数据
    metrics_stations = test_actual_metrics_data()
    
    # 4. 真实映射测试
    create_real_test_with_mapping(station_mapping, metrics_stations)
    
    print(f"\n" + "="*80)
    print("Verification Test Completed")
    print("Please check the generated test images:")
    print("- chinese_test_method_*.png")
    print("- real_mapping_test_method_*.png")
    print("="*80)

if __name__ == "__main__":
    main()

"""
简化版Delaunay三角网可视化工具
解决内存分配问题，生成清晰的三角网图
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
plt.rcParams['axes.unicode_minus'] = False

def create_simple_delaunay_plot():
    """创建简化版Delaunay三角网图"""
    try:
        # 读取站点数据
        stations_df = pd.read_csv('../stations.csv', encoding='utf-8')
        stations_df = stations_df.dropna(subset=['站点', '经度', '纬度'])
        
        print(f"加载了 {len(stations_df)} 个站点")
        
        # 提取坐标
        points = stations_df[['经度', '纬度']].values
        
        # 构建Delaunay三角网
        tri = Delaunay(points)
        
        print(f"构建了 {len(tri.simplices)} 个三角形")
        
        # 创建图形 - 使用较小的尺寸
        fig, ax = plt.subplots(1, 1, figsize=(12, 9))
        
        # 绘制三角网
        ax.triplot(points[:, 0], points[:, 1], tri.simplices, 
                  'b-', alpha=0.5, linewidth=0.8)
        
        # 绘制站点
        ax.scatter(points[:, 0], points[:, 1], 
                  c='red', s=80, alpha=0.8, zorder=5,
                  edgecolors='black', linewidth=1)
        
        # 添加站点标签 - 只显示部分以避免重叠
        for i, (idx, row) in enumerate(stations_df.iterrows()):
            if i % 2 == 0:  # 只显示一半的标签
                ax.annotate(row['站点'], 
                           (row['经度'], row['纬度']),
                           xytext=(3, 3), textcoords='offset points',
                           fontsize=7, alpha=0.7,
                           bbox=dict(boxstyle='round,pad=0.2', 
                                   facecolor='white', alpha=0.7))
        
        # 设置图形属性
        ax.set_xlabel('经度 (°)', fontsize=11)
        ax.set_ylabel('纬度 (°)', fontsize=11)
        ax.set_title('珠江流域雨量站点Delaunay三角剖分图', fontsize=14, pad=15)
        ax.grid(True, alpha=0.3)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        output_path = 'output/Delaunay/delaunay_triangulation_simple.png'
        plt.savefig(output_path, dpi=200, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print(f"简化版三角网图已保存: {output_path}")
        
        return output_path
        
    except Exception as e:
        print(f"生成简化版三角网图失败: {e}")
        return ""

def create_station_highlight_plot(highlight_station='80606500'):
    """创建高亮特定站点的图"""
    try:
        # 读取站点数据和分析结果
        stations_df = pd.read_csv('../stations.csv', encoding='utf-8')
        stations_df = stations_df.dropna(subset=['站点', '经度', '纬度'])
        
        results_df = pd.read_csv('output/Delaunay/delaunay_analysis_summary.csv')
        
        # 提取坐标
        points = stations_df[['经度', '纬度']].values
        
        # 构建Delaunay三角网
        tri = Delaunay(points)
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(12, 9))
        
        # 绘制三角网
        ax.triplot(points[:, 0], points[:, 1], tri.simplices, 
                  'b-', alpha=0.3, linewidth=0.6)
        
        # 绘制所有站点
        ax.scatter(points[:, 0], points[:, 1], 
                  c='lightblue', s=60, alpha=0.7, zorder=3,
                  edgecolors='gray', linewidth=0.5)
        
        # 高亮目标站点
        target_info = stations_df[stations_df['站点'] == highlight_station]
        if len(target_info) > 0:
            target_lon = target_info['经度'].iloc[0]
            target_lat = target_info['纬度'].iloc[0]
            
            ax.scatter(target_lon, target_lat, 
                      c='red', s=150, marker='*', 
                      edgecolors='black', linewidth=2, zorder=6,
                      label=f'验证站点: {highlight_station}')
            
            # 获取包围站点
            result_info = results_df[results_df['验证站点'] == highlight_station]
            if len(result_info) > 0:
                surrounding_stations = [
                    result_info['包围站点1'].iloc[0],
                    result_info['包围站点2'].iloc[0], 
                    result_info['包围站点3'].iloc[0]
                ]
                
                # 高亮包围站点
                for surr_station in surrounding_stations:
                    surr_info = stations_df[stations_df['站点'] == surr_station]
                    if len(surr_info) > 0:
                        surr_lon = surr_info['经度'].iloc[0]
                        surr_lat = surr_info['纬度'].iloc[0]
                        
                        ax.scatter(surr_lon, surr_lat, 
                                  c='green', s=100, marker='s', 
                                  edgecolors='black', linewidth=1.5, zorder=5)
                        
                        # 绘制连接线
                        ax.plot([target_lon, surr_lon], [target_lat, surr_lat],
                               'g--', alpha=0.7, linewidth=2)
                        
                        # 添加标签
                        ax.annotate(surr_station, 
                                   (surr_lon, surr_lat),
                                   xytext=(5, 5), textcoords='offset points',
                                   fontsize=8, weight='bold',
                                   bbox=dict(boxstyle='round,pad=0.2', 
                                           facecolor='lightgreen', alpha=0.8))
            
            # 添加目标站点标签
            ax.annotate(highlight_station, 
                       (target_lon, target_lat),
                       xytext=(10, 10), textcoords='offset points',
                       fontsize=10, weight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', 
                               facecolor='yellow', alpha=0.8))
        
        # 设置图形属性
        ax.set_xlabel('经度 (°)', fontsize=11)
        ax.set_ylabel('纬度 (°)', fontsize=11)
        ax.set_title(f'站点 {highlight_station} 的Delaunay包围关系', fontsize=14, pad=15)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=10)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        output_path = f'output/Delaunay/delaunay_highlight_{highlight_station}.png'
        plt.savefig(output_path, dpi=200, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print(f"高亮站点图已保存: {output_path}")
        
        return output_path
        
    except Exception as e:
        print(f"生成高亮站点图失败: {e}")
        return ""

if __name__ == "__main__":
    print("生成简化版Delaunay三角网可视化...")
    
    # 生成简化版三角网图
    simple_plot = create_simple_delaunay_plot()
    
    # 生成几个示例站点的高亮图
    example_stations = ['80606500', '80607800', '80609500']
    
    for station in example_stations:
        highlight_plot = create_station_highlight_plot(station)
        
    print("可视化生成完成！")

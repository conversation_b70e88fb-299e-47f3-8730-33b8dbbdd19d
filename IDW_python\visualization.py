#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化模块
生成IDW插值结果的各种图表和可视化

作者: 空间插值系统
日期: 2024年
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from typing import Dict, List, Optional
import warnings
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class IDWVisualizer:
    """IDW插值结果可视化类"""
    
    def __init__(self, config):
        self.config = config
        self.setup_style()
    
    def setup_style(self):
        """设置绘图样式"""
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 设置默认参数
        plt.rcParams['figure.figsize'] = self.config.figure_size
        plt.rcParams['figure.dpi'] = self.config.dpi
        plt.rcParams['font.size'] = self.config.font_size
        plt.rcParams['axes.labelsize'] = self.config.font_size
        plt.rcParams['xtick.labelsize'] = self.config.font_size - 2
        plt.rcParams['ytick.labelsize'] = self.config.font_size - 2
        plt.rcParams['legend.fontsize'] = self.config.font_size - 2
    
    def plot_time_series_comparison(self, result_df: pd.DataFrame, station_id: str, 
                                  event_name: str, save_path: Optional[Path] = None) -> Path:
        """绘制时间序列对比图"""
        fig, ax = plt.subplots(figsize=self.config.figure_size)
        
        # 过滤有效数据
        valid_data = result_df.dropna(subset=['观测值', '插值'])
        
        if len(valid_data) == 0:
            logger.warning(f"站点{station_id}没有有效数据用于绘图")
            plt.close(fig)
            return None
        
        # 绘制时间序列
        ax.plot(valid_data['时间'], valid_data['观测值'], 'o-', 
                label='观测值', linewidth=2, markersize=4, alpha=0.8)
        ax.plot(valid_data['时间'], valid_data['插值'], 's-', 
                label='IDW插值', linewidth=2, markersize=4, alpha=0.8)
        
        # 设置标题和标签
        ax.set_title(f'站点{station_id}降雨量时间序列对比\n洪水事件: {event_name}', 
                    fontsize=self.config.font_size + 2, fontweight='bold')
        ax.set_xlabel('时间', fontsize=self.config.font_size)
        ax.set_ylabel('降雨量 (mm)', fontsize=self.config.font_size)
        
        # 格式化x轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        # 添加网格和图例
        ax.grid(True, alpha=0.3)
        ax.legend(loc='best')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        if save_path is None:
            save_path = (self.config.output_dir / "visualizations" / event_name / 
                        f"{station_id}_timeseries.png")
        
        save_path.parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(save_path, dpi=self.config.dpi, bbox_inches='tight')
        plt.close(fig)
        
        return save_path
    
    def plot_scatter_comparison(self, result_df: pd.DataFrame, station_id: str, 
                              event_name: str, save_path: Optional[Path] = None) -> Path:
        """绘制散点对比图"""
        fig, ax = plt.subplots(figsize=self.config.figure_size)
        
        # 过滤有效数据
        valid_data = result_df.dropna(subset=['观测值', '插值'])
        
        if len(valid_data) == 0:
            logger.warning(f"站点{station_id}没有有效数据用于绘图")
            plt.close(fig)
            return None
        
        observed = valid_data['观测值'].values
        predicted = valid_data['插值'].values
        
        # 绘制散点图
        ax.scatter(observed, predicted, alpha=0.6, s=50, edgecolors='black', linewidth=0.5)
        
        # 绘制1:1线
        max_val = max(np.max(observed), np.max(predicted))
        min_val = min(np.min(observed), np.min(predicted))
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', 
                linewidth=2, label='1:1线')
        
        # 计算并显示统计信息
        r2 = np.corrcoef(observed, predicted)[0, 1] ** 2
        rmse = np.sqrt(np.mean((observed - predicted) ** 2))
        mae = np.mean(np.abs(observed - predicted))
        
        # 添加统计信息文本
        stats_text = f'R² = {r2:.3f}\nRMSE = {rmse:.2f} mm\nMAE = {mae:.2f} mm\nN = {len(valid_data)}'
        ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # 设置标题和标签
        ax.set_title(f'站点{station_id}观测值vs插值散点图\n洪水事件: {event_name}', 
                    fontsize=self.config.font_size + 2, fontweight='bold')
        ax.set_xlabel('观测值 (mm)', fontsize=self.config.font_size)
        ax.set_ylabel('IDW插值 (mm)', fontsize=self.config.font_size)
        
        # 设置相等的坐标轴范围
        ax.set_xlim(min_val, max_val)
        ax.set_ylim(min_val, max_val)
        ax.set_aspect('equal')
        
        # 添加网格和图例
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        if save_path is None:
            save_path = (self.config.output_dir / "visualizations" / event_name / 
                        f"{station_id}_scatter.png")
        
        save_path.parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(save_path, dpi=self.config.dpi, bbox_inches='tight')
        plt.close(fig)
        
        return save_path
    
    def plot_metrics_summary(self, event_metrics: Dict[str, Dict[str, float]], 
                           event_name: str, save_path: Optional[Path] = None) -> Path:
        """绘制评估指标汇总图"""
        if not event_metrics:
            logger.warning(f"事件{event_name}没有评估指标数据")
            return None
        
        # 准备数据
        metrics_df = pd.DataFrame.from_dict(event_metrics, orient='index')
        
        # 选择主要指标进行可视化
        main_metrics = ['MAE', 'RMSE', 'NSE', 'R2']
        available_metrics = [m for m in main_metrics if m in metrics_df.columns]
        
        if not available_metrics:
            logger.warning(f"事件{event_name}没有可用的评估指标")
            return None
        
        # 创建子图
        n_metrics = len(available_metrics)
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()
        
        for i, metric in enumerate(available_metrics):
            ax = axes[i]
            
            # 过滤有效数据
            valid_data = metrics_df[metric].dropna()
            
            if len(valid_data) == 0:
                ax.text(0.5, 0.5, f'无有效{metric}数据', ha='center', va='center', 
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'{metric}分布', fontsize=self.config.font_size + 1)
                continue
            
            # 绘制直方图
            ax.hist(valid_data, bins=min(20, len(valid_data)//2 + 1), 
                   alpha=0.7, edgecolor='black', linewidth=0.5)
            
            # 添加统计线
            mean_val = valid_data.mean()
            median_val = valid_data.median()
            ax.axvline(mean_val, color='red', linestyle='--', linewidth=2, label=f'均值: {mean_val:.3f}')
            ax.axvline(median_val, color='orange', linestyle='--', linewidth=2, label=f'中位数: {median_val:.3f}')
            
            # 设置标题和标签
            ax.set_title(f'{metric}分布 (N={len(valid_data)})', fontsize=self.config.font_size + 1)
            ax.set_xlabel(metric, fontsize=self.config.font_size)
            ax.set_ylabel('频数', fontsize=self.config.font_size)
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(n_metrics, len(axes)):
            axes[i].set_visible(False)
        
        # 设置总标题
        fig.suptitle(f'洪水事件{event_name}评估指标分布', 
                    fontsize=self.config.font_size + 4, fontweight='bold')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        if save_path is None:
            save_path = (self.config.output_dir / "visualizations" / event_name / 
                        f"{event_name}_metrics_summary.png")
        
        save_path.parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(save_path, dpi=self.config.dpi, bbox_inches='tight')
        plt.close(fig)
        
        return save_path
    
    def plot_performance_classification(self, event_metrics: Dict[str, Dict[str, float]], 
                                      event_name: str, save_path: Optional[Path] = None) -> Path:
        """绘制性能分类图"""
        if not event_metrics:
            return None
        
        # 计算性能分类
        from evaluation_metrics import EvaluationMetrics
        evaluator = EvaluationMetrics(self.config)
        
        performance_counts = {"优秀": 0, "良好": 0, "满意": 0, "可接受": 0, "不满意": 0, "无效": 0}
        
        for station_id, metrics in event_metrics.items():
            nse = metrics.get('NSE', np.nan)
            r2 = metrics.get('R2', np.nan)
            performance = evaluator.get_performance_classification(nse, r2)
            performance_counts[performance] += 1
        
        # 过滤掉计数为0的类别
        filtered_counts = {k: v for k, v in performance_counts.items() if v > 0}
        
        if not filtered_counts:
            logger.warning(f"事件{event_name}没有有效的性能分类数据")
            return None
        
        # 创建饼图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 饼图
        colors = ['#2E8B57', '#32CD32', '#FFD700', '#FFA500', '#FF6347', '#808080']
        wedges, texts, autotexts = ax1.pie(filtered_counts.values(), 
                                          labels=filtered_counts.keys(),
                                          autopct='%1.1f%%',
                                          colors=colors[:len(filtered_counts)],
                                          startangle=90)
        
        ax1.set_title(f'洪水事件{event_name}性能分类分布', 
                     fontsize=self.config.font_size + 2, fontweight='bold')
        
        # 柱状图
        bars = ax2.bar(filtered_counts.keys(), filtered_counts.values(), 
                      color=colors[:len(filtered_counts)], alpha=0.8, edgecolor='black')
        
        # 在柱子上添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{int(height)}', ha='center', va='bottom')
        
        ax2.set_title(f'各性能等级站点数量', fontsize=self.config.font_size + 1)
        ax2.set_xlabel('性能等级', fontsize=self.config.font_size)
        ax2.set_ylabel('站点数量', fontsize=self.config.font_size)
        ax2.grid(True, alpha=0.3, axis='y')
        
        # 旋转x轴标签
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        if save_path is None:
            save_path = (self.config.output_dir / "visualizations" / event_name / 
                        f"{event_name}_performance_classification.png")
        
        save_path.parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(save_path, dpi=self.config.dpi, bbox_inches='tight')
        plt.close(fig)
        
        return save_path
    
    def create_event_visualization_report(self, event_name: str, event_results: Dict[str, pd.DataFrame], 
                                        event_metrics: Dict[str, Dict[str, float]]) -> List[Path]:
        """为整个洪水事件创建可视化报告"""
        logger.info(f"开始为洪水事件{event_name}创建可视化报告")
        
        generated_plots = []
        
        # 为每个站点创建时间序列和散点图
        for station_id, result_df in event_results.items():
            try:
                # 时间序列图
                ts_path = self.plot_time_series_comparison(result_df, station_id, event_name)
                if ts_path:
                    generated_plots.append(ts_path)
                
                # 散点图
                scatter_path = self.plot_scatter_comparison(result_df, station_id, event_name)
                if scatter_path:
                    generated_plots.append(scatter_path)
                
            except Exception as e:
                logger.error(f"为站点{station_id}创建图表时出错: {e}")
                continue
        
        # 创建汇总图表
        try:
            # 评估指标汇总图
            metrics_path = self.plot_metrics_summary(event_metrics, event_name)
            if metrics_path:
                generated_plots.append(metrics_path)
            
            # 性能分类图
            performance_path = self.plot_performance_classification(event_metrics, event_name)
            if performance_path:
                generated_plots.append(performance_path)
            
        except Exception as e:
            logger.error(f"创建汇总图表时出错: {e}")
        
        logger.info(f"洪水事件{event_name}可视化报告完成，生成{len(generated_plots)}个图表")
        
        return generated_plots

if __name__ == "__main__":
    # 测试可视化模块
    from idw_config import config
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建可视化器
    visualizer = IDWVisualizer(config)
    
    # 生成测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='H')
    test_data = pd.DataFrame({
        '时间': dates,
        '观测值': np.random.rand(100) * 10,
        '插值': np.random.rand(100) * 10
    })
    
    # 测试时间序列图
    print("测试时间序列图...")
    ts_path = visualizer.plot_time_series_comparison(test_data, "TEST001", "TEST_EVENT")
    print(f"时间序列图保存到: {ts_path}")
    
    # 测试散点图
    print("测试散点图...")
    scatter_path = visualizer.plot_scatter_comparison(test_data, "TEST001", "TEST_EVENT")
    print(f"散点图保存到: {scatter_path}")

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IDW插值系统配置文件
基于Delaunay三角网分析结果的反距离权重插值配置管理

作者: 空间插值系统
日期: 2024年
"""

import os
from pathlib import Path

class IDWConfig:
    """IDW插值配置类"""
    
    def __init__(self):
        # 基础路径配置
        self.base_dir = Path("..")  # 从IDW_python目录向上一级
        self.input_dir = self.base_dir / "input_another"
        self.output_dir = self.base_dir / "output" / "IDW"
        self.delaunay_file = self.base_dir / "output" / "Delaunay" / "delaunay_analysis_summary_with_names.csv"
        self.terrain_mask = self.base_dir / "terrain" / "90" / "mask.asc"
        
        # IDW插值参数
        self.idw_power = 2.0  # IDW幂指数，默认为2
        self.use_delaunay_weights = True  # 是否使用Delaunay权重
        self.min_stations = 3  # 最少使用站点数
        self.max_stations = 10  # 最多使用站点数
        
        # 输出控制
        self.enable_raster_output = False  # 是否输出栅格文件
        self.enable_visualization = True   # 是否生成可视化
        self.enable_summary_report = True  # 是否生成汇总报告
        
        # 栅格输出参数
        self.raster_resolution = 0.000833333  # 栅格分辨率
        self.raster_nodata = -9999  # 无数据值
        
        # 评估指标
        self.evaluation_metrics = ['MAE', 'RMSE', 'NSE', 'R2', 'BIAS']
        
        # 并行处理
        self.n_cores = 12  # 并行核心数
        self.enable_parallel = True  # 是否启用并行处理
        
        # 可视化参数
        self.figure_size = (12, 8)
        self.dpi = 300
        self.font_size = 12
        self.chinese_font = 'SimHei'  # 中文字体
        
        # 日志配置
        self.log_level = 'INFO'
        self.log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # 创建输出目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的输出目录"""
        directories = [
            self.output_dir,
            self.output_dir / "interpolation_results",
            self.output_dir / "evaluation_metrics",
            self.output_dir / "visualizations",
            self.output_dir / "summary_reports",
            self.output_dir / "raster_outputs"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_flood_events(self):
        """获取所有洪水事件列表"""
        if not self.input_dir.exists():
            return []
        
        flood_events = []
        for event_dir in self.input_dir.iterdir():
            if event_dir.is_dir():
                flood_events.append(event_dir.name)
        
        return sorted(flood_events)
    
    def get_station_files(self, event_name):
        """获取指定洪水事件的所有站点文件"""
        event_dir = self.input_dir / event_name
        if not event_dir.exists():
            return []
        
        station_files = []
        for file_path in event_dir.glob("*.csv"):
            station_files.append(file_path)
        
        return sorted(station_files)
    
    def validate_config(self):
        """验证配置有效性"""
        errors = []
        
        # 检查必要文件和目录
        if not self.input_dir.exists():
            errors.append(f"输入目录不存在: {self.input_dir}")
        
        if not self.delaunay_file.exists():
            errors.append(f"Delaunay分析文件不存在: {self.delaunay_file}")
        
        if self.enable_raster_output and not self.terrain_mask.exists():
            errors.append(f"地形掩膜文件不存在: {self.terrain_mask}")
        
        # 检查参数范围
        if self.idw_power <= 0:
            errors.append("IDW幂指数必须大于0")
        
        if self.min_stations < 1:
            errors.append("最少站点数必须大于等于1")
        
        if self.max_stations < self.min_stations:
            errors.append("最多站点数必须大于等于最少站点数")
        
        if self.n_cores < 1:
            errors.append("并行核心数必须大于等于1")
        
        return errors
    
    def print_config(self):
        """打印配置信息"""
        print("=" * 60)
        print("IDW插值系统配置信息")
        print("=" * 60)
        print(f"输入目录: {self.input_dir}")
        print(f"输出目录: {self.output_dir}")
        print(f"Delaunay文件: {self.delaunay_file}")
        print(f"地形掩膜: {self.terrain_mask}")
        print("-" * 60)
        print(f"IDW幂指数: {self.idw_power}")
        print(f"使用Delaunay权重: {self.use_delaunay_weights}")
        print(f"站点数范围: {self.min_stations}-{self.max_stations}")
        print("-" * 60)
        print(f"栅格输出: {self.enable_raster_output}")
        print(f"可视化: {self.enable_visualization}")
        print(f"汇总报告: {self.enable_summary_report}")
        print(f"并行处理: {self.enable_parallel} (核心数: {self.n_cores})")
        print("=" * 60)

# 创建全局配置实例
config = IDWConfig()

if __name__ == "__main__":
    # 验证配置
    errors = config.validate_config()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过!")
        config.print_config()

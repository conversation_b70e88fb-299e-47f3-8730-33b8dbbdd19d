# Molan指数（莫兰指数）计算方法详解

## 1. 概述

Molan指数（Moran's Index）是一种用于测量空间自相关性的统计指标，在本项目中用于分析降雨站点之间的空间相关性。该指数能够量化相邻站点之间降雨数据的相似程度，为空间插值提供重要的权重参考。

## 2. 核心计算公式

### 2.1 简化版莫兰指数公式

在本项目的实现中，采用了针对站点对的简化版莫兰指数计算公式：

```
I = w_ij × (1/n) × Σ(z_i × z_j)
```

其中：
- `I` : 莫兰指数值
- `w_ij` : 站点i和站点j之间的空间权重
- `n` : 有效数据点数量
- `z_i` : 站点i的标准化降雨数据
- `z_j` : 站点j的标准化降雨数据

### 2.2 数据标准化公式

```
z_i = (x_i - μ_i) / σ_i
z_j = (x_j - μ_j) / σ_j
```

其中：
- `x_i, x_j` : 原始降雨数据
- `μ_i, μ_j` : 降雨数据的均值
- `σ_i, σ_j` : 降雨数据的标准差

### 2.3 空间权重计算公式

#### 反距离权重法（默认方法）：
```
w_ij = (1/d_ij) / Σ(1/d_ik)
```

#### 二元权重法：
```
w_ij = {1, if d_ij ≤ threshold
       {0, if d_ij > threshold
```

#### 距离计算（Haversine公式）：
```
d = 2R × arcsin(√[sin²(Δφ/2) + cos(φ₁) × cos(φ₂) × sin²(Δλ/2)])
```

其中：
- `R` : 地球半径（6371公里）
- `φ₁, φ₂` : 两点的纬度（弧度）
- `Δφ` : 纬度差
- `Δλ` : 经度差

## 3. 主要参数说明

### 3.1 输入参数

| 参数名称 | 类型 | 含义 | 默认值 |
|---------|------|------|--------|
| `ref_station` | string | 参考站点ID | - |
| `target_station` | string | 目标站点ID | - |
| `station_list` | list | 站点列表 | - |
| `rain_data` | DataFrame | 降雨数据 | - |
| `n_permutations` | int | 置换检验次数 | 99 |
| `method` | string | 权重计算方法 | 'inverse_distance' |

### 3.2 权重计算参数

| 参数名称 | 含义 | 默认值 |
|---------|------|--------|
| `threshold` | 二元权重距离阈值（公里） | 50 |
| `distance_epsilon` | 避免除零的小值 | 1e-10 |

### 3.3 数据质量参数

| 参数名称 | 含义 | 默认值 |
|---------|------|--------|
| `min_data_points` | 最少有效数据点数 | 10 |
| `min_std_threshold` | 最小标准差阈值 | 0 |

## 4. 输出结果说明

### 4.1 主要输出指标

| 指标名称 | 符号 | 含义 | 取值范围 |
|---------|------|------|----------|
| 莫兰指数 | `moran_i` | 空间自相关强度 | [-1, 1] |
| p值 | `p_value` | 统计显著性 | [0, 1] |
| z分数 | `z_score` | 标准化统计量 | (-∞, +∞) |
| 显著性水平 | `significance_level` | 显著性等级 | 符号表示 |

### 4.2 莫兰指数解释

- **I > 0** : 正空间自相关，相邻站点降雨模式相似
- **I = 0** : 无空间自相关，降雨分布随机
- **I < 0** : 负空间自相关，相邻站点降雨模式相反

### 4.3 显著性水平标记

| 符号 | p值范围 | 显著性水平 |
|------|---------|------------|
| `***` | p < 0.001 | 极显著 |
| `**` | p < 0.01 | 高显著 |
| `*` | p < 0.05 | 显著 |
| `.` | p < 0.1 | 边际显著 |
| `NS` | p ≥ 0.1 | 不显著 |

## 5. 统计显著性检验

### 5.1 置换检验方法

本项目采用置换检验（Permutation Test）来评估莫兰指数的统计显著性：

1. **原假设（H₀）**：站点间不存在空间自相关
2. **备择假设（H₁）**：站点间存在空间自相关

### 5.2 检验步骤

1. 计算观测的莫兰指数 `I_observed`
2. 随机置换目标站点数据n次（默认99次）
3. 计算每次置换后的莫兰指数 `I_permuted`
4. 计算p值：
   ```
   p = (count(I_permuted ≥ I_observed) + 1) / (n_permutations + 1)  # 正相关
   p = (count(I_permuted ≤ I_observed) + 1) / (n_permutations + 1)  # 负相关
   ```

### 5.3 z分数计算

```
z = (I_observed - E[I_permuted]) / SD[I_permuted]
```

其中：
- `E[I_permuted]` : 置换莫兰指数的期望值
- `SD[I_permuted]` : 置换莫兰指数的标准差

## 6. 算法流程

### 6.1 数据预处理
1. 检查站点是否存在于降雨数据中
2. 移除缺失值（NaN）
3. 检查有效数据点数量（≥10个）
4. 计算数据的均值和标准差
5. 进行数据标准化

### 6.2 权重矩阵构建
1. 获取站点坐标信息
2. 计算站点间距离（Haversine公式）
3. 根据选择的方法计算权重
4. 进行行标准化处理

### 6.3 莫兰指数计算
1. 获取站点在权重矩阵中的索引
2. 提取对应的空间权重
3. 计算标准化数据的乘积均值
4. 应用权重得到最终莫兰指数

### 6.4 显著性检验
1. 执行置换检验
2. 计算p值和z分数
3. 确定显著性水平
4. 返回完整结果

## 7. 应用场景

### 7.1 空间插值权重优化
- 根据莫兰指数调整插值权重
- 提高空间插值精度
- 识别空间异常值

### 7.2 站点网络分析
- 评估站点布局合理性
- 识别冗余站点
- 优化监测网络设计

### 7.3 降雨模式分析
- 分析降雨的空间分布特征
- 识别降雨集聚区域
- 研究极端降雨事件的空间传播

## 8. 注意事项

### 8.1 数据要求
- 需要足够的有效数据点（≥10个）
- 数据标准差不能为零
- 站点坐标信息必须准确

### 8.2 参数选择
- 置换检验次数影响p值精度
- 权重方法选择影响结果解释
- 距离阈值需根据研究区域调整

### 8.3 结果解释
- 莫兰指数需结合显著性检验解释
- 考虑数据的时间特征
- 注意极端值对结果的影响

## 9. 代码实现细节

### 9.1 核心函数结构

```python
def calculate_moran_index_with_significance(self, ref_station, target_station,
                                          station_list, rain_data, n_permutations=99):
    """
    计算两个站点间的莫兰指数及其显著性检验

    参数:
    - ref_station: 参考站点ID
    - target_station: 目标站点ID
    - station_list: 站点列表
    - rain_data: 降雨数据DataFrame
    - n_permutations: 置换检验次数

    返回:
    - dict: {'moran_i', 'p_value', 'z_score', 'significance_level'}
    """
```

### 9.2 权重矩阵计算

```python
def calculate_spatial_weights(self, station_list, method='inverse_distance'):
    """
    计算空间权重矩阵

    支持方法:
    - 'inverse_distance': 反距离权重（默认）
    - 'binary': 二元权重（基于距离阈值）
    """
```

### 9.3 距离计算实现

```python
def haversine_distance(self, lon1, lat1, lon2, lat2):
    """
    使用Haversine公式计算球面距离

    输入: 经纬度坐标（度）
    输出: 距离（公里）
    """
```

## 10. 批量分析功能

### 10.1 分析流程

1. **洪水事件循环**: 对每个洪水事件进行分析
2. **分组分析**: 按站点分组（整体、大化、太平、水晏）
3. **站点对分析**: 计算每个参考站点与其他站点的莫兰指数
4. **排序筛选**: 选择莫兰指数最高的前3个站点
5. **结果保存**: 生成详细结果和汇总统计

### 10.2 输出文件

| 文件名 | 内容 | 格式 |
|--------|------|------|
| `莫兰指数分析详细结果.csv` | 所有站点对的完整分析结果 | CSV |
| `莫兰指数显著性检验汇总表.csv` | 各组显著性统计汇总 | CSV |
| `莫兰指数分析汇总报告.txt` | 详细统计报告 | TXT |
| `{洪水事件}_莫兰指数分析.csv` | 单个洪水事件结果 | CSV |
| `{分组}_所有洪水事件莫兰指数.csv` | 分组汇总结果 | CSV |

### 10.3 结果字段说明

| 字段名 | 含义 | 数据类型 |
|--------|------|----------|
| `flood_event` | 洪水事件名称 | string |
| `group` | 站点分组 | string |
| `reference_station` | 参考站点 | string |
| `top_1_station` | 第1相关站点 | string |
| `top_1_moran` | 第1相关莫兰指数 | float |
| `top_1_p_value` | 第1相关p值 | float |
| `top_1_z_score` | 第1相关z分数 | float |
| `top_1_significance` | 第1相关显著性 | string |
| `avg_moran` | 前3站点平均莫兰指数 | float |
| `max_moran` | 前3站点最大莫兰指数 | float |
| `significant_count` | 显著站点数量 | int |

## 11. 质量控制措施

### 11.1 数据验证

- **站点存在性检查**: 确保站点在降雨数据中存在
- **数据完整性检查**: 移除缺失值和异常值
- **最小样本量检查**: 确保有效数据点≥10个
- **方差检查**: 确保数据标准差>0

### 11.2 计算稳定性

- **除零保护**: 在距离计算中添加小值避免除零
- **权重标准化**: 确保权重矩阵行和为1
- **异常值处理**: 对极端莫兰指数值进行标记

### 11.3 统计有效性

- **置换检验**: 使用足够的置换次数（默认99次）
- **双尾检验**: 考虑正负相关的可能性
- **多重比较**: 在批量分析中考虑多重比较问题

## 12. 性能优化

### 12.1 计算效率

- **矩阵化计算**: 使用NumPy进行向量化操作
- **内存管理**: 及时释放大型数组内存
- **并行处理**: 支持多核CPU并行计算（可扩展）

### 12.2 存储优化

- **分批保存**: 按洪水事件和分组分别保存结果
- **压缩格式**: 使用UTF-8编码减少文件大小
- **索引优化**: 合理设置DataFrame索引

## 13. 扩展功能

### 13.1 权重方法扩展

可以添加更多权重计算方法：
- **指数衰减权重**: `w = exp(-d/λ)`
- **高斯权重**: `w = exp(-d²/2σ²)`
- **K近邻权重**: 只考虑最近的K个站点

### 13.2 显著性检验扩展

可以添加其他检验方法：
- **正态分布检验**: 基于理论分布的检验
- **Bootstrap检验**: 基于重采样的检验
- **贝叶斯检验**: 基于贝叶斯统计的检验

### 13.3 可视化扩展

可以添加可视化功能：
- **空间分布图**: 显示站点位置和莫兰指数
- **相关性热图**: 显示站点间相关性矩阵
- **时间序列图**: 显示莫兰指数的时间变化

## 14. 常见问题解答

### 14.1 计算问题

**Q: 为什么某些站点对的莫兰指数为0？**
A: 可能原因包括：数据不足、标准差为0、站点坐标缺失等。

**Q: 如何选择合适的置换检验次数？**
A: 一般99次足够，如需更高精度可增加到999次或更多。

**Q: 权重方法如何选择？**
A: 反距离权重适用于大多数情况，二元权重适用于明确距离阈值的场景。

### 14.2 结果解释

**Q: 莫兰指数的实际意义是什么？**
A: 正值表示相邻站点降雨相似，负值表示相邻站点降雨相反，绝对值越大相关性越强。

**Q: 如何判断结果的可靠性？**
A: 主要看p值和显著性水平，p<0.05通常认为结果可靠。

**Q: 不同洪水事件的结果差异很大正常吗？**
A: 正常，不同降雨事件的空间分布模式确实可能差异很大。

---

**文档版本**: 1.0
**创建日期**: 2025-06-16
**适用版本**: 空间插值系统 v1.0
**最后更新**: 2025-06-16

"""
评价指标计算模块
实现各种插值评价指标的计算
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import os
from datetime import datetime

logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
plt.rcParams['axes.unicode_minus'] = False


class EvaluationMetrics:
    """评价指标计算器"""
    
    def __init__(self, config):
        """初始化评价指标计算器"""
        self.config = config
    
    def calculate_mae(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算平均绝对误差 (MAE)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return 0.0
            
            return np.mean(np.abs(observed - predicted))
            
        except Exception as e:
            logger.error(f"计算MAE失败: {e}")
            return 0.0
    
    def calculate_rmse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算均方根误差 (RMSE)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return 0.0
            
            return np.sqrt(np.mean((observed - predicted) ** 2))
            
        except Exception as e:
            logger.error(f"计算RMSE失败: {e}")
            return 0.0
    
    def calculate_nse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算纳什效率系数 (NSE)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return 0.0
            
            # 计算观测值的方差
            obs_var = np.var(observed)
            if obs_var == 0:
                # 如果观测值方差为0，检查预测值是否完全匹配
                return 1.0 if np.array_equal(observed, predicted) else 0.0
            
            # 计算NSE
            numerator = np.sum((observed - predicted) ** 2)
            denominator = np.sum((observed - np.mean(observed)) ** 2)
            
            if denominator == 0:
                return 0.0
            
            nse = 1 - (numerator / denominator)
            return nse
            
        except Exception as e:
            logger.error(f"计算NSE失败: {e}")
            return 0.0
    
    def calculate_correlation(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算相关系数 (R)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return 0.0
            
            # 检查标准差
            if np.std(observed) == 0 or np.std(predicted) == 0:
                return 1.0 if np.array_equal(observed, predicted) else 0.0
            
            # 计算皮尔逊相关系数
            correlation_matrix = np.corrcoef(observed, predicted)
            correlation = correlation_matrix[0, 1]
            
            # 处理NaN值
            if np.isnan(correlation):
                return 0.0
            
            return correlation
            
        except Exception as e:
            logger.error(f"计算相关系数失败: {e}")
            return 0.0
    
    def calculate_r_squared(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算决定系数 (R²)"""
        try:
            correlation = self.calculate_correlation(observed, predicted)
            return correlation ** 2
            
        except Exception as e:
            logger.error(f"计算R²失败: {e}")
            return 0.0
    
    def calculate_bias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算偏差 (Bias)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return 0.0
            
            return np.mean(predicted - observed)
            
        except Exception as e:
            logger.error(f"计算偏差失败: {e}")
            return 0.0
    
    def calculate_pbias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算百分比偏差 (PBIAS)"""
        try:
            if len(observed) == 0 or len(predicted) == 0:
                return 0.0
            
            obs_sum = np.sum(observed)
            if obs_sum == 0:
                return 0.0
            
            bias = np.sum(predicted - observed)
            pbias = (bias / obs_sum) * 100
            
            return pbias
            
        except Exception as e:
            logger.error(f"计算PBIAS失败: {e}")
            return 0.0
    
    def calculate_all_metrics(self, observed: np.ndarray, predicted: np.ndarray) -> Dict[str, float]:
        """计算所有评价指标"""
        try:
            # 移除NaN值
            valid_mask = ~(np.isnan(observed) | np.isnan(predicted))
            obs_clean = observed[valid_mask]
            pred_clean = predicted[valid_mask]
            
            if len(obs_clean) == 0:
                return {
                    'MAE': 0.0,
                    'RMSE': 0.0,
                    'NSE': 0.0,
                    'R': 0.0,
                    'R2': 0.0,
                    'Bias': 0.0,
                    'PBIAS': 0.0,
                    'Count': 0
                }
            
            metrics = {
                'MAE': self.calculate_mae(obs_clean, pred_clean),
                'RMSE': self.calculate_rmse(obs_clean, pred_clean),
                'NSE': self.calculate_nse(obs_clean, pred_clean),
                'R': self.calculate_correlation(obs_clean, pred_clean),
                'R2': self.calculate_r_squared(obs_clean, pred_clean),
                'Bias': self.calculate_bias(obs_clean, pred_clean),
                'PBIAS': self.calculate_pbias(obs_clean, pred_clean),
                'Count': len(obs_clean)
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算评价指标失败: {e}")
            return {
                'MAE': 0.0,
                'RMSE': 0.0,
                'NSE': 0.0,
                'R': 0.0,
                'R2': 0.0,
                'Bias': 0.0,
                'PBIAS': 0.0,
                'Count': 0
            }
    
    def evaluate_time_series(self, observed_series: List[np.ndarray], 
                           predicted_series: List[np.ndarray],
                           time_points: List) -> Dict:
        """评价时间序列插值结果"""
        try:
            logger.info("正在计算时间序列评价指标...")
            
            # 逐时间点计算指标
            time_metrics = []
            all_observed = []
            all_predicted = []
            
            for i, (obs, pred, time_point) in enumerate(zip(observed_series, predicted_series, time_points)):
                # 计算当前时间点的指标
                metrics = self.calculate_all_metrics(obs, pred)
                metrics['TimePoint'] = time_point
                time_metrics.append(metrics)
                
                # 收集所有数据用于总体评价
                valid_mask = ~(np.isnan(obs) | np.isnan(pred))
                all_observed.extend(obs[valid_mask])
                all_predicted.extend(pred[valid_mask])
            
            # 计算总体指标
            overall_metrics = self.calculate_all_metrics(
                np.array(all_observed), np.array(all_predicted)
            )
            
            # 计算非零降雨时的指标
            nonzero_observed = []
            nonzero_predicted = []
            
            for obs, pred in zip(observed_series, predicted_series):
                # 只考虑有降雨的时间点
                nonzero_mask = (obs > 0) | (pred > 0)
                if np.any(nonzero_mask):
                    valid_mask = ~(np.isnan(obs) | np.isnan(pred)) & nonzero_mask
                    nonzero_observed.extend(obs[valid_mask])
                    nonzero_predicted.extend(pred[valid_mask])
            
            nonzero_metrics = self.calculate_all_metrics(
                np.array(nonzero_observed), np.array(nonzero_predicted)
            ) if nonzero_observed else overall_metrics.copy()
            
            # 统计信息
            stats = {
                'total_time_points': len(time_points),
                'total_samples': len(all_observed),
                'nonzero_samples': len(nonzero_observed),
                'zero_ratio': 1 - (len(nonzero_observed) / len(all_observed)) if all_observed else 0
            }
            
            return {
                'time_metrics': time_metrics,
                'overall_metrics': overall_metrics,
                'nonzero_metrics': nonzero_metrics,
                'statistics': stats
            }
            
        except Exception as e:
            logger.error(f"评价时间序列失败: {e}")
            return {}
    
    def create_evaluation_report(self, evaluation_results: Dict, output_dir: str) -> str:
        """创建评价报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = os.path.join(output_dir, f"evaluation_report_{timestamp}.txt")
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("PRISM插值评价报告\n")
                f.write("=" * 50 + "\n\n")
                
                # 总体指标
                if 'overall_metrics' in evaluation_results:
                    overall = evaluation_results['overall_metrics']
                    f.write("总体评价指标:\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"样本数量: {overall['Count']}\n")
                    f.write(f"平均绝对误差 (MAE): {overall['MAE']:.4f} mm\n")
                    f.write(f"均方根误差 (RMSE): {overall['RMSE']:.4f} mm\n")
                    f.write(f"纳什效率系数 (NSE): {overall['NSE']:.4f}\n")
                    f.write(f"相关系数 (R): {overall['R']:.4f}\n")
                    f.write(f"决定系数 (R²): {overall['R2']:.4f}\n")
                    f.write(f"偏差 (Bias): {overall['Bias']:.4f} mm\n")
                    f.write(f"百分比偏差 (PBIAS): {overall['PBIAS']:.2f}%\n\n")
                
                # 非零降雨指标
                if 'nonzero_metrics' in evaluation_results:
                    nonzero = evaluation_results['nonzero_metrics']
                    f.write("非零降雨评价指标:\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"样本数量: {nonzero['Count']}\n")
                    f.write(f"平均绝对误差 (MAE): {nonzero['MAE']:.4f} mm\n")
                    f.write(f"均方根误差 (RMSE): {nonzero['RMSE']:.4f} mm\n")
                    f.write(f"纳什效率系数 (NSE): {nonzero['NSE']:.4f}\n")
                    f.write(f"相关系数 (R): {nonzero['R']:.4f}\n")
                    f.write(f"决定系数 (R²): {nonzero['R2']:.4f}\n")
                    f.write(f"偏差 (Bias): {nonzero['Bias']:.4f} mm\n")
                    f.write(f"百分比偏差 (PBIAS): {nonzero['PBIAS']:.2f}%\n\n")
                
                # 统计信息
                if 'statistics' in evaluation_results:
                    stats = evaluation_results['statistics']
                    f.write("统计信息:\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"总时间点数: {stats['total_time_points']}\n")
                    f.write(f"总样本数: {stats['total_samples']}\n")
                    f.write(f"非零样本数: {stats['nonzero_samples']}\n")
                    f.write(f"零值比例: {stats['zero_ratio']:.2%}\n\n")
                
                # 指标解释
                f.write("指标说明:\n")
                f.write("-" * 30 + "\n")
                f.write("MAE: 平均绝对误差，越小越好\n")
                f.write("RMSE: 均方根误差，越小越好\n")
                f.write("NSE: 纳什效率系数，>0.75表示模型效果很好，>0.5表示满意\n")
                f.write("R: 相关系数，越接近1越好\n")
                f.write("R²: 决定系数，越接近1越好\n")
                f.write("Bias: 偏差，接近0表示无系统性偏差\n")
                f.write("PBIAS: 百分比偏差，±25%以内为满意\n")
            
            logger.info(f"评价报告已保存到: {report_file}")
            return report_file
            
        except Exception as e:
            logger.error(f"创建评价报告失败: {e}")
            return ""
    
    def create_scatter_plot(self, observed: np.ndarray, predicted: np.ndarray, 
                          output_dir: str, title: str = "插值验证散点图") -> str:
        """创建散点图"""
        try:
            # 移除NaN值
            valid_mask = ~(np.isnan(observed) | np.isnan(predicted))
            obs_clean = observed[valid_mask]
            pred_clean = predicted[valid_mask]
            
            if len(obs_clean) == 0:
                logger.warning("没有有效数据用于绘制散点图")
                return ""
            
            # 计算指标
            metrics = self.calculate_all_metrics(obs_clean, pred_clean)
            
            # 创建图形
            plt.figure(figsize=(10, 8))
            
            # 绘制散点
            plt.scatter(obs_clean, pred_clean, alpha=0.6, s=20, c='blue', edgecolors='none')
            
            # 绘制1:1线
            max_val = max(np.max(obs_clean), np.max(pred_clean))
            min_val = min(np.min(obs_clean), np.min(pred_clean))
            plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='1:1线')
            
            # 设置标签和标题
            plt.xlabel('观测值 (mm)', fontsize=12)
            plt.ylabel('预测值 (mm)', fontsize=12)
            plt.title(f'{title}\n'
                     f'R²={metrics["R2"]:.3f}, NSE={metrics["NSE"]:.3f}, '
                     f'RMSE={metrics["RMSE"]:.2f}mm', fontsize=14)
            
            # 添加网格和图例
            plt.grid(True, alpha=0.3)
            plt.legend()
            
            # 设置坐标轴相等
            plt.axis('equal')
            
            # 保存图形
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_file = os.path.join(output_dir, f"scatter_plot_{timestamp}.png")
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"散点图已保存到: {plot_file}")
            return plot_file
            
        except Exception as e:
            logger.error(f"创建散点图失败: {e}")
            return ""
    
    def save_detailed_metrics(self, evaluation_results: Dict, output_dir: str) -> str:
        """保存详细的评价指标到CSV文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_file = os.path.join(output_dir, f"detailed_metrics_{timestamp}.csv")
            
            # 创建DataFrame
            if 'time_metrics' in evaluation_results:
                df = pd.DataFrame(evaluation_results['time_metrics'])
                
                # 添加总体指标行
                if 'overall_metrics' in evaluation_results:
                    overall_row = evaluation_results['overall_metrics'].copy()
                    overall_row['TimePoint'] = 'Overall'
                    df = pd.concat([df, pd.DataFrame([overall_row])], ignore_index=True)
                
                # 添加非零降雨指标行
                if 'nonzero_metrics' in evaluation_results:
                    nonzero_row = evaluation_results['nonzero_metrics'].copy()
                    nonzero_row['TimePoint'] = 'NonZero'
                    df = pd.concat([df, pd.DataFrame([nonzero_row])], ignore_index=True)
                
                # 保存到CSV
                df.to_csv(csv_file, index=False, encoding='utf-8')
                
                logger.info(f"详细评价指标已保存到: {csv_file}")
                return csv_file
            
        except Exception as e:
            logger.error(f"保存详细评价指标失败: {e}")
            return ""

    def print_summary_metrics(self, evaluation_results: Dict):
        """打印评价指标摘要"""
        try:
            print("\n" + "="*60)
            print("PRISM插值评价指标摘要")
            print("="*60)

            if 'overall_metrics' in evaluation_results:
                overall = evaluation_results['overall_metrics']
                print(f"总体评价 (样本数: {overall['Count']}):")
                print(f"  MAE:  {overall['MAE']:.4f} mm")
                print(f"  RMSE: {overall['RMSE']:.4f} mm")
                print(f"  NSE:  {overall['NSE']:.4f}")
                print(f"  R:    {overall['R']:.4f}")
                print(f"  R²:   {overall['R2']:.4f}")

            if 'nonzero_metrics' in evaluation_results:
                nonzero = evaluation_results['nonzero_metrics']
                print(f"\n非零降雨评价 (样本数: {nonzero['Count']}):")
                print(f"  MAE:  {nonzero['MAE']:.4f} mm")
                print(f"  RMSE: {nonzero['RMSE']:.4f} mm")
                print(f"  NSE:  {nonzero['NSE']:.4f}")
                print(f"  R:    {nonzero['R']:.4f}")
                print(f"  R²:   {nonzero['R2']:.4f}")

            if 'statistics' in evaluation_results:
                stats = evaluation_results['statistics']
                print(f"\n统计信息:")
                print(f"  总时间点数: {stats['total_time_points']}")
                print(f"  零值比例: {stats['zero_ratio']:.2%}")

            print("="*60)
            print("指标说明:")
            print("  NSE > 0.75: 模型效果很好")
            print("  NSE > 0.5:  模型效果满意")
            print("  R² > 0.8:   相关性很强")
            print("="*60 + "\n")

        except Exception as e:
            logger.error(f"打印评价指标摘要失败: {e}")

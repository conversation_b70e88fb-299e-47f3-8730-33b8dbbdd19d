# -*- coding: utf-8 -*-
"""
OI插值系统配置文件

这个文件包含了所有可配置的参数，新手可以通过修改这个文件来调整插值参数。
"""

import os
from dataclasses import dataclass
from typing import Optional, List

@dataclass
class Config:
    """
    OI插值系统配置类
    
    所有参数都有详细的中文注释，新手可以根据需要修改
    """
    
    # ==================== 路径配置 ====================
    # 输入数据路径（包含点雨量CSV文件的文件夹）
    input_dir: str = "D:/pythondata/spatial_interpolation/input_another/2009-1"
    
    # 站点信息文件路径（包含站点ID、经度、纬度的CSV文件）
    stations_file: str = "D:/pythondata/spatial_interpolation/stations.csv"
    
    # 流域掩膜文件路径（ASC格式）
    mask_file: str = "D:/pythondata/spatial_interpolation/terrain/90/mask.asc"
    
    # 输出目录路径
    output_dir: str = "D:/pythondata/spatial_interpolation/output/OI/2009-1"
    
    # ==================== 处理参数配置 ====================
    # 并行处理核心数（建议设置为CPU核心数的80%）
    num_processes: int = 20
    
    # 批处理大小（每批处理的时间步数，用于内存管理）
    batch_size: int = 60
    
    # 是否启用调试模式（只处理少量数据进行测试）
    debug_mode: bool = False
    
    # 调试模式下处理的时间步数
    debug_time_steps: int = 10
    
    # ==================== 插值算法参数 ====================
    # 观测误差（用于最优插值算法）
    observation_error: float = 0.01
    
    # 是否使用距离权重调整
    use_distance_weights: bool = True
    
    # 距离影响因子（距离权重的影响程度）
    distance_factor: float = 0.2
    
    # 最大距离阈值（超过此距离的站点权重会降低）
    max_distance_threshold: float = 10.0
    
    # ==================== 零值处理参数 ====================
    # 零值比例阈值（超过此比例时采用简化策略）
    zero_ratio_threshold: float = 0.99
    
    # 极高零值比例阈值（超过此比例时直接返回0）
    high_zero_ratio_threshold: float = 0.999999
    
    # ==================== 输出控制 ====================
    # 是否生成栅格输出（ASC文件）
    generate_raster: bool = False
    
    # 是否生成点雨量输出文件
    generate_point_output: bool = True
    
    # 是否生成Delaunay三角网图
    generate_delaunay_plot: bool = True
    
    # 是否保存详细的验证结果
    save_detailed_validation: bool = True
    
    # ==================== 批量处理配置 ====================
    # 是否启用批量处理模式
    batch_processing: bool = True
    
    # 批量处理的根目录（包含多个洪水事件文件夹）
    batch_root_dir: str = "D:/pythondata/spatial_interpolation/input_another"
    
    # 批量处理输出根目录
    batch_output_root: str = "D:/pythondata/spatial_interpolation/output/OI"
    
    # 需要处理的文件夹列表（为空时处理所有文件夹）
    target_folders: Optional[List[str]] = None
    
    # ==================== 可视化参数 ====================
    # 图片保存格式
    plot_format: str = "png"
    
    # 图片DPI
    plot_dpi: int = 300
    
    # 是否显示图片（False时只保存不显示）
    show_plots: bool = False
    
    # ==================== 日志配置 ====================
    # 日志级别（DEBUG, INFO, WARNING, ERROR）
    log_level: str = "INFO"
    
    # 是否保存日志到文件
    save_log_to_file: bool = True
    
    # 是否在控制台显示日志
    show_log_in_console: bool = True
    
    def __post_init__(self):
        """
        初始化后的验证和处理
        """
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 如果是批量处理模式，确保批量输出目录存在
        if self.batch_processing:
            os.makedirs(self.batch_output_root, exist_ok=True)
    
    def validate(self) -> bool:
        """
        验证配置参数的有效性
        
        Returns:
            bool: 配置是否有效
        """
        errors = []
        
        # 检查必要的文件是否存在
        if not os.path.exists(self.stations_file):
            errors.append(f"站点文件不存在: {self.stations_file}")
        
        if not os.path.exists(self.mask_file):
            errors.append(f"掩膜文件不存在: {self.mask_file}")
        
        if not self.batch_processing and not os.path.exists(self.input_dir):
            errors.append(f"输入目录不存在: {self.input_dir}")
        
        if self.batch_processing and not os.path.exists(self.batch_root_dir):
            errors.append(f"批量处理根目录不存在: {self.batch_root_dir}")
        
        # 检查参数范围
        if self.num_processes < 1:
            errors.append("并行进程数必须大于0")
        
        if self.batch_size < 1:
            errors.append("批处理大小必须大于0")
        
        if not 0 < self.zero_ratio_threshold < 1:
            errors.append("零值比例阈值必须在0和1之间")
        
        if not 0 < self.high_zero_ratio_threshold < 1:
            errors.append("极高零值比例阈值必须在0和1之间")
        
        if self.zero_ratio_threshold >= self.high_zero_ratio_threshold:
            errors.append("零值比例阈值必须小于极高零值比例阈值")
        
        # 如果有错误，打印并返回False
        if errors:
            print("配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        return True
    
    def to_dict(self) -> dict:
        """
        将配置转换为字典格式
        
        Returns:
            dict: 配置字典
        """
        return {
            field.name: getattr(self, field.name)
            for field in self.__dataclass_fields__.values()
        }
    
    @classmethod
    def from_dict(cls, config_dict: dict) -> 'Config':
        """
        从字典创建配置对象
        
        Args:
            config_dict: 配置字典
            
        Returns:
            Config: 配置对象
        """
        return cls(**config_dict)
    
    def save_to_file(self, file_path: str):
        """
        将配置保存到文件
        
        Args:
            file_path: 保存路径
        """
        import json
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load_from_file(cls, file_path: str) -> 'Config':
        """
        从文件加载配置
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            Config: 配置对象
        """
        import json
        
        with open(file_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        return cls.from_dict(config_dict)


# 创建默认配置实例
default_config = Config()

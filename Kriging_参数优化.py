# -*- coding: utf-8 -*-
"""
降雨空间插值 - 基于克里金法的点雨量转面雨量
作者: 水文学教授
功能: 实现克里金法空间插值，将点雨量数据转换为面雨量数据
"""

# 导入所需的库
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay
from scipy.interpolate import griddata
from sklearn.metrics import mean_absolute_error, mean_squared_error
import concurrent.futures
import time
import datetime
import re
import warnings
import logging
from tqdm import tqdm
import math
import random
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 忽略警告
warnings.filterwarnings('ignore')

# 定义SCE-UA优化算法
class SCEUA:
    """
    SCE-UA全局优化算法类 (改进版)
    """
    def __init__(self, obj_func, bounds, max_iter=1000, pop_size=100, num_complexes=2, points_per_complex=50):
        self.obj_func = obj_func
        self.bounds = bounds
        self.dim = len(bounds)
        self.max_iter = max_iter
        self.pop_size = max(pop_size, num_complexes * points_per_complex)
        self.num_complexes = num_complexes
        self.points_per_complex = points_per_complex
        self.m = max(2 * self.dim + 1, self.dim + 2)
        self.evolution_steps = max(10, self.max_iter // 10)
        
        logger.info(f"SCE-UA参数: 维度={self.dim}, 种群大小={self.pop_size}, 复合体数量={self.num_complexes}")
        logger.info(f"参数边界: {self.bounds}")
    
    def _init_population(self):
        """初始化种群"""
        population = np.zeros((self.pop_size, self.dim))
        
        # 在每个维度上随机生成初始值
        for i in range(self.dim):
            population[:, i] = np.random.uniform(
                self.bounds[i][0], self.bounds[i][1], self.pop_size)
        
        return population
    
    def _evaluate_population(self, population):
        """评估种群中每个个体的适应度，增加错误处理"""
        fitness = np.full(population.shape[0], float('inf'))
        
        for i in range(population.shape[0]):
            try:
                fitness[i] = self.obj_func(population[i])
                # 验证结果是否为有效数值
                if not np.isfinite(fitness[i]):
                    logger.warning(f"个体 {i} 评估结果非有限值: {fitness[i]}")
                    fitness[i] = float('inf')
            except Exception as e:
                logger.warning(f"个体 {i} 评估失败: {str(e)}")
                fitness[i] = float('inf')
        
        # 检查是否所有评估都失败
        if np.all(np.isinf(fitness)):
            logger.error("所有个体评估均失败！检查目标函数和参数边界")
        
        return fitness
    
    def _evolve_complex(self, complex_points, complex_fitness):
        """进化单个复合体（修复版）"""
        points = complex_points.copy()
        fitness = complex_fitness.copy()
        
        for _ in range(self.evolution_steps):
            # 计算选择权重（三角分布）
            weights = np.array([
                (2*(self.points_per_complex-i)) / (self.points_per_complex*(self.points_per_complex+1)) 
                for i in range(self.points_per_complex)
            ])
            
            # 选择子复合体
            try:
                idx = np.random.choice(
                    self.points_per_complex, size=self.m, p=weights, replace=False)
                sub_points = points[idx]
                sub_fitness = fitness[idx]
                
                # 找出最差点的索引
                worst_idx = np.argmax(sub_fitness)
                worst_point = sub_points[worst_idx]
                
                # 计算质心（排除最差点）
                mask = np.ones(len(sub_points), dtype=bool)
                mask[worst_idx] = False
                centroid = np.mean(sub_points[mask], axis=0)
                
                # 反射
                alpha = 1.0  # 反射系数
                reflected_point = centroid + alpha * (centroid - worst_point)
                
                # 约束到边界内
                for i in range(self.dim):
                    reflected_point[i] = max(
                        self.bounds[i][0], min(reflected_point[i], self.bounds[i][1]))
                
                # 评估反射点
                try:
                    reflected_fitness = self.obj_func(reflected_point)
                    if not np.isfinite(reflected_fitness):
                        reflected_fitness = float('inf')
                except Exception:
                    reflected_fitness = float('inf')
                
                # 如果反射比最差点好，接受
                if reflected_fitness < sub_fitness[worst_idx]:
                    sub_points[worst_idx] = reflected_point
                    sub_fitness[worst_idx] = reflected_fitness
                else:
                    # 否则，收缩
                    beta = 0.5  # 收缩系数
                    contracted_point = centroid + beta * (worst_point - centroid)
                    
                    try:
                        contracted_fitness = self.obj_func(contracted_point)
                        if not np.isfinite(contracted_fitness):
                            contracted_fitness = float('inf')
                    except Exception:
                        contracted_fitness = float('inf')
                    
                    # 如果收缩比最差点好，接受
                    if contracted_fitness < sub_fitness[worst_idx]:
                        sub_points[worst_idx] = contracted_point
                        sub_fitness[worst_idx] = contracted_fitness
                    else:
                        # 否则，随机替换
                        new_point = np.zeros(self.dim)
                        for i in range(self.dim):
                            new_point[i] = np.random.uniform(
                                self.bounds[i][0], self.bounds[i][1])
                        
                        try:
                            new_fitness = self.obj_func(new_point)
                            if not np.isfinite(new_fitness):
                                new_fitness = float('inf')
                        except Exception:
                            new_fitness = float('inf')
                        
                        sub_points[worst_idx] = new_point
                        sub_fitness[worst_idx] = new_fitness
                
                # 更新子复合体到原复合体
                points[idx] = sub_points
                fitness[idx] = sub_fitness
                
                # 重新排序
                sort_idx = np.argsort(fitness)
                points = points[sort_idx]
                fitness = fitness[sort_idx]
                
            except Exception as e:
                logger.warning(f"复合体进化失败: {str(e)}")
        
        return points, fitness
    
    def optimize(self):
        """运行SCE-UA优化（增强版）"""
        try:
            # 初始化种群
            population = self._init_population()
            
            # 评估初始种群
            fitness = self._evaluate_population(population)
            
            # 检查是否存在有效解
            if np.all(np.isinf(fitness)):
                logger.error("初始种群中没有有效解！返回最佳猜测")
                # 返回边界中点作为默认值
                default_point = np.array([
                    (self.bounds[i][0] + self.bounds[i][1]) / 2.0 
                    for i in range(self.dim)
                ])
                return default_point, float('inf')
            
            # 按适应度排序
            population, fitness = self._sort_population(population, fitness)
            
            # 记录最佳解
            best_point = population[0].copy()
            best_fitness = fitness[0]
            logger.info(f"初始最佳适应度: {best_fitness:.6f}")
            
            # 记录没有改进的迭代次数
            no_improvement = 0
            
            # 迭代优化
            for iteration in range(self.max_iter):
                # 将种群分成复合体
                complexes = self._partition_complexes(population, fitness)
                
                # 独立进化每个复合体
                evolved_complexes = []
                for complex_points, complex_fitness in complexes:
                    evolved_points, evolved_fitness = self._evolve_complex(
                        complex_points, complex_fitness)
                    evolved_complexes.append((evolved_points, evolved_fitness))
                
                # 重新组合复合体
                new_population = np.zeros_like(population)
                new_fitness = np.zeros_like(fitness)
                
                for k in range(self.num_complexes):
                    idx = np.array([
                        k + j * self.num_complexes 
                        for j in range(self.points_per_complex)
                    ])
                    new_population[idx] = evolved_complexes[k][0]
                    new_fitness[idx] = evolved_complexes[k][1]
                
                # 重新排序
                population, fitness = self._sort_population(new_population, new_fitness)
                
                # 更新最佳解
                if fitness[0] < best_fitness:
                    best_point = population[0].copy()
                    best_fitness = fitness[0]
                    logger.info(f"迭代 {iteration+1}/{self.max_iter}, 新的最佳适应度: {best_fitness:.6f}")
                    no_improvement = 0
                else:
                    no_improvement += 1
                
                # 每10次迭代输出一次日志
                if iteration % 10 == 0:
                    logger.info(f"迭代 {iteration+1}/{self.max_iter}, 当前最佳适应度: {best_fitness:.6f}")
                
                # 如果长时间没有改进，提前终止
                if no_improvement >= 20:
                    logger.info(f"连续20次迭代没有改进，提前终止")
                    break
            
            return best_point, best_fitness
            
        except Exception as e:
            logger.error(f"SCE-UA优化失败: {str(e)}")
            # 返回一个合理的默认值
            default_point = np.array([
                (self.bounds[i][0] + self.bounds[i][1]) / 2.0 
                for i in range(self.dim)
            ])
            return default_point, float('inf')

# 定义克里金插值类
class OrdinaryKriging:
    """
    普通克里金插值类
    实现了基本的克里金插值算法，包括变异函数拟合和插值预测
    """
    def __init__(self, coords, values, variogram_model='spherical', nlags=20, weight=False):
        """
        普通克里金插值初始化
        
        参数:
        coords: 坐标点, shape = (n, 2) 为经纬度坐标
        values: 观测值, shape = (n,)
        variogram_model: 变异函数模型，可选'spherical', 'exponential', 'gaussian'
        nlags: 计算变异函数的距离类别数
        weight: 是否使用权重
        """
        # 存储坐标点
        self.coords = np.array(coords)
        # 存储观测值
        self.values = np.array(values)
        # 变异函数模型
        self.variogram_model = variogram_model
        # 距离类别数
        self.nlags = nlags
        # 是否使用权重
        self.weight = weight
        
        # 坐标点数量
        self.n = coords.shape[0]
        
        # 拟合变异函数参数
        self.variogram_parameters = self._fit_variogram()
        
    def _distance_matrix(self):
        """
        计算坐标点间的距离矩阵
        
        返回:
        dm: 距离矩阵，形状为 (n, n)
        """
        # 初始化距离矩阵
        dm = np.zeros((self.n, self.n))
        
        # 计算每对点之间的欧氏距离
        for i in range(self.n):
            for j in range(i+1, self.n):
                # 计算欧氏距离
                dist = np.sqrt(np.sum((self.coords[i] - self.coords[j])**2))
                # 存储距离（矩阵是对称的）
                dm[i, j] = dist
                dm[j, i] = dist
        
        return dm
    
    def _experimental_variogram(self):
        """
        计算试验变异函数
        
        返回:
        lags: 距离间隔中心
        gamma: 对应的变异函数值
        count: 每个间隔的样本数
        """
        # 计算距离矩阵
        d = self._distance_matrix()
        
        # 距离的最大值
        max_dist = np.max(d)
        
        # 距离间隔
        lag_width = max_dist / self.nlags
        
        # 定义距离间隔中心
        lags = np.linspace(lag_width/2, max_dist-lag_width/2, self.nlags)
        
        # 初始化试验变异函数
        gamma = np.zeros(self.nlags)
        
        # 初始化每个距离间隔中的样本数
        count = np.zeros(self.nlags)
        
        # 计算每个距离间隔的变异函数值
        for i in range(self.n):
            for j in range(i+1, self.n):
                # 找到当前距离属于哪个间隔
                lag_idx = min(int(d[i, j] / lag_width), self.nlags-1)
                
                # 计算变异函数贡献
                gamma[lag_idx] += 0.5 * (self.values[i] - self.values[j])**2
                count[lag_idx] += 1
        
        # 计算平均值
        for i in range(self.nlags):
            if count[i] > 0:
                gamma[i] /= count[i]
        
        return lags, gamma, count
    
    def _variogram_model_function(self, h, nugget, sill, range_param):
        """
        变异函数模型
        
        参数:
        h: 距离
        nugget: 块金值
        sill: 基台值
        range_param: 变程
        
        返回:
        result: 变异函数值
        """
        # 计算有效距离（避免零距离）
        h = np.maximum(h, 1e-10)
        
        if self.variogram_model == 'spherical':
            # 球形模型
            result = np.zeros_like(h)
            # h <= range_param
            idx = h <= range_param
            result[idx] = nugget + (sill - nugget) * (1.5 * (h[idx] / range_param) - 0.5 * (h[idx] / range_param)**3)
            # h > range_param
            idx = h > range_param
            result[idx] = nugget + (sill - nugget)
            return result
            
        elif self.variogram_model == 'exponential':
            # 指数模型
            return nugget + (sill - nugget) * (1 - np.exp(-3 * h / range_param))
            
        elif self.variogram_model == 'gaussian':
            # 高斯模型
            return nugget + (sill - nugget) * (1 - np.exp(-3 * (h / range_param)**2))
            
        else:
            raise ValueError(f"不支持的变异函数模型: '{self.variogram_model}'")
    
    def _fit_variogram(self):
        """
        拟合变异函数模型 (增强版)
        """
        # 计算试验变异函数
        lags, gamma, count = self._experimental_variogram()
        
        # 记录变异函数数据
        logger.debug(f"试验变异函数: {len(lags)} 个有效距离间隔")
        logger.debug(f"距离范围: {np.min(lags) if len(lags) > 0 else 'N/A'} - {np.max(lags) if len(lags) > 0 else 'N/A'}")
        logger.debug(f"变异值范围: {np.min(gamma) if len(gamma) > 0 else 'N/A'} - {np.max(gamma) if len(gamma) > 0 else 'N/A'}")
        
        # 过滤掉样本数为0的点
        valid_idx = count > 0
        lags = lags[valid_idx]
        gamma = gamma[valid_idx]
        
        # 如果没有有效数据，使用默认参数
        if len(lags) == 0:
            logger.warning("没有有效的变异函数数据，使用默认参数")
            return {
                'nugget': 0, 
                'sill': np.var(self.values) if len(self.values) > 1 else 1.0, 
                'range': np.max(self._distance_matrix())/3 if self.n > 1 else 1.0
            }
            
        
        # 初始参数估计
        nugget_init = np.min(gamma) * 0.1  # 块金值初始估计为最小变异值的10%
        sill_init = np.max(gamma)  # 基台值初始估计为最大变异值
        range_init = np.max(lags) * 0.3  # 变程初始估计为最大距离的30%
        
        # 定义目标函数（平方误差和）
        def objective_function(params):
            nugget, sill, range_param = params
            # 确保参数有效
            if nugget < 0 or sill <= nugget or range_param <= 0:
                return 1e10  # 惩罚无效参数
            
            # 计算模型预测的变异函数值
            model_gamma = self._variogram_model_function(lags, nugget, sill, range_param)
            
            # 计算加权平方误差
            if self.weight:
                weights = count[valid_idx]
                return np.sum(weights * (gamma - model_gamma)**2) / np.sum(weights)
            else:
                return np.mean((gamma - model_gamma)**2)
        
        # 参数边界
        bounds = [
            (0, np.max(gamma) * 0.5),  # nugget bounds
            (np.min(gamma), np.max(gamma) * 2),  # sill bounds
            (np.min(lags), np.max(lags) * 2)  # range bounds
        ]
        
        # 使用SCE-UA优化
        optimizer = SCEUA(objective_function, bounds, max_iter=50, pop_size=30)
        best_params, _ = optimizer.optimize()
        
        nugget, sill, range_param = best_params
        
        # 返回拟合的参数
        return {'nugget': nugget, 'sill': sill, 'range': range_param}
    
    def _kriging_matrix(self, coords_to_estimate):
        """
        构建克里金方程组
        
        参数:
        coords_to_estimate: 待估计的坐标点
        
        返回:
        A: 克里金方程系数矩阵
        b: 右侧向量
        """
        n_est = len(coords_to_estimate)
        n_obs = self.n
        
        # 计算观测点之间的距离
        d_obs = self._distance_matrix()
        
        # 计算观测点与待估计点之间的距离
        d_est = np.zeros((n_obs, n_est))
        for i in range(n_obs):
            for j in range(n_est):
                d_est[i, j] = np.sqrt(np.sum((self.coords[i] - coords_to_estimate[j])**2))
        
        # 构建克里金方程系数矩阵A
        A = np.zeros((n_obs + 1, n_obs + 1))
        
        # 填充观测点之间的变异函数值
        for i in range(n_obs):
            for j in range(n_obs):
                A[i, j] = self._variogram_model_function(
                    d_obs[i, j],
                    self.variogram_parameters['nugget'],
                    self.variogram_parameters['sill'],
                    self.variogram_parameters['range']
                )
        
        # 最后一行和最后一列填1（拉格朗日乘子）
        A[n_obs, :n_obs] = 1
        A[:n_obs, n_obs] = 1
        A[n_obs, n_obs] = 0  # 拉格朗日乘子对应位置为0
        
        # 构建右侧向量b，对每个估计点都有一个b
        b = np.zeros((n_est, n_obs + 1))
        
        # 填充观测点与待估计点之间的变异函数值
        for j in range(n_est):
            for i in range(n_obs):
                b[j, i] = self._variogram_model_function(
                    d_est[i, j],
                    self.variogram_parameters['nugget'],
                    self.variogram_parameters['sill'],
                    self.variogram_parameters['range']
                )
            # 拉格朗日乘子对应位置为1
            b[j, n_obs] = 1
        
        return A, b
    
    def estimate(self, coords_to_estimate):
        """
        在给定坐标点上进行克里金估计
        
        参数:
        coords_to_estimate: 待估计的坐标点，shape = (m, 2)
        
        返回:
        estimates: 估计值，shape = (m,)
        variances: 估计方差，shape = (m,)
        """
        # 确保坐标是二维数组
        coords_to_estimate = np.atleast_2d(coords_to_estimate)
        n_est = coords_to_estimate.shape[0]
        
        # 初始化结果
        estimates = np.zeros(n_est)
        variances = np.zeros(n_est)
        
        # 构建克里金方程
        A, b = self._kriging_matrix(coords_to_estimate)
        
        # 计算权重和估计
        try:
            # 求解线性方程组，得到权重
            weights = np.linalg.solve(A, b.T)
            
            # 计算估计值
            for i in range(n_est):
                estimates[i] = np.sum(weights[:-1, i] * self.values)
                
                # 计算克里金方差
                variances[i] = np.sum(weights[:-1, i] * b[i, :-1]) + weights[-1, i]
                
                # 确保估计值非负（降雨量不能为负）
                estimates[i] = max(0, estimates[i])
                
        except np.linalg.LinAlgError:
            logger.error("求解克里金方程失败，使用反距离权重插值作为后备方法")
            
            # 回退到反距离权重插值
            for i in range(n_est):
                # 计算到所有观测点的距离
                dists = np.sqrt(np.sum((self.coords - coords_to_estimate[i])**2, axis=1))
                # 避免零距离
                dists = np.maximum(dists, 1e-10)
                # 计算权重
                weights = 1.0 / (dists**2)
                weights = weights / np.sum(weights)
                # 计算加权平均
                estimates[i] = np.sum(weights * self.values)
                
                # 确保估计值非负
                estimates[i] = max(0, estimates[i])
                
                # 设置一个默认的估计方差
                variances[i] = np.var(self.values) * 0.5 if len(self.values) > 1 else 1.0
        
        return estimates, variances

# 定义Delaunay三角网站点筛选函数
def save_as_asc(data, geo_transform, output_path, nodata_value=-9999):
    """
    将栅格数据保存为ASC格式文件
    
    参数:
    data: 栅格数据，2D numpy数组
    geo_transform: 地理变换参数 (xllcorner, cellsize, 0, yllcorner, 0, -cellsize)
    output_path: 输出文件路径
    nodata_value: 无数据值
    """
    # 提取地理信息
    xllcorner = geo_transform[0]
    cellsize = geo_transform[1]
    yllcorner = geo_transform[3] - data.shape[0] * abs(geo_transform[5])
    
    # 获取栅格尺寸
    nrows = data.shape[0]
    ncols = data.shape[1]
    
    # 写入ASC文件
    with open(output_path, 'w') as f:
        f.write(f"ncols        {ncols}\n")
        f.write(f"nrows        {nrows}\n")
        f.write(f"xllcorner    {xllcorner}\n")
        f.write(f"yllcorner    {yllcorner}\n")
        f.write(f"cellsize     {cellsize}\n")
        f.write(f"NODATA_value {nodata_value}\n")
        
        # 写入数据（从上到下，从左到右）
        for i in range(nrows):
            row_values = []
            for j in range(ncols):
                if np.isnan(data[i, j]) or data[i, j] == nodata_value:
                    row_values.append(str(nodata_value))
                else:
                    row_values.append(f"{data[i, j]:.6f}")
            f.write(" ".join(row_values) + "\n")
def interpolate_to_grid(kriging_model, mask, geo_transform, chunk_size=100):
    """
    使用克里金模型对整个栅格进行插值
    
    参数:
    kriging_model: 已经拟合的克里金模型
    mask: 掩码数组，表示哪些单元格需要插值
    geo_transform: 地理变换参数
    chunk_size: 分块处理的大小，用于降低内存使用
    
    返回:
    grid: 插值结果栅格
    """
    # 获取栅格尺寸
    nrows, ncols = mask.shape
    
    # 创建结果栅格，初始化为无数据值
    grid = np.full((nrows, ncols), -9999.0, dtype=np.float32)
    
    # 只对掩码内的单元格进行插值
    valid_cells = np.where(mask > 0)
    total_cells = len(valid_cells[0])
    
    logger.info(f"开始对 {total_cells} 个有效栅格单元进行插值")
    
    # 分块处理以减少内存使用
    for start_idx in range(0, total_cells, chunk_size):
        end_idx = min(start_idx + chunk_size, total_cells)
        chunk_rows = valid_cells[0][start_idx:end_idx]
        chunk_cols = valid_cells[1][start_idx:end_idx]
        
        # 计算经纬度坐标
        coords = []
        for i, j in zip(chunk_rows, chunk_cols):
            # 计算单元格中心点的经纬度
            x = geo_transform[0] + (j + 0.5) * geo_transform[1]
            y = geo_transform[3] + (i + 0.5) * geo_transform[5]
            coords.append([x, y])
        
        # 进行克里金插值
        if coords:
            try:
                estimates, _ = kriging_model.estimate(coords)
                
                # 将结果存入栅格
                for idx, (i, j) in enumerate(zip(chunk_rows, chunk_cols)):
                    grid[i, j] = max(0, estimates[idx])  # 确保非负
            except Exception as e:
                logger.warning(f"栅格插值出错: {e}")
    
    return grid

def select_stations_by_delaunay(target_station, stations_df, max_neighbors=10):
    """
    使用Delaunay三角网筛选目标站点的邻近站点
    
    参数:
    target_station: 目标站点ID
    stations_df: 包含站点坐标的DataFrame，必须包含'经度','纬度','站点'列
    max_neighbors: 最大邻居数量
    
    返回:
    selected_stations: 筛选后的站点DataFrame
    """
    # 确保stations_df包含必要的列
    required_cols = ['经度', '纬度', '站点']
    for col in required_cols:
        if col not in stations_df.columns:
            raise ValueError(f"站点数据缺少必要的列: {col}")
    
    # 获取目标站点信息
    target_info = stations_df[stations_df['站点'] == target_station]
    if len(target_info) == 0:
        raise ValueError(f"未找到目标站点: {target_station}")
    
    # 获取目标站点的经纬度
    target_lon = target_info['经度'].values[0]
    target_lat = target_info['纬度'].values[0]
    
    # 如果站点数量太少，直接返回所有站点
    if len(stations_df) <= max_neighbors + 1:
        return stations_df
    
    # 提取所有站点坐标
    all_stations = stations_df.copy()
    coords = all_stations[['经度', '纬度']].values
    
    try:
        # 创建Delaunay三角网
        tri = Delaunay(coords)
        
        # 找到目标点在三角网中的索引
        target_idx = all_stations[all_stations['站点'] == target_station].index[0]
        
        # 找到与目标点共享三角形的所有点
        neighbors = set()
        for simplex in tri.simplices:
            if target_idx in simplex:
                for idx in simplex:
                    if idx != target_idx:
                        neighbors.add(idx)
        
        # 如果找到的邻居太少，使用距离作为补充
        if len(neighbors) < max_neighbors:
            # 计算所有点到目标点的距离
            target_coord = coords[target_idx]
            distances = np.sqrt(np.sum((coords - target_coord)**2, axis=1))
            
            # 创建距离与索引的对应关系
            dist_idx = [(d, i) for i, d in enumerate(distances) if i != target_idx and i not in neighbors]
            dist_idx.sort()  # 按距离排序
            
            # 添加最近的点直到达到max_neighbors
            additional_neighbors = [i for d, i in dist_idx[:max_neighbors - len(neighbors)]]
            neighbors.update(additional_neighbors)
        
        # 如果找到的邻居太多，保留最近的max_neighbors个
        if len(neighbors) > max_neighbors:
            target_coord = coords[target_idx]
            neighbor_dists = [(np.sqrt(np.sum((coords[i] - target_coord)**2)), i) for i in neighbors]
            neighbor_dists.sort()  # 按距离排序
            neighbors = {i for d, i in neighbor_dists[:max_neighbors]}
        
        # 添加目标站点自身
        neighbors.add(target_idx)
        
        # 返回筛选后的站点
        selected_stations = all_stations.iloc[list(neighbors)].copy()
        return selected_stations
    
    except Exception as e:
        logger.warning(f"Delaunay三角网构建失败，使用距离作为替代: {e}")
        
        # 计算所有站点到目标站点的距离
        all_stations['distance'] = np.sqrt(
            (all_stations['经度'] - target_lon)**2 + 
            (all_stations['纬度'] - target_lat)**2
        )
        
        # 按距离排序并选择最近的max_neighbors个站点
        selected_stations = all_stations.sort_values('distance').head(max_neighbors + 1).copy()
        # 删除临时距离列
        selected_stations.drop('distance', axis=1, inplace=True)
        
        return selected_stations

# 定义评价指标函数
def calculate_metrics(observed, predicted):
    """
    计算评价指标：MAE, MRE, RMSE
    
    参数:
    observed: 观测值数组
    predicted: 预测值数组
    
    返回:
    metrics: 包含MAE、MRE、RMSE的字典
    """
    # 平均绝对误差 (Mean Absolute Error)
    mae = np.mean(np.abs(observed - predicted))
    
    # 平均相对误差 (Mean Relative Error)
    # 避免除以零
    non_zero_observed = observed != 0
    if np.any(non_zero_observed):
        mre = np.mean(np.abs((observed[non_zero_observed] - predicted[non_zero_observed]) / observed[non_zero_observed]))
    else:
        mre = 0.0
    
    # 均方根误差 (Root Mean Square Error)
    rmse = np.sqrt(np.mean((observed - predicted)**2))
    
    return {
        'MAE': mae,
        'MRE': mre,
        'RMSE': rmse
    }

# 定义掩码文件读取函数
def read_mask_file(mask_path):
    """
    读取ASC格式的掩码文件
    
    参数:
    mask_path: 掩码文件路径
    
    返回:
    mask: 掩码数组
    geo_transform: 地理变换参数
    """
    # 读取头信息
    header = {}
    data = []
    
    with open(mask_path, 'r') as f:
        for i in range(6):  # ASC格式通常有6行头信息
            line = f.readline().strip().split()
            header[line[0]] = float(line[1])
        
        # 读取数据部分
        for line in f:
            row = [float(val) for val in line.strip().split()]
            data.append(row)
    
    # 转换为numpy数组
    mask = np.array(data)
    
    # 构建地理变换参数（左上角坐标，像元大小）
    geo_transform = (
        header['xllcorner'],  # 左下角x坐标
        header['cellsize'],   # x方向分辨率
        0,                    # 旋转参数，通常为0
        header['yllcorner'] + header['nrows'] * header['cellsize'],  # 左上角y坐标
        0,                    # 旋转参数，通常为0
        -header['cellsize']   # y方向分辨率（负值）
    )
    
    return mask, geo_transform

# 定义经纬度转掩码索引函数
def coords_to_indices(lon, lat, geo_transform):
    """
    将经纬度坐标转换为掩码数组的索引
    
    参数:
    lon: 经度
    lat: 纬度
    geo_transform: 地理变换参数
    
    返回:
    row, col: 行列索引
    """
    x_origin = geo_transform[0]
    y_origin = geo_transform[3]
    x_res = geo_transform[1]
    y_res = geo_transform[5]
    
    # 计算列索引
    col = int((lon - x_origin) / x_res)
    # 计算行索引
    row = int((lat - y_origin) / y_res)
    
    return row, col

# 定义留一法验证函数
def leave_one_out_validation(time_step, stations_df, rain_data, kriging_params):
    """
    使用留一法验证克里金插值效果
    
    参数:
    time_step: 时间步
    stations_df: 站点信息DataFrame
    rain_data: 包含各站点降雨数据的字典
    kriging_params: 克里金参数字典
    
    返回:
    validation_results: 验证结果DataFrame
    """
    # 初始化结果列表
    results = []
    
    # 遍历所有站点进行留一验证
    for station_id in stations_df['站点'].unique():
        # 提取当前时刻所有站点的降雨量
        rainfall_values = {}
        for sid in stations_df['站点'].unique():
            if sid in rain_data and time_step in rain_data[sid].index:
                rainfall_values[sid] = rain_data[sid].loc[time_step, '雨量']
            else:
                rainfall_values[sid] = 0.0
        
        # 跳过没有数据的时间步
        if len(rainfall_values) == 0:
            continue
        
        # 实际观测值
        observed = rainfall_values.get(station_id, 0.0)
        
        # 去除当前站点，用其他站点进行插值
        training_stations = stations_df[stations_df['站点'] != station_id].copy()
        
        # 如果站点太少，跳过
        if len(training_stations) < 3:
            continue
        
        # 准备插值数据
        coords = []
        values = []
        
        for idx, row in training_stations.iterrows():
            sid = row['站点']
            if sid in rainfall_values:
                coords.append([row['经度'], row['纬度']])
                values.append(rainfall_values[sid])
        
        # 如果有效数据太少，跳过
        if len(coords) < 3:
            continue
        
        # 获取目标站点坐标
        target_station = stations_df[stations_df['站点'] == station_id]
        target_lon = target_station['经度'].values[0]
        target_lat = target_station['纬度'].values[0]
        
        try:
            # 使用克里金插值估计目标站点的值
            ok = OrdinaryKriging(
                coords, 
                values, 
                variogram_model=kriging_params.get('variogram_model', 'spherical'),
                nlags=kriging_params.get('nlags', 20),
                weight=kriging_params.get('weight', False)
            )
            
            # 估计目标站点的值
            estimated, variance = ok.estimate([[target_lon, target_lat]])
            predicted = estimated[0]
            
            # 记录结果
            results.append({
                '时间': time_step,
                '站点': station_id,
                '观测值': observed,
                '预测值': predicted,
                '方差': variance[0]
            })
            
        except Exception as e:
            logger.warning(f"站点 {station_id} 在时间 {time_step} 的留一验证失败: {e}")
    
    # 转换为DataFrame
    if results:
        return pd.DataFrame(results)
    else:
        return pd.DataFrame(columns=['时间', '站点', '观测值', '预测值', '方差'])

# 定义参数优化函数
def optimize_kriging_parameters(stations_df, rain_data, sample_times=10):
    """
    优化克里金插值参数
    
    参数:
    stations_df: 站点信息DataFrame
    rain_data: 包含各站点降雨数据的字典
    sample_times: 用于优化的时间步样本数
    
    返回:
    best_params: 最优参数字典
    metrics: 评价指标
    """
    # 获取所有可用的时间步
    all_times = set()
    for station_id, data in rain_data.items():
        all_times.update(data.index)
    
    all_times = sorted(list(all_times))
    logger.info(f"共有 {len(all_times)} 个时间步可用于参数优化")
    
    # 如果时间步太多，随机抽样
    if len(all_times) > sample_times:
        sample_times = min(sample_times, len(all_times))
        sampled_times = random.sample(all_times, sample_times)
    else:
        sampled_times = all_times
    
    logger.info(f"使用 {len(sampled_times)} 个时间步进行参数优化")
    
    # 定义优化目标函数
    def objective_function(params):
        # 解包参数
        nugget_factor = params[0]  # 块金系数（相对于方差的比例）
        range_factor = params[1]   # 变程系数（相对于最大距离的比例）
        model_idx = int(params[2]) # 变异函数模型索引
        
        # 变异函数模型列表
        models = ['spherical', 'exponential', 'gaussian']
        model = models[min(model_idx, len(models)-1)]
        
        # 累计评价指标
        total_mae = 0
        total_rmse = 0
        total_count = 0
        
        # 对抽样的时间步进行评估
        for time_step in sampled_times:
            # 提取当前时刻所有站点的降雨量
            rainfall_values = {}
            for sid in stations_df['站点'].unique():
                if sid in rain_data and time_step in rain_data[sid].index:
                    rainfall_values[sid] = rain_data[sid].loc[time_step, '雨量']
                else:
                    rainfall_values[sid] = 0.0
            
            # 跳过没有数据的时间步
            if len(rainfall_values) == 0:
                continue
            
            # 进行留一验证
            all_observed = []
            all_predicted = []
            
            for station_id in stations_df['站点'].unique():
                # 实际观测值
                observed = rainfall_values.get(station_id, 0.0)
                
                # 去除当前站点，用其他站点进行插值
                training_stations = stations_df[stations_df['站点'] != station_id].copy()
                
                # 如果站点太少，跳过
                if len(training_stations) < 3:
                    continue
                
                # 准备插值数据
                coords = []
                values = []
                
                for idx, row in training_stations.iterrows():
                    sid = row['站点']
                    if sid in rainfall_values:
                        coords.append([row['经度'], row['纬度']])
                        values.append(rainfall_values[sid])
                
                # 如果有效数据太少，跳过
                if len(coords) < 3:
                    continue
                
                # 获取目标站点坐标
                target_station = stations_df[stations_df['站点'] == station_id]
                target_lon = target_station['经度'].values[0]
                target_lat = target_station['纬度'].values[0]
                
                try:
                    # 计算距离特征
                    distances = []
                    for c in coords:
                        dist = np.sqrt((c[0] - target_lon)**2 + (c[1] - target_lat)**2)
                        distances.append(dist)
                    
                    # 获取最大距离和数据方差
                    max_dist = max(distances) if distances else 1.0
                    data_var = np.var(values) if len(values) > 1 else 1.0
                    
                    # 设置变异函数参数
                    nugget = nugget_factor * data_var
                    sill = data_var
                    range_param = range_factor * max_dist
                    
                    # 手动设置变异函数参数
                    variogram_params = {
                        'nugget': nugget,
                        'sill': sill,
                        'range': range_param
                    }
                    
                    # 创建克里金对象并估计
                    ok = OrdinaryKriging(coords, values, variogram_model=model)
                    ok.variogram_parameters = variogram_params
                    
                    # 估计目标站点的值
                    estimated, _ = ok.estimate([[target_lon, target_lat]])
                    predicted = max(0, estimated[0])  # 确保非负
                    
                    # 记录观测值和预测值
                    all_observed.append(observed)
                    all_predicted.append(predicted)
                    
                except Exception as e:
                    logger.debug(f"优化过程中站点 {station_id} 在时间 {time_step} 的插值失败: {str(e)}")
            
            # 计算当前时间步的评价指标
            if all_observed and all_predicted:
                metrics = calculate_metrics(np.array(all_observed), np.array(all_predicted))
                total_mae += metrics['MAE']
                total_rmse += metrics['RMSE']
                total_count += 1
        
        # 如果没有成功评估任何时间步，返回一个大值
        if total_count == 0:
            return 1e10
        
        # 返回平均MAE作为优化目标
        avg_mae = total_mae / total_count
        return avg_mae
    
    # 定义参数边界
    bounds = [
        (0.0, 0.5),     # nugget_factor: 0-50% of variance
        (0.1, 2.0),     # range_factor: 10-200% of max distance
        (0, 2.99)       # model_idx: 0, 1, 2 (spherical, exponential, gaussian)
    ]
    
    # 使用SCE-UA优化
    logger.info("开始使用SCE-UA优化克里金参数")
    optimizer = SCEUA(objective_function, bounds, max_iter=50, pop_size=30)
    best_params, best_value = optimizer.optimize()
    
    # 解释最优参数
    nugget_factor, range_factor, model_idx = best_params
    models = ['spherical', 'exponential', 'gaussian']
    model = models[min(int(model_idx), len(models)-1)]
    
    # 创建参数字典
    params = {
        'nugget_factor': nugget_factor,
        'range_factor': range_factor,
        'variogram_model': model,
        'nlags': 20,
        'weight': True
    }
    
    logger.info(f"优化的克里金参数: {params}, 最佳MAE: {best_value:.6f}")
    
    # 使用最优参数进行全面评估
    total_metrics = {
        'MAE': 0,
        'MRE': 0,
        'RMSE': 0
    }
    count = 0
    
    # 对部分时间步进行验证
    validation_times = random.sample(all_times, min(20, len(all_times)))
    logger.info(f"使用 {len(validation_times)} 个时间步进行交叉验证评估")
    
    for time_step in validation_times:
        validation_df = leave_one_out_validation(time_step, stations_df, rain_data, params)
        if not validation_df.empty:
            metrics = calculate_metrics(
                validation_df['观测值'].values,
                validation_df['预测值'].values
            )
            for key in total_metrics:
                total_metrics[key] += metrics[key]
            count += 1
    
    # 计算平均指标
    if count > 0:
        for key in total_metrics:
            total_metrics[key] /= count
    
    logger.info(f"交叉验证结果: MAE={total_metrics['MAE']:.4f}, MRE={total_metrics['MRE']:.4f}, RMSE={total_metrics['RMSE']:.4f}")
    
    return params, total_metrics
def optimize_kriging_parameters(stations_df, rain_data, sample_times=10):
    """
    优化克里金插值参数（改进版）
    
    参数:
    stations_df: 站点信息DataFrame
    rain_data: 包含各站点降雨数据的字典
    sample_times: 用于优化的时间步样本数
    
    返回:
    best_params: 最优参数字典
    metrics: 评价指标
    """
    # 获取所有可用的时间步
    all_times = set()
    for station_id, data in rain_data.items():
        all_times.update(data.index)
    
    all_times = sorted(list(all_times))
    logger.info(f"共有 {len(all_times)} 个时间步可用于参数优化")
    
    # 如果时间步太多，随机抽样
    if len(all_times) > sample_times:
        # 确保样本中有降雨和无降雨的时间步
        rain_times = []
        no_rain_times = []
        
        for time_step in all_times:
            has_rain = False
            for station_id, data in rain_data.items():
                if time_step in data.index and data.loc[time_step, '雨量'] > 0:
                    has_rain = True
                    break
            
            if has_rain:
                rain_times.append(time_step)
            else:
                no_rain_times.append(time_step)
        
        # 平衡抽样
        rain_sample_size = min(int(sample_times * 0.7), len(rain_times))
        no_rain_sample_size = min(sample_times - rain_sample_size, len(no_rain_times))
        
        sampled_rain_times = random.sample(rain_times, rain_sample_size) if rain_times else []
        sampled_no_rain_times = random.sample(no_rain_times, no_rain_sample_size) if no_rain_times else []
        
        sampled_times = sampled_rain_times + sampled_no_rain_times
        random.shuffle(sampled_times)
    else:
        sampled_times = all_times
    
    logger.info(f"使用 {len(sampled_times)} 个时间步进行参数优化")
    
    # 定义优化目标函数
    def objective_function(params):
        try:
            # 解包参数
            nugget_factor = params[0]  # 块金系数（相对于方差的比例）
            range_factor = params[1]   # 变程系数（相对于最大距离的比例）
            model_idx = int(params[2]) # 变异函数模型索引
            
            # 变异函数模型列表
            models = ['spherical', 'exponential', 'gaussian']
            model = models[min(model_idx, len(models)-1)]
            
            # 累计评价指标
            total_mae = 0
            total_count = 0
            
            # 对抽样的时间步进行评估
            for time_step in sampled_times:
                # 提取当前时刻所有站点的降雨量
                rainfall_values = {}
                for sid in stations_df['站点'].unique():
                    if sid in rain_data and time_step in rain_data[sid].index:
                        rainfall_values[sid] = rain_data[sid].loc[time_step, '雨量']
                    else:
                        rainfall_values[sid] = 0.0
                
                # 跳过没有数据的时间步
                if not rainfall_values:
                    continue
                
                # 如果所有值都是0，跳过这个时间步
                if all(v == 0 for v in rainfall_values.values()):
                    continue
                
                # 进行留一验证
                all_observed = []
                all_predicted = []
                
                # 只测试一部分站点以加快速度
                test_stations = random.sample(
                    list(stations_df['站点'].unique()), 
                    min(10, len(stations_df['站点'].unique()))
                )
                
                for station_id in test_stations:
                    # 实际观测值
                    observed = rainfall_values.get(station_id, 0.0)
                    
                    # 去除当前站点，用其他站点进行插值
                    training_stations = stations_df[stations_df['站点'] != station_id].copy()
                    
                    # 如果站点太少，跳过
                    if len(training_stations) < 3:
                        continue
                    
                    # 准备插值数据
                    coords = []
                    values = []
                    
                    for idx, row in training_stations.iterrows():
                        sid = row['站点']
                        if sid in rainfall_values:
                            coords.append([row['经度'], row['纬度']])
                            values.append(rainfall_values[sid])
                    
                    # 如果有效数据太少，跳过
                    if len(coords) < 3:
                        continue
                    
                    # 获取目标站点坐标
                    target_station = stations_df[stations_df['站点'] == station_id]
                    target_lon = target_station['经度'].values[0]
                    target_lat = target_station['纬度'].values[0]
                    
                    try:
                        # 计算距离特征
                        distances = []
                        for c in coords:
                            dist = np.sqrt((c[0] - target_lon)**2 + (c[1] - target_lat)**2)
                            distances.append(dist)
                        
                        # 获取最大距离和数据方差
                        max_dist = max(distances) if distances else 1.0
                        data_var = np.var(values) if len(values) > 1 else 1.0
                        
                        # 设置变异函数参数
                        nugget = nugget_factor * data_var
                        sill = data_var
                        range_param = range_factor * max_dist
                        
                        # 手动设置变异函数参数
                        variogram_params = {
                            'nugget': nugget,
                            'sill': sill,
                            'range': range_param
                        }
                        
                        # 创建克里金对象并估计
                        ok = OrdinaryKriging(coords, values, variogram_model=model)
                        ok.variogram_parameters = variogram_params
                        
                        # 估计目标站点的值
                        estimated, _ = ok.estimate([[target_lon, target_lat]])
                        predicted = max(0, estimated[0])  # 确保非负
                        
                        # 记录观测值和预测值
                        all_observed.append(observed)
                        all_predicted.append(predicted)
                        
                    except Exception as e:
                        continue
                
                # 计算当前时间步的评价指标
                if all_observed and all_predicted:
                    metrics = calculate_metrics(np.array(all_observed), np.array(all_predicted))
                    total_mae += metrics['MAE']
                    total_count += 1
            
            # 如果没有成功评估任何时间步，返回一个大值
            if total_count == 0:
                logger.warning("所有时间步评估失败")
                return 1e10
            
            # 返回平均MAE作为优化目标
            avg_mae = total_mae / total_count
            return avg_mae
        
        except Exception as e:
            logger.warning(f"目标函数评估失败: {str(e)}")
            return 1e10
    
    # 定义参数边界
    bounds = [
        (0.0, 0.5),     # nugget_factor: 0-50% of variance
        (0.1, 2.0),     # range_factor: 10-200% of max distance
        (0, 2.99)       # model_idx: 0, 1, 2 (spherical, exponential, gaussian)
    ]
    
    # 使用SCE-UA优化
    logger.info("开始使用SCE-UA优化克里金参数")
    optimizer = SCEUA(objective_function, bounds, max_iter=50, pop_size=30)
    best_params, best_value = optimizer.optimize()
    
    # 解释最优参数
    nugget_factor, range_factor, model_idx = best_params
    models = ['spherical', 'exponential', 'gaussian']
    model = models[min(int(model_idx), len(models)-1)]
    
    # 如果优化失败，使用默认参数
    if np.isinf(best_value) or best_value > 1e9:
        logger.warning("优化失败，使用默认参数")
        nugget_factor = 0.1
        range_factor = 0.5
        model = 'spherical'
    
    # 创建参数字典
    params = {
        'nugget_factor': nugget_factor,
        'range_factor': range_factor,
        'variogram_model': model,
        'nlags': 20,
        'weight': True
    }
    
    logger.info(f"优化的克里金参数: {params}, 最佳MAE: {best_value:.6f}")
    
    # 使用最优参数进行全面评估
    total_metrics = {
        'MAE': 0,
        'MRE': 0,
        'RMSE': 0
    }
    count = 0
    
    # 对部分时间步进行验证
    validation_times = random.sample(all_times, min(20, len(all_times)))
    logger.info(f"使用 {len(validation_times)} 个时间步进行交叉验证评估")
    
    for time_step in validation_times:
        validation_df = leave_one_out_validation(time_step, stations_df, rain_data, params)
        if not validation_df.empty:
            metrics = calculate_metrics(
                validation_df['观测值'].values,
                validation_df['预测值'].values
            )
            for key in total_metrics:
                total_metrics[key] += metrics[key]
            count += 1
    
    # 计算平均指标
    if count > 0:
        for key in total_metrics:
            total_metrics[key] /= count
    
    logger.info(f"交叉验证结果: MAE={total_metrics['MAE']:.4f}, MRE={total_metrics['MRE']:.4f}, RMSE={total_metrics['RMSE']:.4f}")
    
    return params, total_metrics

# 定义数据读取函数
def read_data(input_dir, stations_file, encoding='utf-8'):
    """
    读取站点信息和降雨数据
    
    参数:
    input_dir: 输入数据目录
    stations_file: 站点信息文件路径
    encoding: 文件编码
    
    返回:
    stations_df: 站点信息DataFrame
    rain_data: 包含各站点降雨数据的字典
    """
    # 读取站点信息
    try:
        stations_df = pd.read_csv(stations_file, encoding=encoding)
        logger.info(f"读取站点信息成功: {len(stations_df)} 个站点")
    except Exception as e:
        # 尝试不同的编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
        for enc in encodings:
            try:
                stations_df = pd.read_csv(stations_file, encoding=enc)
                logger.info(f"使用编码 {enc} 读取站点信息成功: {len(stations_df)} 个站点")
                break
            except:
                continue
        else:
            raise ValueError(f"无法读取站点信息文件: {e}")
    
    # 读取降雨数据
    rain_data = {}
    
    # 确保输入目录存在
    if not os.path.exists(input_dir):
        raise ValueError(f"输入目录不存在: {input_dir}")
    
    # 获取输入文件列表
    input_files = [f for f in os.listdir(input_dir) if f.endswith('.csv')]
    logger.info(f"找到 {len(input_files)} 个CSV文件")
    
    # 逐个读取文件
    for filename in input_files:
        station_id = os.path.splitext(filename)[0]
        file_path = os.path.join(input_dir, filename)
        
        try:
            # 尝试读取数据
            df = pd.read_csv(file_path, encoding=encoding)
            
            # 确保存在必要的列
            if '时间' not in df.columns or '雨量' not in df.columns:
                logger.warning(f"文件 {filename} 缺少必要的列 ('时间', '雨量')，跳过")
                continue
            
            # 将时间列设为索引
            df['时间'] = pd.to_datetime(df['时间'])
            df.set_index('时间', inplace=True)
            
            # 存储数据
            rain_data[station_id] = df
            logger.debug(f"读取站点 {station_id} 的降雨数据: {len(df)} 条记录")
            
        except Exception as e:
            # 尝试不同的编码
            success = False
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
            for enc in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=enc)
                    
                    # 确保存在必要的列
                    if '时间' not in df.columns or '雨量' not in df.columns:
                        logger.warning(f"文件 {filename} 缺少必要的列 ('时间', '雨量')，跳过")
                        break
                    
                    # 将时间列设为索引
                    df['时间'] = pd.to_datetime(df['时间'])
                    df.set_index('时间', inplace=True)
                    
                    # 存储数据
                    rain_data[station_id] = df
                    logger.debug(f"使用编码 {enc} 读取站点 {station_id} 的降雨数据: {len(df)} 条记录")
                    success = True
                    break
                except:
                    continue
            
            if not success:
                logger.warning(f"无法读取文件 {filename}: {e}")
    
    logger.info(f"成功读取 {len(rain_data)} 个站点的降雨数据")
    
    # 检查站点ID是否匹配
    missing_stations = []
    for station_id in stations_df['站点'].unique():
        if station_id not in rain_data:
            missing_stations.append(station_id)
    
    if missing_stations:
        logger.warning(f"站点信息中有 {len(missing_stations)} 个站点在降雨数据中缺失: {missing_stations[:10]}...")
    
    extra_stations = []
    for station_id in rain_data:
        if station_id not in stations_df['站点'].values:
            extra_stations.append(station_id)
    
    if extra_stations:
        logger.warning(f"降雨数据中有 {len(extra_stations)} 个站点在站点信息中缺失: {extra_stations[:10]}...")
    
    return stations_df, rain_data

# 定义并行处理函数
def process_time_step(args):
    """
    处理单个时间步的插值
    
    参数:
    args: 包含以下内容的元组:
        time_step: 时间步
        stations_df: 站点信息DataFrame
        rain_data: 包含各站点降雨数据的字典
        kriging_params: 克里金参数
        mask: 掩码数组
        geo_transform: 地理变换参数
        output_dir: 输出目录
        generate_asc: 是否生成ASC文件
        asc_output_dir: ASC文件输出目录
        
    返回:
    result: 包含时间步和插值结果的字典
    """
    time_step, stations_df, rain_data, kriging_params, mask, geo_transform, output_dir, generate_asc, asc_output_dir = args
    
    try:
        # 提取当前时刻所有站点的降雨量
        rainfall_values = {}
        for station_id in stations_df['站点'].unique():
            if station_id in rain_data and time_step in rain_data[station_id].index:
                rainfall_values[station_id] = rain_data[station_id].loc[time_step, '雨量']
            else:
                rainfall_values[station_id] = 0.0
        
        # 如果所有站点的降雨量都为0，则直接返回0
        if all(v == 0 for v in rainfall_values.values()):
            result = {
                'time': time_step,
                'station_results': {station_id: 0.0 for station_id in stations_df['站点'].unique()},
                'success': True
            }
            
            # 如果需要生成ASC文件，为零降雨创建ASC文件
            if generate_asc:
                zero_grid = np.zeros_like(mask, dtype=np.float32)
                zero_grid[mask == 0] = -9999.0  # 设置掩码外为NoData
                
                # 格式化时间为文件名
                time_str = time_step.strftime("%Y%m%d_%H%M%S")
                asc_file = os.path.join(asc_output_dir, f"rainfall_{time_str}.asc")
                
                # 保存ASC文件
                save_as_asc(zero_grid, geo_transform, asc_file)
                result['asc_file'] = asc_file
            
            return result
        
        # 准备用于构建克里金模型的所有数据
        all_coords = []
        all_values = []
        
        for idx, row in stations_df.iterrows():
            station_id = row['站点']
            if station_id in rainfall_values:
                all_coords.append([row['经度'], row['纬度']])
                all_values.append(rainfall_values[station_id])
        
        # 如果有效站点太少，无法插值
        if len(all_coords) < 3:
            logger.warning(f"时间步 {time_step} 的有效站点数量不足 ({len(all_coords)})")
            return {
                'time': time_step,
                'error': "有效站点数量不足",
                'success': False
            }
        
        # 创建并拟合克里金模型
        try:
            # 计算数据统计特征
            data_var = np.var(all_values) if len(all_values) > 1 else 1.0
            
            # 计算最大距离
            distances = []
            for c in all_coords:
                for c2 in all_coords:
                    dist = np.sqrt((c[0] - c2[0])**2 + (c[1] - c2[1])**2)
                    distances.append(dist)
            max_dist = max(distances) if distances else 1.0
            
            # 设置变异函数参数
            nugget = kriging_params.get('nugget_factor', 0.1) * data_var
            range_param = kriging_params.get('range_factor', 0.3) * max_dist
            
            # 创建克里金模型
            ok = OrdinaryKriging(
                all_coords, 
                all_values, 
                variogram_model=kriging_params.get('variogram_model', 'spherical'),
                nlags=kriging_params.get('nlags', 20),
                weight=kriging_params.get('weight', True)
            )
            
            # 手动设置变异函数参数
            ok.variogram_parameters = {
                'nugget': nugget,
                'sill': data_var,
                'range': range_param
            }
        except Exception as e:
            logger.error(f"构建克里金模型失败: {e}")
            return {
                'time': time_step,
                'error': f"构建克里金模型失败: {e}",
                'success': False
            }
        
        # 执行留一法插值和栅格插值
        station_results = {}
        
        # 先执行留一法插值
        # [原有留一法插值代码保持不变]
        
        result = {
            'time': time_step,
            'station_results': station_results,
            'success': True
        }
        
        # 如果需要生成ASC文件
        if generate_asc:
            try:
                # 对整个栅格进行插值
                grid = interpolate_to_grid(ok, mask, geo_transform)
                
                # 设置掩码外为NoData
                grid[mask == 0] = -9999.0
                
                # 格式化时间为文件名
                time_str = time_step.strftime("%Y%m%d_%H%M%S")
                asc_file = os.path.join(asc_output_dir, f"rainfall_{time_str}.asc")
                
                # 保存ASC文件
                save_as_asc(grid, geo_transform, asc_file)
                result['asc_file'] = asc_file
                
            except Exception as e:
                logger.error(f"生成ASC文件失败: {e}")
                result['asc_error'] = str(e)
        
        return result
    
    except Exception as e:
        logger.error(f"处理时间步 {time_step} 时发生错误: {e}")
        return {
            'time': time_step,
            'error': str(e),
            'success': False
        }


# 主函数
def main(input_dir, stations_file, mask_file, output_dir, max_workers=12, generate_asc=False, asc_output_dir=None):
    """
    主函数，协调整个插值过程
    
    参数:
    input_dir: 输入数据目录
    stations_file: 站点信息文件路径
    mask_file: 掩码文件路径
    output_dir: 输出目录
    max_workers: 并行处理的最大工作线程数
    generate_asc: 是否生成ASC栅格文件
    asc_output_dir: ASC文件输出目录，如果为None则使用output_dir/asc
    """
    # 记录开始时间
    start_time = time.time()
    logger.info("开始克里金插值处理")
    logger.info(f"输入目录: {input_dir}")
    logger.info(f"站点文件: {stations_file}")
    logger.info(f"掩码文件: {mask_file}")
    logger.info(f"输出目录: {output_dir}")
    logger.info(f"并行线程数: {max_workers}")
    logger.info(f"是否生成ASC文件: {generate_asc}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 如果需要生成ASC文件，创建ASC输出目录
    if generate_asc:
        if asc_output_dir is None:
            asc_output_dir = os.path.join(output_dir, "asc")
        os.makedirs(asc_output_dir, exist_ok=True)
        logger.info(f"ASC文件输出目录: {asc_output_dir}")
    else:
        asc_output_dir = None
    
    # 读取站点信息和降雨数据
    logger.info("开始读取站点信息和降雨数据")
    stations_df, rain_data = read_data(input_dir, stations_file)
    
    # 读取掩码文件
    logger.info("开始读取掩码文件")
    mask, geo_transform = read_mask_file(mask_file)
    logger.info(f"掩码大小: {mask.shape}")
    
    # 获取所有唯一的时间步
    all_times = set()
    for station_id, data in rain_data.items():
        all_times.update(data.index)
    
    all_times = sorted(list(all_times))
    logger.info(f"共有 {len(all_times)} 个时间步需要处理")
    
    # 优化克里金参数
    logger.info("开始优化克里金参数")
    best_params, metrics = optimize_kriging_parameters(stations_df, rain_data)
    logger.info(f"优化完成，最佳参数: {best_params}")
    logger.info(f"交叉验证指标: MAE={metrics['MAE']:.4f}, MRE={metrics['MRE']:.4f}, RMSE={metrics['RMSE']:.4f}")
    
        # 准备并行处理参数
    args_list = [(time_step, stations_df, rain_data, best_params, mask, geo_transform, 
                  output_dir, generate_asc, asc_output_dir) for time_step in all_times]
  
    # 创建结果存储字典
    station_time_series = {station_id: [] for station_id in stations_df['站点'].unique()}
    
    # 并行处理所有时间步
    logger.info(f"开始并行处理，使用 {max_workers} 个工作线程")
    
    # 使用ProcessPoolExecutor进行并行处理
    with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 使用tqdm显示进度条
        results = list(tqdm(executor.map(process_time_step, args_list), total=len(args_list), desc="处理进度"))
    
    # 收集结果
    success_count = 0
    error_count = 0
    asc_count = 0
    
    
    for result in results:
        if result['success']:
            success_count += 1
            time_step = result['time']
            station_results = result['station_results']
            
            # 存储每个站点的结果
            for station_id, value in station_results.items():
                if station_id in station_time_series:
                    station_time_series[station_id].append((time_step, value))
                # 记录ASC文件生成情况
                if 'asc_file' in result:
                    asc_count += 1
        
        else:
            error_count += 1
            logger.error(f"时间步 {result['time']} 处理失败: {result.get('error', 'Unknown error')}")
    
    logger.info(f"处理完成: {success_count} 个时间步成功, {error_count} 个时间步失败")
    if generate_asc:
        logger.info(f"生成了 {asc_count} 个ASC文件")
    # 保存结果
    logger.info("开始保存结果")
    
    for station_id, time_series in station_time_series.items():
        if not time_series:
            logger.warning(f"站点 {station_id} 没有结果数据，跳过")
            continue
        
        # 创建DataFrame
        df = pd.DataFrame(time_series, columns=['时间', '雨量'])
        df.set_index('时间', inplace=True)
        df.sort_index(inplace=True)
        
        # 格式化文件名，避免非法字符
        safe_station_id = re.sub(r'[\\/*?:"<>|]', '_', station_id)
        output_file = os.path.join(output_dir, f"{safe_station_id}.csv")
        
        # 保存到CSV
        df.to_csv(output_file, encoding='utf-8')
        logger.info(f"站点 {station_id} 的结果保存到 {output_file}")
    
    # 计算总运行时间
    elapsed_time = time.time() - start_time
    logger.info(f"处理完成，总耗时: {elapsed_time:.2f}秒")

# 如果直接运行脚本，提供命令行接口
if __name__ == "__main__":
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="基于克里金方法的降雨空间插值")
    parser.add_argument("--input", type=str, required=True, help="输入目录，包含站点降雨CSV文件")
    parser.add_argument("--stations", type=str, required=True, help="站点信息CSV文件")
    parser.add_argument("--mask", type=str, required=True, help="掩码ASC文件")
    parser.add_argument("--output", type=str, required=True, help="输出目录")
    parser.add_argument("--workers", type=int, default=12, help="并行处理的工作线程数")
    parser.add_argument("--generate-asc", action="store_true", help="是否生成ASC栅格文件")
    parser.add_argument("--asc-output", type=str, help="ASC文件输出目录，默认为输出目录下的asc子目录")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 调用主函数
    main(args.input, args.stations, args.mask, args.output, args.workers, args.generate_asc, args.asc_output)





#####################运行命令###############
#    python kriging_参数优化.py --input "./input/2020_1" --stations "./stations.csv" --mask "./terrain/90/mask.asc" --output "./output/Kriging/2020_4_1" --workers 23
   
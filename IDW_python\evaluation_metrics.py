#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
评估指标计算模块
计算IDW插值结果的各种评估指标

作者: 空间插值系统
日期: 2024年
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from scipy import stats
import warnings

logger = logging.getLogger(__name__)

class EvaluationMetrics:
    """评估指标计算类"""
    
    def __init__(self, config):
        self.config = config
        self.metrics_results = {}
    
    def calculate_mae(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算平均绝对误差 (MAE)"""
        return np.mean(np.abs(observed - predicted))
    
    def calculate_rmse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算均方根误差 (RMSE)"""
        return np.sqrt(np.mean((observed - predicted) ** 2))
    
    def calculate_nse(self, observed: np.ndar<PERSON>, predicted: np.ndarray) -> float:
        """计算Nash-Sutcliffe效率系数 (NSE)"""
        numerator = np.sum((observed - predicted) ** 2)
        denominator = np.sum((observed - np.mean(observed)) ** 2)
        
        if denominator == 0:
            return np.nan
        
        nse = 1 - (numerator / denominator)
        return nse
    
    def calculate_r2(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算决定系数 (R²)"""
        try:
            correlation_matrix = np.corrcoef(observed, predicted)
            correlation = correlation_matrix[0, 1]
            r2 = correlation ** 2
            return r2 if not np.isnan(r2) else 0.0
        except:
            return 0.0
    
    def calculate_bias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算偏差 (BIAS)"""
        return np.mean(predicted - observed)
    
    def calculate_pbias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算百分比偏差 (PBIAS)"""
        numerator = np.sum(predicted - observed)
        denominator = np.sum(observed)
        
        if denominator == 0:
            return np.nan
        
        pbias = (numerator / denominator) * 100
        return pbias
    
    def calculate_kge(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算Kling-Gupta效率 (KGE)"""
        try:
            # 计算相关系数
            r = np.corrcoef(observed, predicted)[0, 1]
            if np.isnan(r):
                r = 0.0
            
            # 计算变异系数比
            alpha = np.std(predicted) / np.std(observed) if np.std(observed) > 0 else 1.0
            
            # 计算均值比
            beta = np.mean(predicted) / np.mean(observed) if np.mean(observed) > 0 else 1.0
            
            # 计算KGE
            kge = 1 - np.sqrt((r - 1)**2 + (alpha - 1)**2 + (beta - 1)**2)
            return kge
            
        except:
            return np.nan
    
    def calculate_all_metrics(self, observed: np.ndarray, predicted: np.ndarray) -> Dict[str, float]:
        """计算所有评估指标"""
        # 过滤掉NaN值
        mask = ~(np.isnan(observed) | np.isnan(predicted))
        obs_clean = observed[mask]
        pred_clean = predicted[mask]
        
        if len(obs_clean) == 0:
            return {metric: np.nan for metric in self.config.evaluation_metrics}
        
        metrics = {}
        
        try:
            metrics['MAE'] = self.calculate_mae(obs_clean, pred_clean)
            metrics['RMSE'] = self.calculate_rmse(obs_clean, pred_clean)
            metrics['NSE'] = self.calculate_nse(obs_clean, pred_clean)
            metrics['R2'] = self.calculate_r2(obs_clean, pred_clean)
            metrics['BIAS'] = self.calculate_bias(obs_clean, pred_clean)
            metrics['PBIAS'] = self.calculate_pbias(obs_clean, pred_clean)
            metrics['KGE'] = self.calculate_kge(obs_clean, pred_clean)
            
            # 添加统计信息
            metrics['N'] = len(obs_clean)
            metrics['Mean_Observed'] = np.mean(obs_clean)
            metrics['Mean_Predicted'] = np.mean(pred_clean)
            metrics['Std_Observed'] = np.std(obs_clean)
            metrics['Std_Predicted'] = np.std(pred_clean)
            
        except Exception as e:
            logger.error(f"计算评估指标时出错: {e}")
            metrics = {metric: np.nan for metric in self.config.evaluation_metrics}
        
        return metrics
    
    def evaluate_station_results(self, result_df: pd.DataFrame, station_id: str) -> Dict[str, float]:
        """评估单个站点的插值结果"""
        # 过滤有效数据
        valid_data = result_df.dropna(subset=['观测值', '插值'])
        
        if len(valid_data) == 0:
            logger.warning(f"站点{station_id}没有有效的对比数据")
            return {metric: np.nan for metric in self.config.evaluation_metrics}
        
        observed = valid_data['观测值'].values
        predicted = valid_data['插值'].values
        
        metrics = self.calculate_all_metrics(observed, predicted)
        metrics['Station_ID'] = station_id
        
        return metrics
    
    def evaluate_event_results(self, event_results: Dict[str, pd.DataFrame], 
                             event_name: str) -> Dict[str, Dict[str, float]]:
        """评估整个洪水事件的插值结果"""
        logger.info(f"开始评估洪水事件{event_name}的插值结果")
        
        event_metrics = {}
        
        for station_id, result_df in event_results.items():
            try:
                station_metrics = self.evaluate_station_results(result_df, station_id)
                event_metrics[station_id] = station_metrics
                
            except Exception as e:
                logger.error(f"评估站点{station_id}时出错: {e}")
                continue
        
        # 保存结果
        self.metrics_results[event_name] = event_metrics
        
        logger.info(f"洪水事件{event_name}评估完成，成功评估{len(event_metrics)}个站点")
        
        return event_metrics
    
    def calculate_event_summary_metrics(self, event_metrics: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """计算事件级别的汇总指标"""
        if not event_metrics:
            return {}
        
        # 收集所有站点的指标值
        all_metrics = {}
        for metric_name in self.config.evaluation_metrics:
            values = []
            for station_metrics in event_metrics.values():
                if metric_name in station_metrics and not np.isnan(station_metrics[metric_name]):
                    values.append(station_metrics[metric_name])
            
            if values:
                all_metrics[f'{metric_name}_Mean'] = np.mean(values)
                all_metrics[f'{metric_name}_Median'] = np.median(values)
                all_metrics[f'{metric_name}_Std'] = np.std(values)
                all_metrics[f'{metric_name}_Min'] = np.min(values)
                all_metrics[f'{metric_name}_Max'] = np.max(values)
                all_metrics[f'{metric_name}_Count'] = len(values)
            else:
                all_metrics[f'{metric_name}_Mean'] = np.nan
                all_metrics[f'{metric_name}_Median'] = np.nan
                all_metrics[f'{metric_name}_Std'] = np.nan
                all_metrics[f'{metric_name}_Min'] = np.nan
                all_metrics[f'{metric_name}_Max'] = np.nan
                all_metrics[f'{metric_name}_Count'] = 0
        
        return all_metrics
    
    def save_evaluation_results(self, event_name: str, event_metrics: Dict[str, Dict[str, float]]):
        """保存评估结果"""
        output_dir = self.config.output_dir / "evaluation_metrics" / event_name
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存详细的站点评估结果
        if event_metrics:
            metrics_df = pd.DataFrame.from_dict(event_metrics, orient='index')
            detailed_file = output_dir / f"{event_name}_detailed_metrics.csv"
            metrics_df.to_csv(detailed_file, encoding='utf-8')
            
            # 保存汇总指标
            summary_metrics = self.calculate_event_summary_metrics(event_metrics)
            summary_df = pd.DataFrame([summary_metrics])
            summary_file = output_dir / f"{event_name}_summary_metrics.csv"
            summary_df.to_csv(summary_file, index=False, encoding='utf-8')
            
            logger.info(f"评估结果已保存到: {output_dir}")
    
    def get_performance_classification(self, nse: float, r2: float) -> str:
        """根据NSE和R²值对性能进行分类"""
        if np.isnan(nse) or np.isnan(r2):
            return "无效"
        
        if nse > 0.75 and r2 > 0.75:
            return "优秀"
        elif nse > 0.65 and r2 > 0.65:
            return "良好"
        elif nse > 0.50 and r2 > 0.50:
            return "满意"
        elif nse > 0.30 and r2 > 0.30:
            return "可接受"
        else:
            return "不满意"
    
    def generate_performance_report(self, event_name: str) -> Dict:
        """生成性能评估报告"""
        if event_name not in self.metrics_results:
            return {}
        
        event_metrics = self.metrics_results[event_name]
        
        # 统计各性能等级的站点数
        performance_stats = {
            "优秀": 0, "良好": 0, "满意": 0, "可接受": 0, "不满意": 0, "无效": 0
        }
        
        station_performances = {}
        
        for station_id, metrics in event_metrics.items():
            nse = metrics.get('NSE', np.nan)
            r2 = metrics.get('R2', np.nan)
            performance = self.get_performance_classification(nse, r2)
            
            performance_stats[performance] += 1
            station_performances[station_id] = {
                'performance': performance,
                'NSE': nse,
                'R2': r2,
                'MAE': metrics.get('MAE', np.nan),
                'RMSE': metrics.get('RMSE', np.nan)
            }
        
        # 计算总体统计
        total_stations = len(event_metrics)
        success_rate = (performance_stats["优秀"] + performance_stats["良好"] + 
                       performance_stats["满意"]) / total_stations if total_stations > 0 else 0
        
        report = {
            'event_name': event_name,
            'total_stations': total_stations,
            'performance_distribution': performance_stats,
            'success_rate': success_rate,
            'station_performances': station_performances,
            'summary_metrics': self.calculate_event_summary_metrics(event_metrics)
        }
        
        return report

if __name__ == "__main__":
    # 测试评估指标计算
    from idw_config import config
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建评估器
    evaluator = EvaluationMetrics(config)
    
    # 生成测试数据
    np.random.seed(42)
    observed = np.random.rand(100) * 10
    predicted = observed + np.random.normal(0, 1, 100)  # 添加噪声
    
    # 计算指标
    metrics = evaluator.calculate_all_metrics(observed, predicted)
    print("测试指标计算结果:")
    for metric, value in metrics.items():
        print(f"  {metric}: {value:.4f}")
    
    # 测试性能分类
    nse_test = 0.8
    r2_test = 0.75
    performance = evaluator.get_performance_classification(nse_test, r2_test)
    print(f"\nNSE={nse_test}, R²={r2_test} 的性能等级: {performance}")

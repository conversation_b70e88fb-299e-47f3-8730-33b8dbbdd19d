# -*- coding: utf-8 -*-
"""
最优插值(OI)核心算法模块

实现最优插值算法的核心计算
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict
import logging
from moran_index import calculate_moran_weights_for_triangle, calculate_simple_distance_weights

def calculate_covariance_matrix(stations: pd.DataFrame, rainfall_data: pd.DataFrame) -> np.ndarray:
    """
    计算所有站点之间的降雨量协方差矩阵
    
    Args:
        stations: 包含站点经纬度的DataFrame
        rainfall_data: 降雨数据DataFrame
    
    Returns:
        np.ndarray: 协方差矩阵
    """
    try:
        n_stations = len(stations)
        station_ids = stations['station_id'].tolist()
        
        # 创建协方差矩阵
        cov_matrix = np.zeros((n_stations, n_stations))
        
        # 计算所有站点对的协方差
        for i in range(n_stations):
            for j in range(i, n_stations):
                station1 = station_ids[i]
                station2 = station_ids[j]
                
                # 检查站点是否在降雨数据中
                if station1 not in rainfall_data.columns or station2 not in rainfall_data.columns:
                    cov_matrix[i, j] = cov_matrix[j, i] = 0
                    continue
                
                # 提取两个站点的降雨数据
                data1 = rainfall_data[station1].values
                data2 = rainfall_data[station2].values
                
                # 计算协方差
                valid_indices = ~np.isnan(data1) & ~np.isnan(data2)
                if np.sum(valid_indices) > 1:
                    cov_value = np.cov(data1[valid_indices], data2[valid_indices])[0, 1]
                else:
                    cov_value = 0
                
                cov_matrix[i, j] = cov_matrix[j, i] = cov_value
        
        # 确保协方差矩阵正定（避免数值不稳定）
        min_eig = np.min(np.linalg.eigvals(cov_matrix))
        if min_eig < 0:
            logging.warning("协方差矩阵不是正定的，添加微小的对角线元素使其正定")
            cov_matrix += np.eye(n_stations) * (abs(min_eig) + 1e-5)
        
        logging.info("成功计算站点协方差矩阵")
        return cov_matrix
    
    except Exception as e:
        logging.error(f"计算协方差矩阵时出错: {e}")
        # 出错时返回单位矩阵，让程序继续运行
        return np.eye(len(stations))

def optimal_interpolation_basic(target_point: List[float], observed_points: List[List[float]], 
                               observed_values: List[float], cov_matrix: np.ndarray, 
                               stations: pd.DataFrame, obs_error: float = 0.01) -> float:
    """
    基础最优插值算法
    
    Args:
        target_point: 目标点坐标 [经度, 纬度]
        observed_points: 观测点坐标列表 [[经度1, 纬度1], [经度2, 纬度2], ...]
        observed_values: 观测值列表
        cov_matrix: 协方差矩阵
        stations: 站点信息DataFrame
        obs_error: 观测误差
    
    Returns:
        float: 插值结果
    """
    try:
        # 观测点数量
        n = len(observed_points)
        
        # 如果没有观测点，返回0
        if n == 0:
            return 0.0
        
        # 如果只有一个观测点，直接返回该观测点的值
        if n == 1:
            return observed_values[0]
        
        # 计算目标点到各观测点的距离
        distances = []
        for point in observed_points:
            dist = np.sqrt((target_point[0] - point[0])**2 + (target_point[1] - point[1])**2)
            distances.append(dist)
        distances = np.array(distances)
        
        # 构建观测点之间的误差协方差矩阵R
        R = np.zeros((n, n))
        
        # 获取观测点在stations中的索引
        obs_indices = []
        for i, point in enumerate(observed_points):
            # 查找最接近的站点
            dists = np.sqrt((stations['longitude'].values - point[0])**2 + 
                           (stations['latitude'].values - point[1])**2)
            obs_indices.append(np.argmin(dists))
        
        # 填充误差协方差矩阵
        for i in range(n):
            for j in range(n):
                if i == j:
                    # 对角线上加入观测误差
                    R[i, j] = cov_matrix[obs_indices[i], obs_indices[j]] + obs_error
                else:
                    R[i, j] = cov_matrix[obs_indices[i], obs_indices[j]]
        
        # 构建目标点与观测点之间的协方差向量
        p = np.zeros(n)
        
        # 查找目标点最接近的站点
        target_dists = np.sqrt((stations['longitude'].values - target_point[0])**2 + 
                             (stations['latitude'].values - target_point[1])**2)
        target_index = np.argmin(target_dists)
        
        # 填充协方差向量
        for i in range(n):
            p[i] = cov_matrix[target_index, obs_indices[i]]
        
        # 计算权重
        try:
            # 添加微小的对角线元素，增强数值稳定性
            R += np.eye(n) * 1e-5
            # 求解线性方程组 R*w = p，得到权重w
            weights = np.linalg.solve(R, p)
        except np.linalg.LinAlgError:
            # 如果矩阵奇异，使用伪逆
            logging.debug("使用伪逆计算权重")
            weights = np.linalg.lstsq(R, p, rcond=None)[0]
        
        # 计算插值结果: 观测值的加权和
        result = np.dot(weights, observed_values)
        
        # 确保结果非负（降雨量不能为负）
        result = max(0.0, result)
        
        return result
    
    except Exception as e:
        logging.error(f"最优插值计算时出错: {e}")
        # 出错时返回0，让程序继续运行
        return 0.0

def optimal_interpolation_with_moran(target_station_id: str, neighbor_station_ids: List[str],
                                   stations: pd.DataFrame, rainfall_data: pd.DataFrame,
                                   time_index: pd.Timestamp, cov_matrix: np.ndarray,
                                   use_moran: bool = True) -> Tuple[float, Dict[str, float]]:
    """
    使用莫兰指数权重的最优插值算法
    
    Args:
        target_station_id: 目标站点ID
        neighbor_station_ids: 邻近站点ID列表
        stations: 站点信息DataFrame
        rainfall_data: 降雨数据DataFrame
        time_index: 当前时间索引
        cov_matrix: 协方差矩阵
        use_moran: 是否使用莫兰指数权重
    
    Returns:
        Tuple[float, Dict[str, float]]: (插值结果, 权重字典)
    """
    try:
        # 获取目标站点坐标
        target_row = stations[stations['station_id'] == target_station_id]
        if target_row.empty:
            return 0.0, {}
        
        target_point = [target_row.iloc[0]['longitude'], target_row.iloc[0]['latitude']]
        
        # 获取邻近站点的坐标和观测值
        observed_points = []
        observed_values = []
        valid_neighbor_ids = []
        
        current_rainfall = rainfall_data.loc[time_index]
        
        for neighbor_id in neighbor_station_ids:
            if neighbor_id not in current_rainfall or pd.isna(current_rainfall[neighbor_id]):
                continue
            
            neighbor_row = stations[stations['station_id'] == neighbor_id]
            if neighbor_row.empty:
                continue
            
            observed_points.append([neighbor_row.iloc[0]['longitude'], neighbor_row.iloc[0]['latitude']])
            observed_values.append(current_rainfall[neighbor_id])
            valid_neighbor_ids.append(neighbor_id)
        
        if not observed_points:
            return 0.0, {}
        
        # 计算权重
        if use_moran and len(valid_neighbor_ids) > 1:
            # 使用莫兰指数权重
            moran_weights = calculate_moran_weights_for_triangle(
                target_station_id, valid_neighbor_ids, stations, rainfall_data, time_index
            )
        else:
            # 使用简单距离权重
            moran_weights = calculate_simple_distance_weights(
                target_station_id, valid_neighbor_ids, stations
            )
        
        # 使用基础最优插值算法
        oi_result = optimal_interpolation_basic(
            target_point, observed_points, observed_values, cov_matrix, stations
        )
        
        # 结合莫兰权重进行调整
        if moran_weights:
            # 计算莫兰权重的加权平均
            moran_result = sum(moran_weights.get(neighbor_id, 0) * observed_values[i] 
                             for i, neighbor_id in enumerate(valid_neighbor_ids))
            
            # 结合两种方法的结果（可以调整权重比例）
            alpha = 0.7  # OI算法的权重
            beta = 0.3   # 莫兰权重的权重
            final_result = alpha * oi_result + beta * moran_result
        else:
            final_result = oi_result
            moran_weights = {neighbor_id: 1.0/len(valid_neighbor_ids) 
                           for neighbor_id in valid_neighbor_ids}
        
        # 确保结果非负
        final_result = max(0.0, final_result)
        
        return final_result, moran_weights
    
    except Exception as e:
        logging.error(f"莫兰最优插值计算时出错: {e}")
        return 0.0, {}

def leave_one_out_validation(stations: pd.DataFrame, rainfall_data: pd.DataFrame, 
                           tri, time_index: pd.Timestamp, cov_matrix: np.ndarray,
                           config) -> pd.DataFrame:
    """
    留一法验证
    
    Args:
        stations: 站点信息DataFrame
        rainfall_data: 降雨数据DataFrame
        tri: Delaunay三角网对象
        time_index: 当前时间索引
        cov_matrix: 协方差矩阵
        config: 配置对象
    
    Returns:
        pd.DataFrame: 验证结果DataFrame
    """
    try:
        from delaunay_triangulation import find_neighbor_stations_delaunay
        
        results = []
        
        # 提取当前时间步的降雨数据
        current_rainfall = rainfall_data.loc[time_index]
        
        # 检查是否大部分站点降雨为零
        valid_values = current_rainfall.dropna().values
        zero_count = np.sum(valid_values == 0)
        zero_ratio = zero_count / len(valid_values) if len(valid_values) > 0 else 0
        
        # 如果超过阈值的站点降雨为零，采用简化策略
        if zero_ratio > config.zero_ratio_threshold:
            logging.debug(f"时间步 {time_index} 零值比例 {zero_ratio:.3f}，采用简化验证")
            
            for i, row in stations.iterrows():
                station_id = row['station_id']
                
                # 跳过缺失数据的站点
                if station_id not in current_rainfall or np.isnan(current_rainfall[station_id]):
                    continue
                
                # 获取实际降雨量
                actual_value = current_rainfall[station_id]
                
                # 对于无降雨区域，直接使用0作为插值结果
                if zero_ratio > config.high_zero_ratio_threshold:
                    interpolated_value = 0.0
                    weights = {}
                else:
                    # 使用常规插值
                    neighbor_stations, _ = find_neighbor_stations_delaunay(station_id, stations, tri)
                    interpolated_value, weights = optimal_interpolation_with_moran(
                        station_id, neighbor_stations, stations, rainfall_data, 
                        time_index, cov_matrix, use_moran=True
                    )
                
                # 记录结果
                results.append({
                    'station_id': station_id,
                    'actual_value': actual_value,
                    'interpolated_value': interpolated_value,
                    'error': interpolated_value - actual_value,
                    'abs_error': abs(interpolated_value - actual_value),
                    'weights': str(weights)
                })
        else:
            # 常规留一法验证
            for i, row in stations.iterrows():
                station_id = row['station_id']
                
                # 跳过缺失数据的站点
                if station_id not in current_rainfall or np.isnan(current_rainfall[station_id]):
                    continue
                
                # 获取实际降雨量
                actual_value = current_rainfall[station_id]
                
                # 找到邻近站点
                neighbor_stations, _ = find_neighbor_stations_delaunay(station_id, stations, tri)
                
                # 使用最优插值法计算该站点的降雨量
                interpolated_value, weights = optimal_interpolation_with_moran(
                    station_id, neighbor_stations, stations, rainfall_data, 
                    time_index, cov_matrix, use_moran=True
                )
                
                # 记录结果
                results.append({
                    'station_id': station_id,
                    'actual_value': actual_value,
                    'interpolated_value': interpolated_value,
                    'error': interpolated_value - actual_value,
                    'abs_error': abs(interpolated_value - actual_value),
                    'weights': str(weights)
                })
        
        # 转换为DataFrame
        validation_df = pd.DataFrame(results)
        
        # 计算评价指标
        if not validation_df.empty:
            mae = np.mean(validation_df['abs_error'])
            rmse = np.sqrt(np.mean(validation_df['error']**2))
            
            logging.debug(f"留一法验证结果 - 时间: {time_index}, MAE: {mae:.4f}, RMSE: {rmse:.4f}")
        
        return validation_df
    
    except Exception as e:
        logging.error(f"留一法验证时出错: {e}")
        # 出错时返回空DataFrame，让程序继续运行
        return pd.DataFrame()

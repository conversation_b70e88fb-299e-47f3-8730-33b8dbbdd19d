"""
IDW空间插值数据处理模块

主要功能：
1. 加载和处理站点数据
2. 加载和处理降雨数据
3. 加载Delaunay权重数据
4. 数据质量检查和清理
5. 数据格式转换

作者：空间插值系统
版本：1.0
"""

import os
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理器
    
    负责所有数据的加载、处理和质量控制
    """
    
    def __init__(self, input_dir: str, stations_file: str, delaunay_weights_file: str):
        """
        初始化数据处理器

        参数:
            input_dir: 输入数据目录
            stations_file: 站点信息文件
            delaunay_weights_file: Delaunay权重文件
        """
        self.input_dir = input_dir
        self.stations_file = stations_file
        self.delaunay_weights_file = delaunay_weights_file
        self.excel_stations_file = "../水晏泰森.xlsx"

        # 缓存数据
        self._stations_df = None
        self._delaunay_weights = None
        self._station_names = None
        
    def load_stations(self) -> Optional[pd.DataFrame]:
        """
        加载站点信息
        
        返回:
            pd.DataFrame: 站点数据框，包含站点、经度、纬度列
        """
        try:
            if self._stations_df is not None:
                return self._stations_df
                
            logger.info(f"加载站点信息: {self.stations_file}")
            
            # 读取站点文件
            stations_df = pd.read_csv(self.stations_file, encoding='utf-8')
            
            # 检查必要列
            required_columns = ['站点', '经度', '纬度']
            missing_columns = [col for col in required_columns if col not in stations_df.columns]
            if missing_columns:
                raise ValueError(f"站点文件缺少必要列: {missing_columns}")
            
            # 数据清理
            stations_df = stations_df.dropna(subset=required_columns)
            
            # 检查坐标范围
            lon_range = (stations_df['经度'].min(), stations_df['经度'].max())
            lat_range = (stations_df['纬度'].min(), stations_df['纬度'].max())
            
            logger.info(f"加载了{len(stations_df)}个站点")
            logger.info(f"经度范围: {lon_range[0]:.3f} - {lon_range[1]:.3f}")
            logger.info(f"纬度范围: {lat_range[0]:.3f} - {lat_range[1]:.3f}")
            
            self._stations_df = stations_df
            return stations_df
            
        except Exception as e:
            logger.error(f"加载站点信息失败: {e}")
            return None

    def load_station_names(self) -> Dict[str, str]:
        """
        加载站点名称映射

        返回:
            Dict[str, str]: 站点代码到站点名称的映射
        """
        try:
            if self._station_names is not None:
                return self._station_names

            if not os.path.exists(self.excel_stations_file):
                logger.warning(f"Excel站点文件不存在: {self.excel_stations_file}")
                return {}

            logger.info(f"加载站点名称映射: {self.excel_stations_file}")

            # 读取Excel文件
            df = pd.read_excel(self.excel_stations_file)

            # 检查必要列
            if 'PSTCD' not in df.columns or 'NAME' not in df.columns:
                logger.warning("Excel文件缺少PSTCD或NAME列")
                return {}

            # 创建站点代码到名称的映射
            station_names = {}
            for _, row in df.iterrows():
                station_code = str(row['PSTCD'])
                station_name = str(row['NAME'])
                if pd.notna(station_code) and pd.notna(station_name):
                    station_names[station_code] = station_name

            logger.info(f"加载了{len(station_names)}个站点的名称映射")

            # 显示一些示例
            sample_items = list(station_names.items())[:5]
            for code, name in sample_items:
                logger.info(f"  {code} -> {name}")

            self._station_names = station_names
            return station_names

        except Exception as e:
            logger.error(f"加载站点名称映射失败: {e}")
            return {}

    def get_station_name(self, station_code: str) -> str:
        """
        获取站点名称

        参数:
            station_code: 站点代码

        返回:
            str: 站点名称，如果没有找到则返回原代码
        """
        try:
            station_names = self.load_station_names()
            return station_names.get(str(station_code), str(station_code))
        except:
            return str(station_code)
    
    def load_delaunay_weights(self) -> Dict:
        """
        加载Delaunay权重数据
        
        返回:
            Dict: Delaunay权重字典
        """
        try:
            if self._delaunay_weights is not None:
                return self._delaunay_weights
                
            if not os.path.exists(self.delaunay_weights_file):
                logger.warning(f"Delaunay权重文件不存在: {self.delaunay_weights_file}")
                return {}
                
            logger.info(f"加载Delaunay权重: {self.delaunay_weights_file}")
            
            # 读取权重文件
            weights_df = pd.read_csv(self.delaunay_weights_file, encoding='utf-8')
            
            # 转换为字典格式
            delaunay_weights = {}
            for _, row in weights_df.iterrows():
                target_station = str(row['验证站点'])
                
                # 提取包围站点和权重
                surrounding_stations = []
                weights = []
                
                for i in range(1, 4):  # 假设最多3个包围站点
                    station_col = f'包围站点{i}'
                    weight_col = f'权重{i}'
                    
                    if station_col in row and weight_col in row:
                        station = str(row[station_col])
                        weight = float(row[weight_col])
                        
                        if pd.notna(station) and pd.notna(weight) and station != 'nan':
                            surrounding_stations.append(station)
                            weights.append(weight)
                
                if surrounding_stations:
                    delaunay_weights[target_station] = {
                        'stations': surrounding_stations,
                        'weights': weights
                    }
            
            logger.info(f"加载了{len(delaunay_weights)}个站点的Delaunay权重")
            
            self._delaunay_weights = delaunay_weights
            return delaunay_weights
            
        except Exception as e:
            logger.error(f"加载Delaunay权重失败: {e}")
            return {}
    
    def load_event_data(self, event_name: str) -> Optional[Dict[str, pd.DataFrame]]:
        """
        加载单个洪水事件的降雨数据
        
        参数:
            event_name: 事件名称（如'2015-4'）
            
        返回:
            Dict[str, pd.DataFrame]: 站点降雨数据字典
        """
        try:
            event_dir = os.path.join(self.input_dir, event_name)
            if not os.path.exists(event_dir):
                logger.error(f"事件目录不存在: {event_dir}")
                return None
            
            logger.info(f"加载事件数据: {event_name}")
            
            # 获取所有CSV文件
            csv_files = [f for f in os.listdir(event_dir) if f.endswith('.csv')]
            
            if not csv_files:
                logger.warning(f"事件目录中没有CSV文件: {event_dir}")
                return None
            
            # 加载每个站点的数据
            station_data = {}
            for csv_file in csv_files:
                station_name = csv_file.replace('.csv', '')
                file_path = os.path.join(event_dir, csv_file)
                
                try:
                    # 读取降雨数据
                    df = pd.read_csv(file_path, encoding='utf-8')
                    
                    # 检查数据格式
                    if len(df.columns) >= 2:
                        # 假设第一列是时间，第二列是雨量
                        df.columns = ['时间', '雨量']
                        
                        # 转换时间格式
                        df['时间'] = pd.to_datetime(df['时间'], errors='coerce')
                        
                        # 转换雨量为数值
                        df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce')
                        
                        # 移除无效数据
                        df = df.dropna(subset=['时间', '雨量'])
                        
                        if len(df) > 0:
                            station_data[station_name] = df
                        else:
                            logger.warning(f"站点{station_name}没有有效数据")
                    else:
                        logger.warning(f"站点{station_name}数据格式不正确")
                        
                except Exception as e:
                    logger.warning(f"读取站点{station_name}数据失败: {e}")
                    continue
            
            logger.info(f"成功加载{len(station_data)}个站点的数据")
            return station_data
            
        except Exception as e:
            logger.error(f"加载事件数据失败: {e}")
            return None
    
    def get_event_list(self) -> List[str]:
        """
        获取所有可用的洪水事件列表
        
        返回:
            List[str]: 事件名称列表
        """
        try:
            if not os.path.exists(self.input_dir):
                logger.error(f"输入目录不存在: {self.input_dir}")
                return []
            
            # 获取所有子目录
            events = []
            for item in os.listdir(self.input_dir):
                item_path = os.path.join(self.input_dir, item)
                if os.path.isdir(item_path):
                    # 检查是否包含CSV文件
                    csv_files = [f for f in os.listdir(item_path) if f.endswith('.csv')]
                    if csv_files:
                        events.append(item)
            
            # 排序事件列表
            events.sort()
            
            logger.info(f"找到{len(events)}个洪水事件: {events}")
            return events
            
        except Exception as e:
            logger.error(f"获取事件列表失败: {e}")
            return []
    
    def calculate_event_statistics(self, station_data: Dict[str, pd.DataFrame]) -> Dict:
        """
        计算事件统计信息
        
        参数:
            station_data: 站点数据字典
            
        返回:
            Dict: 统计信息
        """
        try:
            stats = {
                'total_stations': len(station_data),
                'total_rainfall': {},
                'max_rainfall': {},
                'data_duration': {},
                'valid_data_ratio': {}
            }
            
            for station, df in station_data.items():
                if len(df) > 0:
                    # 总降雨量
                    total_rain = df['雨量'].sum()
                    stats['total_rainfall'][station] = total_rain
                    
                    # 最大降雨量
                    max_rain = df['雨量'].max()
                    stats['max_rainfall'][station] = max_rain
                    
                    # 数据时长
                    duration = (df['时间'].max() - df['时间'].min()).total_seconds() / 3600
                    stats['data_duration'][station] = duration
                    
                    # 有效数据比例
                    valid_ratio = (df['雨量'] >= 0).sum() / len(df)
                    stats['valid_data_ratio'][station] = valid_ratio
            
            return stats
            
        except Exception as e:
            logger.error(f"计算统计信息失败: {e}")
            return {}
    
    def prepare_interpolation_data(self, station_data: Dict[str, pd.DataFrame]) -> Tuple[Dict[str, float], pd.DataFrame]:
        """
        准备插值所需的数据
        
        参数:
            station_data: 站点数据字典
            
        返回:
            Tuple[Dict[str, float], pd.DataFrame]: (降雨数据字典, 时间序列)
        """
        try:
            # 计算每个站点的总降雨量
            rainfall_dict = {}
            for station, df in station_data.items():
                if len(df) > 0:
                    total_rainfall = df['雨量'].sum()
                    rainfall_dict[station] = total_rainfall
            
            # 创建时间序列（使用第一个站点的时间作为基准）
            time_series = None
            if station_data:
                first_station = list(station_data.keys())[0]
                time_series = station_data[first_station][['时间']].copy()
            
            return rainfall_dict, time_series
            
        except Exception as e:
            logger.error(f"准备插值数据失败: {e}")
            return {}, pd.DataFrame()

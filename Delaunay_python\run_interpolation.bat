@echo off
chcp 65001 >nul
echo ================================================================
echo Delaunay三角剖分空间插值系统
echo ================================================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH环境变量中
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 运行系统测试...
python test_system.py
if errorlevel 1 (
    echo.
    echo 系统测试失败，请检查配置和数据
    pause
    exit /b 1
)

echo.
echo 系统测试通过，开始运行插值分析...
echo.

python run_delaunay_interpolation.py

echo.
echo 程序执行完成
pause

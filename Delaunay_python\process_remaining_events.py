#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门处理剩余洪水事件的脚本
确保所有洪水场次都有完整的输出
"""

import sys
import gc
import json
from pathlib import Path
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from config import DelaunayConfig
from data_loader import DelaunayDataLoader
from interpolator import DelaunayInterpolator
from evaluation import EvaluationMetrics
from visualization import DelaunayVisualization
from utils import setup_logging

def setup_simple_logging():
    """设置简单的日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('remaining_events.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def check_event_completion(event_name, output_dir):
    """检查洪水事件是否完全处理完成"""
    event_dir = output_dir / event_name
    metrics_file = event_dir / f"{event_name}_metrics.csv"
    summary_file = event_dir / f"{event_name}_summary.json"
    
    return metrics_file.exists() and summary_file.exists()

def process_single_event_simple(event_name, interpolator, evaluator, config, logger):
    """简化的单事件处理函数"""
    try:
        logger.info(f"🚀 开始处理洪水事件: {event_name}")
        
        # 检查是否已完成
        if check_event_completion(event_name, config.OUTPUT_DIR):
            logger.info(f"✅ 事件{event_name}已完成，跳过")
            return True
        
        # 创建输出目录
        event_output_dir = config.OUTPUT_DIR / event_name
        event_output_dir.mkdir(parents=True, exist_ok=True)
        
        # 处理插值
        logger.info(f"🔄 处理事件{event_name}的插值...")
        result = interpolator.process_flood_event(event_name)
        
        if not result:
            logger.error(f"❌ 事件{event_name}插值处理失败")
            return False
        
        # 评估结果
        logger.info(f"📊 评估事件{event_name}的结果...")
        event_metrics, summary_stats = evaluator.evaluate_event_results(
            result, event_name, event_output_dir
        )
        
        # 保存汇总统计
        summary_file = event_output_dir / f"{event_name}_summary.json"
        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary_stats, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"✅ 事件{event_name}汇总统计已保存")
        except Exception as e:
            logger.warning(f"⚠️ 事件{event_name}汇总统计保存失败: {e}")
        
        # 输出处理结果
        avg_nse = summary_stats.get('avg_nse', 0)
        nse_above_threshold = summary_stats.get('nse_above_threshold_percentage', 0)
        logger.info(f"✅ 洪水事件{event_name}处理完成")
        logger.info(f"   - 处理站点数: {len(event_metrics)}")
        logger.info(f"   - 平均NSE: {avg_nse:.4f}")
        logger.info(f"   - NSE>0.7比例: {nse_above_threshold:.1f}%")
        
        # 强制清理内存
        gc.collect()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 处理事件{event_name}时发生异常: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False

def main():
    """主函数 - 专门处理剩余洪水事件"""
    logger = setup_simple_logging()
    
    try:
        logger.info("🎯 启动剩余洪水事件处理程序")
        
        # 加载配置
        config = DelaunayConfig()
        logger.info(f"📋 输出目录: {config.OUTPUT_DIR}")
        
        # 初始化组件
        data_loader = DelaunayDataLoader(config)
        evaluator = EvaluationMetrics(config)
        interpolator = DelaunayInterpolator(config, data_loader, evaluator)
        
        # 获取所有洪水事件
        flood_events = config.get_flood_events()
        logger.info(f"🌊 发现{len(flood_events)}个洪水事件")
        
        # 检查哪些事件需要处理
        incomplete_events = []
        completed_events = []
        
        for event_name in flood_events:
            if check_event_completion(event_name, config.OUTPUT_DIR):
                completed_events.append(event_name)
            else:
                incomplete_events.append(event_name)
        
        logger.info(f"✅ 已完成事件: {len(completed_events)}个")
        logger.info(f"🔄 需要处理事件: {len(incomplete_events)}个")
        
        if not incomplete_events:
            logger.info("🎉 所有洪水事件已完成处理！")
            return 0
        
        logger.info(f"待处理事件列表: {incomplete_events}")
        
        # 处理未完成的事件
        success_count = 0
        for i, event_name in enumerate(incomplete_events):
            logger.info(f"🌊 处理进度: {i+1}/{len(incomplete_events)} - {event_name}")
            
            success = process_single_event_simple(
                event_name, interpolator, evaluator, config, logger
            )
            
            if success:
                success_count += 1
                logger.info(f"✅ 事件{event_name}处理成功")
            else:
                logger.error(f"❌ 事件{event_name}处理失败")
            
            # 定期清理内存
            if (i + 1) % 5 == 0:
                logger.info("🧹 执行内存清理...")
                gc.collect()
        
        # 最终验证
        logger.info("🔍 最终验证所有洪水事件处理状态...")
        final_completed = 0
        final_missing = []
        
        for event_name in flood_events:
            if check_event_completion(event_name, config.OUTPUT_DIR):
                final_completed += 1
            else:
                final_missing.append(event_name)
        
        logger.info(f"✅ 最终完成事件数: {final_completed}/{len(flood_events)}")
        if final_missing:
            logger.warning(f"⚠️ 仍未完成事件: {final_missing}")
        else:
            logger.info("🎉 所有洪水事件处理完成！")
        
        logger.info(f"📊 本次处理成功: {success_count}/{len(incomplete_events)}")
        logger.info("🎯 剩余洪水事件处理程序完成")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 程序运行失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

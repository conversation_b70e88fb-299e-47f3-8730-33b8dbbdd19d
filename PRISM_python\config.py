"""
PRISM插值系统配置文件
用于设置所有参数和路径
"""

import os
from dataclasses import dataclass
from typing import Optional, List


@dataclass
class Config:
    """PRISM插值系统配置类"""
    
    # ==================== 路径配置 ====================
    # 输入数据路径
    input_dir: str = "D:/pythondata/spatial_interpolation/input_another/2009-1"
    
    # 地形数据路径
    terrain_dir: str = "D:/pythondata/spatial_interpolation/terrain/90"
    
    # 输出路径
    output_dir: str = "D:/pythondata/spatial_interpolation/output/PRISM/2009-1"
    
    # 站点信息文件
    stations_file: str = "D:/pythondata/spatial_interpolation/stations.csv"
    
    # ==================== 插值参数 ====================
    # 邻近站点数量（用于Delaunay三角网）
    neighbor_count: int = 3
    
    # 最小角度约束（度）- 用于优化Delaunay三角网质量
    min_triangle_angle: float = 20.0
    
    # 距离权重指数
    distance_power: float = 2.0
    
    # 地形权重系数
    elevation_weight: float = 0.4
    slope_weight: float = 0.3
    aspect_weight: float = 0.2
    moran_weight: float = 0.1
    
    # 降雨阈值（mm）- 区分微量降雨和明显降雨
    rainfall_threshold: float = 0.5
    
    # ==================== 计算参数 ====================
    # 并行计算核心数（0表示自动检测）
    num_cores: int = 20
    
    # 批处理大小（用于栅格插值）
    batch_size: int = 20
    
    # 内存优化选项
    memory_efficient: bool = True
    
    # ==================== 输出控制 ====================
    # 是否输出栅格文件
    output_raster: bool = False
    
    # 是否输出Delaunay三角网图
    output_delaunay_plot: bool = True
    
    # 是否输出详细权重信息
    output_weight_info: bool = True
    
    # 是否输出评价指标
    output_evaluation: bool = True
    
    # ==================== 批量处理 ====================
    # 是否启用批量处理
    enable_batch_processing: bool = True
    
    # 批量处理的根目录
    batch_input_root: str = "D:/pythondata/spatial_interpolation/input_another"
    batch_output_root: str = "D:/pythondata/spatial_interpolation/output/PRISM"
    
    # 需要处理的文件夹列表（为空则处理所有）
    batch_folders: Optional[List[str]] = None
    
    # ==================== 调试选项 ====================
    # 调试模式
    debug_mode: bool = False
    
    # 详细日志
    verbose_logging: bool = True
    
    # 保存中间结果
    save_intermediate: bool = True
    
    def __post_init__(self):
        """初始化后的验证和设置"""
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 验证必要文件是否存在
        if not os.path.exists(self.stations_file):
            raise FileNotFoundError(f"站点文件不存在: {self.stations_file}")
        
        # 验证地形数据文件
        required_terrain_files = ['dem.asc', 'slope.asc', 'aspect.asc']
        for file in required_terrain_files:
            file_path = os.path.join(self.terrain_dir, file)
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"地形数据文件不存在: {file_path}")
        
        # 设置并行核心数
        if self.num_cores <= 0:
            import multiprocessing
            self.num_cores = min(multiprocessing.cpu_count(), 12)
        
        # 验证权重系数总和
        total_weight = (self.elevation_weight + self.slope_weight + 
                       self.aspect_weight + self.moran_weight)
        if abs(total_weight - 1.0) > 0.01:
            print(f"警告：地形权重系数总和为 {total_weight:.3f}，建议调整为1.0")
    
    def get_terrain_files(self):
        """获取地形数据文件路径"""
        return {
            'dem': os.path.join(self.terrain_dir, 'dem.asc'),
            'slope': os.path.join(self.terrain_dir, 'slope.asc'),
            'aspect': os.path.join(self.terrain_dir, 'aspect.asc')
        }
    
    def get_output_dirs(self):
        """获取输出目录结构"""
        dirs = {
            'main': self.output_dir,
            'points': os.path.join(self.output_dir, 'points'),
            'rasters': os.path.join(self.output_dir, 'rasters'),
            'plots': os.path.join(self.output_dir, 'plots'),
            'evaluation': os.path.join(self.output_dir, 'evaluation')
        }
        
        # 创建所有输出目录
        for dir_path in dirs.values():
            os.makedirs(dir_path, exist_ok=True)
        
        return dirs
    
    def update_paths_for_batch(self, folder_name: str):
        """为批量处理更新路径"""
        self.input_dir = os.path.join(self.batch_input_root, folder_name)
        self.output_dir = os.path.join(self.batch_output_root, folder_name)
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'input_dir': self.input_dir,
            'terrain_dir': self.terrain_dir,
            'output_dir': self.output_dir,
            'stations_file': self.stations_file,
            'neighbor_count': self.neighbor_count,
            'min_triangle_angle': self.min_triangle_angle,
            'distance_power': self.distance_power,
            'elevation_weight': self.elevation_weight,
            'slope_weight': self.slope_weight,
            'aspect_weight': self.aspect_weight,
            'moran_weight': self.moran_weight,
            'rainfall_threshold': self.rainfall_threshold,
            'num_cores': self.num_cores,
            'batch_size': self.batch_size,
            'memory_efficient': self.memory_efficient,
            'output_raster': self.output_raster,
            'output_delaunay_plot': self.output_delaunay_plot,
            'output_weight_info': self.output_weight_info,
            'output_evaluation': self.output_evaluation,
            'enable_batch_processing': self.enable_batch_processing,
            'debug_mode': self.debug_mode,
            'verbose_logging': self.verbose_logging
        }


# 默认配置实例
default_config = Config()


def create_config_from_dict(config_dict: dict) -> Config:
    """从字典创建配置对象"""
    return Config(**config_dict)


def save_config_to_file(config: Config, file_path: str):
    """保存配置到文件"""
    import json
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(config.to_dict(), f, indent=4, ensure_ascii=False)


def load_config_from_file(file_path: str) -> Config:
    """从文件加载配置"""
    import json
    with open(file_path, 'r', encoding='utf-8') as f:
        config_dict = json.load(f)
    return create_config_from_dict(config_dict)

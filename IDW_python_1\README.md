# IDW空间插值系统

基于Delaunay三角剖分权重的反距离权重(IDW)空间插值系统，专为珠江流域降雨数据设计。

## 系统特点

- **智能权重计算**: 优先使用Delaunay三角剖分权重，回退到距离权重
- **高精度插值**: 支持留一法验证和多种评估指标
- **批量处理**: 自动处理多个洪水事件
- **可视化输出**: 生成验证图表和站点分布图
- **栅格支持**: 可选的栅格输出和面雨量计算
- **并行计算**: 支持多核并行处理
- **新手友好**: 配置文件管理，详细的操作指南

## 快速开始

### 1. 环境准备

```bash
# 安装Python依赖包
pip install -r requirements.txt
```

### 2. 数据准备

确保以下文件存在：
- `input_another/`: 洪水事件数据目录
- `stations.csv`: 站点信息文件
- `output/Delaunay/delaunay_analysis_summary.csv`: Delaunay权重文件
- `terrain/90/mask.asc`: 流域掩膜文件（可选）

### 3. 配置参数

编辑 `config.py` 文件，主要参数：

```python
# IDW插值参数
IDW_POWER = 2.0              # IDW权重指数
MIN_STATIONS = 3             # 最少使用站点数
MAX_STATIONS = 10            # 最多使用站点数
SEARCH_RADIUS = 0.5          # 搜索半径（度）

# 输出控制
GENERATE_RASTER = False      # 是否生成栅格（建议False节省时间）
CALCULATE_AREAL_RAINFALL = True  # 是否计算面雨量
GENERATE_PLOTS = True        # 是否生成图表

# 并行计算
USE_PARALLEL = True          # 是否使用并行计算
N_PROCESSES = 0              # 进程数（0=自动检测）
```

### 4. 运行系统

```bash
# 批量处理所有洪水事件
python main.py

# 运行单个事件演示
python main.py --demo
```

## 输出结果

运行完成后，结果保存在 `output/IDW/` 目录：

```
output/IDW/
├── batch_processing_summary.csv    # 批量处理汇总
├── idw_interpolation.log           # 运行日志
├── plots/                          # 可视化图表
│   ├── metrics_summary.png         # 指标汇总图
│   └── ...
├── 2015-4/                         # 各事件结果
│   ├── 2015-4_validation_summary.csv
│   ├── 2015-4_validation_details.csv
│   └── ...
└── ...
```

## 主要文件说明

- **config.py**: 配置文件，包含所有可调参数
- **main.py**: 主程序，程序入口
- **idw_interpolator.py**: IDW插值核心算法
- **data_processor.py**: 数据加载和处理
- **batch_processor.py**: 批量处理管理
- **visualization.py**: 可视化生成
- **raster_processor.py**: 栅格处理和面雨量计算

## 参数调整指南

### 插值精度调整

如果插值精度不理想，可以调整以下参数：

1. **IDW_POWER**: 
   - 增大值(如3.0)：距离衰减更快，局部特征更明显
   - 减小值(如1.5)：距离衰减更慢，平滑效果更强

2. **MAX_STATIONS**: 
   - 增加站点数：可能提高精度但增加计算量
   - 减少站点数：计算更快但可能降低精度

3. **SEARCH_RADIUS**: 
   - 增大搜索半径：使用更多远距离站点
   - 减小搜索半径：只使用近距离站点

### 性能优化

1. **并行计算**:
   ```python
   USE_PARALLEL = True
   N_PROCESSES = 12  # 根据CPU核心数调整
   ```

2. **关闭栅格输出**:
   ```python
   GENERATE_RASTER = False  # 大幅节省时间
   ```

3. **内存优化**:
   ```python
   MEMORY_OPTIMIZATION = True
   ```

## 评估指标说明

- **MAE** (平均绝对误差): 越小越好，单位mm
- **RMSE** (均方根误差): 越小越好，单位mm  
- **NSE** (纳什效率系数): 越接近1越好，>0.5为可接受
- **相关系数**: 越接近1越好，表示线性相关性

## 常见问题

### 1. 运行出错
- 检查Python环境和依赖包
- 确认数据文件路径正确
- 查看日志文件了解详细错误

### 2. 精度不理想
- 调整IDW参数（权重指数、站点数等）
- 检查Delaunay权重文件质量
- 考虑数据质量和站点分布

### 3. 运行速度慢
- 关闭栅格输出
- 启用并行计算
- 减少处理的事件数量

### 4. 内存不足
- 启用内存优化模式
- 减少并行进程数
- 分批处理事件

## 技术支持

如有问题，请检查：
1. 配置文件参数设置
2. 数据文件格式和路径
3. 运行日志中的错误信息
4. Python环境和依赖包版本

## 运行步骤总结

### 新手操作步骤

1. **环境准备**
   ```bash
   pip install -r requirements.txt
   ```

2. **检查数据文件**
   - 确保 `input_another/` 目录存在且包含洪水事件数据
   - 确保 `stations.csv` 文件存在
   - 确保 `output/Delaunay/delaunay_analysis_summary.csv` 文件存在

3. **修改配置参数**
   - 打开 `config.py` 文件
   - 根据需要修改以下关键参数：
     ```python
     IDW_POWER = 2.0              # IDW权重指数
     MAX_STATIONS = 10            # 最多使用站点数
     GENERATE_RASTER = False      # 是否生成栅格（建议False）
     USE_PARALLEL = True          # 是否并行计算
     ```

4. **运行系统**
   ```bash
   # 批量处理所有事件
   python main.py

   # 或运行单事件演示
   python main.py --demo
   ```

5. **查看结果**
   - 结果保存在 `output/IDW/` 目录
   - 查看 `batch_processing_summary.csv` 了解整体性能
   - 查看 `plots/` 目录中的可视化图表

6. **生成额外图表**（可选）
   ```bash
   python generate_summary_plots.py
   ```

### 参数修改位置

在 `config.py` 文件中修改以下参数：

- **第20行**: `IDW_POWER = 2.0` - 调整权重指数
- **第25行**: `MAX_STATIONS = 10` - 调整最大站点数
- **第28行**: `SEARCH_RADIUS = 0.5` - 调整搜索半径
- **第45行**: `GENERATE_RASTER = False` - 控制是否生成栅格
- **第55行**: `USE_PARALLEL = True` - 控制是否并行计算

### 系统性能结果

基于43个洪水事件的测试结果：
- **处理成功率**: 100.0% (43/43)
- **平均MAE**: 74.29 ± 39.58 mm
- **平均RMSE**: 98.93 ± 53.98 mm
- **平均NSE**: -0.446 ± 0.564
- **NSE > 0的事件**: 11/43 (25.6%)
- **最佳事件**: 2019-4 (NSE: 0.676)

## 版本信息

- 版本: 1.0
- 更新日期: 2024年12月
- 适用于: 珠江流域降雨空间插值研究
- 基于: Delaunay三角剖分权重的IDW插值算法

# 🎯 Kriging空间插值系统 - 新手使用指南

## 🚀 最简单的开始方式

### 方法1：一键测试（推荐新手）
```bash
# 在终端中运行
cd Kriging_python
python test_system.py
```
这会自动测试系统是否正常工作！

### 方法2：直接运行
```bash
# 在终端中运行
cd Kriging_python
python run_kriging.py
```
然后按照屏幕提示选择操作。

### 方法3：交互式设置
```bash
# 在终端中运行
cd Kriging_python
python run_kriging.py --setup
```
提供完全自定义的配置选项。

## 📁 确保您的数据结构正确

```
D:/pythondata/spatial_interpolation/
├── stations.csv                    # 站点信息文件
├── terrain/90/                     # 地形数据（可选）
│   ├── dem.asc                     # 数字高程模型
│   ├── slope.asc                   # 坡度数据
│   └── aspect.asc                  # 坡向数据
└── input_another/
    ├── 2009-1/                     # 降雨数据文件夹
    │   ├── 3633.csv               # 站点降雨数据
    │   ├── 80606500.csv           # 站点降雨数据
    │   └── ...
    ├── 2015-3/                     # 另一个降雨数据文件夹
    └── ...
```

## 🎯 三种主要使用模式

### 1. 系统测试模式 ⭐ 推荐新手
**用途**: 验证系统功能，检查环境配置
**运行**: `python test_system.py`
**特点**: 
- 自动检查所有依赖包
- 验证数据路径
- 测试核心功能
- 1分钟完成全面检查

### 2. 单个文件夹处理模式
**用途**: 处理特定的降雨事件（如2009-1文件夹）
**运行**: `python run_kriging.py` 选择选项1
**特点**:
- 处理一个指定文件夹
- 可自定义参数
- 生成完整的插值结果
- 包含栅格输出

### 3. 批量处理模式
**用途**: 一次性处理多个文件夹（如2009-1, 2015-3等）
**运行**: `python run_kriging.py` 选择选项2
**特点**:
- 自动识别所有文件夹
- 批量处理，节省时间
- 生成汇总报告
- 对比分析结果

## 🛠️ 常用参数说明

### 基本参数
- **半变异函数模型**: 
  - `spherical` (球状) - 推荐，适合大多数情况
  - `exponential` (指数) - 适合连续变化的数据
  - `gaussian` (高斯) - 适合平滑的数据
  - `linear` (线性) - 适合线性趋势数据

- **邻近站点数**: 建议3-5个，太少精度不够，太多计算慢
- **并行核数**: 建议设置为您CPU核数的一半（如24核CPU设置为12）

### 高级参数
- **参数优化**: 启用会自动优化半变异函数参数（耗时较长但精度更高）
- **莫兰指数权重**: 推荐启用，考虑空间自相关性
- **Delaunay三角网**: 自动启用，用于站点选择

## 📊 结果文件说明

### 测试模式输出
```
终端显示测试结果，无文件输出
```

### 单文件夹处理输出
```
output/Kriging/2009-1/
├── evaluation/
│   ├── evaluation_metrics.csv      # 详细评价指标
│   └── optimization_history.csv    # 参数优化历史
├── points/
│   └── leave_one_out_results.csv   # 留一法验证结果
├── plots/
│   ├── kriging_scatter_plot.png     # 观测vs预测散点图
│   ├── kriging_residual_plot.png    # 残差分析图
│   ├── delaunay_triangulation.png   # 三角网图
│   └── kriging_raster.png          # 栅格可视化
├── rasters/
│   └── kriging_interpolation.asc    # 插值栅格文件
└── kriging_interpolation.log        # 详细日志
```

### 批量处理输出
```
output/Kriging/
├── 2009-1/                         # 各文件夹结果
├── 2015-3/
├── ...
└── batch_summary/                   # 汇总结果
    ├── batch_detailed_results_*.csv # 详细结果
    ├── batch_summary_*.csv         # 汇总统计
    └── batch_comparison_*.png      # 对比图表
```

## 🔍 评价指标解释

- **NSE** (纳什效率系数): 
  - 接近1最好
  - >0.75为优秀
  - >0.65为良好  
  - >0.5为可接受
  - ≤0.5为较差

- **RMSE** (均方根误差): 越小越好，表示预测精度
- **MAE** (平均绝对误差): 越小越好，表示平均误差
- **R²** (决定系数): 接近1最好，表示相关性
- **CORR** (相关系数): 接近1最好，表示线性相关

## ⚠️ 常见问题解决

### 1. 程序无法启动
```bash
# 检查Python环境
python --version

# 安装依赖包
pip install numpy pandas scipy matplotlib scikit-learn seaborn
```

### 2. 依赖包安装失败
```bash
# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple numpy pandas scipy matplotlib scikit-learn seaborn

# 或者使用conda
conda install numpy pandas scipy matplotlib scikit-learn seaborn
```

### 3. 数据文件找不到
- 检查文件路径是否正确
- 确保CSV文件包含中文列名"时间"和"雨量"
- 确保stations.csv包含"站点"、"经度"、"纬度"列

### 4. 内存不足
- 减少并行核数（如设置为6-8）
- 选择不保存栅格文件
- 使用测试模式处理少量数据

### 5. 处理速度慢
- 增加并行核数
- 禁用参数优化
- 使用批量处理时不保存栅格

### 6. 结果不理想（NSE<0.5）
- 启用参数优化功能
- 尝试不同的半变异函数模型
- 检查站点分布是否合理
- 增加邻近站点数量

## 🎯 使用建议

### 新手入门流程
1. **第一步**: 运行 `python test_system.py` 验证系统
2. **第二步**: 运行 `python run_kriging.py --setup` 进行交互式设置
3. **第三步**: 尝试单个文件夹处理
4. **第四步**: 熟悉后使用批量处理功能

### 参数设置建议

#### 初学者设置
```json
{
    "variogram_model": "spherical",
    "neighbor_count": 3,
    "enable_moran_weighting": true,
    "enable_parameter_optimization": false,
    "num_cores": 12,
    "output_raster": true
}
```

#### 高精度设置
```json
{
    "variogram_model": "spherical",
    "neighbor_count": 5,
    "enable_moran_weighting": true,
    "enable_parameter_optimization": true,
    "optimization_iterations": 10,
    "num_cores": 24
}
```

#### 快速处理设置
```json
{
    "variogram_model": "spherical",
    "neighbor_count": 3,
    "enable_moran_weighting": false,
    "enable_parameter_optimization": false,
    "output_raster": false,
    "num_cores": 24
}
```

### 性能优化建议
- **小数据集** (<100个时间点): 使用单线程，启用所有功能
- **中等数据集** (100-1000个时间点): 使用12-24核并行
- **大数据集** (>1000个时间点): 使用最大核数，禁用栅格输出

## 📞 获取帮助

1. **运行测试**: `python test_system.py`
2. **查看日志**: 检查output目录中的.log文件
3. **检查配置**: 确认kriging_config.json中的路径设置
4. **查看文档**: 阅读README.md

## 🎉 成功标志

当您看到以下信息时，说明处理成功：
- ✅ 数据加载成功
- ✅ Delaunay三角网构建完成
- ✅ Kriging插值处理完成
- ✅ 评价指标计算完成
- ✅ 结果文件已保存

恭喜您！现在可以查看output目录中的结果文件了。

## 🔧 参数修改位置

### 方法1：修改配置文件（推荐）
编辑 `kriging_config.json` 文件：
```json
{
    "variogram_model": "spherical",    // 在这里修改半变异函数模型
    "neighbor_count": 3,               // 在这里修改邻近站点数
    "num_cores": 24,                   // 在这里修改并行核数
    "enable_parameter_optimization": true  // 在这里启用/禁用参数优化
}
```

### 方法2：交互式设置
运行 `python run_kriging.py --setup`，按提示修改参数

### 方法3：直接修改代码
在 `config.py` 文件中修改默认值

---

**记住**: 如果遇到任何问题，最简单的方法是运行 `python test_system.py`，这个程序会告诉您哪里有问题！

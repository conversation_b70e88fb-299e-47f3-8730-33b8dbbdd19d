"""
简化的PRISM批量运行脚本
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from config import Config
from prism_main import PRISMInterpolation

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("simple_batch_run.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def run_batch_processing():
    """运行批量处理"""
    print("="*80)
    print("PRISM空间插值系统 - 简化批量处理")
    print("="*80)
    
    # 获取所有洪水事件文件夹
    input_root = "D:/pythondata/spatial_interpolation/input_another"
    output_root = "D:/pythondata/spatial_interpolation/output/PRISM"
    
    if not os.path.exists(input_root):
        print(f"❌ 输入根目录不存在: {input_root}")
        return
    
    # 获取所有事件文件夹
    event_folders = [f for f in os.listdir(input_root) 
                    if os.path.isdir(os.path.join(input_root, f))]
    event_folders.sort()
    
    print(f"发现 {len(event_folders)} 个洪水事件")
    print(f"前10个事件: {event_folders[:10]}")
    
    # 处理结果
    results = {}
    poor_events = []  # NSE < 0.5的事件
    
    # 逐个处理事件
    for i, event_name in enumerate(event_folders, 1):
        print(f"\n[{i}/{len(event_folders)}] 正在处理事件: {event_name}")
        print("-" * 50)
        
        try:
            # 创建配置
            config = Config(
                input_dir=os.path.join(input_root, event_name),
                terrain_dir="D:/pythondata/spatial_interpolation/terrain/90",
                output_dir=os.path.join(output_root, event_name),
                stations_file="D:/pythondata/spatial_interpolation/stations.csv",
                num_cores=12,
                memory_efficient=True,
                output_raster=False,  # 批量处理时关闭栅格输出
                output_delaunay_plot=True,
                output_evaluation=True,
                verbose_logging=False
            )
            
            # 检查输入数据
            csv_files = [f for f in os.listdir(config.input_dir) if f.endswith('.csv')]
            if len(csv_files) == 0:
                print(f"  ❌ 跳过（无CSV文件）")
                results[event_name] = {'status': 'failed', 'reason': 'no_csv_files'}
                continue
            
            print(f"  发现 {len(csv_files)} 个CSV文件")
            
            # 运行PRISM插值
            prism = PRISMInterpolation(config)
            evaluation_results = prism.run_complete_workflow()
            
            if evaluation_results and 'overall_metrics' in evaluation_results:
                metrics = evaluation_results['overall_metrics']
                nse = metrics.get('NSE', -999)
                r2 = metrics.get('R2', 0)
                rmse = metrics.get('RMSE', 0)
                mae = metrics.get('MAE', 0)
                
                print(f"  ✅ 处理成功")
                print(f"     NSE: {nse:.4f}")
                print(f"     R²:  {r2:.4f}")
                print(f"     RMSE: {rmse:.2f} mm")
                print(f"     MAE: {mae:.2f} mm")
                
                # 评价结果
                if nse > 0.75:
                    status = "优秀"
                elif nse > 0.5:
                    status = "良好"
                else:
                    status = "需要优化"
                    poor_events.append((event_name, nse))
                
                print(f"     评价: {status}")
                
                results[event_name] = {
                    'status': 'success',
                    'nse': nse,
                    'r2': r2,
                    'rmse': rmse,
                    'mae': mae,
                    'evaluation': status
                }
            else:
                print(f"  ❌ 处理失败")
                results[event_name] = {'status': 'failed', 'reason': 'processing_error'}
                
        except Exception as e:
            print(f"  ❌ 处理出错: {e}")
            results[event_name] = {'status': 'failed', 'reason': str(e)}
    
    # 生成汇总报告
    print("\n" + "="*80)
    print("批量处理完成 - 结果汇总")
    print("="*80)
    
    successful_events = [k for k, v in results.items() if v.get('status') == 'success']
    failed_events = [k for k, v in results.items() if v.get('status') == 'failed']
    
    print(f"总事件数: {len(event_folders)}")
    print(f"成功处理: {len(successful_events)}")
    print(f"处理失败: {len(failed_events)}")
    print(f"成功率: {len(successful_events)/len(event_folders)*100:.1f}%")
    
    if successful_events:
        nse_values = [results[k]['nse'] for k in successful_events]
        print(f"\nNSE统计:")
        print(f"  平均NSE: {np.mean(nse_values):.4f}")
        print(f"  最大NSE: {np.max(nse_values):.4f}")
        print(f"  最小NSE: {np.min(nse_values):.4f}")
        
        # 分类统计
        excellent = sum(1 for nse in nse_values if nse > 0.75)
        good = sum(1 for nse in nse_values if 0.5 <= nse <= 0.75)
        poor = sum(1 for nse in nse_values if nse < 0.5)
        
        print(f"  优秀 (NSE > 0.75): {excellent} ({excellent/len(nse_values)*100:.1f}%)")
        print(f"  良好 (0.5 ≤ NSE ≤ 0.75): {good} ({good/len(nse_values)*100:.1f}%)")
        print(f"  较差 (NSE < 0.5): {poor} ({poor/len(nse_values)*100:.1f}%)")
    
    # 显示表现较差的事件
    if poor_events:
        print(f"\n表现较差的事件 (NSE < 0.5):")
        for event_name, nse in sorted(poor_events, key=lambda x: x[1]):
            print(f"  {event_name}: NSE = {nse:.4f}")
    
    # 保存结果
    save_batch_results(results, output_root)
    
    # 优化表现较差的事件
    if poor_events:
        print(f"\n开始优化表现较差的事件...")
        optimize_poor_events(poor_events[:5], input_root, output_root)  # 只优化前5个最差的
    
    print("\n" + "="*80)
    print("批量处理完成！")
    print(f"详细结果请查看: {output_root}")
    print("="*80)


def optimize_poor_events(poor_events, input_root, output_root):
    """优化表现较差的事件"""
    print(f"正在优化 {len(poor_events)} 个表现较差的事件...")
    
    optimization_results = {}
    
    for event_name, original_nse in poor_events:
        print(f"\n正在优化事件: {event_name} (原始NSE: {original_nse:.4f})")
        
        # 尝试优化参数
        best_nse = original_nse
        best_params = None
        
        # 参数组合1：增加邻站数，减少地形权重
        params1 = {
            'neighbor_count': 5,
            'elevation_weight': 0.2,
            'slope_weight': 0.2,
            'aspect_weight': 0.2,
            'moran_weight': 0.4
        }
        
        # 参数组合2：更平滑的插值
        params2 = {
            'neighbor_count': 4,
            'distance_power': 1.5,
            'elevation_weight': 0.3,
            'moran_weight': 0.3
        }
        
        for i, params in enumerate([params1, params2], 1):
            print(f"  尝试参数组合 {i}...")
            
            try:
                # 创建优化配置
                config = Config(
                    input_dir=os.path.join(input_root, event_name),
                    terrain_dir="D:/pythondata/spatial_interpolation/terrain/90",
                    output_dir=os.path.join(output_root, f"{event_name}_opt_{i}"),
                    stations_file="D:/pythondata/spatial_interpolation/stations.csv",
                    num_cores=12,
                    memory_efficient=True,
                    output_raster=False,
                    output_delaunay_plot=False,
                    output_evaluation=True,
                    verbose_logging=False
                )
                
                # 应用参数
                for key, value in params.items():
                    setattr(config, key, value)
                
                # 运行优化
                prism = PRISMInterpolation(config)
                eval_results = prism.run_complete_workflow()
                
                if eval_results and 'overall_metrics' in eval_results:
                    nse = eval_results['overall_metrics'].get('NSE', -999)
                    print(f"    NSE: {nse:.4f}")
                    
                    if nse > best_nse:
                        best_nse = nse
                        best_params = params.copy()
                        print(f"    ✅ 找到更好的配置！")
                
                # 清理临时文件
                import shutil
                if os.path.exists(config.output_dir):
                    shutil.rmtree(config.output_dir)
                    
            except Exception as e:
                print(f"    ❌ 优化失败: {e}")
        
        if best_params:
            improvement = best_nse - original_nse
            print(f"  优化完成: NSE从 {original_nse:.4f} 提升到 {best_nse:.4f} (提升 {improvement:.4f})")
            
            optimization_results[event_name] = {
                'original_nse': original_nse,
                'optimized_nse': best_nse,
                'improvement': improvement,
                'best_params': best_params
            }
        else:
            print(f"  优化失败，未找到更好的参数")
            optimization_results[event_name] = {
                'original_nse': original_nse,
                'optimization_failed': True
            }
    
    # 保存优化结果
    if optimization_results:
        save_optimization_results(optimization_results, output_root)


def save_batch_results(results, output_root):
    """保存批量处理结果"""
    try:
        summary_dir = os.path.join(output_root, "batch_summary")
        os.makedirs(summary_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建汇总DataFrame
        summary_data = []
        for event_name, result in results.items():
            if result.get('status') == 'success':
                summary_data.append({
                    'Event': event_name,
                    'Status': 'Success',
                    'NSE': result.get('nse', 0),
                    'R2': result.get('r2', 0),
                    'RMSE': result.get('rmse', 0),
                    'MAE': result.get('mae', 0),
                    'Evaluation': result.get('evaluation', '')
                })
            else:
                summary_data.append({
                    'Event': event_name,
                    'Status': 'Failed',
                    'NSE': 0,
                    'R2': 0,
                    'RMSE': 0,
                    'MAE': 0,
                    'Evaluation': result.get('reason', 'Unknown')
                })
        
        summary_df = pd.DataFrame(summary_data)
        csv_file = os.path.join(summary_dir, f"batch_summary_{timestamp}.csv")
        summary_df.to_csv(csv_file, index=False, encoding='utf-8')
        
        print(f"\n批量处理结果已保存到: {csv_file}")
        
    except Exception as e:
        logger.error(f"保存批量结果失败: {e}")


def save_optimization_results(optimization_results, output_root):
    """保存优化结果"""
    try:
        summary_dir = os.path.join(output_root, "optimization_summary")
        os.makedirs(summary_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建优化汇总DataFrame
        summary_data = []
        for event_name, result in optimization_results.items():
            summary_data.append({
                'Event': event_name,
                'Original_NSE': result.get('original_nse', 0),
                'Optimized_NSE': result.get('optimized_nse', 0),
                'Improvement': result.get('improvement', 0),
                'Success': not result.get('optimization_failed', False),
                'Best_Params': str(result.get('best_params', {}))
            })
        
        summary_df = pd.DataFrame(summary_data)
        csv_file = os.path.join(summary_dir, f"optimization_summary_{timestamp}.csv")
        summary_df.to_csv(csv_file, index=False, encoding='utf-8')
        
        print(f"\n优化结果已保存到: {csv_file}")
        
    except Exception as e:
        logger.error(f"保存优化结果失败: {e}")


def main():
    """主函数"""
    print("PRISM简化批量运行脚本")
    print("此脚本将：")
    print("1. 批量处理所有洪水事件")
    print("2. 分析处理结果")
    print("3. 自动优化表现较差的事件（NSE < 0.5）")
    print()
    
    run_batch_processing()


if __name__ == "__main__":
    main()

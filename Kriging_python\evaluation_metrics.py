"""
Kriging插值系统评价指标模块
实现各种插值效果评价指标
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from typing import Dict, List, Tuple, Optional
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import os

logger = logging.getLogger(__name__)


class EvaluationMetrics:
    """评价指标计算器"""
    
    def __init__(self, config):
        """初始化评价指标计算器"""
        self.config = config
        self.metrics_history = []
        
    def calculate_all_metrics(self, observed: np.ndarray, predicted: np.ndarray) -> Dict:
        """计算所有评价指标"""
        try:
            # 数据清理
            observed, predicted = self._clean_data(observed, predicted)
            
            if len(observed) == 0:
                logger.warning("没有有效的观测-预测数据对")
                return self._get_empty_metrics()
            
            metrics = {}
            
            # 基本统计指标
            metrics['RMSE'] = self.calculate_rmse(observed, predicted)
            metrics['MAE'] = self.calculate_mae(observed, predicted)
            metrics['NSE'] = self.calculate_nse(observed, predicted)
            metrics['R2'] = self.calculate_r2(observed, predicted)
            metrics['CORR'] = self.calculate_correlation(observed, predicted)
            
            # 偏差指标
            metrics['BIAS'] = self.calculate_bias(observed, predicted)
            metrics['PBIAS'] = self.calculate_pbias(observed, predicted)
            
            # 相对误差指标
            metrics['MAPE'] = self.calculate_mape(observed, predicted)
            metrics['RRMSE'] = self.calculate_rrmse(observed, predicted)
            
            # 数据统计
            metrics['N'] = len(observed)
            metrics['OBS_MEAN'] = float(np.mean(observed))
            metrics['PRED_MEAN'] = float(np.mean(predicted))
            metrics['OBS_STD'] = float(np.std(observed))
            metrics['PRED_STD'] = float(np.std(predicted))
            
            # 零值统计
            metrics['ZERO_OBS'] = int(np.sum(observed == 0))
            metrics['ZERO_PRED'] = int(np.sum(predicted == 0))
            metrics['ZERO_RATIO_OBS'] = float(np.sum(observed == 0) / len(observed))
            metrics['ZERO_RATIO_PRED'] = float(np.sum(predicted == 0) / len(predicted))
            
            logger.debug(f"计算评价指标完成: NSE={metrics['NSE']:.4f}, RMSE={metrics['RMSE']:.4f}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算评价指标失败: {e}")
            return self._get_empty_metrics()
    
    def _clean_data(self, observed: np.ndarray, predicted: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """清理数据，移除无效值"""
        observed = np.array(observed, dtype=float)
        predicted = np.array(predicted, dtype=float)
        
        # 移除NaN值
        valid_mask = ~(np.isnan(observed) | np.isnan(predicted))
        
        # 移除无穷大值
        valid_mask = valid_mask & np.isfinite(observed) & np.isfinite(predicted)
        
        return observed[valid_mask], predicted[valid_mask]
    
    def calculate_rmse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算均方根误差"""
        try:
            return float(np.sqrt(mean_squared_error(observed, predicted)))
        except:
            return float('inf')
    
    def calculate_mae(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算平均绝对误差"""
        try:
            return float(mean_absolute_error(observed, predicted))
        except:
            return float('inf')
    
    def calculate_nse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算纳什效率系数"""
        try:
            obs_mean = np.mean(observed)
            numerator = np.sum((observed - predicted) ** 2)
            denominator = np.sum((observed - obs_mean) ** 2)
            
            if denominator == 0:
                return 0.0
            
            nse = 1 - (numerator / denominator)
            return float(nse)
        except:
            return -float('inf')
    
    def calculate_r2(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算决定系数"""
        try:
            return float(r2_score(observed, predicted))
        except:
            return 0.0
    
    def calculate_correlation(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算相关系数"""
        try:
            corr_matrix = np.corrcoef(observed, predicted)
            return float(corr_matrix[0, 1])
        except:
            return 0.0
    
    def calculate_bias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算偏差"""
        try:
            return float(np.mean(predicted - observed))
        except:
            return 0.0
    
    def calculate_pbias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算百分比偏差"""
        try:
            obs_sum = np.sum(observed)
            if obs_sum == 0:
                return 0.0
            
            bias = np.sum(predicted - observed)
            pbias = (bias / obs_sum) * 100
            return float(pbias)
        except:
            return 0.0
    
    def calculate_mape(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算平均绝对百分比误差"""
        try:
            # 避免除零
            mask = observed != 0
            if not np.any(mask):
                return 0.0
            
            mape = np.mean(np.abs((observed[mask] - predicted[mask]) / observed[mask])) * 100
            return float(mape)
        except:
            return float('inf')
    
    def calculate_rrmse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算相对均方根误差"""
        try:
            obs_mean = np.mean(observed)
            if obs_mean == 0:
                return float('inf')
            
            rmse = self.calculate_rmse(observed, predicted)
            rrmse = (rmse / obs_mean) * 100
            return float(rrmse)
        except:
            return float('inf')
    
    def _get_empty_metrics(self) -> Dict:
        """获取空的指标字典"""
        return {
            'RMSE': float('inf'),
            'MAE': float('inf'),
            'NSE': -float('inf'),
            'R2': 0.0,
            'CORR': 0.0,
            'BIAS': 0.0,
            'PBIAS': 0.0,
            'MAPE': float('inf'),
            'RRMSE': float('inf'),
            'N': 0,
            'OBS_MEAN': 0.0,
            'PRED_MEAN': 0.0,
            'OBS_STD': 0.0,
            'PRED_STD': 0.0,
            'ZERO_OBS': 0,
            'ZERO_PRED': 0,
            'ZERO_RATIO_OBS': 0.0,
            'ZERO_RATIO_PRED': 0.0
        }
    
    def create_scatter_plot(self, observed: np.ndarray, predicted: np.ndarray,
                          title: str = "Kriging插值验证", 
                          output_path: str = None) -> str:
        """创建散点图"""
        try:
            # 数据清理
            observed, predicted = self._clean_data(observed, predicted)
            
            if len(observed) == 0:
                logger.warning("没有有效数据创建散点图")
                return ""
            
            # 计算指标
            metrics = self.calculate_all_metrics(observed, predicted)
            
            # 创建图形
            plt.figure(figsize=(10, 8))
            
            # 散点图
            plt.scatter(observed, predicted, alpha=0.6, s=30, color='blue', edgecolors='black', linewidth=0.5)
            
            # 1:1线
            min_val = min(np.min(observed), np.min(predicted))
            max_val = max(np.max(observed), np.max(predicted))
            plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='1:1线')
            
            # 回归线
            try:
                z = np.polyfit(observed, predicted, 1)
                p = np.poly1d(z)
                plt.plot(observed, p(observed), 'g-', linewidth=2, alpha=0.8, label=f'回归线 (y={z[0]:.3f}x+{z[1]:.3f})')
            except:
                pass
            
            # 设置标签和标题
            plt.xlabel('观测值 (mm)', fontsize=12)
            plt.ylabel('预测值 (mm)', fontsize=12)
            plt.title(title, fontsize=14, fontweight='bold')
            
            # 添加统计信息
            stats_text = (
                f'N = {metrics["N"]}\n'
                f'NSE = {metrics["NSE"]:.4f}\n'
                f'R² = {metrics["R2"]:.4f}\n'
                f'RMSE = {metrics["RMSE"]:.4f}\n'
                f'MAE = {metrics["MAE"]:.4f}\n'
                f'CORR = {metrics["CORR"]:.4f}'
            )
            
            plt.text(0.05, 0.95, stats_text, transform=plt.gca().transAxes, 
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                    fontsize=10)
            
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.axis('equal')
            
            # 保存图形
            if output_path is None:
                output_dirs = self.config.get_output_dirs()
                output_path = os.path.join(output_dirs['plots'], 'kriging_scatter_plot.png')
            
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"散点图已保存: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"创建散点图失败: {e}")
            return ""
    
    def create_residual_plot(self, observed: np.ndarray, predicted: np.ndarray,
                           output_path: str = None) -> str:
        """创建残差图"""
        try:
            # 数据清理
            observed, predicted = self._clean_data(observed, predicted)
            
            if len(observed) == 0:
                return ""
            
            # 计算残差
            residuals = predicted - observed
            
            # 创建图形
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 残差散点图
            ax1.scatter(observed, residuals, alpha=0.6, s=30)
            ax1.axhline(y=0, color='r', linestyle='--', linewidth=2)
            ax1.set_xlabel('观测值 (mm)')
            ax1.set_ylabel('残差 (预测值 - 观测值)')
            ax1.set_title('残差散点图')
            ax1.grid(True, alpha=0.3)
            
            # 残差直方图
            ax2.hist(residuals, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
            ax2.axvline(x=0, color='r', linestyle='--', linewidth=2)
            ax2.set_xlabel('残差 (mm)')
            ax2.set_ylabel('频数')
            ax2.set_title('残差分布直方图')
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图形
            if output_path is None:
                output_dirs = self.config.get_output_dirs()
                output_path = os.path.join(output_dirs['plots'], 'kriging_residual_plot.png')
            
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"残差图已保存: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"创建残差图失败: {e}")
            return ""
    
    def save_metrics_to_csv(self, metrics: Dict, output_path: str = None) -> str:
        """保存指标到CSV文件"""
        try:
            if output_path is None:
                output_dirs = self.config.get_output_dirs()
                output_path = os.path.join(output_dirs['evaluation'], 'evaluation_metrics.csv')
            
            # 转换为DataFrame
            df = pd.DataFrame([metrics])
            
            # 保存到CSV
            df.to_csv(output_path, index=False, encoding='utf-8')
            
            logger.info(f"评价指标已保存: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"保存评价指标失败: {e}")
            return ""
    
    def get_performance_summary(self, metrics: Dict) -> str:
        """获取性能总结"""
        try:
            nse = metrics.get('NSE', -999)
            r2 = metrics.get('R2', 0)
            rmse = metrics.get('RMSE', 999)
            
            # 性能等级判断
            if nse > 0.75:
                performance = "优秀"
            elif nse > 0.65:
                performance = "良好"
            elif nse > 0.5:
                performance = "可接受"
            else:
                performance = "较差"
            
            summary = f"""
Kriging插值性能评估:
==================
性能等级: {performance}
NSE: {nse:.4f}
R²: {r2:.4f}
RMSE: {rmse:.4f} mm
样本数量: {metrics.get('N', 0)}
零值比例: {metrics.get('ZERO_RATIO_OBS', 0):.2%}
            """
            
            return summary.strip()
            
        except Exception as e:
            logger.error(f"生成性能总结失败: {e}")
            return "性能总结生成失败"
    
    def cleanup(self):
        """清理内存"""
        try:
            self.metrics_history.clear()
            logger.debug("评价指标计算器内存清理完成")
        except Exception as e:
            logger.warning(f"评价指标计算器内存清理失败: {e}")

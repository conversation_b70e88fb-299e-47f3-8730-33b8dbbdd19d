# -*- coding: utf-8 -*-
"""
数据处理模块

负责读取和处理站点数据、降雨数据和掩膜数据
"""

import os
import pandas as pd
import numpy as np
import glob
import logging
from typing import Tuple, Dict, List

def read_stations(stations_file: str) -> pd.DataFrame:
    """
    读取站点信息文件
    
    Args:
        stations_file: 站点信息文件路径，包含站点ID、经度和纬度
    
    Returns:
        pd.DataFrame: 包含站点ID、经度和纬度的DataFrame
    """
    try:
        # 读取站点信息文件，假设列名为"站点"、"经度"、"纬度"
        stations = pd.read_csv(stations_file, encoding='utf-8')
        
        # 检查必要的列是否存在
        required_columns = ["站点", "经度", "纬度"]
        for col in required_columns:
            if col not in stations.columns:
                raise ValueError(f"站点信息文件缺少必要的列: {col}")
        
        # 将站点ID转换为字符串类型
        stations["站点"] = stations["站点"].astype(str)
        
        # 重命名列以便后续处理
        stations = stations.rename(columns={"站点": "station_id", "经度": "longitude", "纬度": "latitude"})
        
        # 移除重复站点
        stations = stations.drop_duplicates(subset=['station_id'])
        
        # 重置索引
        stations = stations.reset_index(drop=True)
        
        logging.info(f"成功读取站点信息，共 {len(stations)} 个站点")
        return stations
    
    except Exception as e:
        logging.error(f"读取站点信息文件时出错: {e}")
        raise

def read_rainfall_data(input_dir: str, station_ids: List[str]) -> pd.DataFrame:
    """
    读取所有站点的降雨数据
    
    Args:
        input_dir: 输入数据目录，包含各站点的降雨数据CSV文件
        station_ids: 站点ID列表
    
    Returns:
        pd.DataFrame: 包含所有站点降雨数据的DataFrame，索引为时间，列为站点ID
    """
    try:
        # 创建一个空字典来存储每个站点的数据
        all_data = {}
        
        # 查找输入目录中的所有CSV文件
        csv_files = glob.glob(os.path.join(input_dir, "*.csv"))
        
        # 如果没有找到CSV文件，抛出错误
        if not csv_files:
            raise ValueError(f"在目录 {input_dir} 中没有找到CSV文件")
        
        # 统计成功读取的站点数
        successful_stations = 0
        
        # 遍历每个站点
        for station_id in station_ids:
            file_path = os.path.join(input_dir, f"{station_id}.csv")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logging.warning(f"站点 {station_id} 的数据文件不存在: {file_path}")
                continue
            
            # 读取降雨数据，尝试不同编码方式
            try:
                station_data = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                # 如果utf-8编码失败，尝试其他编码
                try:
                    station_data = pd.read_csv(file_path, encoding='gbk')
                except UnicodeDecodeError:
                    try:
                        station_data = pd.read_csv(file_path, encoding='latin1')
                    except Exception as e:
                        logging.warning(f"无法读取站点 {station_id} 的数据文件: {e}")
                        continue
            
            # 检查必要的列是否存在
            required_columns = ["时间", "雨量"]
            missing_columns = [col for col in required_columns if col not in station_data.columns]
            if missing_columns:
                logging.warning(f"站点 {station_id} 的数据文件缺少必要的列: {missing_columns}")
                continue
            
            try:
                # 将时间列转换为datetime类型
                station_data["时间"] = pd.to_datetime(station_data["时间"])
                
                # 确保雨量数据为数值类型
                station_data["雨量"] = pd.to_numeric(station_data["雨量"], errors='coerce')
                
                # 将数据添加到字典中
                all_data[station_id] = station_data.set_index("时间")["雨量"]
                successful_stations += 1
                
            except Exception as e:
                logging.warning(f"处理站点 {station_id} 的数据时出错: {e}")
                continue
        
        # 将所有站点数据合并为一个DataFrame
        if not all_data:
            raise ValueError("没有成功读取任何站点的降雨数据")
        
        rainfall_data = pd.DataFrame(all_data)
        
        # 按时间排序
        rainfall_data = rainfall_data.sort_index()
        
        # 填充缺失值为0（假设缺失数据表示无降雨）
        rainfall_data = rainfall_data.fillna(0)
        
        logging.info(f"成功读取降雨数据，时间范围: {rainfall_data.index.min()} 到 {rainfall_data.index.max()}")
        logging.info(f"共读取了 {len(rainfall_data)} 个时间步，{len(rainfall_data.columns)} 个站点的数据")
        logging.info(f"成功读取 {successful_stations}/{len(station_ids)} 个站点的数据")
        
        return rainfall_data
    
    except Exception as e:
        logging.error(f"读取降雨数据时出错: {e}")
        raise

def read_mask(mask_file: str) -> Tuple[np.ndarray, Dict]:
    """
    读取流域掩膜文件(ASC格式)
    
    Args:
        mask_file: 掩膜文件路径
    
    Returns:
        Tuple[np.ndarray, Dict]: (mask_array, header)，mask_array为掩膜数组，header为ASC文件头信息
    """
    try:
        # 读取ASC文件头信息
        header = {}
        mask_data = []
        
        with open(mask_file, 'r') as f:
            # 读取前6行作为头信息
            for i in range(6):
                line = f.readline().strip()
                if not line:
                    raise ValueError(f"ASC文件格式错误：第{i+1}行为空")
                
                parts = line.split()
                if len(parts) != 2:
                    raise ValueError(f"ASC文件格式错误：第{i+1}行格式不正确")
                
                key, value = parts
                try:
                    header[key.lower()] = float(value)
                except ValueError:
                    raise ValueError(f"ASC文件格式错误：第{i+1}行的值无法转换为数字")
            
            # 读取数据部分
            for line_num, line in enumerate(f, start=7):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    row = [float(x) for x in line.split()]
                    mask_data.append(row)
                except ValueError as e:
                    logging.warning(f"ASC文件第{line_num}行数据格式错误，跳过: {e}")
                    continue
        
        # 将数据转换为NumPy数组
        if not mask_data:
            raise ValueError("ASC文件中没有有效的数据行")
        
        mask_array = np.array(mask_data)
        
        # 检查数组维度是否与头信息一致
        nrows = int(header['nrows'])
        ncols = int(header['ncols'])
        
        if mask_array.shape[0] != nrows:
            logging.warning(f"实际行数 {mask_array.shape[0]} 与头信息中的行数 {nrows} 不一致")
        
        if mask_array.shape[1] != ncols:
            logging.warning(f"实际列数 {mask_array.shape[1]} 与头信息中的列数 {ncols} 不一致")
        
        # 如果维度不匹配，调整数组大小
        if mask_array.shape != (nrows, ncols):
            logging.warning(f"调整掩膜数组大小从 {mask_array.shape} 到 ({nrows}, {ncols})")
            # 裁剪或填充数组
            adjusted_array = np.full((nrows, ncols), header.get('nodata_value', -9999))
            
            min_rows = min(mask_array.shape[0], nrows)
            min_cols = min(mask_array.shape[1], ncols)
            
            adjusted_array[:min_rows, :min_cols] = mask_array[:min_rows, :min_cols]
            mask_array = adjusted_array
        
        logging.info(f"成功读取掩膜文件，维度: {mask_array.shape}")
        logging.info(f"掩膜文件头信息: {header}")
        
        return mask_array, header
    
    except Exception as e:
        logging.error(f"读取掩膜文件时出错: {e}")
        raise

def filter_stations_by_data(stations: pd.DataFrame, rainfall_data: pd.DataFrame) -> pd.DataFrame:
    """
    根据降雨数据过滤站点，只保留有数据的站点
    
    Args:
        stations: 站点信息DataFrame
        rainfall_data: 降雨数据DataFrame
    
    Returns:
        pd.DataFrame: 过滤后的站点信息
    """
    # 获取有降雨数据的站点ID
    available_stations = set(rainfall_data.columns)
    
    # 过滤站点信息
    filtered_stations = stations[stations['station_id'].isin(available_stations)].copy()
    
    # 重置索引
    filtered_stations = filtered_stations.reset_index(drop=True)
    
    logging.info(f"根据数据可用性过滤站点：{len(stations)} -> {len(filtered_stations)}")
    
    return filtered_stations

def analyze_data_quality(rainfall_data: pd.DataFrame) -> Dict:
    """
    分析降雨数据质量
    
    Args:
        rainfall_data: 降雨数据DataFrame
    
    Returns:
        Dict: 数据质量分析结果
    """
    analysis = {}
    
    # 基本统计
    analysis['total_time_steps'] = len(rainfall_data)
    analysis['total_stations'] = len(rainfall_data.columns)
    
    # 零值分析
    zero_counts = (rainfall_data == 0).sum()
    analysis['zero_ratio_by_station'] = (zero_counts / len(rainfall_data)).to_dict()
    analysis['overall_zero_ratio'] = (rainfall_data == 0).sum().sum() / (len(rainfall_data) * len(rainfall_data.columns))
    
    # 缺失值分析
    missing_counts = rainfall_data.isnull().sum()
    analysis['missing_ratio_by_station'] = (missing_counts / len(rainfall_data)).to_dict()
    analysis['overall_missing_ratio'] = missing_counts.sum() / (len(rainfall_data) * len(rainfall_data.columns))
    
    # 极值分析
    analysis['max_values_by_station'] = rainfall_data.max().to_dict()
    analysis['mean_values_by_station'] = rainfall_data.mean().to_dict()
    analysis['std_values_by_station'] = rainfall_data.std().to_dict()
    
    # 时间步零值比例分析
    zero_ratio_by_time = (rainfall_data == 0).sum(axis=1) / len(rainfall_data.columns)
    analysis['high_zero_time_steps'] = (zero_ratio_by_time > 0.8).sum()
    analysis['extreme_zero_time_steps'] = (zero_ratio_by_time > 0.9).sum()
    
    logging.info(f"数据质量分析完成:")
    logging.info(f"  - 总体零值比例: {analysis['overall_zero_ratio']:.3f}")
    logging.info(f"  - 总体缺失值比例: {analysis['overall_missing_ratio']:.3f}")
    logging.info(f"  - 高零值时间步数: {analysis['high_zero_time_steps']}")
    logging.info(f"  - 极高零值时间步数: {analysis['extreme_zero_time_steps']}")
    
    return analysis

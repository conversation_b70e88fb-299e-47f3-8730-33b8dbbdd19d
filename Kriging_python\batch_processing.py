"""
Kriging插值系统批量处理模块
支持批量处理多个洪水事件
"""

import os
import time
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

from config import Config
from kriging_main import KrigingInterpolation

logger = logging.getLogger(__name__)


class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, config: Config):
        """初始化批量处理器"""
        self.config = config
        self.batch_results = {}
        self.summary_metrics = []
        
    def get_batch_folders(self) -> List[str]:
        """获取需要批量处理的文件夹列表"""
        try:
            if not os.path.exists(self.config.batch_input_root):
                logger.error(f"批量输入根目录不存在: {self.config.batch_input_root}")
                return []
            
            # 如果指定了特定文件夹，使用指定的
            if self.config.batch_folders:
                folders = []
                for folder in self.config.batch_folders:
                    folder_path = os.path.join(self.config.batch_input_root, folder)
                    if os.path.exists(folder_path):
                        folders.append(folder)
                    else:
                        logger.warning(f"指定的文件夹不存在: {folder_path}")
                return folders
            
            # 否则自动发现所有文件夹
            all_items = os.listdir(self.config.batch_input_root)
            folders = []
            
            for item in all_items:
                item_path = os.path.join(self.config.batch_input_root, item)
                if os.path.isdir(item_path):
                    # 检查文件夹是否包含CSV文件
                    csv_files = [f for f in os.listdir(item_path) if f.endswith('.csv')]
                    if csv_files:
                        folders.append(item)
            
            # 按名称排序
            folders.sort()
            
            logger.info(f"发现 {len(folders)} 个待处理文件夹: {folders}")
            
            return folders
            
        except Exception as e:
            logger.error(f"获取批量处理文件夹失败: {e}")
            return []
    
    def process_single_folder(self, folder_name: str) -> Optional[Dict]:
        """处理单个文件夹"""
        try:
            logger.info(f"开始处理文件夹: {folder_name}")
            
            # 创建该文件夹的配置副本
            folder_config = Config(**self.config.to_dict())
            folder_config.update_paths_for_batch(folder_name)
            
            # 验证输入文件夹
            if not os.path.exists(folder_config.input_dir):
                logger.warning(f"输入文件夹不存在: {folder_config.input_dir}")
                return None
            
            # 检查是否有CSV文件
            csv_files = [f for f in os.listdir(folder_config.input_dir) if f.endswith('.csv')]
            if not csv_files:
                logger.warning(f"文件夹 {folder_name} 中没有CSV文件")
                return None
            
            # 运行Kriging插值
            start_time = time.time()
            
            kriging = KrigingInterpolation(folder_config)
            results = kriging.run_complete_workflow()
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if results:
                # 添加额外信息
                results['folder_name'] = folder_name
                results['processing_time'] = processing_time
                results['csv_file_count'] = len(csv_files)
                results['output_dir'] = folder_config.output_dir
                
                logger.info(f"文件夹 {folder_name} 处理完成: NSE={results.get('NSE', 0):.4f}, 耗时={processing_time:.1f}秒")
                
                return results
            else:
                logger.warning(f"文件夹 {folder_name} 处理失败")
                return None
                
        except Exception as e:
            logger.error(f"处理文件夹 {folder_name} 失败: {e}")
            return None
    
    def run_batch_processing(self) -> Dict:
        """运行批量处理"""
        try:
            logger.info("="*60)
            logger.info("开始批量处理")
            logger.info("="*60)
            
            # 获取文件夹列表
            folders = self.get_batch_folders()
            
            if not folders:
                logger.warning("没有找到需要处理的文件夹")
                return {}
            
            # 处理每个文件夹
            for i, folder in enumerate(folders, 1):
                logger.info(f"处理进度: {i}/{len(folders)} - {folder}")
                
                folder_result = self.process_single_folder(folder)
                if folder_result:
                    self.batch_results[folder] = folder_result
                    self.summary_metrics.append(folder_result)
            
            # 生成批量处理报告
            self.generate_batch_report()
            
            logger.info("="*60)
            logger.info(f"批量处理完成！成功处理 {len(self.batch_results)} 个文件夹")
            logger.info("="*60)
            
            return self.batch_results
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return {}
    
    def generate_batch_report(self):
        """生成批量处理报告"""
        try:
            if not self.summary_metrics:
                logger.warning("没有批量处理结果可生成报告")
                return
            
            # 创建汇总DataFrame
            summary_df = pd.DataFrame(self.summary_metrics)
            
            # 保存详细结果
            batch_output_dir = os.path.join(self.config.batch_output_root, 'batch_summary')
            os.makedirs(batch_output_dir, exist_ok=True)
            
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            
            # 保存详细结果
            detailed_path = os.path.join(batch_output_dir, f'batch_detailed_results_{timestamp}.csv')
            summary_df.to_csv(detailed_path, index=False, encoding='utf-8')
            
            # 生成汇总统计
            summary_stats = self.calculate_batch_statistics(summary_df)
            
            # 保存汇总统计
            stats_path = os.path.join(batch_output_dir, f'batch_summary_{timestamp}.csv')
            stats_df = pd.DataFrame([summary_stats])
            stats_df.to_csv(stats_path, index=False, encoding='utf-8')
            
            # 创建对比图
            self.create_batch_comparison_plots(summary_df, batch_output_dir, timestamp)
            
            # 输出汇总信息
            self.print_batch_summary(summary_stats)
            
            logger.info(f"批量处理报告已保存到: {batch_output_dir}")
            
        except Exception as e:
            logger.error(f"生成批量处理报告失败: {e}")
    
    def calculate_batch_statistics(self, summary_df: pd.DataFrame) -> Dict:
        """计算批量处理统计信息"""
        try:
            stats = {}
            
            # 基本统计
            stats['total_folders'] = len(summary_df)
            stats['total_processing_time'] = summary_df['processing_time'].sum()
            stats['avg_processing_time'] = summary_df['processing_time'].mean()
            
            # 性能指标统计
            metrics = ['NSE', 'RMSE', 'MAE', 'R2', 'CORR']
            
            for metric in metrics:
                if metric in summary_df.columns:
                    stats[f'{metric}_mean'] = summary_df[metric].mean()
                    stats[f'{metric}_std'] = summary_df[metric].std()
                    stats[f'{metric}_min'] = summary_df[metric].min()
                    stats[f'{metric}_max'] = summary_df[metric].max()
            
            # 性能等级统计
            if 'NSE' in summary_df.columns:
                nse_values = summary_df['NSE']
                stats['excellent_count'] = len(nse_values[nse_values > 0.75])
                stats['good_count'] = len(nse_values[(nse_values > 0.65) & (nse_values <= 0.75)])
                stats['acceptable_count'] = len(nse_values[(nse_values > 0.5) & (nse_values <= 0.65)])
                stats['poor_count'] = len(nse_values[nse_values <= 0.5])
                
                stats['excellent_ratio'] = stats['excellent_count'] / len(nse_values)
                stats['good_ratio'] = stats['good_count'] / len(nse_values)
                stats['acceptable_ratio'] = stats['acceptable_count'] / len(nse_values)
                stats['poor_ratio'] = stats['poor_count'] / len(nse_values)
            
            return stats
            
        except Exception as e:
            logger.error(f"计算批量统计信息失败: {e}")
            return {}
    
    def create_batch_comparison_plots(self, summary_df: pd.DataFrame, 
                                    output_dir: str, timestamp: str):
        """创建批量对比图"""
        try:
            # 创建多子图
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('Kriging插值批量处理结果对比', fontsize=16, fontweight='bold')
            
            # NSE对比
            if 'NSE' in summary_df.columns:
                axes[0, 0].bar(range(len(summary_df)), summary_df['NSE'])
                axes[0, 0].set_title('NSE (纳什效率系数)')
                axes[0, 0].set_ylabel('NSE')
                axes[0, 0].axhline(y=0.75, color='r', linestyle='--', label='优秀线(0.75)')
                axes[0, 0].axhline(y=0.5, color='orange', linestyle='--', label='可接受线(0.5)')
                axes[0, 0].legend()
                axes[0, 0].grid(True, alpha=0.3)
            
            # RMSE对比
            if 'RMSE' in summary_df.columns:
                axes[0, 1].bar(range(len(summary_df)), summary_df['RMSE'])
                axes[0, 1].set_title('RMSE (均方根误差)')
                axes[0, 1].set_ylabel('RMSE (mm)')
                axes[0, 1].grid(True, alpha=0.3)
            
            # R²对比
            if 'R2' in summary_df.columns:
                axes[1, 0].bar(range(len(summary_df)), summary_df['R2'])
                axes[1, 0].set_title('R² (决定系数)')
                axes[1, 0].set_ylabel('R²')
                axes[1, 0].grid(True, alpha=0.3)
            
            # 处理时间对比
            if 'processing_time' in summary_df.columns:
                axes[1, 1].bar(range(len(summary_df)), summary_df['processing_time'])
                axes[1, 1].set_title('处理时间')
                axes[1, 1].set_ylabel('时间 (秒)')
                axes[1, 1].grid(True, alpha=0.3)
            
            # 设置x轴标签
            for ax in axes.flat:
                ax.set_xticks(range(len(summary_df)))
                ax.set_xticklabels(summary_df['folder_name'], rotation=45, ha='right')
            
            plt.tight_layout()
            
            # 保存图形
            plot_path = os.path.join(output_dir, f'batch_comparison_{timestamp}.png')
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"批量对比图已保存: {plot_path}")
            
        except Exception as e:
            logger.error(f"创建批量对比图失败: {e}")
    
    def print_batch_summary(self, stats: Dict):
        """打印批量处理汇总"""
        try:
            print("\n" + "="*60)
            print("           批量处理结果汇总")
            print("="*60)
            
            print(f"总处理文件夹数: {stats.get('total_folders', 0)}")
            print(f"总处理时间: {stats.get('total_processing_time', 0):.1f} 秒")
            print(f"平均处理时间: {stats.get('avg_processing_time', 0):.1f} 秒/文件夹")
            
            print(f"\n性能指标统计:")
            print(f"  NSE: {stats.get('NSE_mean', 0):.4f} ± {stats.get('NSE_std', 0):.4f}")
            print(f"  RMSE: {stats.get('RMSE_mean', 0):.4f} ± {stats.get('RMSE_std', 0):.4f}")
            print(f"  R²: {stats.get('R2_mean', 0):.4f} ± {stats.get('R2_std', 0):.4f}")
            
            print(f"\n性能等级分布:")
            print(f"  优秀 (NSE>0.75): {stats.get('excellent_count', 0)} ({stats.get('excellent_ratio', 0):.1%})")
            print(f"  良好 (0.65<NSE≤0.75): {stats.get('good_count', 0)} ({stats.get('good_ratio', 0):.1%})")
            print(f"  可接受 (0.5<NSE≤0.65): {stats.get('acceptable_count', 0)} ({stats.get('acceptable_ratio', 0):.1%})")
            print(f"  较差 (NSE≤0.5): {stats.get('poor_count', 0)} ({stats.get('poor_ratio', 0):.1%})")
            
            print("="*60)
            
        except Exception as e:
            logger.error(f"打印批量汇总失败: {e}")
    
    def get_batch_results(self) -> Dict:
        """获取批量处理结果"""
        return self.batch_results
    
    def cleanup(self):
        """清理内存"""
        try:
            self.batch_results.clear()
            self.summary_metrics.clear()
            logger.debug("批量处理器内存清理完成")
        except Exception as e:
            logger.warning(f"批量处理器内存清理失败: {e}")

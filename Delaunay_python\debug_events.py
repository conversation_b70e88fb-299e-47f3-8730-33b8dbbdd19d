#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试洪水事件处理问题

检查哪些事件有问题
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from pathlib import Path
import warnings

warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from config import DelaunayConfig
from data_loader import DelaunayDataLoader

def check_event_data(event_name, data_loader):
    """检查单个事件的数据"""
    try:
        print(f"\n检查事件: {event_name}")
        
        # 检查目录是否存在
        event_dir = data_loader.config.INPUT_DIR / event_name
        if not event_dir.exists():
            print(f"  ❌ 目录不存在: {event_dir}")
            return False
        
        # 检查CSV文件
        csv_files = list(event_dir.glob("*.csv"))
        print(f"  📁 CSV文件数量: {len(csv_files)}")
        
        if not csv_files:
            print(f"  ❌ 无CSV文件")
            return False
        
        # 尝试加载数据
        try:
            station_data = data_loader.load_flood_event_data(event_name, memory_efficient=True)
            print(f"  ✅ 成功加载{len(station_data)}个站点数据")
            
            # 检查数据质量
            total_points = 0
            valid_points = 0
            
            for station_id, df in station_data.items():
                total_points += len(df)
                valid_points += df['雨量'].notna().sum()
            
            print(f"  📊 总数据点: {total_points}, 有效点: {valid_points}")
            
            # 检查时间范围
            start_time, end_time = data_loader.get_common_time_range(station_data)
            if start_time and end_time:
                duration = end_time - start_time
                print(f"  ⏰ 时间范围: {start_time} 到 {end_time} (持续{duration})")
            else:
                print(f"  ❌ 无法确定时间范围")
                return False
            
            # 检查可用验证站点
            available_stations = data_loader.get_available_stations_for_event(event_name)
            print(f"  🎯 可用验证站点: {len(available_stations)}")
            
            if len(available_stations) == 0:
                print(f"  ❌ 无可用验证站点")
                return False
            
            return True
            
        except Exception as e:
            print(f"  ❌ 加载数据失败: {e}")
            return False
            
    except Exception as e:
        print(f"  ❌ 检查事件失败: {e}")
        return False

def main():
    """主函数"""
    print("Delaunay插值系统事件调试")
    print("=" * 50)
    
    # 初始化配置
    config = DelaunayConfig()
    config.MEMORY_EFFICIENT_MODE = True
    
    # 设置简单日志
    logging.basicConfig(level=logging.WARNING)
    
    # 初始化数据加载器
    data_loader = DelaunayDataLoader(config)
    
    # 加载Delaunay分析结果
    try:
        delaunay_analysis = data_loader.load_delaunay_analysis()
        print(f"✅ 成功加载{len(delaunay_analysis)}个站点的Delaunay分析结果")
    except Exception as e:
        print(f"❌ 加载Delaunay分析失败: {e}")
        return 1
    
    # 获取所有洪水事件
    flood_events = config.get_flood_events()
    print(f"📋 发现{len(flood_events)}个洪水事件")
    
    # 检查每个事件
    good_events = []
    bad_events = []
    
    for i, event_name in enumerate(flood_events):
        print(f"\n进度: {i+1}/{len(flood_events)}")
        
        if check_event_data(event_name, data_loader):
            good_events.append(event_name)
            print(f"  ✅ 事件{event_name}检查通过")
        else:
            bad_events.append(event_name)
            print(f"  ❌ 事件{event_name}检查失败")
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("检查结果汇总")
    print("=" * 50)
    print(f"✅ 正常事件数: {len(good_events)}")
    print(f"❌ 问题事件数: {len(bad_events)}")
    
    if good_events:
        print(f"\n✅ 正常事件列表:")
        for event in good_events:
            print(f"  - {event}")
    
    if bad_events:
        print(f"\n❌ 问题事件列表:")
        for event in bad_events:
            print(f"  - {event}")
    
    # 检查是否有特定年份的问题
    good_years = set()
    bad_years = set()
    
    for event in good_events:
        year = event.split('-')[0]
        good_years.add(year)
    
    for event in bad_events:
        year = event.split('-')[0]
        bad_years.add(year)
    
    print(f"\n📅 涉及年份分析:")
    print(f"  正常年份: {sorted(good_years)}")
    print(f"  问题年份: {sorted(bad_years)}")
    
    # 找出问题开始的年份
    if bad_events:
        first_bad_event = min(bad_events)
        print(f"\n🔍 第一个问题事件: {first_bad_event}")
        
        # 检查这个事件的详细信息
        print(f"\n详细检查第一个问题事件: {first_bad_event}")
        event_dir = config.INPUT_DIR / first_bad_event
        if event_dir.exists():
            csv_files = list(event_dir.glob("*.csv"))
            print(f"  CSV文件: {len(csv_files)}")
            
            if csv_files:
                # 检查第一个文件
                sample_file = csv_files[0]
                print(f"  样本文件: {sample_file.name}")
                
                try:
                    df = pd.read_csv(sample_file, encoding='utf-8')
                    print(f"  文件行数: {len(df)}")
                    print(f"  文件列名: {list(df.columns)}")
                    
                    if '时间' in df.columns:
                        print(f"  时间范围: {df['时间'].min()} 到 {df['时间'].max()}")
                    
                    if '雨量' in df.columns:
                        print(f"  雨量统计: 最小={df['雨量'].min()}, 最大={df['雨量'].max()}, 平均={df['雨量'].mean():.4f}")
                        
                except Exception as e:
                    print(f"  读取样本文件失败: {e}")
    
    return 0 if not bad_events else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

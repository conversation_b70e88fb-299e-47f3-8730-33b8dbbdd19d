"""
IDW空间插值系统主程序

这是一个基于Delaunay三角剖分权重的IDW空间插值系统，专为珠江流域降雨数据设计。

主要功能：
1. 基于Delaunay权重的IDW插值
2. 留一法验证
3. 批量处理多个洪水事件
4. 精度评估和可视化
5. 栅格输出和面雨量计算

使用方法：
1. 修改config.py中的参数
2. 运行 python main.py

作者：空间插值系统
版本：1.0
"""

import os
import sys
import logging
import time
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入配置
import config

# 导入模块
from data_processor import DataProcessor
from idw_interpolator import IDWInterpolator
from raster_processor import RasterProcessor
from visualization import IDWVisualizer
from batch_processor import BatchProcessor


def setup_logging():
    """设置日志系统"""
    # 创建输出目录
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    # 配置日志
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 设置日志级别
    log_level = getattr(logging, config.LOG_LEVEL.upper(), logging.INFO)
    
    # 配置根日志器
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
        ]
    )
    
    # 如果需要保存日志文件
    if config.SAVE_LOG_FILE:
        log_file = os.path.join(config.OUTPUT_DIR, config.LOG_FILE)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)
    
    return logging.getLogger(__name__)


def initialize_components(logger):
    """初始化所有组件"""
    try:
        logger.info("初始化系统组件...")
        
        # 1. 初始化数据处理器
        logger.info("初始化数据处理器...")
        data_processor = DataProcessor(
            input_dir=config.INPUT_DIR,
            stations_file=config.STATIONS_FILE,
            delaunay_weights_file=config.DELAUNAY_WEIGHTS_FILE
        )
        
        # 2. 加载站点数据
        logger.info("加载站点数据...")
        stations_df = data_processor.load_stations()
        if stations_df is None or len(stations_df) == 0:
            raise ValueError("无法加载站点数据")
        
        # 3. 加载Delaunay权重
        logger.info("加载Delaunay权重...")
        delaunay_weights = data_processor.load_delaunay_weights()
        
        # 4. 初始化IDW插值器
        logger.info("初始化IDW插值器...")
        interpolator = IDWInterpolator(
            stations_df=stations_df,
            delaunay_weights=delaunay_weights,
            power=config.IDW_POWER,
            min_stations=config.MIN_STATIONS,
            max_stations=config.MAX_STATIONS,
            search_radius=config.SEARCH_RADIUS
        )
        
        # 5. 初始化栅格处理器（如果需要）
        raster_processor = None
        if config.GENERATE_RASTER or config.CALCULATE_AREAL_RAINFALL:
            logger.info("初始化栅格处理器...")
            raster_processor = RasterProcessor(
                mask_file=config.MASK_FILE,
                resolution=config.RASTER_RESOLUTION
            )
        
        # 6. 初始化可视化器（如果需要）
        visualizer = None
        if config.GENERATE_PLOTS:
            logger.info("初始化可视化器...")
            viz_output_dir = os.path.join(config.OUTPUT_DIR, 'plots')
            visualizer = IDWVisualizer(
                output_dir=viz_output_dir,
                dpi=config.PLOT_DPI,
                format=config.PLOT_FORMAT,
                use_chinese=config.USE_CHINESE_LABELS
            )
        
        # 7. 初始化批量处理器
        logger.info("初始化批量处理器...")
        batch_processor = BatchProcessor(
            data_processor=data_processor,
            interpolator=interpolator,
            raster_processor=raster_processor,
            visualizer=visualizer,
            use_parallel=config.USE_PARALLEL,
            n_processes=config.N_PROCESSES
        )
        
        logger.info("所有组件初始化完成")
        
        return {
            'data_processor': data_processor,
            'interpolator': interpolator,
            'raster_processor': raster_processor,
            'visualizer': visualizer,
            'batch_processor': batch_processor,
            'stations_df': stations_df
        }
        
    except Exception as e:
        logger.error(f"组件初始化失败: {e}")
        raise


def run_single_event_demo(components, logger):
    """运行单个事件演示"""
    try:
        logger.info("="*60)
        logger.info("运行单个事件演示")
        logger.info("="*60)
        
        data_processor = components['data_processor']
        interpolator = components['interpolator']
        visualizer = components['visualizer']
        
        # 获取第一个可用事件
        events = data_processor.get_event_list()
        if not events:
            logger.warning("没有可用的事件数据")
            return
        
        demo_event = events[0]
        logger.info(f"使用事件: {demo_event}")
        
        # 加载事件数据
        station_data = data_processor.load_event_data(demo_event)
        if not station_data:
            logger.error(f"无法加载事件{demo_event}的数据")
            return
        
        # 准备插值数据
        rainfall_dict, _ = data_processor.prepare_interpolation_data(station_data)
        if not rainfall_dict:
            logger.error("没有有效的降雨数据")
            return
        
        logger.info(f"加载了{len(rainfall_dict)}个站点的数据")
        logger.info(f"总降雨量: {sum(rainfall_dict.values()):.2f}mm")
        
        # 执行留一法验证
        logger.info("执行留一法验证...")
        validation_results = interpolator.validate_interpolation(rainfall_dict)
        
        if validation_results:
            mae = validation_results.get('mae', 0)
            rmse = validation_results.get('rmse', 0)
            nse = validation_results.get('nse', 0)
            correlation = validation_results.get('correlation', 0)
            
            logger.info("验证结果:")
            logger.info(f"  MAE: {mae:.3f} mm")
            logger.info(f"  RMSE: {rmse:.3f} mm")
            logger.info(f"  NSE: {nse:.3f}")
            logger.info(f"  相关系数: {correlation:.3f}")
            
            # 生成可视化
            if visualizer:
                logger.info("生成可视化图表...")
                visualizer.plot_validation_results(validation_results, demo_event)
                visualizer.plot_station_map(components['stations_df'], rainfall_dict, demo_event)
        
        logger.info("单个事件演示完成")
        
    except Exception as e:
        logger.error(f"单个事件演示失败: {e}")


def run_batch_processing(components, logger):
    """运行批量处理"""
    try:
        logger.info("="*60)
        logger.info("开始批量处理所有洪水事件")
        logger.info("="*60)
        
        batch_processor = components['batch_processor']
        
        # 执行批量处理
        start_time = time.time()
        
        summary_results = batch_processor.process_all_events(
            output_dir=config.OUTPUT_DIR,
            generate_raster=config.GENERATE_RASTER,
            calculate_areal=config.CALCULATE_AREAL_RAINFALL
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 显示汇总结果
        if summary_results:
            logger.info("="*60)
            logger.info("批量处理汇总结果")
            logger.info("="*60)
            logger.info(f"总事件数: {summary_results.get('total_events', 0)}")
            logger.info(f"成功处理: {summary_results.get('successful_events', 0)}")
            logger.info(f"平均MAE: {summary_results.get('mean_mae', 0):.3f} mm")
            logger.info(f"平均RMSE: {summary_results.get('mean_rmse', 0):.3f} mm")
            logger.info(f"平均NSE: {summary_results.get('mean_nse', 0):.3f}")
            logger.info(f"平均相关系数: {summary_results.get('mean_correlation', 0):.3f}")
            logger.info(f"总处理时间: {total_time:.2f} 秒")
            logger.info("="*60)
        
        return summary_results
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        return {}


def main():
    """主函数"""
    try:
        # 显示欢迎信息
        print("="*80)
        print("IDW空间插值系统")
        print("基于Delaunay三角剖分的反距离权重插值")
        print("="*80)
        
        # 验证配置
        if not config.validate_config():
            print("配置验证失败，请检查config.py文件")
            return
        
        # 显示配置信息
        config.print_config()
        
        # 设置日志
        logger = setup_logging()
        logger.info("IDW空间插值系统启动")
        
        # 初始化组件
        components = initialize_components(logger)
        
        # 运行单个事件演示（可选）
        if len(sys.argv) > 1 and sys.argv[1] == '--demo':
            run_single_event_demo(components, logger)
            return
        
        # 运行批量处理
        summary_results = run_batch_processing(components, logger)
        
        # 显示最终结果
        if summary_results:
            logger.info("IDW空间插值系统运行完成！")
            logger.info(f"结果保存在: {config.OUTPUT_DIR}")
            
            # 显示性能统计
            successful_events = summary_results.get('successful_events', 0)
            total_events = summary_results.get('total_events', 0)
            success_rate = successful_events / total_events * 100 if total_events > 0 else 0
            
            print("\n" + "="*60)
            print("最终统计结果")
            print("="*60)
            print(f"处理成功率: {success_rate:.1f}% ({successful_events}/{total_events})")
            print(f"平均插值精度 (MAE): {summary_results.get('mean_mae', 0):.3f} mm")
            print(f"平均插值精度 (NSE): {summary_results.get('mean_nse', 0):.3f}")
            print(f"结果保存目录: {config.OUTPUT_DIR}")
            print("="*60)
        else:
            logger.error("批量处理未产生有效结果")
            
    except KeyboardInterrupt:
        print("\n用户中断程序运行")
        logger.info("程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        logger.error(f"程序运行出错: {e}")
        raise


if __name__ == "__main__":
    main()

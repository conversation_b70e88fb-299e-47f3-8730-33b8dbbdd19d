#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行NSE监控分析 - 检查所有洪水场次的NSE值

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0
"""

import sys
import os
from pathlib import Path
import logging
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
import json
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def setup_logging():
    """设置日志"""
    log_dir = Path('output/Delaunay_interpolation/nse_monitoring_logs')
    log_dir.mkdir(parents=True, exist_ok=True)
    
    log_file = log_dir / f'nse_monitoring_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

def check_existing_metrics():
    """检查现有的评价指标文件中的NSE值"""
    logger = logging.getLogger(__name__)
    
    metrics_dir = Path('output/Delaunay_interpolation/metrics')
    if not metrics_dir.exists():
        logger.warning(f"评价指标目录不存在: {metrics_dir}")
        return None
    
    csv_files = list(metrics_dir.glob('*_metrics.csv'))
    if not csv_files:
        logger.warning(f"未找到评价指标文件")
        return None
    
    logger.info(f"找到{len(csv_files)}个评价指标文件")
    
    # 收集所有NSE值
    all_nse_data = []
    low_nse_records = []
    
    for csv_file in csv_files:
        try:
            df = pd.read_csv(csv_file, encoding='utf-8')
            
            if 'NSE' not in df.columns:
                logger.warning(f"文件{csv_file.name}中没有NSE列")
                continue
            
            event_name = csv_file.stem.replace('_metrics', '')
            
            for _, row in df.iterrows():
                nse_value = row.get('NSE', np.nan)
                station_code = row.get('站点代码', 'Unknown')
                
                if pd.notna(nse_value):
                    all_nse_data.append({
                        'event_name': event_name,
                        'station_code': station_code,
                        'NSE': nse_value
                    })
                    
                    # 检查是否小于-10
                    if nse_value < -10:
                        low_nse_records.append({
                            'event_name': event_name,
                            'station_code': station_code,
                            'NSE': nse_value,
                            'file': csv_file.name
                        })
        
        except Exception as e:
            logger.error(f"读取文件{csv_file.name}失败: {e}")
    
    if not all_nse_data:
        logger.warning("未找到有效的NSE数据")
        return None
    
    # 转换为DataFrame
    nse_df = pd.DataFrame(all_nse_data)
    
    # 统计分析
    total_records = len(nse_df)
    low_nse_count = len(low_nse_records)
    
    nse_stats = {
        'total_records': total_records,
        'low_nse_count': low_nse_count,
        'low_nse_percentage': (low_nse_count / total_records * 100) if total_records > 0 else 0,
        'nse_min': nse_df['NSE'].min(),
        'nse_max': nse_df['NSE'].max(),
        'nse_mean': nse_df['NSE'].mean(),
        'nse_median': nse_df['NSE'].median(),
        'nse_std': nse_df['NSE'].std()
    }
    
    logger.info("="*80)
    logger.info("NSE值统计分析结果")
    logger.info("="*80)
    logger.info(f"总记录数: {total_records:,}")
    logger.info(f"NSE < -10的记录数: {low_nse_count:,}")
    logger.info(f"NSE < -10的比例: {nse_stats['low_nse_percentage']:.2f}%")
    logger.info(f"NSE最小值: {nse_stats['nse_min']:.4f}")
    logger.info(f"NSE最大值: {nse_stats['nse_max']:.4f}")
    logger.info(f"NSE平均值: {nse_stats['nse_mean']:.4f}")
    logger.info(f"NSE中位数: {nse_stats['nse_median']:.4f}")
    logger.info(f"NSE标准差: {nse_stats['nse_std']:.4f}")
    logger.info("="*80)
    
    if low_nse_records:
        logger.info(f"\n发现{len(low_nse_records)}个NSE < -10的记录:")
        logger.info("-" * 60)
        
        # 按NSE值排序（从最低开始）
        low_nse_records.sort(key=lambda x: x['NSE'])
        
        for i, record in enumerate(low_nse_records[:20], 1):  # 只显示前20个最差的
            logger.info(f"{i:2d}. 事件: {record['event_name']}, "
                       f"站点: {record['station_code']}, "
                       f"NSE: {record['NSE']:.4f}")
        
        if len(low_nse_records) > 20:
            logger.info(f"... 还有{len(low_nse_records) - 20}个记录")
    else:
        logger.info("✅ 未发现NSE < -10的记录")
    
    return {
        'nse_stats': nse_stats,
        'low_nse_records': low_nse_records,
        'all_nse_data': nse_df
    }

def save_nse_analysis_report(analysis_results):
    """保存NSE分析报告"""
    logger = logging.getLogger(__name__)
    
    if not analysis_results:
        return
    
    output_dir = Path('output/Delaunay_interpolation/nse_analysis')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存详细报告
    report_file = output_dir / f'nse_analysis_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("="*80 + "\n")
        f.write("Delaunay插值系统NSE值分析报告\n")
        f.write("NSE Value Analysis Report for Delaunay Interpolation System\n")
        f.write("="*80 + "\n\n")
        
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"NSE阈值: -10.0\n\n")
        
        # 基本统计
        stats = analysis_results['nse_stats']
        f.write("基本统计 Basic Statistics:\n")
        f.write("-" * 40 + "\n")
        f.write(f"总记录数: {stats['total_records']:,}\n")
        f.write(f"NSE < -10记录数: {stats['low_nse_count']:,}\n")
        f.write(f"NSE < -10比例: {stats['low_nse_percentage']:.2f}%\n")
        f.write(f"NSE最小值: {stats['nse_min']:.4f}\n")
        f.write(f"NSE最大值: {stats['nse_max']:.4f}\n")
        f.write(f"NSE平均值: {stats['nse_mean']:.4f}\n")
        f.write(f"NSE中位数: {stats['nse_median']:.4f}\n")
        f.write(f"NSE标准差: {stats['nse_std']:.4f}\n\n")
        
        # 低NSE记录详情
        low_nse_records = analysis_results['low_nse_records']
        if low_nse_records:
            f.write("NSE < -10的详细记录 Detailed Records with NSE < -10:\n")
            f.write("-" * 40 + "\n")
            
            # 按NSE值排序
            low_nse_records.sort(key=lambda x: x['NSE'])
            
            for i, record in enumerate(low_nse_records, 1):
                f.write(f"{i:3d}. 事件: {record['event_name']:<15} "
                       f"站点: {record['station_code']:<12} "
                       f"NSE: {record['NSE']:>10.4f} "
                       f"文件: {record['file']}\n")
            
            # 按事件统计
            f.write(f"\n按事件统计 Statistics by Event:\n")
            f.write("-" * 40 + "\n")
            
            event_stats = {}
            for record in low_nse_records:
                event = record['event_name']
                if event not in event_stats:
                    event_stats[event] = []
                event_stats[event].append(record['NSE'])
            
            for event, nse_values in sorted(event_stats.items()):
                f.write(f"{event}: {len(nse_values)}个站点, "
                       f"最低NSE: {min(nse_values):.4f}, "
                       f"平均NSE: {np.mean(nse_values):.4f}\n")
            
            # 按站点统计
            f.write(f"\n按站点统计 Statistics by Station:\n")
            f.write("-" * 40 + "\n")
            
            station_stats = {}
            for record in low_nse_records:
                station = record['station_code']
                if station not in station_stats:
                    station_stats[station] = []
                station_stats[station].append(record['NSE'])
            
            for station, nse_values in sorted(station_stats.items()):
                f.write(f"{station}: {len(nse_values)}个事件, "
                       f"最低NSE: {min(nse_values):.4f}, "
                       f"平均NSE: {np.mean(nse_values):.4f}\n")
        else:
            f.write("✅ 未发现NSE < -10的记录\n")
    
    # 保存低NSE记录的CSV文件
    if low_nse_records:
        low_nse_df = pd.DataFrame(low_nse_records)
        csv_file = output_dir / f'low_nse_records_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        low_nse_df.to_csv(csv_file, index=False, encoding='utf-8')
        logger.info(f"低NSE记录CSV文件已保存: {csv_file}")
    
    # 保存统计数据JSON
    json_file = output_dir / f'nse_statistics_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(analysis_results['nse_stats'], f, ensure_ascii=False, indent=2)
    
    logger.info(f"NSE分析报告已保存: {report_file}")
    logger.info(f"统计数据已保存: {json_file}")

def main():
    """主程序"""
    try:
        print("="*80)
        print("Delaunay插值系统NSE值监控分析")
        print("NSE Value Monitoring Analysis for Delaunay Interpolation System")
        print("="*80)
        print("功能: 检查所有洪水场次的NSE值，识别NSE < -10的情况")
        print("="*80)
        
        # 设置日志
        logger = setup_logging()
        logger.info("NSE监控分析开始")
        
        # 检查现有的评价指标
        logger.info("检查现有的评价指标文件...")
        analysis_results = check_existing_metrics()
        
        if analysis_results is None:
            logger.error("无法获取NSE数据，分析终止")
            return 1
        
        # 保存分析报告
        save_nse_analysis_report(analysis_results)
        
        # 总结
        stats = analysis_results['nse_stats']
        low_nse_count = stats['low_nse_count']
        total_records = stats['total_records']
        
        print("\n" + "="*80)
        print("NSE监控分析完成")
        print("="*80)
        print(f"📊 总记录数: {total_records:,}")
        print(f"⚠️  NSE < -10记录数: {low_nse_count:,}")
        print(f"📈 NSE < -10比例: {stats['low_nse_percentage']:.2f}%")
        print(f"📉 NSE最小值: {stats['nse_min']:.4f}")
        print(f"📊 NSE平均值: {stats['nse_mean']:.4f}")
        
        if low_nse_count > 0:
            print(f"\n🚨 发现{low_nse_count}个NSE < -10的记录!")
            print("💡 建议运行改进的插值分析以自动调整权重")
            print("📁 详细报告已保存在: output/Delaunay_interpolation/nse_analysis/")
        else:
            print("\n✅ 未发现NSE < -10的记录，系统表现良好")
        
        print("="*80)
        
        return 0
        
    except Exception as e:
        print(f"\n❌ NSE监控分析失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

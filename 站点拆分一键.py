import os
import pandas as pd

# 定义基础路径
base_path = r"D:/pythondata/spline/"
# 定义输出目录
output_base_dir = os.path.join(base_path, "all/点雨量")
if not os.path.exists(output_base_dir):
    os.makedirs(output_base_dir)

# 用于存储每个站点的数据
site_data_dict = {}

# 遍历年份文件夹
for year_folder in os.listdir(base_path):
    year_folder_path = os.path.join(base_path, year_folder)
    if os.path.isdir(year_folder_path):
        print(f"处理年份文件夹: {year_folder_path}")
        # 定义地点文件列表
        location_filenames = ['大化点雨量.xlsx', '水晏点雨量.xlsx', '太平点雨量.xlsx']
        for filename in location_filenames:
            input_file_path = os.path.join(year_folder_path, filename)
            if os.path.exists(input_file_path):
                try:
                    # 读取 Excel 文件
                    excel_file = pd.ExcelFile(input_file_path)
                    df = excel_file.parse('JSON')
                    if df.empty:
                        print(f"{input_file_path} 数据为空，跳过处理。")
                        continue
                    # 按站点拆分数据
                    for column in df.columns[:-1]:
                        single_site_df = df[['tm', column]]
                        single_site_df.columns = ['时间', '雨量']
                        if column not in site_data_dict:
                            site_data_dict[column] = single_site_df
                        else:
                            site_data_dict[column] = pd.concat([site_data_dict[column], single_site_df], ignore_index=True)
                except Exception as e:
                    print(f"处理 {input_file_path} 时出错: {e}")

# 将合并后的数据保存到文件
for site, data in site_data_dict.items():
    output_file_path = os.path.join(output_base_dir, f'{site}.csv')
    data.to_csv(output_file_path, index=False)

print("处理完成，合并后的数据已保存到 all/点雨量 目录下。")
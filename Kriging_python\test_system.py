#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Kriging插值系统测试脚本
用于验证系统功能是否正常
"""

import os
import sys
import time
import logging

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        # 测试核心模块导入
        from config import Config
        from data_processing import DataProcessor
        from delaunay_triangulation import DelaunayTriangulation
        from moran_index import MoranIndex
        from kriging_core import KrigingCore
        from evaluation_metrics import EvaluationMetrics
        from kriging_main import KrigingInterpolation
        
        print("✅ 所有核心模块导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def test_dependencies():
    """测试依赖包"""
    print("测试依赖包...")
    
    required_packages = [
        'numpy', 'pandas', 'scipy', 'matplotlib', 'scikit-learn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包: {missing_packages}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖包检查通过")
    return True


def test_config():
    """测试配置系统"""
    print("测试配置系统...")
    
    try:
        from config import Config, save_config_to_file, load_config_from_file
        
        # 创建配置
        config = Config()
        print(f"✅ 配置创建成功")
        
        # 测试配置保存和加载
        test_config_file = "test_config.json"
        save_config_to_file(config, test_config_file)
        
        if os.path.exists(test_config_file):
            loaded_config = load_config_from_file(test_config_file)
            print("✅ 配置保存和加载成功")
            
            # 清理测试文件
            os.remove(test_config_file)
            
            return True
        else:
            print("❌ 配置文件保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置系统测试失败: {e}")
        return False


def test_data_paths():
    """测试数据路径"""
    print("测试数据路径...")
    
    try:
        from config import Config
        config = Config()
        
        # 检查关键路径
        paths_to_check = [
            ("站点文件", config.stations_file),
            ("输入根目录", config.batch_input_root),
            ("地形目录", config.terrain_dir)
        ]
        
        all_exist = True
        
        for name, path in paths_to_check:
            if os.path.exists(path):
                print(f"✅ {name}: {path}")
            else:
                print(f"⚠️  {name}不存在: {path}")
                if name == "站点文件":
                    all_exist = False
        
        if all_exist:
            print("✅ 关键数据路径检查通过")
        else:
            print("⚠️  部分数据路径不存在，请检查配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据路径测试失败: {e}")
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("测试基本功能...")
    
    try:
        from config import Config
        from data_processing import DataProcessor
        from delaunay_triangulation import DelaunayTriangulation
        from kriging_core import KrigingCore
        
        # 创建配置
        config = Config()
        config.debug_mode = True
        config.verbose_logging = False
        
        # 测试数据处理器
        data_processor = DataProcessor(config)
        print("✅ 数据处理器创建成功")
        
        # 测试三角网构建器
        delaunay_tri = DelaunayTriangulation(config)
        print("✅ Delaunay三角网构建器创建成功")
        
        # 测试Kriging核心
        kriging_core = KrigingCore(config)
        print("✅ Kriging核心算法创建成功")
        
        # 测试半变异函数
        variogram_func = kriging_core.get_variogram_function()
        print("✅ 半变异函数获取成功")
        
        print("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False


def test_sample_data():
    """测试样本数据处理"""
    print("测试样本数据处理...")
    
    try:
        import numpy as np
        import pandas as pd
        from config import Config
        from kriging_core import KrigingCore
        
        # 创建样本数据
        np.random.seed(42)
        
        # 样本站点坐标
        coords = np.array([
            [113.0, 23.0],
            [113.1, 23.1],
            [113.2, 23.0],
            [113.1, 22.9]
        ])
        
        # 样本降雨值
        values = np.array([10.5, 8.2, 12.1, 9.8])
        
        # 目标点
        target_coord = np.array([113.05, 23.05])
        
        # 测试Kriging插值
        config = Config()
        kriging_core = KrigingCore(config)
        
        predicted_value, variance = kriging_core.ordinary_kriging(
            target_coord, coords, values
        )
        
        print(f"✅ 样本插值成功: 预测值={predicted_value:.2f}, 方差={variance:.2f}")
        
        if 0 <= predicted_value <= 20:  # 合理的降雨值范围
            print("✅ 插值结果合理")
            return True
        else:
            print("⚠️  插值结果可能不合理")
            return True  # 仍然算作通过，因为功能正常
        
    except Exception as e:
        print(f"❌ 样本数据测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("="*60)
    print("           Kriging插值系统测试")
    print("="*60)
    
    tests = [
        ("依赖包检查", test_dependencies),
        ("模块导入", test_imports),
        ("配置系统", test_config),
        ("数据路径", test_data_paths),
        ("基本功能", test_basic_functionality),
        ("样本数据", test_sample_data)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        
        start_time = time.time()
        success = test_func()
        end_time = time.time()
        
        results.append((test_name, success, end_time - start_time))
        
        if success:
            print(f"✅ {test_name} 通过 ({end_time - start_time:.2f}秒)")
        else:
            print(f"❌ {test_name} 失败 ({end_time - start_time:.2f}秒)")
    
    # 输出测试总结
    print("\n" + "="*60)
    print("           测试结果总结")
    print("="*60)
    
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    
    for test_name, success, duration in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:15} {status:8} ({duration:.2f}秒)")
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统可以正常使用。")
        print("\n下一步:")
        print("1. 运行 python run_kriging.py 开始使用系统")
        print("2. 或者运行 python run_kriging.py --setup 进行交互式设置")
        return True
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关问题。")
        return False


if __name__ == "__main__":
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程出错: {e}")
        sys.exit(1)

# Molan指数分析系统技术参数配置说明

## 1. 系统配置参数

### 1.1 文件路径配置

```python
# 主要文件路径
excel_file = "../水晏泰森.xlsx"          # 站点信息文件
stations_file = "../stations.csv"        # 站点坐标文件  
input_dir = "../input_another"           # 洪水事件数据目录
output_dir = "../output/molan"           # 结果输出目录
```

### 1.2 站点分组配置

```python
# 站点分组定义
grouped_stations = {
    '整体': ['所有站点'],                    # 包含所有可用站点
    '大化': ['dahua_stations'],             # 大化县站点（行2-19）
    '太平': ['taiping_stations'],           # 太平镇站点（行20-34）
    '水晏': ['shuiyan_stations']            # 水晏镇站点（行35-40）
}
```

### 1.3 站点剔除配置

```python
# 站点剔除开关
exclude_stations = False                   # True: 启用剔除, False: 保留所有站点

# 各组剔除站点列表（当exclude_stations=True时生效）
excluded_stations = {
    '整体': [],                           # 整体组剔除的站点
    '大化': [],                           # 大化组剔除的站点  
    '太平': [],                           # 太平组剔除的站点
    '水晏': []                            # 水晏组剔除的站点
}
```

## 2. 计算参数配置

### 2.1 莫兰指数计算参数

```python
# 核心计算参数
n_permutations = 99                       # 置换检验次数（建议范围：99-999）
min_data_points = 10                      # 最少有效数据点数
min_std_threshold = 0                     # 最小标准差阈值
distance_epsilon = 1e-10                  # 避免除零的小值
```

### 2.2 空间权重参数

```python
# 权重计算方法
weight_method = 'inverse_distance'        # 'inverse_distance' 或 'binary'

# 反距离权重参数
power_parameter = 1.0                     # 距离幂次参数（通常为1-2）

# 二元权重参数  
distance_threshold = 50                   # 距离阈值（公里）
```

### 2.3 地理计算参数

```python
# Haversine距离计算参数
earth_radius = 6371                       # 地球半径（公里）
coordinate_precision = 6                  # 坐标精度（小数位数）
```

## 3. 显著性检验参数

### 3.1 显著性水平定义

```python
# p值阈值设置
significance_levels = {
    'extreme': 0.001,                     # 极显著 (***)
    'high': 0.01,                         # 高显著 (**)  
    'moderate': 0.05,                     # 显著 (*)
    'marginal': 0.1,                      # 边际显著 (.)
    'non_significant': 1.0                # 不显著 (NS)
}
```

### 3.2 检验方法参数

```python
# 置换检验参数
permutation_method = 'random'             # 置换方法：'random' 或 'systematic'
random_seed = None                        # 随机种子（None为随机，整数为固定种子）
test_type = 'two_tailed'                  # 检验类型：'two_tailed', 'one_tailed_positive', 'one_tailed_negative'
```

## 4. 数据处理参数

### 4.1 数据质量控制

```python
# 数据验证参数
allow_zero_values = True                  # 是否允许零值
missing_value_threshold = 0.5             # 缺失值比例阈值
outlier_detection = False                 # 是否启用异常值检测
outlier_method = 'iqr'                    # 异常值检测方法：'iqr', 'zscore', 'isolation'
```

### 4.2 数据标准化参数

```python
# 标准化方法
normalization_method = 'zscore'           # 'zscore', 'minmax', 'robust'
center_data = True                        # 是否中心化数据
scale_data = True                         # 是否缩放数据
```

## 5. 输出控制参数

### 5.1 结果保存参数

```python
# 文件输出控制
save_detailed_results = True              # 保存详细结果
save_summary_statistics = True            # 保存汇总统计
save_significance_summary = True          # 保存显著性汇总
save_individual_events = True             # 保存单个事件结果
save_group_summaries = True               # 保存分组汇总
```

### 5.2 文件格式参数

```python
# 输出格式设置
output_encoding = 'utf-8-sig'             # 文件编码
decimal_places = 4                        # 小数位数
date_format = '%Y-%m-%d %H:%M:%S'         # 日期格式
include_index = False                     # 是否包含行索引
```

## 6. 性能优化参数

### 6.1 内存管理参数

```python
# 内存控制
max_memory_usage = '2GB'                  # 最大内存使用量
chunk_size = 1000                         # 数据块大小
garbage_collection = True                 # 是否启用垃圾回收
```

### 6.2 并行计算参数

```python
# 并行处理（扩展功能）
enable_parallel = False                   # 是否启用并行计算
n_jobs = -1                              # 并行作业数（-1为使用所有CPU核心）
parallel_backend = 'threading'            # 并行后端：'threading', 'multiprocessing'
```

## 7. 调试和日志参数

### 7.1 日志配置

```python
# 日志设置
log_level = 'INFO'                        # 日志级别：'DEBUG', 'INFO', 'WARNING', 'ERROR'
log_to_file = True                        # 是否保存日志到文件
log_file_path = 'molan_analysis.log'      # 日志文件路径
console_output = True                     # 是否输出到控制台
```

### 7.2 调试参数

```python
# 调试控制
debug_mode = False                        # 调试模式
verbose_output = True                     # 详细输出
progress_bar = True                       # 显示进度条
intermediate_results = False              # 保存中间结果
```

## 8. 高级配置选项

### 8.1 算法优化参数

```python
# 算法优化
use_fast_distance = True                  # 使用快速距离计算
cache_weights = True                      # 缓存权重矩阵
optimize_memory = True                    # 内存优化
vectorized_operations = True              # 向量化操作
```

### 8.2 扩展功能参数

```python
# 扩展功能
enable_visualization = False              # 启用可视化（需要额外依赖）
export_to_gis = False                     # 导出GIS格式（需要额外依赖）
statistical_tests = ['permutation']       # 统计检验方法列表
```

## 9. 参数验证规则

### 9.1 数值参数验证

```python
# 参数范围检查
parameter_ranges = {
    'n_permutations': (10, 10000),        # 置换次数范围
    'min_data_points': (5, 1000),         # 最小数据点范围
    'distance_threshold': (1, 1000),      # 距离阈值范围（公里）
    'significance_levels': (0.001, 0.5)   # 显著性水平范围
}
```

### 9.2 字符串参数验证

```python
# 允许的字符串值
allowed_values = {
    'weight_method': ['inverse_distance', 'binary', 'exponential', 'gaussian'],
    'normalization_method': ['zscore', 'minmax', 'robust'],
    'test_type': ['two_tailed', 'one_tailed_positive', 'one_tailed_negative'],
    'log_level': ['DEBUG', 'INFO', 'WARNING', 'ERROR']
}
```

## 10. 配置文件示例

### 10.1 基础配置文件（config.yaml）

```yaml
# Molan指数分析配置文件
analysis:
  n_permutations: 99
  min_data_points: 10
  weight_method: "inverse_distance"
  
paths:
  excel_file: "../水晏泰森.xlsx"
  stations_file: "../stations.csv"
  input_dir: "../input_another"
  output_dir: "../output/molan"
  
significance:
  extreme: 0.001
  high: 0.01
  moderate: 0.05
  marginal: 0.1
  
output:
  encoding: "utf-8-sig"
  decimal_places: 4
  save_detailed: true
  save_summary: true
```

### 10.2 高级配置文件（advanced_config.yaml）

```yaml
# 高级配置选项
performance:
  enable_parallel: false
  n_jobs: -1
  max_memory: "2GB"
  chunk_size: 1000
  
quality_control:
  allow_zero_values: true
  missing_threshold: 0.5
  outlier_detection: false
  
debugging:
  debug_mode: false
  verbose_output: true
  log_level: "INFO"
  log_to_file: true
```

## 11. 参数调优建议

### 11.1 性能调优

- **置换次数**: 99次适合快速分析，999次适合精确分析
- **数据点数**: 根据数据质量调整最小数据点要求
- **内存设置**: 根据系统内存容量调整最大使用量

### 11.2 精度调优

- **显著性水平**: 根据研究要求调整p值阈值
- **权重方法**: 根据站点分布特征选择合适方法
- **距离阈值**: 根据研究区域大小调整

### 11.3 稳定性调优

- **异常值处理**: 在数据质量较差时启用
- **缺失值阈值**: 根据数据完整性调整
- **随机种子**: 设置固定种子确保结果可重现

---

**文档版本**: 1.0  
**创建日期**: 2025-06-16  
**适用版本**: 空间插值系统 v1.0

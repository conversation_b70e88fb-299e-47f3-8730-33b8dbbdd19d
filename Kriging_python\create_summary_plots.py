#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建Kriging插值系统汇总可视化图表
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_summary_plots():
    """创建汇总可视化图表"""
    
    # 读取详细结果数据
    data_file = "D:/pythondata/spatial_interpolation/output/Kriging/batch_summary/batch_detailed_results_20250611_150156.csv"
    
    if not os.path.exists(data_file):
        print(f"数据文件不存在: {data_file}")
        return
    
    df = pd.read_csv(data_file)
    
    # 创建图形
    fig = plt.figure(figsize=(20, 16))
    
    # 1. NSE分布直方图
    plt.subplot(3, 3, 1)
    plt.hist(df['NSE'], bins=15, alpha=0.7, color='skyblue', edgecolor='black')
    plt.axvline(x=0.75, color='red', linestyle='--', label='优秀线(0.75)')
    plt.axvline(x=0.5, color='orange', linestyle='--', label='可接受线(0.5)')
    plt.xlabel('NSE值')
    plt.ylabel('频数')
    plt.title('NSE分布直方图')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. NSE时间序列
    plt.subplot(3, 3, 2)
    years = [int(folder.split('-')[0]) for folder in df['folder_name']]
    plt.scatter(years, df['NSE'], alpha=0.7, s=60)
    plt.axhline(y=0.75, color='red', linestyle='--', alpha=0.7)
    plt.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7)
    plt.xlabel('年份')
    plt.ylabel('NSE')
    plt.title('NSE时间趋势')
    plt.grid(True, alpha=0.3)
    
    # 3. RMSE vs NSE散点图
    plt.subplot(3, 3, 3)
    plt.scatter(df['NSE'], df['RMSE'], alpha=0.7, s=60)
    plt.xlabel('NSE')
    plt.ylabel('RMSE (mm)')
    plt.title('NSE vs RMSE关系')
    plt.grid(True, alpha=0.3)
    
    # 4. 年度性能对比
    plt.subplot(3, 3, 4)
    year_stats = []
    for year in sorted(set(years)):
        year_data = df[df['folder_name'].str.startswith(str(year))]
        year_stats.append({
            'year': year,
            'mean_nse': year_data['NSE'].mean(),
            'count': len(year_data)
        })
    
    year_df = pd.DataFrame(year_stats)
    bars = plt.bar(year_df['year'], year_df['mean_nse'], alpha=0.7)
    plt.axhline(y=0.75, color='red', linestyle='--', alpha=0.7)
    plt.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7)
    plt.xlabel('年份')
    plt.ylabel('平均NSE')
    plt.title('年度平均NSE对比')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 在柱子上标注事件数量
    for bar, count in zip(bars, year_df['count']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{count}', ha='center', va='bottom', fontsize=8)
    
    # 5. 性能等级饼图
    plt.subplot(3, 3, 5)
    excellent = len(df[df['NSE'] > 0.75])
    good = len(df[(df['NSE'] > 0.65) & (df['NSE'] <= 0.75)])
    acceptable = len(df[(df['NSE'] > 0.5) & (df['NSE'] <= 0.65)])
    poor = len(df[df['NSE'] <= 0.5])
    
    labels = ['优秀\n(NSE>0.75)', '良好\n(0.65<NSE≤0.75)', '可接受\n(0.5<NSE≤0.65)', '较差\n(NSE≤0.5)']
    sizes = [excellent, good, acceptable, poor]
    colors = ['green', 'lightgreen', 'yellow', 'lightcoral']
    
    plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    plt.title('性能等级分布')
    
    # 6. 处理时间分布
    plt.subplot(3, 3, 6)
    plt.hist(df['processing_time'], bins=15, alpha=0.7, color='lightgreen', edgecolor='black')
    plt.xlabel('处理时间 (秒)')
    plt.ylabel('频数')
    plt.title('处理时间分布')
    plt.grid(True, alpha=0.3)
    
    # 7. 相关系数分布
    plt.subplot(3, 3, 7)
    plt.hist(df['CORR'], bins=15, alpha=0.7, color='purple', edgecolor='black')
    plt.xlabel('相关系数')
    plt.ylabel('频数')
    plt.title('相关系数分布')
    plt.grid(True, alpha=0.3)
    
    # 8. 零值比例分析
    plt.subplot(3, 3, 8)
    plt.scatter(df['ZERO_RATIO_OBS'], df['NSE'], alpha=0.7, s=60)
    plt.xlabel('零值比例')
    plt.ylabel('NSE')
    plt.title('零值比例 vs NSE')
    plt.grid(True, alpha=0.3)
    
    # 9. 最佳和最差事件对比
    plt.subplot(3, 3, 9)
    top_5 = df.nlargest(5, 'NSE')
    bottom_5 = df.nsmallest(5, 'NSE')
    
    x_pos = np.arange(5)
    width = 0.35
    
    plt.bar(x_pos - width/2, top_5['NSE'], width, label='最佳5个', color='green', alpha=0.7)
    plt.bar(x_pos + width/2, bottom_5['NSE'], width, label='最差5个', color='red', alpha=0.7)
    
    plt.xlabel('排名')
    plt.ylabel('NSE')
    plt.title('最佳vs最差事件对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图形
    output_path = "Kriging插值系统汇总可视化.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"汇总可视化图表已保存: {output_path}")
    
    # 创建详细的事件排名图
    create_detailed_ranking_plot(df)


def create_detailed_ranking_plot(df):
    """创建详细的事件排名图"""
    
    # 按NSE排序
    df_sorted = df.sort_values('NSE', ascending=False)
    
    # 创建图形
    plt.figure(figsize=(16, 10))
    
    # 颜色映射
    colors = []
    for nse in df_sorted['NSE']:
        if nse > 0.75:
            colors.append('green')
        elif nse > 0.65:
            colors.append('lightgreen')
        elif nse > 0.5:
            colors.append('yellow')
        else:
            colors.append('lightcoral')
    
    # 创建条形图
    bars = plt.bar(range(len(df_sorted)), df_sorted['NSE'], color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)
    
    # 添加水平线
    plt.axhline(y=0.75, color='red', linestyle='--', linewidth=2, label='优秀线(0.75)')
    plt.axhline(y=0.65, color='orange', linestyle='--', linewidth=2, label='良好线(0.65)')
    plt.axhline(y=0.5, color='blue', linestyle='--', linewidth=2, label='可接受线(0.5)')
    plt.axhline(y=0, color='black', linestyle='-', linewidth=1)
    
    # 设置标签
    plt.xlabel('洪水事件排名', fontsize=12)
    plt.ylabel('NSE值', fontsize=12)
    plt.title('Kriging插值系统 - 43个洪水事件NSE排名', fontsize=14, fontweight='bold')
    
    # 设置x轴标签
    plt.xticks(range(len(df_sorted)), df_sorted['folder_name'], rotation=45, ha='right')
    
    # 添加图例
    plt.legend(loc='upper right')
    
    # 添加网格
    plt.grid(True, alpha=0.3, axis='y')
    
    # 在最佳和最差的几个事件上添加数值标签
    for i in range(min(5, len(df_sorted))):  # 前5个
        plt.text(i, df_sorted.iloc[i]['NSE'] + 0.02, f'{df_sorted.iloc[i]["NSE"]:.3f}', 
                ha='center', va='bottom', fontsize=8, fontweight='bold')
    
    for i in range(max(0, len(df_sorted)-5), len(df_sorted)):  # 后5个
        plt.text(i, df_sorted.iloc[i]['NSE'] + 0.02, f'{df_sorted.iloc[i]["NSE"]:.3f}', 
                ha='center', va='bottom', fontsize=8, fontweight='bold')
    
    plt.tight_layout()
    
    # 保存图形
    output_path = "洪水事件NSE排名图.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"事件排名图已保存: {output_path}")


def create_performance_summary_table(df):
    """创建性能汇总表"""
    
    # 计算统计信息
    stats = {
        '指标': ['NSE', 'RMSE', 'MAE', 'R²', '相关系数', '处理时间'],
        '平均值': [
            f"{df['NSE'].mean():.4f}",
            f"{df['RMSE'].mean():.4f}",
            f"{df['MAE'].mean():.4f}",
            f"{df['R2'].mean():.4f}",
            f"{df['CORR'].mean():.4f}",
            f"{df['processing_time'].mean():.2f}"
        ],
        '标准差': [
            f"{df['NSE'].std():.4f}",
            f"{df['RMSE'].std():.4f}",
            f"{df['MAE'].std():.4f}",
            f"{df['R2'].std():.4f}",
            f"{df['CORR'].std():.4f}",
            f"{df['processing_time'].std():.2f}"
        ],
        '最小值': [
            f"{df['NSE'].min():.4f}",
            f"{df['RMSE'].min():.4f}",
            f"{df['MAE'].min():.4f}",
            f"{df['R2'].min():.4f}",
            f"{df['CORR'].min():.4f}",
            f"{df['processing_time'].min():.2f}"
        ],
        '最大值': [
            f"{df['NSE'].max():.4f}",
            f"{df['RMSE'].max():.4f}",
            f"{df['MAE'].max():.4f}",
            f"{df['R2'].max():.4f}",
            f"{df['CORR'].max():.4f}",
            f"{df['processing_time'].max():.2f}"
        ]
    }
    
    stats_df = pd.DataFrame(stats)
    
    # 保存到CSV
    output_path = "性能统计汇总表.csv"
    stats_df.to_csv(output_path, index=False, encoding='utf-8')

    print(f"性能统计表已保存: {output_path}")
    
    return stats_df


if __name__ == "__main__":
    try:
        print("开始创建汇总可视化图表...")
        create_summary_plots()
        
        # 读取数据并创建统计表
        data_file = "D:/pythondata/spatial_interpolation/output/Kriging/batch_summary/batch_detailed_results_20250611_150156.csv"
        if os.path.exists(data_file):
            df = pd.read_csv(data_file)
            create_performance_summary_table(df)
        
        print("所有图表和统计表创建完成！")
        
    except Exception as e:
        print(f"创建图表失败: {e}")

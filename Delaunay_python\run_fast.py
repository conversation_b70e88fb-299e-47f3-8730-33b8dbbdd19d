#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay插值系统快速运行脚本

跳过可视化，只进行插值和评估，解决2015年后文件夹为空的问题
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from pathlib import Path
import warnings
import json
import gc

warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from config import DelaunayConfig
from data_loader import DelaunayDataLoader
from evaluation_metrics import EvaluationMetrics
from delaunay_interpolator import DelaunayInterpolator

def setup_logging(config):
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("Delaunay插值系统快速运行启动")
    return logger

def save_results(results, output_dir, filename):
    """保存结果"""
    try:
        filepath = output_dir / filename
        
        if filename.endswith('.csv'):
            results.to_csv(filepath, index=True, encoding='utf-8', float_format='%.6f')
        elif filename.endswith('.json'):
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        logging.info(f"结果已保存: {filepath}")
        
    except Exception as e:
        logging.error(f"保存结果失败: {e}")

def process_event_fast(event_name, interpolator, evaluator, config):
    """快速处理单个洪水事件（无可视化）"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"开始处理洪水事件: {event_name}")
        
        # 创建事件输出目录
        event_output_dir = config.get_event_output_dir(event_name)
        
        # 进行插值
        event_results = interpolator.interpolate_flood_event(event_name, memory_efficient=True)
        
        if not event_results:
            logger.warning(f"洪水事件{event_name}无插值结果")
            return None
        
        # 计算评估指标
        event_metrics = evaluator.evaluate_event_performance(event_results)
        
        # 只保存关键结果（不保存详细的插值结果以节省空间和时间）
        try:
            # 保存评估指标
            metrics_df = pd.DataFrame(event_metrics).T
            save_results(metrics_df, event_output_dir, f"{event_name}_metrics.csv")
            del metrics_df
            
            # 计算并保存汇总统计
            summary_stats = evaluator.calculate_summary_statistics(event_metrics)
            save_results(summary_stats, event_output_dir, f"{event_name}_summary.json")
            
            logger.info(f"洪水事件{event_name}处理完成")
            logger.info(f"  - 处理站点数: {len(event_results)}")
            logger.info(f"  - 平均NSE: {summary_stats.get('NSE_mean', np.nan):.4f}")
            logger.info(f"  - NSE>0.7比例: {summary_stats.get('NSE_good_ratio', 0):.1%}")
            
            # 创建轻量级返回结果
            result = {
                'event_name': event_name,
                'metrics': event_metrics,
                'summary': summary_stats,
                'station_count': len(event_results)
            }
            
            # 立即清理大型数据
            del event_results
            gc.collect()
            
            return result
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            return None
        
    except Exception as e:
        logger.error(f"处理洪水事件{event_name}失败: {e}")
        gc.collect()
        return None

def main():
    """主函数"""
    try:
        print("Delaunay插值系统快速运行启动...")
        
        # 初始化配置（强制优化设置）
        config = DelaunayConfig()
        config.MEMORY_EFFICIENT_MODE = True
        config.MAX_EVENTS_IN_MEMORY = 1
        config.CHUNK_SIZE = 50
        config.BATCH_SIZE = 5
        config.GENERATE_PLOTS = False  # 关闭图表生成
        config.SAVE_DETAILED_RESULTS = False  # 关闭详细结果保存
        
        # 验证配置
        config_errors = config.validate_config()
        if config_errors:
            print("配置验证失败:")
            for error in config_errors:
                print(f"  - {error}")
            return 1
        
        # 设置日志
        logger = setup_logging(config)
        logger.info("快速运行模式已启用（无可视化）")
        
        # 初始化组件
        data_loader = DelaunayDataLoader(config)
        evaluator = EvaluationMetrics(config)
        interpolator = DelaunayInterpolator(config, data_loader, evaluator)
        
        # 加载Delaunay分析结果
        logger.info("加载Delaunay分析结果...")
        delaunay_analysis = data_loader.load_delaunay_analysis()
        logger.info(f"成功加载{len(delaunay_analysis)}个站点的分析结果")
        
        # 获取洪水事件列表
        flood_events = config.get_flood_events()
        logger.info(f"发现{len(flood_events)}个洪水事件")
        
        if not flood_events:
            logger.warning("未发现洪水事件，程序退出")
            return 1
        
        # 处理所有洪水事件
        all_results = []
        
        for i, event_name in enumerate(flood_events):
            logger.info(f"处理洪水事件 {i+1}/{len(flood_events)}: {event_name}")
            
            result = process_event_fast(event_name, interpolator, evaluator, config)
            
            if result:
                all_results.append(result)
            
            # 每处理一个事件就清理内存
            if hasattr(data_loader, 'clear_cache'):
                data_loader.clear_cache()
            gc.collect()
            
            # 记录进度
            logger.info(f"已完成 {i+1}/{len(flood_events)} 个事件")
        
        # 生成最终报告
        logger.info("生成最终报告...")
        
        # 收集所有指标
        all_metrics = {}
        overall_summary = {
            'total_events': len(all_results),
            'total_stations': 0,
            'events_summary': {}
        }
        
        for result in all_results:
            if result is None:
                continue
                
            event_name = result['event_name']
            event_metrics = result['metrics']
            event_summary = result['summary']
            
            all_metrics[event_name] = event_metrics
            overall_summary['events_summary'][event_name] = event_summary
            overall_summary['total_stations'] += len(event_metrics)
        
        # 计算总体统计
        all_station_metrics = {}
        for event_metrics in all_metrics.values():
            all_station_metrics.update(event_metrics)
        
        overall_stats = evaluator.calculate_summary_statistics(all_station_metrics)
        overall_summary['overall_statistics'] = overall_stats
        
        # 保存总体报告
        save_results(overall_summary, config.OUTPUT_DIR, "overall_report.json")
        
        # 生成详细的总体指标表
        all_metrics_df = pd.DataFrame()
        for event_name, event_metrics in all_metrics.items():
            for station_id, station_metrics in event_metrics.items():
                row = station_metrics.copy()
                row['event_name'] = event_name
                row['station_id'] = station_id
                all_metrics_df = pd.concat([all_metrics_df, pd.DataFrame([row])], ignore_index=True)
        
        if not all_metrics_df.empty:
            save_results(all_metrics_df, config.OUTPUT_DIR, "all_stations_metrics.csv")
        
        # 打印总体统计
        logger.info("="*60)
        logger.info("Delaunay插值系统总体性能报告（快速运行）")
        logger.info("="*60)
        logger.info(f"处理洪水事件数: {overall_summary['total_events']}")
        logger.info(f"总站点数: {overall_summary['total_stations']}")
        logger.info(f"平均NSE: {overall_stats.get('NSE_mean', np.nan):.4f}")
        logger.info(f"NSE>0.7站点数: {overall_stats.get('good_nse_stations', 0)}")
        logger.info(f"NSE>0.7比例: {overall_stats.get('NSE_good_ratio', 0):.1%}")
        logger.info(f"平均MAE: {overall_stats.get('MAE_mean', np.nan):.4f}")
        logger.info(f"平均RMSE: {overall_stats.get('RMSE_mean', np.nan):.4f}")
        logger.info("="*60)
        
        logger.info("Delaunay插值系统快速运行完成！")
        logger.info(f"结果保存在: {config.OUTPUT_DIR}")
        
        return 0
        
    except Exception as e:
        print(f"程序运行失败: {e}")
        if 'logger' in locals():
            logger.error(f"程序运行失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

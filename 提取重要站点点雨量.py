import os 
import shutil 
import pandas as pd 
 
# 设置输入和输出路径 
input_base_path = 'D:/pythondata/spline/'  # 包含年份-月份文件夹的根目录 
output_path = 'D:/pythondata/spline/rains/'  # 提取后文件的输出目录 
 
# 创建输出目录(如果不存在)
os.makedirs(output_path,  exist_ok=True)
 
# 遍历输入目录下的所有子目录 
for dir_name in os.listdir(input_base_path): 
    dir_path = os.path.join(input_base_path,  dir_name)
    
    # 检查是否是目录且包含点雨量.csv文件 
    if os.path.isdir(dir_path): 
        source_file = os.path.join(dir_path,  '点雨量.csv')
        
        if os.path.exists(source_file): 
            # 将文件夹名转换为YYYYMM格式(如"2009-1"→"200901")
            year_month = dir_name.replace('-',  '')
            if len(year_month) == 5:  # 处理单数月的情况(如"2009-1")
                year_month = year_month[:4] + '0' + year_month[4:]
            
            # 设置目标文件名 
            dest_file = os.path.join(output_path,  f'{year_month}.csv')
            
            # 复制并重命名文件 
            shutil.copy2(source_file,  dest_file)
            print(f'已提取: {dir_name}/点雨量.csv → {year_month}.csv')
 
print("所有点雨量文件提取完成！")
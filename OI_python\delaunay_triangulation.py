"""
标准化Delaunay三角网模块
统一的Delaunay三角网实现，包含权重计算和记录保存功能
基于Kriging系统的标准实现，适用于所有插值方法
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay, KDTree
from scipy.spatial.distance import cdist
import logging
from typing import List, Dict
import matplotlib
import os
matplotlib.use('Agg')  # 使用非交互式后端

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)


class DelaunayTriangulation:
    """标准化Delaunay三角网构建器

    统一的Delaunay三角网实现，包含：
    - 基础三角网构建
    - 邻站查找
    - 权重计算
    - 留一法验证
    - 记录保存
    - 可视化功能
    """

    def __init__(self, config):
        """初始化Delaunay三角网构建器"""
        self.config = config
        self.triangulation = None
        self.points = None
        self.station_indices = {}
        self.stations_df = None
        self.triangle_records = []
        self.kdtree = None  # 为PRISM系统兼容性添加
        
    def build_triangulation(self, stations_df: pd.DataFrame) -> Delaunay:
        """构建标准化Delaunay三角网"""
        try:
            logger.info("构建标准化Delaunay三角网...")

            # 保存站点数据
            self.stations_df = stations_df.copy()

            # 提取站点坐标（适配英文列名）
            points = stations_df[['longitude', 'latitude']].values
            station_names = stations_df['station_id'].values

            # 创建站点索引映射
            self.station_indices = {name: idx for idx, name in enumerate(station_names)}

            # 构建Delaunay三角网
            tri = Delaunay(points)

            # 验证三角网质量
            self._validate_triangulation_quality(tri, points)

            self.triangulation = tri
            self.points = points

            # 构建KDTree用于PRISM系统兼容性
            self.kdtree = KDTree(points)

            # 初始化记录
            self.triangle_records = []

            logger.info(f"成功构建标准化Delaunay三角网: {len(points)} 个站点, {len(tri.simplices)} 个三角形")

            return tri

        except Exception as e:
            logger.error(f"构建Delaunay三角网失败: {e}")
            raise
    
    def _validate_triangulation_quality(self, tri: Delaunay, points: np.ndarray):
        """验证三角网质量"""
        try:
            # 计算三角形角度
            angles = []
            
            for simplex in tri.simplices:
                triangle_points = points[simplex]
                triangle_angles = self._calculate_triangle_angles(triangle_points)
                angles.extend(triangle_angles)
            
            angles = np.array(angles)
            min_angle = np.min(angles)
            mean_angle = np.mean(angles)
            
            logger.info(f"三角网质量统计:")
            logger.info(f"  最小角度: {min_angle:.2f}°")
            logger.info(f"  平均角度: {mean_angle:.2f}°")
            logger.info(f"  角度标准差: {np.std(angles):.2f}°")
            
            if min_angle < self.config.min_triangle_angle:
                logger.warning(f"存在过小的三角形角度 ({min_angle:.2f}°)，可能影响插值质量")
            
        except Exception as e:
            logger.warning(f"三角网质量验证失败: {e}")
    
    def _calculate_triangle_angles(self, triangle_points: np.ndarray) -> List[float]:
        """计算三角形的三个角度"""
        try:
            p1, p2, p3 = triangle_points
            
            # 计算边长
            a = np.linalg.norm(p2 - p3)  # 对边a
            b = np.linalg.norm(p1 - p3)  # 对边b
            c = np.linalg.norm(p1 - p2)  # 对边c
            
            # 使用余弦定理计算角度
            angle_A = np.arccos(np.clip((b**2 + c**2 - a**2) / (2*b*c), -1, 1))
            angle_B = np.arccos(np.clip((a**2 + c**2 - b**2) / (2*a*c), -1, 1))
            angle_C = np.arccos(np.clip((a**2 + b**2 - c**2) / (2*a*b), -1, 1))
            
            # 转换为度
            angles = [np.degrees(angle_A), np.degrees(angle_B), np.degrees(angle_C)]
            
            return angles
            
        except Exception as e:
            logger.warning(f"计算三角形角度失败: {e}")
            return [60.0, 60.0, 60.0]  # 返回默认值
    
    def find_neighbors_by_triangulation(self, target_station: str, 
                                      stations_df: pd.DataFrame,
                                      max_neighbors: int = None) -> List[int]:
        """基于Delaunay三角网找到邻近站点"""
        try:
            if self.triangulation is None:
                raise ValueError("Delaunay三角网尚未构建")
            
            if max_neighbors is None:
                max_neighbors = self.config.neighbor_count
            
            # 获取目标站点索引
            if target_station not in self.station_indices:
                raise ValueError(f"目标站点 {target_station} 不在三角网中")
            
            target_idx = self.station_indices[target_station]
            
            # 找到包含目标站点的所有三角形
            neighbor_indices = set()
            
            for simplex in self.triangulation.simplices:
                if target_idx in simplex:
                    # 添加三角形中的其他顶点
                    for idx in simplex:
                        if idx != target_idx:
                            neighbor_indices.add(idx)
            
            neighbor_indices = list(neighbor_indices)
            
            # 如果邻居数量超过限制，按距离排序选择最近的
            if len(neighbor_indices) > max_neighbors:
                target_point = self.points[target_idx]
                neighbor_points = self.points[neighbor_indices]
                
                # 计算距离
                distances = np.linalg.norm(neighbor_points - target_point, axis=1)
                
                # 按距离排序
                sorted_indices = np.argsort(distances)
                neighbor_indices = [neighbor_indices[i] for i in sorted_indices[:max_neighbors]]
            
            logger.debug(f"站点 {target_station} 的邻居数量: {len(neighbor_indices)}")
            
            return neighbor_indices

        except Exception as e:
            logger.error(f"基于三角网查找邻居失败: {e}")
            # 备选方案：使用距离查找
            return self._find_neighbors_by_distance(target_station, stations_df, max_neighbors)

    def find_containing_triangle_with_weights(self, test_point: np.ndarray,
                                            remaining_coords: np.ndarray,
                                            remaining_ids: np.ndarray,
                                            power: float = 2.0) -> Dict:
        """
        找到包含测试点的三角形并计算权重（留一法专用）

        参数:
            test_point: 测试点坐标 (2,)
            remaining_coords: 剩余站点坐标 (n-1, 2)
            remaining_ids: 剩余站点ID (n-1,)
            power: IDW权重指数

        返回:
            dict: 包含三角形顶点信息和权重的字典
        """
        try:
            # 构建剩余站点的三角网
            if len(remaining_coords) < 3:
                raise ValueError("剩余站点数量不足3个，无法构建三角形")

            tri = Delaunay(remaining_coords)

            # 找到包含测试点的三角形
            simplex_index = tri.find_simplex(test_point)

            if simplex_index == -1:
                # 测试点不在任何三角形内，找最近的三角形
                logger.debug(f"测试点 {test_point} 不在三角网内，寻找最近三角形")
                simplex_index = self._find_nearest_triangle(test_point, tri, remaining_coords)

            # 获取三角形顶点
            triangle_vertices = tri.simplices[simplex_index]
            vertex_coords = remaining_coords[triangle_vertices]
            vertex_ids = remaining_ids[triangle_vertices]

            # 计算权重
            weights_info = self._calculate_weights(test_point, vertex_coords, power)

            result = {
                'triangle_index': simplex_index,
                'vertex_coords': vertex_coords,
                'vertex_ids': vertex_ids,
                'vertex_indices': triangle_vertices,
                'distances': weights_info['distances'],
                'weights': weights_info['weights'],
                'test_point': test_point,
                'power': power
            }

            return result

        except Exception as e:
            logger.error(f"寻找包含三角形失败: {e}")
            # 返回最近的3个站点作为备选方案
            return self._fallback_nearest_stations(test_point, remaining_coords, remaining_ids, power)

    def _find_nearest_triangle(self, test_point: np.ndarray, tri: Delaunay, coords: np.ndarray) -> int:
        """找到距离测试点最近的三角形"""
        min_distance = float('inf')
        nearest_triangle = 0

        for i, simplex in enumerate(tri.simplices):
            # 计算测试点到三角形重心的距离
            triangle_coords = coords[simplex]
            centroid = np.mean(triangle_coords, axis=0)
            dist = np.linalg.norm(test_point - centroid)

            if dist < min_distance:
                min_distance = dist
                nearest_triangle = i

        return nearest_triangle

    def _calculate_weights(self, test_point: np.ndarray, vertex_coords: np.ndarray, power: float) -> Dict:
        """计算IDW权重"""
        # 计算距离
        distances = cdist([test_point], vertex_coords)[0]

        # 避免除零错误和极小距离导致的权重爆炸
        min_distance = 1e-6  # 最小距离
        distances = np.maximum(distances, min_distance)

        # IDW权重计算
        weights = 1.0 / (distances ** power)

        # 检查权重是否合理
        if np.any(np.isnan(weights)) or np.any(np.isinf(weights)):
            logger.warning(f"权重计算异常，使用等权重。距离: {distances}")
            weights = np.ones(len(distances)) / len(distances)
        else:
            # 归一化权重
            weight_sum = np.sum(weights)
            if weight_sum > 0:
                weights = weights / weight_sum
            else:
                weights = np.ones(len(distances)) / len(distances)

        return {
            'distances': distances,
            'weights': weights
        }

    def _fallback_nearest_stations(self, test_point: np.ndarray, coords: np.ndarray,
                                 station_ids: np.ndarray, power: float) -> Dict:
        """备选方案：找到最近的3个站点"""
        logger.warning("使用备选方案：最近的3个站点")

        # 计算所有站点到测试点的距离
        distances = cdist([test_point], coords)[0]

        # 找到最近的3个站点
        nearest_indices = np.argsort(distances)[:3]

        vertex_coords = coords[nearest_indices]
        vertex_ids = station_ids[nearest_indices]

        # 计算权重
        weights_info = self._calculate_weights(test_point, vertex_coords, power)

        return {
            'triangle_index': -1,  # 表示这是备选方案
            'vertex_coords': vertex_coords,
            'vertex_ids': vertex_ids,
            'vertex_indices': nearest_indices,
            'distances': weights_info['distances'],
            'weights': weights_info['weights'],
            'test_point': test_point,
            'power': power
        }
    
    def _find_neighbors_by_distance(self, target_station: str, 
                                   stations_df: pd.DataFrame,
                                   max_neighbors: int) -> List[int]:
        """备选方案：基于距离查找邻近站点"""
        try:
            logger.info(f"使用距离方法查找站点 {target_station} 的邻居")
            
            # 获取目标站点信息（适配英文列名）
            target_info = stations_df[stations_df['station_id'] == target_station]
            if len(target_info) == 0:
                raise ValueError(f"未找到目标站点: {target_station}")

            target_lon = target_info['longitude'].iloc[0]
            target_lat = target_info['latitude'].iloc[0]

            # 计算到所有其他站点的距离
            other_stations = stations_df[stations_df['station_id'] != target_station].copy()

            distances = np.sqrt(
                (other_stations['longitude'] - target_lon)**2 +
                (other_stations['latitude'] - target_lat)**2
            )
            
            # 按距离排序
            other_stations['距离'] = distances
            nearest_stations = other_stations.nsmallest(max_neighbors, '距离')
            
            # 返回索引
            neighbor_indices = nearest_stations.index.tolist()
            
            logger.debug(f"基于距离找到 {len(neighbor_indices)} 个邻居")
            
            return neighbor_indices
            
        except Exception as e:
            logger.error(f"基于距离查找邻居失败: {e}")
            return []

    def perform_leave_one_out_analysis(self, power: float = 2.0) -> Dict:
        """
        执行留一法分析，为每个站点找到最佳三角网

        参数:
            power: IDW权重指数

        返回:
            dict: 留一法分析结果
        """
        if self.stations_df is None or self.triangulation is None:
            raise ValueError("请先构建三角网")

        logger.info("执行标准化留一法分析...")

        leave_one_out_results = {}
        stations = self.stations_df['station_id'].values
        coordinates = self.points

        for i, target_station in enumerate(stations):
            logger.debug(f"处理站点 {target_station} ({i+1}/{len(stations)})")

            # 创建不包含目标站点的站点列表
            remaining_indices = [j for j in range(len(stations)) if j != i]
            remaining_coords = coordinates[remaining_indices]
            remaining_ids = stations[remaining_indices]

            if len(remaining_coords) < 3:
                logger.warning(f"站点 {target_station} 周围站点不足，跳过")
                continue

            try:
                # 找到包含目标站点的三角形并计算权重
                target_point = coordinates[i]
                triangle_info = self.find_containing_triangle_with_weights(
                    target_point, remaining_coords, remaining_ids, power
                )

                leave_one_out_results[target_station] = {
                    'triangle_vertices': triangle_info['vertex_ids'].tolist(),
                    'vertex_coordinates': triangle_info['vertex_coords'].tolist(),
                    'triangle_index': triangle_info['triangle_index'],
                    'target_coordinates': target_point.tolist(),
                    'distances': triangle_info['distances'].tolist(),
                    'weights': triangle_info['weights'].tolist(),
                    'power': power
                }

                # 记录到三角网记录中
                self.triangle_records.append({
                    '验证站点': target_station,
                    '目标经度': target_point[0],
                    '目标纬度': target_point[1],
                    '顶点1': triangle_info['vertex_ids'][0] if len(triangle_info['vertex_ids']) > 0 else '',
                    '顶点2': triangle_info['vertex_ids'][1] if len(triangle_info['vertex_ids']) > 1 else '',
                    '顶点3': triangle_info['vertex_ids'][2] if len(triangle_info['vertex_ids']) > 2 else '',
                    '三角形索引': triangle_info['triangle_index'],
                    '权重1': triangle_info['weights'][0] if len(triangle_info['weights']) > 0 else 0,
                    '权重2': triangle_info['weights'][1] if len(triangle_info['weights']) > 1 else 0,
                    '权重3': triangle_info['weights'][2] if len(triangle_info['weights']) > 2 else 0,
                    '距离1': triangle_info['distances'][0] if len(triangle_info['distances']) > 0 else 0,
                    '距离2': triangle_info['distances'][1] if len(triangle_info['distances']) > 1 else 0,
                    '距离3': triangle_info['distances'][2] if len(triangle_info['distances']) > 2 else 0,
                    'IDW指数': power
                })

            except Exception as e:
                logger.warning(f"处理站点 {target_station} 时出错: {e}")
                continue

        logger.info(f"留一法分析完成，成功处理 {len(leave_one_out_results)} 个站点")

        return leave_one_out_results

    def save_triangulation_records(self, output_path: str) -> str:
        """
        保存三角网记录到CSV文件

        参数:
            output_path: 输出文件路径

        返回:
            str: 保存的文件路径
        """
        try:
            if not self.triangle_records:
                logger.warning("没有三角网记录可保存，请先执行留一法分析")
                return ""

            logger.info("保存标准化三角网记录...")

            # 保存到CSV
            df = pd.DataFrame(self.triangle_records)
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            df.to_csv(output_path, index=False, encoding='utf-8-sig')

            logger.info(f"三角网记录已保存: {output_path}")
            logger.info(f"共保存 {len(self.triangle_records)} 条记录")

            return output_path

        except Exception as e:
            logger.error(f"保存三角网记录失败: {e}")
            return ""
    
    def plot_triangulation(self, output_path: str = None,
                          leave_one_out_results: Dict = None,
                          show_weights: bool = False) -> str:
        """绘制标准化Delaunay三角网图"""
        try:
            if self.triangulation is None or self.stations_df is None:
                raise ValueError("Delaunay三角网尚未构建")

            logger.info("绘制标准化Delaunay三角网图...")

            # 创建图形
            plt.figure(figsize=(14, 10))

            # 绘制三角网
            plt.triplot(self.points[:, 0], self.points[:, 1],
                       self.triangulation.simplices, 'b-', alpha=0.3, linewidth=0.5)

            # 绘制站点
            plt.scatter(self.points[:, 0], self.points[:, 1],
                       c='red', s=100, alpha=0.8, zorder=5,
                       edgecolors='black', linewidth=1, label='雨量站点')

            # 添加站点标签
            if len(self.stations_df) <= 50:
                for idx, row in self.stations_df.iterrows():
                    plt.annotate(row['station_id'],
                               (row['longitude'], row['latitude']),
                               xytext=(5, 5), textcoords='offset points',
                               fontsize=8, alpha=0.7,
                               bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7))

            # 绘制留一法验证点
            if leave_one_out_results:
                for target_station, result in leave_one_out_results.items():
                    target_coords = result['target_coordinates']
                    plt.scatter(target_coords[0], target_coords[1],
                              c='blue', s=150, marker='*',
                              edgecolors='black', linewidth=2,
                              zorder=6, alpha=0.8,
                              label='验证点' if target_station == list(leave_one_out_results.keys())[0] else "")

                    # 显示权重信息
                    if show_weights and 'weights' in result:
                        weights = result['weights']
                        for i, weight in enumerate(weights):
                            plt.annotate(f'{weight:.3f}',
                                       (target_coords[0], target_coords[1]),
                                       xytext=(10 + i*15, 10 + i*10),
                                       textcoords='offset points',
                                       fontsize=6, color='blue',
                                       bbox=dict(boxstyle='round,pad=0.2', facecolor='lightblue', alpha=0.7))

            plt.xlabel('经度 (°)', fontsize=12)
            plt.ylabel('纬度 (°)', fontsize=12)
            plt.title(f'标准化Delaunay三角网\n({len(self.points)} 个站点, {len(self.triangulation.simplices)} 个三角形)',
                     fontsize=14, fontweight='bold')
            plt.grid(True, alpha=0.3)
            plt.axis('equal')

            # 添加图例
            handles, labels = plt.gca().get_legend_handles_labels()
            if handles:
                # 去重图例
                unique_labels = []
                unique_handles = []
                for handle, label in zip(handles, labels):
                    if label not in unique_labels:
                        unique_labels.append(label)
                        unique_handles.append(handle)
                plt.legend(unique_handles, unique_labels, loc='upper right')

            # 保存图形
            if output_path is None:
                output_path = 'delaunay_triangulation_standard.png'

            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"标准化Delaunay三角网图已保存: {output_path}")

            return output_path

        except Exception as e:
            logger.error(f"绘制Delaunay三角网图失败: {e}")
            plt.close()
            return ""
    
    def get_triangulation_info(self) -> Dict:
        """获取三角网信息"""
        try:
            if self.triangulation is None:
                return {}
            
            info = {
                'num_points': len(self.points),
                'num_triangles': len(self.triangulation.simplices),
                'num_edges': len(self.triangulation.simplices) * 3 // 2,  # 近似值
                'convex_hull_points': len(self.triangulation.convex_hull)
            }
            
            return info
            
        except Exception as e:
            logger.error(f"获取三角网信息失败: {e}")
            return {}
    
    def get_triangulation_summary(self) -> Dict:
        """获取三角网统计摘要"""
        try:
            if self.triangulation is None:
                return {}

            # 基础统计
            info = {
                'num_points': len(self.points),
                'num_triangles': len(self.triangulation.simplices),
                'num_edges': len(self.triangulation.simplices) * 3 // 2,  # 近似值
                'convex_hull_points': len(self.triangulation.convex_hull),
                'records_count': len(self.triangle_records)
            }

            # 质量统计
            if hasattr(self, '_triangle_quality_stats'):
                info.update(self._triangle_quality_stats)

            return info

        except Exception as e:
            logger.error(f"获取三角网信息失败: {e}")
            return {}

    def cleanup(self):
        """清理内存"""
        try:
            self.triangulation = None
            self.points = None
            self.stations_df = None
            self.station_indices.clear()
            self.triangle_records.clear()

            logger.debug("标准化Delaunay三角网内存清理完成")

        except Exception as e:
            logger.warning(f"Delaunay三角网内存清理失败: {e}")


# 兼容性函数，保持向后兼容
def create_delaunay_triangulation(stations_df: pd.DataFrame, config=None) -> Delaunay:
    """
    创建标准化Delaunay三角网（兼容性函数）

    参数:
        stations_df: 站点数据DataFrame
        config: 配置对象（可选）

    返回:
        Delaunay: 三角网对象
    """
    if config is None:
        # 创建默认配置
        class DefaultConfig:
            def __init__(self):
                self.neighbor_count = 4
                self.min_triangle_angle = 15.0
        config = DefaultConfig()

    triangulator = DelaunayTriangulation(config)
    return triangulator.build_triangulation(stations_df)


def find_neighbor_stations_delaunay(target_station: str, stations_df: pd.DataFrame,
                                   triangulation_obj: DelaunayTriangulation = None,
                                   max_neighbors: int = 4) -> List[int]:
    """
    查找邻近站点（兼容性函数）

    参数:
        target_station: 目标站点名称
        stations_df: 站点数据DataFrame
        triangulation_obj: 三角网对象
        max_neighbors: 最大邻站数量

    返回:
        List[int]: 邻近站点索引列表
    """
    if triangulation_obj is None:
        # 创建临时三角网对象
        class DefaultConfig:
            def __init__(self):
                self.neighbor_count = max_neighbors
                self.min_triangle_angle = 15.0

        config = DefaultConfig()
        triangulation_obj = DelaunayTriangulation(config)
        triangulation_obj.build_triangulation(stations_df)

    return triangulation_obj.find_neighbors_by_triangulation(target_station, stations_df, max_neighbors)

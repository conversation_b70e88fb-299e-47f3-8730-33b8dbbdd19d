#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复缺失洪水事件的脚本
直接处理每个缺失的事件
"""

import sys
import os
import gc
import json
import time
import traceback
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print("🎯 修复缺失洪水事件脚本启动")
print("=" * 50)

try:
    # 导入必要的模块
    from config import DelaunayConfig
    from data_loader import DelaunayDataLoader
    from delaunay_interpolator import DelaunayInterpolator
    from evaluation_metrics import EvaluationMetrics
    
    print("✅ 模块导入成功")
    
    # 初始化配置
    config = DelaunayConfig()
    print(f"📋 配置加载完成")
    print(f"   输入目录: {config.INPUT_DIR}")
    print(f"   输出目录: {config.OUTPUT_DIR}")
    
    # 初始化组件
    print("🔧 初始化组件...")
    data_loader = DelaunayDataLoader(config)
    evaluator = EvaluationMetrics(config)
    interpolator = DelaunayInterpolator(config, data_loader, evaluator)
    print("✅ 组件初始化完成")
    
    # 检查缺失的事件
    def check_event_complete(event_name):
        """检查事件是否完整"""
        event_dir = config.OUTPUT_DIR / event_name
        metrics_file = event_dir / f"{event_name}_metrics.csv"
        summary_file = event_dir / f"{event_name}_summary.json"
        return metrics_file.exists() and summary_file.exists()
    
    # 获取所有洪水事件
    flood_events = config.get_flood_events()
    print(f"🌊 总洪水事件数: {len(flood_events)}")
    
    # 找出缺失的事件
    missing_events = []
    for event in flood_events:
        if not check_event_complete(event):
            missing_events.append(event)
    
    print(f"❌ 缺失事件数: {len(missing_events)}")
    print(f"✅ 完成事件数: {len(flood_events) - len(missing_events)}")
    
    if not missing_events:
        print("🎉 所有事件已完成！")
        sys.exit(0)
    
    print(f"📝 缺失事件列表: {missing_events[:10]}...")  # 只显示前10个
    print("=" * 50)
    
    # 处理单个事件的函数
    def process_single_event(event_name):
        """处理单个洪水事件"""
        print(f"\n🌊 处理事件: {event_name}")
        start_time = time.time()
        
        try:
            # 创建输出目录
            event_output_dir = config.OUTPUT_DIR / event_name
            event_output_dir.mkdir(parents=True, exist_ok=True)
            
            # 再次检查是否已完成
            if check_event_complete(event_name):
                print(f"✅ 事件{event_name}已完成，跳过")
                return True
            
            print(f"🔄 开始插值处理...")
            
            # 处理插值
            result = interpolator.interpolate_flood_event(event_name)
            if not result:
                print(f"❌ 插值处理失败")
                return False
            
            print(f"📊 开始评估...")
            
            # 评估结果
            event_metrics, summary_stats = evaluator.evaluate_event_results(
                result, event_name, event_output_dir
            )
            
            print(f"💾 保存结果...")
            
            # 保存汇总统计
            summary_file = event_output_dir / f"{event_name}_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary_stats, f, ensure_ascii=False, indent=2, default=str)
            
            # 验证完成
            if check_event_complete(event_name):
                elapsed = time.time() - start_time
                avg_nse = summary_stats.get('avg_nse', 0)
                nse_above_threshold = summary_stats.get('nse_above_threshold_percentage', 0)
                
                print(f"✅ 事件{event_name}处理完成 (耗时: {elapsed:.1f}秒)")
                print(f"   - 站点数: {len(event_metrics)}")
                print(f"   - 平均NSE: {avg_nse:.4f}")
                print(f"   - NSE>0.7比例: {nse_above_threshold:.1f}%")
                return True
            else:
                print(f"❌ 输出文件验证失败")
                return False
                
        except Exception as e:
            print(f"❌ 处理异常: {e}")
            print(f"详细错误: {traceback.format_exc()}")
            return False
    
    # 逐个处理缺失的事件
    success_count = 0
    failed_events = []
    
    for i, event_name in enumerate(missing_events):
        print(f"\n{'='*15} 进度: {i+1}/{len(missing_events)} {'='*15}")
        
        success = process_single_event(event_name)
        
        if success:
            success_count += 1
            print(f"✅ 成功")
        else:
            failed_events.append(event_name)
            print(f"❌ 失败")
        
        # 每处理5个事件清理一次内存
        if (i + 1) % 5 == 0:
            print("🧹 内存清理...")
            gc.collect()
        
        print(f"📊 当前统计: 成功 {success_count}, 失败 {len(failed_events)}")
    
    # 最终统计
    print("\n" + "=" * 50)
    print("🔍 最终验证...")
    
    final_complete = 0
    final_missing = []
    
    for event in flood_events:
        if check_event_complete(event):
            final_complete += 1
        else:
            final_missing.append(event)
    
    print(f"✅ 最终完成: {final_complete}/{len(flood_events)}")
    print(f"📊 本次成功: {success_count}/{len(missing_events)}")
    
    if final_missing:
        print(f"❌ 仍缺失: {final_missing}")
    else:
        print("🎉 所有洪水事件处理完成！")
    
    print("🎯 修复脚本完成")
    
except Exception as e:
    print(f"❌ 脚本运行失败: {e}")
    print(f"详细错误: {traceback.format_exc()}")
    sys.exit(1)

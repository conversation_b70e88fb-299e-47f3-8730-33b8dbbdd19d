"""
Kriging插值系统莫兰指数模块
实现空间自相关性分析，用于权重计算
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional
from scipy.spatial.distance import pdist, squareform
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class MoranIndex:
    """莫兰指数计算器"""
    
    def __init__(self, config):
        """初始化莫兰指数计算器"""
        self.config = config
        self.spatial_weights = None
        self.moran_values = {}
        
    def calculate_spatial_weights(self, stations_df: pd.DataFrame, 
                                method: str = "inverse_distance") -> np.ndarray:
        """计算空间权重矩阵"""
        try:
            logger.info(f"计算空间权重矩阵 (方法: {method})...")
            
            n_stations = len(stations_df)
            coordinates = stations_df[['经度', '纬度']].values
            
            if method == "inverse_distance":
                # 反距离权重
                distances = squareform(pdist(coordinates))
                
                # 避免除零错误
                distances[distances == 0] = 1e-10
                
                # 计算反距离权重
                weights = 1.0 / distances
                
                # 对角线设为0（站点与自身的权重为0）
                np.fill_diagonal(weights, 0)
                
            elif method == "exponential":
                # 指数衰减权重
                distances = squareform(pdist(coordinates))
                weights = np.exp(-distances / np.mean(distances))
                np.fill_diagonal(weights, 0)
                
            elif method == "gaussian":
                # 高斯权重
                distances = squareform(pdist(coordinates))
                sigma = np.std(distances)
                weights = np.exp(-(distances**2) / (2 * sigma**2))
                np.fill_diagonal(weights, 0)
                
            else:
                raise ValueError(f"不支持的权重计算方法: {method}")
            
            # 行标准化
            row_sums = np.sum(weights, axis=1)
            row_sums[row_sums == 0] = 1  # 避免除零
            weights = weights / row_sums[:, np.newaxis]
            
            self.spatial_weights = weights
            
            logger.info(f"空间权重矩阵计算完成: {weights.shape}")
            
            return weights
            
        except Exception as e:
            logger.error(f"计算空间权重矩阵失败: {e}")
            raise
    
    def calculate_global_moran_index(self, rainfall_values: np.ndarray) -> float:
        """计算全局莫兰指数"""
        try:
            if self.spatial_weights is None:
                raise ValueError("空间权重矩阵尚未计算")
            
            n = len(rainfall_values)
            
            # 去除无效值
            valid_mask = ~np.isnan(rainfall_values)
            if not np.any(valid_mask):
                return 0.0
            
            values = rainfall_values[valid_mask]
            weights = self.spatial_weights[np.ix_(valid_mask, valid_mask)]
            
            # 计算均值
            mean_value = np.mean(values)
            
            # 中心化
            deviations = values - mean_value
            
            # 计算莫兰指数
            numerator = 0.0
            denominator = 0.0
            W = 0.0  # 权重总和
            
            for i in range(len(values)):
                for j in range(len(values)):
                    if i != j:
                        numerator += weights[i, j] * deviations[i] * deviations[j]
                        W += weights[i, j]
                
                denominator += deviations[i]**2
            
            if denominator == 0 or W == 0:
                return 0.0
            
            moran_i = (len(values) / W) * (numerator / denominator)
            
            return moran_i
            
        except Exception as e:
            logger.error(f"计算全局莫兰指数失败: {e}")
            return 0.0
    
    def calculate_local_moran_index(self, rainfall_values: np.ndarray) -> np.ndarray:
        """计算局部莫兰指数"""
        try:
            if self.spatial_weights is None:
                raise ValueError("空间权重矩阵尚未计算")
            
            n = len(rainfall_values)
            local_moran = np.zeros(n)
            
            # 去除无效值
            valid_mask = ~np.isnan(rainfall_values)
            if not np.any(valid_mask):
                return local_moran
            
            values = rainfall_values.copy()
            values[~valid_mask] = np.nanmean(values)  # 用均值填充无效值
            
            # 计算均值和方差
            mean_value = np.mean(values[valid_mask])
            var_value = np.var(values[valid_mask])
            
            if var_value == 0:
                return local_moran
            
            # 中心化
            deviations = values - mean_value
            
            # 计算局部莫兰指数
            for i in range(n):
                if valid_mask[i]:
                    weighted_sum = 0.0
                    weight_sum = 0.0
                    
                    for j in range(n):
                        if i != j and valid_mask[j]:
                            weighted_sum += self.spatial_weights[i, j] * deviations[j]
                            weight_sum += self.spatial_weights[i, j]
                    
                    if weight_sum > 0:
                        local_moran[i] = (deviations[i] / var_value) * weighted_sum
            
            return local_moran
            
        except Exception as e:
            logger.error(f"计算局部莫兰指数失败: {e}")
            return np.zeros(len(rainfall_values))
    
    def calculate_moran_weights(self, target_station_idx: int, 
                              neighbor_indices: List[int],
                              rainfall_values: np.ndarray) -> np.ndarray:
        """计算基于莫兰指数的权重"""
        try:
            if not self.config.enable_moran_weighting:
                # 如果未启用莫兰指数权重，返回均匀权重
                return np.ones(len(neighbor_indices)) / len(neighbor_indices)
            
            if self.spatial_weights is None:
                logger.warning("空间权重矩阵未计算，使用均匀权重")
                return np.ones(len(neighbor_indices)) / len(neighbor_indices)
            
            # 获取目标站点与邻居站点的空间权重
            moran_weights = []
            
            for neighbor_idx in neighbor_indices:
                if (target_station_idx < self.spatial_weights.shape[0] and 
                    neighbor_idx < self.spatial_weights.shape[1]):
                    weight = self.spatial_weights[target_station_idx, neighbor_idx]
                else:
                    weight = 0.0
                
                moran_weights.append(weight)
            
            moran_weights = np.array(moran_weights)
            
            # 标准化权重
            weight_sum = np.sum(moran_weights)
            if weight_sum > 0:
                moran_weights = moran_weights / weight_sum
            else:
                # 如果权重和为0，使用均匀权重
                moran_weights = np.ones(len(neighbor_indices)) / len(neighbor_indices)
            
            return moran_weights
            
        except Exception as e:
            logger.error(f"计算莫兰权重失败: {e}")
            return np.ones(len(neighbor_indices)) / len(neighbor_indices)
    
    def analyze_spatial_autocorrelation(self, stations_df: pd.DataFrame,
                                      rainfall_data: Dict[str, pd.DataFrame]) -> Dict:
        """分析空间自相关性"""
        try:
            logger.info("分析空间自相关性...")
            
            # 计算空间权重矩阵
            self.calculate_spatial_weights(stations_df)
            
            results = {
                'global_moran': {},
                'local_moran': {},
                'summary': {}
            }
            
            # 获取时间点列表（使用第一个站点的时间序列）
            first_station = list(rainfall_data.keys())[0]
            time_points = rainfall_data[first_station]['时间'].tolist()
            
            # 限制分析的时间点数量（避免计算时间过长）
            max_time_points = min(100, len(time_points))
            sample_indices = np.linspace(0, len(time_points)-1, max_time_points, dtype=int)
            sample_time_points = [time_points[i] for i in sample_indices]
            
            global_moran_values = []
            
            for time_point in sample_time_points[:10]:  # 只分析前10个时间点作为示例
                # 构建该时间点的降雨值数组
                rainfall_values = []
                
                for _, station_row in stations_df.iterrows():
                    station_name = str(station_row['站点'])
                    
                    if station_name in rainfall_data:
                        station_df = rainfall_data[station_name]
                        matching_rows = station_df[station_df['时间'] == time_point]
                        
                        if len(matching_rows) > 0:
                            rainfall_values.append(matching_rows['雨量'].iloc[0])
                        else:
                            rainfall_values.append(0.0)
                    else:
                        rainfall_values.append(0.0)
                
                rainfall_values = np.array(rainfall_values)
                
                # 计算全局莫兰指数
                global_moran = self.calculate_global_moran_index(rainfall_values)
                global_moran_values.append(global_moran)
                
                results['global_moran'][time_point] = global_moran
            
            # 计算汇总统计
            if global_moran_values:
                results['summary'] = {
                    'mean_global_moran': np.mean(global_moran_values),
                    'std_global_moran': np.std(global_moran_values),
                    'min_global_moran': np.min(global_moran_values),
                    'max_global_moran': np.max(global_moran_values),
                    'analyzed_time_points': len(global_moran_values)
                }
                
                logger.info(f"空间自相关分析完成:")
                logger.info(f"  平均全局莫兰指数: {results['summary']['mean_global_moran']:.4f}")
                logger.info(f"  标准差: {results['summary']['std_global_moran']:.4f}")
                logger.info(f"  分析时间点数: {results['summary']['analyzed_time_points']}")
            
            return results
            
        except Exception as e:
            logger.error(f"空间自相关分析失败: {e}")
            return {}
    
    def get_moran_statistics(self) -> Dict:
        """获取莫兰指数统计信息"""
        try:
            if not self.moran_values:
                return {}
            
            stats = {
                'total_calculations': len(self.moran_values),
                'spatial_weights_shape': self.spatial_weights.shape if self.spatial_weights is not None else None,
                'moran_weighting_enabled': self.config.enable_moran_weighting
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取莫兰指数统计失败: {e}")
            return {}
    
    def cleanup(self):
        """清理内存"""
        try:
            self.spatial_weights = None
            self.moran_values.clear()
            
            logger.debug("莫兰指数计算器内存清理完成")
            
        except Exception as e:
            logger.warning(f"莫兰指数计算器内存清理失败: {e}")

# OI插值系统使用步骤说明

## 系统概述

我已经为您创建了一个完整的OI插值系统，该系统实现了您要求的所有功能：

### 主要功能
1. ✅ **Delaunay三角网构建**：自动构建站点间的三角网络
2. ✅ **莫兰指数权重计算**：基于空间自相关性计算权重
3. ✅ **最优插值算法**：实现高精度的空间插值
4. ✅ **留一法验证**：自动验证插值精度
5. ✅ **评价指标计算**：计算MAE、RMSE、NSE、R²等指标
6. ✅ **栅格输出**：生成ASC格式的栅格文件
7. ✅ **批量处理**：支持多个洪水事件的批量处理
8. ✅ **并行计算**：支持24核并行处理
9. ✅ **内存优化**：分批处理避免内存溢出
10. ✅ **零值处理**：专门优化处理大量零值数据

## 第一步：系统测试

在开始使用之前，请先测试系统是否正常工作：

```bash
python test_oi_system.py
```

这个脚本会检查：
- 模块导入是否正常
- 配置系统是否工作
- 数据文件是否存在
- 基本功能是否正常

## 第二步：选择运行方式

### 方式一：自动安装并运行（推荐新手）

```bash
python install_and_run.py
```

这个脚本会：
1. 自动检查Python版本
2. 自动安装必要的依赖包
3. 创建默认配置文件
4. 启动OI插值系统

### 方式二：直接运行

```bash
python OI_python/run_oi.py
```

然后选择运行模式：
- 选择 `1`：使用默认配置运行
- 选择 `2`：交互式配置运行
- 选择 `3`：创建配置文件模板

### 方式三：命令行运行

```bash
# 调试模式（只处理少量数据）
python -m OI_python.easy_run --debug

# 单事件处理
python -m OI_python.easy_run --input "D:/pythondata/spatial_interpolation/input_another/2009-1" --output "D:/pythondata/spatial_interpolation/output/OI/2009-1"

# 批量处理
python -m OI_python.easy_run --batch

# 交互式模式
python -m OI_python.easy_run --interactive
```

## 第三步：配置参数

### 默认配置路径
系统使用以下默认路径（您可以根据需要修改）：

```
输入目录: D:/pythondata/spatial_interpolation/input_another/2009-1
站点文件: D:/pythondata/spatial_interpolation/stations.csv
掩膜文件: D:/pythondata/spatial_interpolation/terrain/90/mask.asc
输出目录: D:/pythondata/spatial_interpolation/output/OI/2009-1
```

### 重要参数说明

1. **并行处理参数**
   - `num_processes`: 20（建议设为CPU核心数的80%）
   - `batch_size`: 50（每批处理的时间步数）

2. **调试参数**
   - `debug_mode`: False（设为True只处理少量数据）
   - `debug_time_steps`: 10（调试模式下处理的时间步数）

3. **零值处理参数**
   - `zero_ratio_threshold`: 0.8（零值比例阈值）
   - `high_zero_ratio_threshold`: 0.9（极高零值比例阈值）

4. **输出控制**
   - `generate_raster`: True（是否生成栅格输出）
   - `generate_point_output`: True（是否生成点雨量输出）
   - `generate_delaunay_plot`: True（是否生成三角网图）

## 第四步：运行处理

### 单事件处理示例

1. **调试模式测试**（推荐先运行）：
```bash
python -m OI_python.easy_run --debug
```

2. **完整单事件处理**：
```bash
python -m OI_python.easy_run --input "D:/pythondata/spatial_interpolation/input_another/2009-1" --output "D:/pythondata/spatial_interpolation/output/OI/2009-1"
```

### 批量处理示例

处理所有洪水事件：
```bash
python -m OI_python.easy_run --batch
```

## 第五步：查看结果

### 输出文件结构

```
output/OI/2009-1/
├── validation_results.csv          # 留一法验证结果
├── interpolated_rainfall.csv       # 插值后的降雨数据
├── evaluation_metrics_*.json       # 评价指标文件
├── delaunay_triangulation.png      # Delaunay三角网图
├── station_relationships.csv       # 站点关系表
├── point_rainfall/                 # 点雨量输出文件夹
│   ├── 80606500.csv
│   ├── 80607800.csv
│   └── ...
├── raster_output/                  # 栅格输出文件夹（可选）
│   ├── rainfall_2009-04-16_03-00.asc
│   ├── rainfall_2009-04-16_04-00.asc
│   └── ...
├── areal_rainfall.csv              # 流域面雨量数据
├── logs/                           # 日志文件夹
│   └── oi_interpolation_*.log
└── processing_summary.json         # 处理摘要
```

### 批量处理结果

```
output/OI/
├── 2009-1/                        # 各事件的输出
├── 2009-2/
├── ...
├── batch_results_*.csv            # 所有事件的评价指标汇总
└── batch_results_*.json           # 详细的批量处理结果
```

## 第六步：结果分析

### 评价指标说明

- **MAE**：平均绝对误差，越小越好
- **RMSE**：均方根误差，越小越好
- **NSE**：纳什效率系数，接近1最好
- **R²**：决定系数，接近1最好
- **CC**：相关系数，接近1最好

### 查看结果

1. **查看评价指标**：
   - 打开 `evaluation_metrics_*.json` 文件
   - 查看控制台输出的评价摘要

2. **查看Delaunay三角网图**：
   - 打开 `delaunay_triangulation.png`

3. **查看插值结果**：
   - 查看 `interpolated_rainfall.csv`
   - 查看 `point_rainfall/` 文件夹中的各站点文件

## 常见问题解决

### 1. 内存不足
```python
# 修改配置文件中的参数
batch_size = 20  # 减少批处理大小
num_processes = 10  # 减少并行进程数
debug_mode = True  # 先用调试模式测试
```

### 2. 处理速度慢
```python
# 优化参数
num_processes = 20  # 增加并行进程数（不超过CPU核心数）
generate_raster = False  # 跳过栅格生成以节省时间
```

### 3. 路径错误
确保路径格式正确：
```python
# 正确的路径格式
"D:/pythondata/spatial_interpolation/input_another/2009-1"
# 或
"D:\\pythondata\\spatial_interpolation\\input_another\\2009-1"
```

## 高级使用

### 自定义配置文件

1. 创建配置文件：
```bash
python -m OI_python.easy_run --create-config my_config.json
```

2. 编辑配置文件，修改参数

3. 使用配置文件运行：
```bash
python -m OI_python.easy_run --config my_config.json
```

### 批量处理特定事件

修改配置文件中的 `target_folders` 参数：
```json
{
  "target_folders": ["2009-1", "2009-2", "2015-3"]
}
```

## 技术支持

如果遇到问题：

1. **查看日志文件**：`output/OI/*/logs/oi_interpolation_*.log`
2. **运行测试脚本**：`python test_oi_system.py`
3. **使用调试模式**：`python -m OI_python.easy_run --debug`

## 系统要求

- **Python**: 3.7+
- **内存**: 建议8GB以上
- **CPU**: 支持多核并行处理
- **硬盘**: 足够空间存储结果文件

## 依赖包

系统会自动安装以下依赖包：
- numpy >= 1.19.0
- pandas >= 1.3.0
- scipy >= 1.7.0
- matplotlib >= 3.3.0

---

**恭喜！您现在可以开始使用OI插值系统了！**

建议先运行调试模式测试系统，然后再进行完整的处理。

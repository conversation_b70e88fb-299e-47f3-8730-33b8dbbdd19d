#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文字体和站点名称映射

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def test_chinese_fonts():
    """测试中文字体支持"""
    print("Testing Chinese font support...")
    
    # 尝试设置中文字体
    font_options = [
        'SimHei',
        'Microsoft YaHei', 
        'DejaVu Sans',
        'Arial Unicode MS',
        'WenQuanYi Micro Hei',
        'Noto Sans CJK SC'
    ]
    
    working_font = None
    for font in font_options:
        try:
            matplotlib.rcParams['font.sans-serif'] = [font]
            plt.rcParams['font.sans-serif'] = [font]
            
            # 测试绘制中文
            fig, ax = plt.subplots(figsize=(8, 6))
            ax.text(0.5, 0.5, '测试中文字体显示', fontsize=16, ha='center', va='center')
            ax.set_title('Chinese Font Test', fontsize=14)
            
            # 保存测试图片
            test_path = Path('test_chinese_font.png')
            plt.savefig(test_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            working_font = font
            print(f"✅ Font '{font}' works correctly")
            break
            
        except Exception as e:
            print(f"❌ Font '{font}' failed: {e}")
            continue
    
    if working_font:
        print(f"Using font: {working_font}")
        return working_font
    else:
        print("⚠️  No suitable Chinese font found, using default")
        return None

def test_station_mapping():
    """测试站点名称映射"""
    print("\nTesting station name mapping...")
    
    try:
        # 读取水晏泰森.xlsx
        station_file = Path('水晏泰森.xlsx')
        if not station_file.exists():
            print(f"❌ Station file not found: {station_file}")
            return {}
        
        df = pd.read_excel(station_file)
        print(f"✅ Loaded Excel file with {len(df)} rows")
        print(f"Columns: {df.columns.tolist()}")
        
        # 创建映射
        name_mapping = {}
        for _, row in df.iterrows():
            station_code_orig = str(row['PSTCD']).strip()
            station_name = str(row['NAME']).strip()
            
            # 同时存储多种格式
            for code_variant in [station_code_orig, station_code_orig.upper(), station_code_orig.lower()]:
                name_mapping[code_variant] = station_name
        
        print(f"✅ Created mapping with {len(name_mapping)} entries")
        
        # 显示一些示例
        print("\nSample mappings:")
        unique_codes = set()
        for code, name in name_mapping.items():
            if len(unique_codes) < 10:
                base_code = code.upper()
                if base_code not in unique_codes:
                    print(f"  {code} -> {name}")
                    unique_codes.add(base_code)
        
        return name_mapping
        
    except Exception as e:
        print(f"❌ Failed to load station mapping: {e}")
        return {}

def test_metrics_stations():
    """测试评价指标文件中的站点"""
    print("\nTesting stations in metrics files...")
    
    try:
        metrics_dir = Path('output/Delaunay_interpolation/metrics')
        if not metrics_dir.exists():
            print(f"❌ Metrics directory not found: {metrics_dir}")
            return []
        
        csv_files = list(metrics_dir.glob('*_metrics.csv'))
        if not csv_files:
            print(f"❌ No CSV files found in {metrics_dir}")
            return []
        
        # 读取第一个文件获取站点列表
        df = pd.read_csv(csv_files[0])
        stations = df['站点代码'].unique()
        
        print(f"✅ Found {len(stations)} stations in metrics")
        print("Stations in metrics:")
        for station in sorted(stations):
            print(f"  {station}")
        
        return stations
        
    except Exception as e:
        print(f"❌ Failed to load metrics stations: {e}")
        return []

def test_mapping_coverage(station_mapping, metrics_stations):
    """测试映射覆盖率"""
    print("\nTesting mapping coverage...")
    
    matched = 0
    unmatched = []
    
    for station in metrics_stations:
        station_variants = [station, station.upper(), station.lower()]
        found = False
        
        for variant in station_variants:
            if variant in station_mapping:
                chinese_name = station_mapping[variant]
                print(f"✅ {station} -> {chinese_name}")
                matched += 1
                found = True
                break
        
        if not found:
            print(f"❌ {station} -> NOT FOUND")
            unmatched.append(station)
    
    print(f"\nMapping coverage: {matched}/{len(metrics_stations)} ({matched/len(metrics_stations)*100:.1f}%)")
    
    if unmatched:
        print(f"Unmatched stations: {unmatched}")
    
    return matched, unmatched

def create_test_visualization(station_mapping, metrics_stations, working_font):
    """创建测试可视化"""
    print("\nCreating test visualization...")
    
    try:
        if working_font:
            matplotlib.rcParams['font.sans-serif'] = [working_font]
            plt.rcParams['font.sans-serif'] = [working_font]
        
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建测试图
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 测试数据
        test_stations = metrics_stations[:10]  # 前10个站点
        test_values = np.random.rand(len(test_stations))
        
        # 获取中文名称
        chinese_names = []
        for station in test_stations:
            chinese_name = None
            for variant in [station, station.upper(), station.lower()]:
                if variant in station_mapping:
                    chinese_name = station_mapping[variant]
                    break
            
            if chinese_name and chinese_name != 'nan':
                chinese_names.append(chinese_name)
            else:
                chinese_names.append(station)
        
        # 绘制条形图
        bars = ax.barh(range(len(test_stations)), test_values)
        
        # 设置y轴标签为中文名称
        ax.set_yticks(range(len(test_stations)))
        ax.set_yticklabels(chinese_names, fontsize=10)
        
        ax.set_xlabel('Test Values', fontsize=12)
        ax.set_title('Station Name Test (Chinese Names)', fontsize=14, fontweight='bold')
        
        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, test_values)):
            ax.text(value + 0.01, i, f'{value:.3f}', va='center', fontsize=9)
        
        plt.tight_layout()
        
        # 保存测试图
        test_path = Path('test_station_names.png')
        plt.savefig(test_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"✅ Test visualization saved: {test_path}")
        
    except Exception as e:
        print(f"❌ Failed to create test visualization: {e}")

def main():
    """主函数"""
    print("="*60)
    print("Chinese Font and Station Name Mapping Test")
    print("="*60)
    
    # 1. 测试中文字体
    working_font = test_chinese_fonts()
    
    # 2. 测试站点映射
    station_mapping = test_station_mapping()
    
    # 3. 测试评价指标中的站点
    metrics_stations = test_metrics_stations()
    
    # 4. 测试映射覆盖率
    if station_mapping and len(metrics_stations) > 0:
        matched, unmatched = test_mapping_coverage(station_mapping, metrics_stations)
        
        # 5. 创建测试可视化
        create_test_visualization(station_mapping, metrics_stations, working_font)
        
        print(f"\n" + "="*60)
        print("Test Summary:")
        print(f"- Working font: {working_font or 'None'}")
        print(f"- Station mappings: {len(station_mapping)}")
        print(f"- Metrics stations: {len(metrics_stations)}")
        print(f"- Mapping coverage: {matched}/{len(metrics_stations)} ({matched/len(metrics_stations)*100:.1f}%)")
        print("="*60)
        
        if matched == len(metrics_stations):
            print("✅ All tests passed! Ready for visualization.")
        else:
            print("⚠️  Some issues found. Check the output above.")
    
    else:
        print("❌ Cannot proceed with tests due to missing data.")

if __name__ == "__main__":
    main()

# Molan指数分析系统使用示例与最佳实践

## 1. 快速开始示例

### 1.1 基本使用流程

```python
# 导入必要的库
import pandas as pd
import numpy as np
from batch_molan_analysis import BatchMoranAnalysis

# 设置文件路径
excel_file = "../水晏泰森.xlsx"
stations_file = "../stations.csv"
input_dir = "../input_another"
output_dir = "../output/molan"

# 创建分析实例
analyzer = BatchMoranAnalysis(
    excel_file=excel_file,
    stations_file=stations_file,
    input_dir=input_dir,
    output_dir=output_dir,
    exclude_stations=False  # 不剔除任何站点
)

# 运行批量分析
results = analyzer.run_batch_analysis()

print(f"分析完成！共生成 {len(results)} 条记录")
```

### 1.2 单个洪水事件分析

```python
# 分析特定洪水事件
flood_event = "20200701"
event_results = analyzer.analyze_flood_event(flood_event)

# 查看结果
for result in event_results[:5]:  # 显示前5条结果
    print(f"事件: {result['flood_event']}")
    print(f"分组: {result['group']}")
    print(f"参考站点: {result['reference_station']}")
    print(f"最相关站点: {result['top_1_station']}")
    print(f"莫兰指数: {result['top_1_moran']:.4f}")
    print(f"显著性: {result['top_1_significance']}")
    print("-" * 40)
```

### 1.3 自定义参数分析

```python
# 创建自定义配置的分析实例
analyzer_custom = BatchMoranAnalysis(
    excel_file=excel_file,
    stations_file=stations_file,
    input_dir=input_dir,
    output_dir=output_dir,
    exclude_stations=True  # 启用站点剔除
)

# 自定义剔除站点
analyzer_custom.excluded_stations = {
    '整体': ['station_01', 'station_02'],
    '大化': ['station_03'],
    '太平': [],
    '水晏': ['station_04']
}

# 运行分析
custom_results = analyzer_custom.run_batch_analysis()
```

## 2. 高级使用示例

### 2.1 单站点对分析

```python
# 计算特定站点对的莫兰指数
ref_station = "station_01"
target_station = "station_02"
station_list = ["station_01", "station_02", "station_03", "station_04"]

# 加载降雨数据
rain_data = analyzer.load_flood_data("20200701")

# 计算莫兰指数
result = analyzer.calculate_moran_index_with_significance(
    ref_station=ref_station,
    target_station=target_station,
    station_list=station_list,
    rain_data=rain_data,
    n_permutations=999  # 使用更多置换次数提高精度
)

print(f"莫兰指数: {result['moran_i']:.4f}")
print(f"p值: {result['p_value']:.4f}")
print(f"z分数: {result['z_score']:.4f}")
print(f"显著性: {result['significance_level']}")
```

### 2.2 自定义权重方法

```python
# 使用二元权重方法
weights_binary = analyzer.calculate_spatial_weights(
    station_list=station_list,
    method='binary'
)

# 使用反距离权重方法（默认）
weights_inverse = analyzer.calculate_spatial_weights(
    station_list=station_list,
    method='inverse_distance'
)

print("二元权重矩阵:")
print(weights_binary)
print("\n反距离权重矩阵:")
print(weights_inverse)
```

### 2.3 批量结果筛选

```python
# 筛选显著结果
significant_results = [
    r for r in results 
    if r['top_1_significance'] in ['*', '**', '***']
]

print(f"显著结果数量: {len(significant_results)}")

# 按莫兰指数排序
sorted_results = sorted(
    significant_results, 
    key=lambda x: x['top_1_moran'], 
    reverse=True
)

# 显示最强相关的前10个结果
print("\n最强空间相关性（前10）:")
for i, result in enumerate(sorted_results[:10], 1):
    print(f"{i:2d}. {result['reference_station']} -> {result['top_1_station']}: "
          f"{result['top_1_moran']:.4f} ({result['top_1_significance']})")
```

## 3. 数据预处理最佳实践

### 3.1 数据质量检查

```python
def check_data_quality(rain_data):
    """检查降雨数据质量"""
    quality_report = {}
    
    for column in rain_data.columns:
        if column == '时间':
            continue
            
        data = rain_data[column]
        quality_report[column] = {
            'total_points': len(data),
            'missing_points': data.isna().sum(),
            'zero_points': (data == 0).sum(),
            'negative_points': (data < 0).sum(),
            'mean_value': data.mean(),
            'std_value': data.std(),
            'max_value': data.max(),
            'min_value': data.min()
        }
    
    return quality_report

# 使用示例
flood_data = analyzer.load_flood_data("20200701")
quality = check_data_quality(flood_data)

# 显示质量报告
for station, stats in quality.items():
    print(f"\n站点 {station}:")
    print(f"  总数据点: {stats['total_points']}")
    print(f"  缺失点: {stats['missing_points']}")
    print(f"  零值点: {stats['zero_points']}")
    print(f"  均值: {stats['mean_value']:.2f}")
    print(f"  标准差: {stats['std_value']:.2f}")
```

### 3.2 异常值检测

```python
def detect_outliers(data, method='iqr', threshold=1.5):
    """检测异常值"""
    if method == 'iqr':
        Q1 = data.quantile(0.25)
        Q3 = data.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - threshold * IQR
        upper_bound = Q3 + threshold * IQR
        outliers = (data < lower_bound) | (data > upper_bound)
    
    elif method == 'zscore':
        z_scores = np.abs((data - data.mean()) / data.std())
        outliers = z_scores > threshold
    
    return outliers

# 使用示例
station_data = flood_data['station_01']
outliers = detect_outliers(station_data, method='iqr')
print(f"检测到 {outliers.sum()} 个异常值")
```

## 4. 结果分析最佳实践

### 4.1 显著性解释

```python
def interpret_significance(p_value, moran_i):
    """解释显著性结果"""
    if p_value < 0.001:
        significance = "极显著"
        confidence = "99.9%"
    elif p_value < 0.01:
        significance = "高显著"
        confidence = "99%"
    elif p_value < 0.05:
        significance = "显著"
        confidence = "95%"
    elif p_value < 0.1:
        significance = "边际显著"
        confidence = "90%"
    else:
        significance = "不显著"
        confidence = "< 90%"
    
    if moran_i > 0:
        correlation_type = "正相关（相似模式）"
    elif moran_i < 0:
        correlation_type = "负相关（相反模式）"
    else:
        correlation_type = "无相关"
    
    return {
        'significance': significance,
        'confidence': confidence,
        'correlation_type': correlation_type,
        'strength': abs(moran_i)
    }

# 使用示例
interpretation = interpret_significance(0.001, 0.75)
print(f"显著性: {interpretation['significance']}")
print(f"置信度: {interpretation['confidence']}")
print(f"相关类型: {interpretation['correlation_type']}")
print(f"相关强度: {interpretation['strength']:.3f}")
```

### 4.2 结果汇总统计

```python
def summarize_results(results):
    """汇总分析结果"""
    summary = {}
    
    # 按分组统计
    for group in ['整体', '大化', '太平', '水晏']:
        group_results = [r for r in results if r['group'] == group]
        if not group_results:
            continue
            
        moran_values = [r['top_1_moran'] for r in group_results]
        significant_count = sum(1 for r in group_results 
                              if r['top_1_significance'] in ['*', '**', '***'])
        
        summary[group] = {
            'total_pairs': len(group_results),
            'significant_pairs': significant_count,
            'significance_rate': significant_count / len(group_results) * 100,
            'mean_moran': np.mean(moran_values),
            'max_moran': np.max(moran_values),
            'min_moran': np.min(moran_values),
            'std_moran': np.std(moran_values)
        }
    
    return summary

# 使用示例
summary = summarize_results(results)
for group, stats in summary.items():
    print(f"\n{group}组统计:")
    print(f"  总站点对: {stats['total_pairs']}")
    print(f"  显著站点对: {stats['significant_pairs']}")
    print(f"  显著率: {stats['significance_rate']:.1f}%")
    print(f"  平均莫兰指数: {stats['mean_moran']:.4f}")
    print(f"  最大莫兰指数: {stats['max_moran']:.4f}")
```

## 5. 性能优化最佳实践

### 5.1 内存管理

```python
import gc

def memory_efficient_analysis(analyzer, flood_events):
    """内存高效的批量分析"""
    all_results = []
    
    for i, event in enumerate(flood_events):
        print(f"处理事件 {i+1}/{len(flood_events)}: {event}")
        
        # 分析单个事件
        event_results = analyzer.analyze_flood_event(event)
        all_results.extend(event_results)
        
        # 定期清理内存
        if (i + 1) % 10 == 0:
            gc.collect()
            print(f"已处理 {i+1} 个事件，执行内存清理")
    
    return all_results
```

### 5.2 进度监控

```python
from tqdm import tqdm

def analysis_with_progress(analyzer, flood_events):
    """带进度条的分析"""
    all_results = []
    
    # 使用进度条
    for event in tqdm(flood_events, desc="分析洪水事件"):
        try:
            event_results = analyzer.analyze_flood_event(event)
            all_results.extend(event_results)
        except Exception as e:
            print(f"分析事件 {event} 时出错: {e}")
            continue
    
    return all_results
```

## 6. 错误处理最佳实践

### 6.1 异常处理

```python
def robust_moran_calculation(analyzer, ref_station, target_station, 
                           station_list, rain_data):
    """健壮的莫兰指数计算"""
    try:
        result = analyzer.calculate_moran_index_with_significance(
            ref_station, target_station, station_list, rain_data
        )
        return result
    
    except ValueError as e:
        print(f"数值错误: {e}")
        return {'moran_i': 0.0, 'p_value': 1.0, 'z_score': 0.0, 'significance_level': 'NS'}
    
    except KeyError as e:
        print(f"站点不存在: {e}")
        return {'moran_i': 0.0, 'p_value': 1.0, 'z_score': 0.0, 'significance_level': 'NS'}
    
    except Exception as e:
        print(f"未知错误: {e}")
        return {'moran_i': 0.0, 'p_value': 1.0, 'z_score': 0.0, 'significance_level': 'NS'}
```

### 6.2 数据验证

```python
def validate_input_data(rain_data, station_list):
    """验证输入数据"""
    errors = []
    
    # 检查数据框是否为空
    if rain_data.empty:
        errors.append("降雨数据为空")
    
    # 检查站点是否存在
    missing_stations = [s for s in station_list if s not in rain_data.columns]
    if missing_stations:
        errors.append(f"缺失站点: {missing_stations}")
    
    # 检查数据类型
    for station in station_list:
        if station in rain_data.columns:
            if not pd.api.types.is_numeric_dtype(rain_data[station]):
                errors.append(f"站点 {station} 数据类型不是数值型")
    
    return errors

# 使用示例
errors = validate_input_data(flood_data, ['station_01', 'station_02'])
if errors:
    print("数据验证失败:")
    for error in errors:
        print(f"  - {error}")
else:
    print("数据验证通过")
```

## 7. 结果可视化建议

### 7.1 基础可视化

```python
import matplotlib.pyplot as plt
import seaborn as sns

def plot_moran_distribution(results):
    """绘制莫兰指数分布图"""
    moran_values = [r['top_1_moran'] for r in results]
    
    plt.figure(figsize=(10, 6))
    plt.hist(moran_values, bins=30, alpha=0.7, edgecolor='black')
    plt.xlabel('莫兰指数')
    plt.ylabel('频次')
    plt.title('莫兰指数分布')
    plt.axvline(x=0, color='red', linestyle='--', label='零线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

# 使用示例
plot_moran_distribution(results)
```

### 7.2 分组比较

```python
def plot_group_comparison(results):
    """绘制分组比较图"""
    groups = ['整体', '大化', '太平', '水晏']
    group_data = {}
    
    for group in groups:
        group_results = [r for r in results if r['group'] == group]
        group_data[group] = [r['top_1_moran'] for r in group_results]
    
    plt.figure(figsize=(12, 8))
    plt.boxplot(group_data.values(), labels=group_data.keys())
    plt.ylabel('莫兰指数')
    plt.title('各组莫兰指数分布比较')
    plt.grid(True, alpha=0.3)
    plt.show()

# 使用示例
plot_group_comparison(results)
```

---

**文档版本**: 1.0  
**创建日期**: 2025-06-16  
**适用版本**: 空间插值系统 v1.0

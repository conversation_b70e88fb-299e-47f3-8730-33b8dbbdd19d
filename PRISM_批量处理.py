import os
import subprocess
import pandas as pd
import re
import time
import datetime

# 基本配置
BASE_DIR = r"D:/pythondata/spatial_interpolation"
INPUT_BASE_DIR = os.path.join(BASE_DIR, "input_another")
OUTPUT_BASE_DIR = os.path.join(BASE_DIR, "output/PRISM")
OVERALL_RESULTS_FILE = os.path.join(BASE_DIR, "prism_evaluation_overall.csv")
NONZERO_RAIN_RESULTS_FILE = os.path.join(BASE_DIR, "prism_evaluation_nonzero_rain.csv")

# 创建临时工作目录
TEMP_DIR = os.path.join(BASE_DIR, "temp_prism_runs")
os.makedirs(TEMP_DIR, exist_ok=True)

print("---------- PRISM降雨插值批量处理 ----------")
print(f"工作目录: {BASE_DIR}")
print(f"Overall结果将保存到: {OVERALL_RESULTS_FILE}")
print(f"NonZeroRain结果将保存到: {NONZERO_RAIN_RESULTS_FILE}")

# 存储结果的列表
overall_results = []
nonzero_rain_results = []

# 遍历input目录下的所有子文件夹
all_dirs = os.listdir(INPUT_BASE_DIR)
input_dirs = sorted([d for d in all_dirs if os.path.isdir(os.path.join(INPUT_BASE_DIR, d))])

print(f"\n找到 {len(input_dirs)} 个数据文件夹需要处理:")
for d in input_dirs:
    print(f"  - {d}")
print("\n开始批量处理...")

# 处理每个文件夹
for directory in input_dirs:
    print(f"\n{'='*50}")
    print(f"处理文件夹: {directory}")
    print(f"{'='*50}")
    
    start_time = time.time()
    
    # 构建完整路径
    folder_path = os.path.join(INPUT_BASE_DIR, directory)
    output_folder = os.path.join(OUTPUT_BASE_DIR, directory)
    
    # 确保输出目录存在
    os.makedirs(output_folder, exist_ok=True)
    
    try:
        # 读取原始PRISM_局部优化4站点.py文件
        with open("PRISM_局部优化4站点.py", "r", encoding="utf-8") as f:
            prism_code = f.read()
        
        # 修改路径变量
        modified_code = prism_code.replace(
            'INPUT_DIR = r"D:/pythondata/spatial_interpolation/input_another/2015-3"', 
            f'INPUT_DIR = r"{folder_path}"'
        )
        modified_code = modified_code.replace(
            'OUTPUT_DIR = r"D:/pythondata/spatial_interpolation/output/PRISM/2015-3"', 
            f'OUTPUT_DIR = r"{output_folder}"'
        )
        
        # 创建临时脚本文件
        temp_script = os.path.join(TEMP_DIR, f"PRISM_temp_{directory}.py")
        with open(temp_script, "w", encoding="utf-8") as f:
            f.write(modified_code)
        
        # 运行临时脚本
        print(f"运行PRISM插值脚本，处理 {directory}...")
        process = subprocess.Popen(
            ["python", temp_script], 
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT,
            text=True
        )
        
        # 向脚本发送输入
        input_mode = "2\n"  # 选择模式 2 并添加换行符
        process.stdin.write(input_mode)
        process.stdin.close()  # 关闭标准输入

        # 捕获输出
        output = ""
        for line in process.stdout:
            print(line, end='')
            output += line
        
        process.wait()
        
        # 从输出中提取评价指标
        overall_metrics = {}
        nonzero_rain_metrics = {}
        
        # 首先尝试从评价指标文件中获取
        eval_files = [f for f in os.listdir(output_folder) if f.startswith('evaluation_') and f.endswith('.csv')]
        if eval_files:
            # 获取最新的评价文件
            latest_eval_file = os.path.join(output_folder, sorted(eval_files)[-1])
            print(f"找到评价指标文件: {latest_eval_file}")
            
            try:
                # 读取CSV文件
                eval_df = pd.read_csv(latest_eval_file)
                
                # 提取Overall行
                overall_row = eval_df[eval_df['TimePoint'] == 'Overall']
                if not overall_row.empty:
                    mae = overall_row['MAE'].values[0]
                    rmse = overall_row['RMSE'].values[0]
                    nse = overall_row['NSE'].values[0]
                    corr = overall_row['CORR'].values[0] if 'CORR' in overall_row.columns else float('nan')
                    
                    overall_metrics = {
                        'MAE': mae,
                        'RMSE': rmse,
                        'NSE': nse,
                        'CORR': corr
                    }
                    
                    print(f"Overall指标: MAE={mae:.4f}, RMSE={rmse:.4f}, NSE={nse:.4f}")
                else:
                    print(f"警告: 未找到Overall行")
                
                # 提取NonZeroRain行
                nonzero_rain_row = eval_df[eval_df['TimePoint'] == 'NonZeroRain']
                if not nonzero_rain_row.empty:
                    mae = nonzero_rain_row['MAE'].values[0]
                    rmse = nonzero_rain_row['RMSE'].values[0]
                    nse = nonzero_rain_row['NSE'].values[0]
                    corr = nonzero_rain_row['CORR'].values[0] if 'CORR' in nonzero_rain_row.columns else float('nan')
                    
                    nonzero_rain_metrics = {
                        'MAE': mae,
                        'RMSE': rmse,
                        'NSE': nse,
                        'CORR': corr
                    }
                    
                    print(f"NonZeroRain指标: MAE={mae:.4f}, RMSE={rmse:.4f}, NSE={nse:.4f}")
                else:
                    print(f"警告: 未找到NonZeroRain行")
            
            except Exception as e:
                print(f"读取评价指标文件时出错: {e}")
        else:
            print(f"警告: 在 {output_folder} 中未找到evaluation_*.csv文件")
        
        # 添加到结果列表
        if overall_metrics:
            overall_results.append({
                '文件夹': directory,
                'MAE': overall_metrics.get('MAE', float('nan')),
                'RMSE': overall_metrics.get('RMSE', float('nan')),
                'NSE': overall_metrics.get('NSE', float('nan')),
                'CORR': overall_metrics.get('CORR', float('nan')),
                '处理时间(秒)': round(time.time() - start_time, 2)
            })
        
        if nonzero_rain_metrics:
            nonzero_rain_results.append({
                '文件夹': directory,
                'MAE': nonzero_rain_metrics.get('MAE', float('nan')),
                'RMSE': nonzero_rain_metrics.get('RMSE', float('nan')),
                'NSE': nonzero_rain_metrics.get('NSE', float('nan')),
                'CORR': nonzero_rain_metrics.get('CORR', float('nan')),
                '处理时间(秒)': round(time.time() - start_time, 2)
            })
        
        elapsed_time = time.time() - start_time
        print(f"处理完成! 耗时: {int(elapsed_time//60)}分{int(elapsed_time%60)}秒")
    
    except KeyboardInterrupt:
        print("\n用户中断处理。保存已完成的结果...")
        break
        
    except Exception as e:
        print(f"处理文件夹 {directory} 时出错: {e}")
        
        # 添加错误记录
        overall_results.append({
            '文件夹': directory,
            'MAE': float('nan'),
            'RMSE': float('nan'),
            'NSE': float('nan'),
            'CORR': float('nan'),
            '处理时间(秒)': round(time.time() - start_time, 2),
            '错误': str(e)
        })
        nonzero_rain_results.append({
            '文件夹': directory,
            'MAE': float('nan'),
            'RMSE': float('nan'),
            'NSE': float('nan'),
            'CORR': float('nan'),
            '处理时间(秒)': round(time.time() - start_time, 2),
            '错误': str(e)
        })

# 将结果保存到CSV文件
try:
    if overall_results:
        overall_df = pd.DataFrame(overall_results)
        overall_df.to_csv(OVERALL_RESULTS_FILE, index=False, encoding='utf-8-sig')
        print(f"\nOverall结果已保存到 {OVERALL_RESULTS_FILE}")
    else:
        print(f"没有收集到Overall结果")
    
    if nonzero_rain_results:
        nonzero_rain_df = pd.DataFrame(nonzero_rain_results)
        nonzero_rain_df.to_csv(NONZERO_RAIN_RESULTS_FILE, index=False, encoding='utf-8-sig')
        print(f"\nNonZeroRain结果已保存到 {NONZERO_RAIN_RESULTS_FILE}")
    else:
        print(f"没有收集到NonZeroRain结果")
except Exception as e:
    print(f"保存结果时出错: {e}")

# 清理临时文件
try:
    for file in os.listdir(TEMP_DIR):
        if file.startswith("PRISM_temp_"):
            os.remove(os.path.join(TEMP_DIR, file))
    print("临时文件已清理")
except Exception as e:
    print(f"清理临时文件时出错: {e}")

print("\n批处理完成!")
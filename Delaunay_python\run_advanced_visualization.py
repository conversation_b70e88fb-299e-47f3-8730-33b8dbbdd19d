#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay三角剖分插值系统高级可视化启动脚本

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0

功能：
1. 检查数据完整性
2. 运行高级可视化分析
3. 生成综合分析报告
"""

import sys
import os
from pathlib import Path
import logging

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_requirements():
    """检查运行要求"""
    print("检查运行要求...")
    
    # 检查Python包
    required_packages = ['pandas', 'numpy', 'matplotlib', 'seaborn']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要的Python包: {', '.join(missing_packages)}")
        print(f"请运行: pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ Python包检查通过")
    return True

def check_data_availability():
    """检查数据可用性"""
    print("\n检查数据可用性...")
    
    # 检查输出目录
    output_dir = current_dir.parent / 'output' / 'Delaunay_interpolation'
    if not output_dir.exists():
        print(f"❌ Delaunay插值输出目录不存在: {output_dir}")
        print("请先运行Delaunay插值分析生成数据")
        return False
    
    # 检查评价指标目录
    metrics_dir = output_dir / 'metrics'
    if not metrics_dir.exists():
        print(f"❌ 评价指标目录不存在: {metrics_dir}")
        print("请先运行Delaunay插值分析生成评价指标")
        return False
    
    # 检查评价指标文件
    csv_files = list(metrics_dir.glob('*_metrics.csv'))
    if not csv_files:
        print(f"❌ 评价指标目录中没有CSV文件: {metrics_dir}")
        print("请先运行Delaunay插值分析生成评价指标")
        return False
    
    print(f"✅ 找到 {len(csv_files)} 个评价指标文件")
    
    # 检查数据完整性
    total_records = 0
    events = set()
    stations = set()
    
    try:
        import pandas as pd
        for file_path in csv_files[:5]:  # 检查前5个文件
            df = pd.read_csv(file_path)
            total_records += len(df)
            if '事件名称' in df.columns:
                events.update(df['事件名称'].unique())
            if '站点代码' in df.columns:
                stations.update(df['站点代码'].unique())
    except Exception as e:
        print(f"❌ 数据文件读取失败: {e}")
        return False
    
    print(f"✅ 数据检查通过:")
    print(f"   - 样本记录数: {total_records:,}+")
    print(f"   - 洪水事件数: {len(events)}+")
    print(f"   - 验证站点数: {len(stations)}+")
    
    return True

def run_visualization():
    """运行可视化分析"""
    print("\n开始运行高级可视化分析...")
    print("="*60)
    
    try:
        from advanced_visualization import DelaunayAdvancedVisualizer
        
        # 设置路径
        output_dir = current_dir.parent / 'output' / 'Delaunay_interpolation'
        metrics_dir = output_dir / 'metrics'
        
        # 创建可视化器
        visualizer = DelaunayAdvancedVisualizer(output_dir)
        
        # 生成所有可视化图表
        generated_files = visualizer.generate_all_visualizations(metrics_dir)
        
        print("\n" + "="*60)
        print("🎉 高级可视化分析完成！")
        print("="*60)
        
        return True, generated_files
        
    except Exception as e:
        print(f"\n❌ 可视化分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False, []

def show_results(generated_files):
    """显示结果"""
    if not generated_files:
        return
    
    print(f"\n📊 生成的可视化文件 ({len(generated_files)}个):")
    print("-" * 60)
    
    # 按类型分组显示
    file_groups = {
        '整体分析': [],
        '站点分析': [],
        '时间分析': [],
        '性能分析': [],
        '报告': []
    }
    
    for file_path in generated_files:
        filename = file_path.name
        if 'overall' in filename:
            file_groups['整体分析'].append(filename)
        elif 'station' in filename:
            file_groups['站点分析'].append(filename)
        elif 'temporal' in filename:
            file_groups['时间分析'].append(filename)
        elif 'performance' in filename:
            file_groups['性能分析'].append(filename)
        elif filename.endswith('.md'):
            file_groups['报告'].append(filename)
    
    for group_name, files in file_groups.items():
        if files:
            print(f"\n📁 {group_name}:")
            for filename in files:
                print(f"   • {filename}")
    
    # 显示保存路径
    viz_dir = generated_files[0].parent
    print(f"\n📂 所有文件保存在: {viz_dir}")
    
    # 提供使用建议
    print(f"\n💡 使用建议:")
    print(f"   1. 查看 'comprehensive_analysis_report.md' 获取详细分析报告")
    print(f"   2. 打开PNG图片文件查看可视化图表")
    print(f"   3. 可以将图片插入到论文或报告中")
    print(f"   4. 所有图表都是高分辨率(300 DPI)，适合学术发表")

def main():
    """主函数"""
    print("="*80)
    print("Delaunay三角剖分插值系统高级可视化分析")
    print("="*80)
    print("功能: 生成所有洪水场次的评价指标可视化")
    print("包括: 站点年平均分析、多年指标可视化、时间趋势分析等")
    print("="*80)
    
    try:
        # 1. 检查运行要求
        if not check_requirements():
            return 1
        
        # 2. 检查数据可用性
        if not check_data_availability():
            return 1
        
        # 3. 询问用户是否继续
        print(f"\n准备开始高级可视化分析...")
        response = input("是否继续? (y/n): ").strip().lower()
        if response not in ['y', 'yes', '是']:
            print("用户取消操作")
            return 0
        
        # 4. 运行可视化分析
        success, generated_files = run_visualization()
        if not success:
            return 1
        
        # 5. 显示结果
        show_results(generated_files)
        
        print(f"\n" + "="*80)
        print("✅ 高级可视化分析完成！")
        print("🎯 现在您可以查看生成的图表和报告进行深入分析")
        print("="*80)
        
        return 0
        
    except KeyboardInterrupt:
        print(f"\n\n用户中断程序执行")
        return 1
    except Exception as e:
        print(f"\n程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误
        format='%(levelname)s: %(message)s'
    )
    
    exit_code = main()
    sys.exit(exit_code)

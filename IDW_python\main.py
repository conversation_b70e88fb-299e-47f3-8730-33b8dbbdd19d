#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IDW插值系统主程序
基于Delaunay三角网分析结果的反距离权重插值系统

作者: 空间插值系统
日期: 2024年
"""

import sys
import logging
import argparse
from pathlib import Path
from datetime import datetime
import warnings

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 导入自定义模块
from idw_config import config
from data_loader import DataLoader
from idw_interpolation import IDWInterpolator
from evaluation_metrics import EvaluationMetrics
from visualization import IDWVisualizer
from raster_output import RasterOutput
from batch_processor import BatchProcessor

# 忽略警告
warnings.filterwarnings('ignore')

def setup_logging():
    """设置日志系统"""
    log_dir = config.output_dir / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"idw_interpolation_{timestamp}.log"
    
    # 配置日志
    logging.basicConfig(
        level=getattr(logging, config.log_level),
        format=config.log_format,
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统已启动，日志文件: {log_file}")
    
    return logger

def validate_system():
    """验证系统配置和依赖"""
    logger = logging.getLogger(__name__)
    
    logger.info("开始系统验证...")
    
    # 验证配置
    config_errors = config.validate_config()
    if config_errors:
        logger.error("配置验证失败:")
        for error in config_errors:
            logger.error(f"  - {error}")
        return False
    
    # 验证数据文件
    try:
        loader = DataLoader(config)
        available_stations = loader.get_available_stations()
        flood_events = config.get_flood_events()
        
        logger.info(f"找到{len(available_stations)}个验证站点")
        logger.info(f"找到{len(flood_events)}个洪水事件")
        
        if len(available_stations) == 0:
            logger.error("没有找到可用的验证站点")
            return False
        
        if len(flood_events) == 0:
            logger.error("没有找到洪水事件数据")
            return False
        
    except Exception as e:
        logger.error(f"数据验证失败: {e}")
        return False
    
    logger.info("系统验证通过")
    return True

def process_single_event(event_name: str):
    """处理单个洪水事件"""
    logger = logging.getLogger(__name__)
    
    logger.info(f"开始处理单个洪水事件: {event_name}")
    
    try:
        # 创建组件
        loader = DataLoader(config)
        interpolator = IDWInterpolator(config, loader)
        evaluator = EvaluationMetrics(config)
        visualizer = IDWVisualizer(config)
        raster_output = RasterOutput(config, loader) if config.enable_raster_output else None
        
        # 创建批处理器
        processor = BatchProcessor(
            config, loader, interpolator, evaluator, visualizer, raster_output
        )
        
        # 处理单个事件
        result = processor.process_single_event(event_name)
        
        if result['success']:
            logger.info(f"事件{event_name}处理成功")
            logger.info(f"处理时间: {result['processing_time']:.2f}秒")
            logger.info(f"插值站点数: {len(result['interpolation_results'])}")
            logger.info(f"生成可视化文件数: {len(result['visualization_files'])}")
            if config.enable_raster_output:
                logger.info(f"生成栅格文件数: {len(result['raster_files'])}")
        else:
            logger.error(f"事件{event_name}处理失败: {result['error_message']}")
        
        return result
        
    except Exception as e:
        logger.error(f"处理事件{event_name}时发生异常: {e}")
        return None

def process_all_events(event_list=None):
    """处理所有洪水事件"""
    logger = logging.getLogger(__name__)
    
    logger.info("开始批量处理洪水事件")
    
    try:
        # 创建组件
        loader = DataLoader(config)
        interpolator = IDWInterpolator(config, loader)
        evaluator = EvaluationMetrics(config)
        visualizer = IDWVisualizer(config)
        raster_output = RasterOutput(config, loader) if config.enable_raster_output else None
        
        # 创建批处理器
        processor = BatchProcessor(
            config, loader, interpolator, evaluator, visualizer, raster_output
        )
        
        # 批量处理
        results = processor.process_all_events(event_list)
        
        # 打印处理报告
        report = processor.get_processing_report()
        logger.info("批处理完成，报告如下:")
        logger.info(report)
        
        return results
        
    except Exception as e:
        logger.error(f"批量处理时发生异常: {e}")
        return None

def create_comprehensive_summary():
    """创建综合汇总报告"""
    logger = logging.getLogger(__name__)
    
    logger.info("开始创建综合汇总报告")
    
    try:
        # 读取所有评估结果
        evaluation_dir = config.output_dir / "evaluation_metrics"
        
        if not evaluation_dir.exists():
            logger.warning("评估结果目录不存在，无法创建汇总报告")
            return
        
        all_metrics = []
        event_summaries = []
        
        # 遍历所有事件的评估结果
        for event_dir in evaluation_dir.iterdir():
            if event_dir.is_dir():
                event_name = event_dir.name
                
                # 读取详细指标
                detailed_file = event_dir / f"{event_name}_detailed_metrics.csv"
                if detailed_file.exists():
                    event_metrics = pd.read_csv(detailed_file, index_col=0)
                    event_metrics['Event'] = event_name
                    all_metrics.append(event_metrics)
                
                # 读取汇总指标
                summary_file = event_dir / f"{event_name}_summary_metrics.csv"
                if summary_file.exists():
                    event_summary = pd.read_csv(summary_file)
                    event_summary['Event'] = event_name
                    event_summaries.append(event_summary)
        
        if all_metrics:
            # 合并所有详细指标
            combined_metrics = pd.concat(all_metrics, ignore_index=True)
            
            # 保存综合详细指标
            comprehensive_file = config.output_dir / "summary_reports" / "comprehensive_detailed_metrics.csv"
            combined_metrics.to_csv(comprehensive_file, index=False, encoding='utf-8')
            logger.info(f"综合详细指标已保存: {comprehensive_file}")
        
        if event_summaries:
            # 合并所有事件汇总
            combined_summaries = pd.concat(event_summaries, ignore_index=True)
            
            # 保存综合事件汇总
            summary_file = config.output_dir / "summary_reports" / "comprehensive_event_summaries.csv"
            combined_summaries.to_csv(summary_file, index=False, encoding='utf-8')
            logger.info(f"综合事件汇总已保存: {summary_file}")
        
        logger.info("综合汇总报告创建完成")
        
    except Exception as e:
        logger.error(f"创建综合汇总报告时发生异常: {e}")

def main():
    """主函数"""
    # 设置命令行参数
    parser = argparse.ArgumentParser(description='IDW插值系统 - 基于Delaunay三角网分析结果')
    parser.add_argument('--mode', choices=['single', 'batch', 'summary'], default='batch',
                       help='运行模式: single(单个事件), batch(批量处理), summary(创建汇总)')
    parser.add_argument('--event', type=str, help='单个事件模式下的事件名称')
    parser.add_argument('--events', nargs='+', help='指定要处理的事件列表')
    parser.add_argument('--disable-raster', action='store_true', help='禁用栅格输出')
    parser.add_argument('--disable-viz', action='store_true', help='禁用可视化')
    parser.add_argument('--cores', type=int, help='并行处理核心数')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    
    logger.info("="*60)
    logger.info("IDW插值系统启动")
    logger.info("="*60)
    
    # 应用命令行参数
    if args.disable_raster:
        config.enable_raster_output = False
        logger.info("栅格输出已禁用")
    
    if args.disable_viz:
        config.enable_visualization = False
        logger.info("可视化已禁用")
    
    if args.cores:
        config.n_cores = args.cores
        logger.info(f"并行核心数设置为: {args.cores}")
    
    # 打印配置信息
    config.print_config()
    
    # 验证系统
    if not validate_system():
        logger.error("系统验证失败，程序退出")
        sys.exit(1)
    
    # 根据模式执行相应操作
    try:
        if args.mode == 'single':
            if not args.event:
                logger.error("单个事件模式需要指定 --event 参数")
                sys.exit(1)
            
            result = process_single_event(args.event)
            if result and result['success']:
                logger.info("单个事件处理成功完成")
            else:
                logger.error("单个事件处理失败")
                sys.exit(1)
        
        elif args.mode == 'batch':
            event_list = args.events if args.events else None
            results = process_all_events(event_list)
            
            if results:
                successful_count = sum(1 for r in results.values() if r.get('success', False))
                total_count = len(results)
                logger.info(f"批量处理完成: {successful_count}/{total_count} 个事件成功")
                
                if successful_count == 0:
                    logger.error("所有事件处理失败")
                    sys.exit(1)
            else:
                logger.error("批量处理失败")
                sys.exit(1)
        
        elif args.mode == 'summary':
            create_comprehensive_summary()
            logger.info("汇总报告创建完成")
        
        logger.info("="*60)
        logger.info("IDW插值系统运行完成")
        logger.info("="*60)
        
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

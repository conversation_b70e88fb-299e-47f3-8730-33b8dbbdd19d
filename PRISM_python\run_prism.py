#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PRISM空间插值系统 - 主运行脚本
简单易用的PRISM插值系统入口

使用方法:
1. 直接运行: python run_prism.py
2. 交互式设置: python run_prism.py --setup
3. 批量处理: python run_prism.py --mode batch
4. 指定配置: python run_prism.py --config my_config.json

作者: AI助手
版本: 1.0
"""

import os
import sys
import json
from datetime import datetime

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from easy_run import main, run_prism_interpolation, interactive_setup, create_default_config_file


def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'numpy', 'pandas', 'scipy', 'matplotlib', 'rasterio', 
        'scikit-learn', 'tqdm'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("错误: 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def show_welcome():
    """显示欢迎信息"""
    print("="*70)
    print("                    PRISM空间插值系统")
    print("              基于PRISM方法的降雨空间插值")
    print("="*70)
    print("功能特点:")
    print("  ✓ 基于Delaunay三角网的站点选择")
    print("  ✓ 莫兰指数空间自相关分析")
    print("  ✓ 地形特征权重计算")
    print("  ✓ 留一法交叉验证")
    print("  ✓ 多核并行计算支持")
    print("  ✓ 栅格输出和可视化")
    print("  ✓ 批量处理支持")
    print("="*70)


def quick_start():
    """快速开始向导"""
    print("\n快速开始向导")
    print("-"*30)
    
    # 检查是否存在配置文件
    config_file = "prism_config.json"
    
    if os.path.exists(config_file):
        print(f"发现配置文件: {config_file}")
        
        # 询问运行模式
        print("\n请选择运行模式:")
        print("1. 单文件夹处理")
        print("2. 批量处理")
        print("3. 重新配置")
        print("4. 退出")
        
        while True:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                print("\n开始单文件夹处理...")
                return run_prism_interpolation(config_file, "single")
            elif choice == '2':
                print("\n开始批量处理...")
                return run_prism_interpolation(config_file, "batch")
            elif choice == '3':
                print("\n启动交互式配置...")
                return interactive_setup()
            elif choice == '4':
                print("退出程序")
                return True
            else:
                print("无效选择，请重新输入")
    else:
        print(f"未发现配置文件，将创建默认配置: {config_file}")
        
        # 询问是否使用交互式设置
        use_interactive = input("是否使用交互式设置? (y/n) [y]: ").strip().lower()
        
        if use_interactive != 'n':
            return interactive_setup()
        else:
            # 创建默认配置文件
            create_default_config_file(config_file)
            print(f"\n默认配置文件已创建: {config_file}")
            print("请根据您的数据路径修改配置文件，然后重新运行程序")
            return True


def main_entry():
    """主入口函数"""
    try:
        # 显示欢迎信息
        show_welcome()
        
        # 检查依赖
        if not check_dependencies():
            return False
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            # 有命令行参数，使用原始main函数
            main()
        else:
            # 无命令行参数，使用快速开始向导
            success = quick_start()
            if success:
                print("\n程序执行完成！")
            else:
                print("\n程序执行失败！")
                return False
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n用户中断程序执行")
        return False
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        return False


if __name__ == "__main__":
    success = main_entry()
    sys.exit(0 if success else 1)

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OI插值系统安装和运行脚本

这个脚本会自动检查和安装必要的依赖包，然后运行OI插值系统
"""

import os
import sys
import subprocess
import importlib

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误：需要Python 3.7或更高版本")
        print(f"当前版本：{sys.version}")
        return False
    print(f"Python版本检查通过：{sys.version}")
    return True

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"{package_name} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"安装 {package_name} 失败")
        return False

def check_and_install_dependencies():
    """检查并安装依赖包"""
    required_packages = {
        'numpy': 'numpy>=1.19.0',
        'pandas': 'pandas>=1.3.0',
        'scipy': 'scipy>=1.7.0',
        'matplotlib': 'matplotlib>=3.3.0'
    }
    
    missing_packages = []
    
    # 检查每个包是否已安装
    for package_name, package_spec in required_packages.items():
        try:
            importlib.import_module(package_name)
            print(f"✓ {package_name} 已安装")
        except ImportError:
            print(f"✗ {package_name} 未安装")
            missing_packages.append(package_spec)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n需要安装 {len(missing_packages)} 个包...")
        for package in missing_packages:
            if not install_package(package):
                return False
        print("所有依赖包安装完成")
    else:
        print("所有依赖包已安装")
    
    return True

def create_default_config():
    """创建默认配置文件"""
    try:
        from OI_python.config import Config
        
        config = Config()
        config_file = "oi_config.json"
        config.save_to_file(config_file)
        
        print(f"默认配置文件已创建：{config_file}")
        print("您可以编辑此文件来修改配置参数")
        return config_file
    except Exception as e:
        print(f"创建配置文件失败：{e}")
        return None

def run_oi_system():
    """运行OI插值系统"""
    try:
        from OI_python.run_oi import main
        main()
    except Exception as e:
        print(f"运行OI系统失败：{e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("="*60)
    print("OI插值系统 - 自动安装和运行脚本")
    print("="*60)
    
    # 1. 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 2. 检查并安装依赖
    print("\n" + "="*40)
    print("检查和安装依赖包")
    print("="*40)
    
    if not check_and_install_dependencies():
        print("依赖包安装失败，请手动安装")
        input("按回车键退出...")
        return
    
    # 3. 创建默认配置文件
    print("\n" + "="*40)
    print("创建配置文件")
    print("="*40)
    
    config_file = create_default_config()
    
    # 4. 运行系统
    print("\n" + "="*40)
    print("启动OI插值系统")
    print("="*40)
    
    try:
        run_oi_system()
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"运行出错：{e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()

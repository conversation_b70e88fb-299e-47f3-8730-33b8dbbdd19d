"""
Kriging插值系统简易运行接口
为新手用户提供友好的运行方式
"""

import os
import sys
import json
import argparse
from datetime import datetime
from typing import Dict, Optional

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from config import Config, save_config_to_file, load_config_from_file
from kriging_main import KrigingInterpolation


def create_default_config_file(config_path: str = "kriging_config.json") -> str:
    """创建默认配置文件"""
    try:
        default_config = Config()
        save_config_to_file(default_config, config_path)
        print(f"默认配置文件已创建: {config_path}")
        return config_path
    except Exception as e:
        print(f"创建默认配置文件失败: {e}")
        return ""


def interactive_setup() -> bool:
    """交互式设置"""
    try:
        print("\n" + "="*60)
        print("           Kriging插值系统 - 交互式设置")
        print("="*60)
        
        # 获取基本路径信息
        print("\n请输入以下路径信息（直接回车使用默认值）:")
        
        # 输入目录
        input_dir = input(f"输入数据目录 [D:/pythondata/spatial_interpolation/input_another/2015-4]: ").strip()
        if not input_dir:
            input_dir = "D:/pythondata/spatial_interpolation/input_another/2015-4"

        # 输出目录
        output_dir = input(f"输出目录 [D:/pythondata/spatial_interpolation/output/Kriging/2015-4]: ").strip()
        if not output_dir:
            output_dir = "D:/pythondata/spatial_interpolation/output/Kriging/2015-4"
        
        # 站点文件
        stations_file = input(f"站点文件 [D:/pythondata/spatial_interpolation/stations.csv]: ").strip()
        if not stations_file:
            stations_file = "D:/pythondata/spatial_interpolation/stations.csv"
        
        # Kriging参数设置
        print("\n请设置Kriging参数:")
        
        # 半变异函数模型
        print("半变异函数模型选择:")
        print("1. spherical (球状) - 推荐")
        print("2. exponential (指数)")
        print("3. gaussian (高斯)")
        print("4. linear (线性)")
        
        model_choice = input("请选择模型 (1-4) [1]: ").strip()
        model_map = {'1': 'spherical', '2': 'exponential', '3': 'gaussian', '4': 'linear'}
        variogram_model = model_map.get(model_choice, 'spherical')
        
        # 邻近站点数量
        neighbor_count = input("邻近站点数量 [3]: ").strip()
        try:
            neighbor_count = int(neighbor_count) if neighbor_count else 3
        except:
            neighbor_count = 3
        
        # 并行核心数
        num_cores = input("并行核心数 [24]: ").strip()
        try:
            num_cores = int(num_cores) if num_cores else 24
        except:
            num_cores = 24
        
        # 是否启用莫兰指数权重
        enable_moran = input("是否启用莫兰指数权重? (y/n) [y]: ").strip().lower()
        enable_moran_weighting = enable_moran != 'n'
        
        # 是否启用参数优化
        enable_opt = input("是否启用参数优化? (y/n) [y]: ").strip().lower()
        enable_parameter_optimization = enable_opt != 'n'
        
        # 是否输出栅格
        output_raster = input("是否输出栅格文件? (y/n) [y]: ").strip().lower()
        output_raster = output_raster != 'n'
        
        # 创建配置对象
        config = Config(
            input_dir=input_dir,
            output_dir=output_dir,
            stations_file=stations_file,
            variogram_model=variogram_model,
            neighbor_count=neighbor_count,
            num_cores=num_cores,
            enable_moran_weighting=enable_moran_weighting,
            enable_parameter_optimization=enable_parameter_optimization,
            output_raster=output_raster
        )
        
        # 保存配置
        config_file = "kriging_config.json"
        save_config_to_file(config, config_file)
        
        print(f"\n配置已保存到: {config_file}")
        
        # 询问是否立即运行
        run_now = input("\n是否立即运行插值? (y/n) [y]: ").strip().lower()
        
        if run_now != 'n':
            return run_kriging_interpolation(config_file, "single")
        else:
            print("配置完成！您可以稍后运行 python run_kriging.py 开始插值")
            return True
        
    except KeyboardInterrupt:
        print("\n用户取消设置")
        return False
    except Exception as e:
        print(f"交互式设置失败: {e}")
        return False


def run_kriging_interpolation(config_file: str, mode: str = "single") -> bool:
    """运行Kriging插值"""
    try:
        print(f"\n开始运行Kriging插值 (模式: {mode})...")
        
        # 加载配置
        if not os.path.exists(config_file):
            print(f"配置文件不存在: {config_file}")
            return False
        
        config = load_config_from_file(config_file)
        
        # 验证路径
        if not os.path.exists(config.input_dir):
            print(f"输入目录不存在: {config.input_dir}")
            return False
        
        if not os.path.exists(config.stations_file):
            print(f"站点文件不存在: {config.stations_file}")
            return False
        
        # 创建输出目录
        os.makedirs(config.output_dir, exist_ok=True)
        
        print(f"配置信息:")
        print(f"  输入目录: {config.input_dir}")
        print(f"  输出目录: {config.output_dir}")
        print(f"  站点文件: {config.stations_file}")
        print(f"  半变异函数模型: {config.variogram_model}")
        print(f"  邻近站点数: {config.neighbor_count}")
        print(f"  并行核心数: {config.num_cores}")
        print(f"  莫兰指数权重: {'启用' if config.enable_moran_weighting else '禁用'}")
        print(f"  参数优化: {'启用' if config.enable_parameter_optimization else '禁用'}")
        
        # 运行插值
        kriging = KrigingInterpolation(config)
        results = kriging.run_complete_workflow()
        
        if results:
            print(f"\n✅ Kriging插值完成!")
            print(f"NSE: {results.get('NSE', 0):.4f}")
            print(f"RMSE: {results.get('RMSE', 0):.4f}")
            print(f"R²: {results.get('R2', 0):.4f}")
            print(f"结果保存在: {config.output_dir}")
            return True
        else:
            print("❌ Kriging插值失败")
            return False
        
    except Exception as e:
        print(f"运行Kriging插值失败: {e}")
        return False


def show_help():
    """显示帮助信息"""
    help_text = """
Kriging空间插值系统 - 使用帮助
================================

基本用法:
  python easy_run.py                    # 交互式运行
  python easy_run.py --setup            # 交互式设置
  python easy_run.py --config FILE      # 使用指定配置文件
  python easy_run.py --mode MODE        # 指定运行模式

参数说明:
  --setup                 启动交互式设置向导
  --config FILE          指定配置文件路径
  --mode MODE            运行模式 (single/batch)
  --help                 显示此帮助信息

配置文件:
  系统会自动创建 kriging_config.json 配置文件
  您可以直接编辑此文件来修改参数

示例:
  python easy_run.py --setup                    # 交互式设置
  python easy_run.py --config my_config.json    # 使用自定义配置
  python easy_run.py --mode single              # 单文件夹模式

更多信息请查看 README.md 文档
    """
    print(help_text)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Kriging空间插值系统')
    parser.add_argument('--setup', action='store_true', help='交互式设置')
    parser.add_argument('--config', type=str, default='kriging_config.json', help='配置文件路径')
    parser.add_argument('--mode', type=str, choices=['single', 'batch'], default='single', help='运行模式')
    parser.add_argument('--help-detail', action='store_true', help='显示详细帮助')
    
    args = parser.parse_args()
    
    try:
        if args.help_detail:
            show_help()
            return True
        
        if args.setup:
            # 交互式设置
            return interactive_setup()
        
        # 检查配置文件
        if not os.path.exists(args.config):
            print(f"配置文件不存在: {args.config}")
            
            # 询问是否创建默认配置
            create_default = input("是否创建默认配置文件? (y/n) [y]: ").strip().lower()
            
            if create_default != 'n':
                config_file = create_default_config_file(args.config)
                if not config_file:
                    return False
                
                print("请根据您的数据路径修改配置文件，然后重新运行")
                return True
            else:
                return False
        
        # 运行插值
        return run_kriging_interpolation(args.config, args.mode)
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return False
    except Exception as e:
        print(f"程序执行失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

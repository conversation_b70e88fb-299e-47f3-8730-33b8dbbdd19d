#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay插值系统数据加载器

负责加载Delaunay分析结果和洪水事件数据
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings
import gc

warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class DelaunayDataLoader:
    """Delaunay插值数据加载器"""
    
    def __init__(self, config):
        self.config = config
        self.delaunay_analysis = None
        self.station_mapping = {}
        
    def load_delaunay_analysis(self) -> pd.DataFrame:
        """加载Delaunay分析结果"""
        try:
            logger.info(f"加载Delaunay分析文件: {self.config.DELAUNAY_ANALYSIS_FILE}")
            
            # 读取CSV文件
            df = pd.read_csv(self.config.DELAUNAY_ANALYSIS_FILE, encoding='utf-8')
            
            # 验证必要列是否存在
            required_cols = [
                '验证站点代码', '验证站点名称', '验证站点经度', '验证站点纬度',
                '包围站点1代码', '包围站点1名称', '包围站点2代码', '包围站点2名称', 
                '包围站点3代码', '包围站点3名称', '权重1', '权重2', '权重3'
            ]
            
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                raise ValueError(f"缺少必要列: {missing_cols}")
            
            # 清理数据
            df = df.dropna(subset=['验证站点代码'])
            
            # 转换数据类型
            df['验证站点代码'] = df['验证站点代码'].astype(str)
            df['包围站点1代码'] = df['包围站点1代码'].astype(str)
            df['包围站点2代码'] = df['包围站点2代码'].astype(str)
            df['包围站点3代码'] = df['包围站点3代码'].astype(str)
            
            # 创建站点映射
            for _, row in df.iterrows():
                station_id = str(row['验证站点代码'])
                self.station_mapping[station_id] = {
                    'name': row['验证站点名称'],
                    'longitude': row['验证站点经度'],
                    'latitude': row['验证站点纬度'],
                    'surrounding_stations': [
                        {
                            'id': str(row['包围站点1代码']),
                            'name': row['包围站点1名称'],
                            'weight': row['权重1']
                        },
                        {
                            'id': str(row['包围站点2代码']),
                            'name': row['包围站点2名称'],
                            'weight': row['权重2']
                        },
                        {
                            'id': str(row['包围站点3代码']),
                            'name': row['包围站点3名称'],
                            'weight': row['权重3']
                        }
                    ]
                }
            
            self.delaunay_analysis = df
            logger.info(f"成功加载{len(df)}个站点的Delaunay分析结果")
            
            return df
            
        except Exception as e:
            logger.error(f"加载Delaunay分析文件失败: {e}")
            raise
    
    def get_station_info(self, station_id: str) -> Optional[Dict]:
        """获取站点信息"""
        return self.station_mapping.get(str(station_id))
    
    def load_flood_event_data(self, event_name: str, memory_efficient: bool = True) -> Dict[str, pd.DataFrame]:
        """加载指定洪水事件的所有站点数据（内存优化版本）"""
        try:
            event_dir = self.config.INPUT_DIR / event_name
            if not event_dir.exists():
                raise FileNotFoundError(f"洪水事件目录不存在: {event_dir}")

            logger.info(f"加载洪水事件数据: {event_name}")

            station_data = {}
            csv_files = list(event_dir.glob("*.csv"))

            # 内存优化：分批处理文件
            if memory_efficient and hasattr(self.config, 'MEMORY_EFFICIENT_MODE') and self.config.MEMORY_EFFICIENT_MODE:
                chunk_size = getattr(self.config, 'CHUNK_SIZE', 50)
                for i in range(0, len(csv_files), chunk_size):
                    chunk_files = csv_files[i:i + chunk_size]
                    logger.debug(f"处理文件块 {i//chunk_size + 1}/{(len(csv_files)-1)//chunk_size + 1}")

                    for csv_file in chunk_files:
                        station_id = csv_file.stem

                        try:
                            # 使用更少内存的方式读取数据
                            df = pd.read_csv(csv_file, encoding='utf-8',
                                           dtype={'雨量': 'float32'},  # 使用float32节省内存
                                           parse_dates=['时间'])

                            # 验证数据格式
                            if '时间' not in df.columns or '雨量' not in df.columns:
                                logger.warning(f"站点{station_id}数据格式不正确，跳过")
                                continue

                            # 转换雨量列为数值类型
                            df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce').astype('float32')

                            # 设置时间为索引
                            df = df.set_index('时间')

                            # 去除重复时间
                            df = df[~df.index.duplicated(keep='first')]

                            # 排序
                            df = df.sort_index()

                            station_data[station_id] = df

                        except Exception as e:
                            logger.warning(f"加载站点{station_id}数据失败: {e}")
                            continue

                    # 强制垃圾回收
                    gc.collect()
            else:
                # 原始方式（非内存优化）
                for csv_file in csv_files:
                    station_id = csv_file.stem

                    try:
                        # 读取站点数据
                        df = pd.read_csv(csv_file, encoding='utf-8')

                        # 验证数据格式
                        if '时间' not in df.columns or '雨量' not in df.columns:
                            logger.warning(f"站点{station_id}数据格式不正确，跳过")
                            continue

                        # 转换时间列
                        df['时间'] = pd.to_datetime(df['时间'])

                        # 转换雨量列为数值类型
                        df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce')

                        # 设置时间为索引
                        df = df.set_index('时间')

                        # 去除重复时间
                        df = df[~df.index.duplicated(keep='first')]

                        # 排序
                        df = df.sort_index()

                        station_data[station_id] = df

                    except Exception as e:
                        logger.warning(f"加载站点{station_id}数据失败: {e}")
                        continue

            logger.info(f"成功加载{len(station_data)}个站点的数据")
            return station_data

        except Exception as e:
            logger.error(f"加载洪水事件{event_name}数据失败: {e}")
            raise
    
    def get_common_time_range(self, station_data: Dict[str, pd.DataFrame]) -> Tuple[pd.Timestamp, pd.Timestamp]:
        """获取所有站点的公共时间范围"""
        if not station_data:
            return None, None
        
        # 获取所有站点的时间范围
        start_times = []
        end_times = []
        
        for station_id, df in station_data.items():
            if not df.empty:
                start_times.append(df.index.min())
                end_times.append(df.index.max())
        
        if not start_times:
            return None, None
        
        # 公共时间范围是所有站点的交集
        common_start = max(start_times)
        common_end = min(end_times)
        
        return common_start, common_end
    
    def get_available_stations_for_event(self, event_name: str) -> List[str]:
        """获取洪水事件中可用的验证站点列表"""
        try:
            # 加载事件数据
            station_data = self.load_flood_event_data(event_name)
            
            # 筛选在Delaunay分析中的站点
            available_stations = []
            for station_id in station_data.keys():
                if station_id in self.station_mapping:
                    available_stations.append(station_id)
            
            return available_stations
            
        except Exception as e:
            logger.error(f"获取事件{event_name}可用站点失败: {e}")
            return []
    
    def validate_station_data(self, station_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """验证和清理站点数据"""
        validated_data = {}
        
        for station_id, df in station_data.items():
            try:
                # 检查数据是否为空
                if df.empty:
                    logger.warning(f"站点{station_id}数据为空，跳过")
                    continue
                
                # 检查是否有有效的雨量数据
                valid_data = df['雨量'].dropna()
                if valid_data.empty:
                    logger.warning(f"站点{station_id}无有效雨量数据，跳过")
                    continue
                
                # 填充缺失值为0（根据用户偏好）
                df_clean = df.copy()
                df_clean['雨量'] = df_clean['雨量'].fillna(0.0)
                
                validated_data[station_id] = df_clean
                
            except Exception as e:
                logger.warning(f"验证站点{station_id}数据失败: {e}")
                continue
        
        return validated_data

    def clear_cache(self):
        """清理缓存以释放内存"""
        try:
            self.station_mapping.clear()
            if hasattr(self, 'delaunay_analysis'):
                del self.delaunay_analysis
                self.delaunay_analysis = None
            gc.collect()
            logger.debug("缓存已清理")
        except Exception as e:
            logger.warning(f"清理缓存失败: {e}")

    def get_memory_usage(self):
        """获取当前内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return {
                'rss': memory_info.rss / 1024 / 1024,  # MB
                'vms': memory_info.vms / 1024 / 1024   # MB
            }
        except ImportError:
            return {'rss': 0, 'vms': 0}

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OI插值系统运行脚本

这是一个简单的运行脚本，新手可以直接运行此文件来使用OI插值系统
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from OI_python.config import Config
from OI_python.easy_run import run_oi_interpolation, interactive_run, create_sample_config

def main():
    """
    主函数
    """
    print("="*60)
    print("OI插值系统 - 简单运行脚本")
    print("="*60)
    print("本系统实现了基于最优插值法的点雨量到面雨量的空间插值")
    print("使用Delaunay三角网进行站点筛选，并应用留一法进行验证")
    print("="*60)
    
    print("\n请选择运行方式:")
    print("1. 使用默认配置运行（推荐新手）")
    print("2. 交互式配置运行")
    print("3. 创建配置文件模板")
    print("4. 退出")
    
    while True:
        choice = input("\n请输入选择 (1/2/3/4): ").strip()
        
        if choice == '1':
            # 使用默认配置运行
            print("\n使用默认配置运行...")
            print("注意：请确保以下文件和目录存在：")
            
            config = Config()
            print(f"  - 输入目录: {config.input_dir}")
            print(f"  - 站点文件: {config.stations_file}")
            print(f"  - 掩膜文件: {config.mask_file}")
            print(f"  - 输出目录: {config.output_dir}")
            
            confirm = input("\n确认运行? (y/n): ").strip().lower()
            if confirm in ['y', 'yes']:
                success = run_oi_interpolation()
                if success:
                    print("\n处理完成！请查看输出目录中的结果文件。")
                else:
                    print("\n处理失败，请检查错误信息。")
            break
        
        elif choice == '2':
            # 交互式配置运行
            print("\n启动交互式配置...")
            success = interactive_run()
            if success:
                print("\n处理完成！请查看输出目录中的结果文件。")
            else:
                print("\n处理失败，请检查错误信息。")
            break
        
        elif choice == '3':
            # 创建配置文件模板
            config_path = input("\n配置文件保存路径 (默认: oi_config.json): ").strip()
            if not config_path:
                config_path = "oi_config.json"
            
            if create_sample_config(config_path):
                print(f"\n配置文件已创建: {config_path}")
                print("您可以编辑此文件来修改配置参数，然后使用以下命令运行：")
                print(f"python -m OI_python.easy_run --config {config_path}")
            break
        
        elif choice == '4':
            print("\n退出程序")
            break
        
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()

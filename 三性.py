'''D:/pythondata/spatial_interpolation/input/全年/rain
                                                ├─ /2009-1/rain.csv
                                                ├── 2009-2/rain.csv
                                                ...
                                                └── 2023-1/rain.csv
                                            └─ output/sanxing/
                                            └─stations.csv（包含站点经纬度信息的表格）'''
import pandas as pd
import numpy as np
import os
import glob
import logging
import matplotlib.pyplot as plt
from scipy.spatial.distance import pdist, squareform
import traceback

# 定义根目录
root_dir = 'D:/pythondata/spatial_interpolation'
input_dir = os.path.join(root_dir, 'input', '全年', 'rain')
output_dir = os.path.join(root_dir, 'output', 'sanxing')
stations_path = os.path.join(root_dir, 'stations.csv')

# 创建输出目录
os.makedirs(output_dir, exist_ok=True)

# 设置日志记录
log_file = os.path.join(output_dir, 'correction_log.log')
logging.basicConfig(filename=log_file, level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    filemode='w')  # 每次运行覆盖日志文件
logging.info("=== 开始点雨量数据三性检查 ===")

print(f"根目录: {root_dir}")
print(f"输入目录: {input_dir}")
print(f"输出目录: {output_dir}")
print(f"站点文件: {stations_path}")

# 读取站点信息
try:
    stations_df = pd.read_csv(stations_path)
    print(f"成功读取站点信息，共 {len(stations_df)} 个站点")
    print(f"Stations.csv的列名: {stations_df.columns.tolist()}")
    print("Stations.csv的前5行示例:")
    print(stations_df.head())
    logging.info(f"成功读取站点信息，共 {len(stations_df)} 个站点")
except Exception as e:
    print(f"错误: 读取站点信息失败: {str(e)}")
    logging.error(f"读取站点信息失败: {str(e)}")
    raise

# 获取所有事件文件夹
try:
    event_folders = [f for f in os.listdir(input_dir) if os.path.isdir(os.path.join(input_dir, f))]
    event_folders.sort()  # 按名称排序
    print(f"找到 {len(event_folders)} 个事件文件夹: {', '.join(event_folders)}")
    logging.info(f"找到 {len(event_folders)} 个事件文件夹: {', '.join(event_folders)}")
except Exception as e:
    print(f"错误: 扫描事件文件夹失败: {str(e)}")
    logging.error(f"扫描事件文件夹失败: {str(e)}")
    raise

# 计算站点之间的距离矩阵
def calculate_distance_matrix(stations):
    """计算站点之间的距离矩阵"""
    print("正在计算站点距离矩阵...")
    
    # 检查必要的列是否存在
    if '经度' not in stations.columns or '纬度' not in stations.columns:
        raise ValueError(f"stations_df缺少必要的列。现有列: {stations.columns.tolist()}")
        
    coords = stations[['经度', '纬度']].values
    
    # 检查坐标值是否有效
    if np.isnan(coords).any():
        print("警告: 站点坐标中存在NaN值")
    
    dist_matrix = squareform(pdist(coords, metric='euclidean'))
    print(f"站点距离矩阵计算完成，形状: {dist_matrix.shape}")
    return dist_matrix

try:
    distance_matrix = calculate_distance_matrix(stations_df)
    logging.info("已计算站点距离矩阵")
except Exception as e:
    print(f"错误: 计算站点距离矩阵失败: {str(e)}")
    print(traceback.format_exc())
    logging.error(f"计算站点距离矩阵失败: {str(e)}")
    logging.error(traceback.format_exc())
    raise

def get_station_index(station_name, stations_df):
    """获取站点在distance_matrix中的索引"""
    try:
        # 首先在站点表中查找
        idx = stations_df.index[stations_df['站点'] == station_name].tolist()
        if idx:
            return idx[0]
        return None
    except Exception as e:
        print(f"警告: 获取站点 {station_name} 索引时出错: {str(e)}")
        logging.warning(f"获取站点 {station_name} 索引时出错: {str(e)}")
        return None

def filter_stations(station_cols):
    """过滤掉包含 'r' 的站点"""
    return [col for col in station_cols if 'r' not in col.lower()]

def perform_sanxing_check(df, distance_matrix, stations_df, event_name):
    """执行三性检查和异常值处理"""
    logging.info(f"\n{'='*50}\n开始处理事件: {event_name}\n{'='*50}")
    modified = False
    result_df = df.copy()
    
    # 提取时间列和站点列
    time_col = df.columns[0]
    all_station_cols = df.columns[1:]
    
    # 过滤掉包含 'r' 的站点
    station_cols = filter_stations(all_station_cols)
    excluded_stations = [col for col in all_station_cols if col not in station_cols]
    
    logging.info(f"时间列名: {time_col}, 数据点数: {len(df)}, 有效站点数: {len(station_cols)}/{len(all_station_cols)}")
    print(f"处理事件 {event_name}: 时间列名: {time_col}, 数据点数: {len(df)}, 有效站点数: {len(station_cols)}/{len(all_station_cols)}")
    
    if excluded_stations:
        print(f"已排除 {len(excluded_stations)} 个包含'r'的站点: {excluded_stations}")
        logging.info(f"已排除 {len(excluded_stations)} 个包含'r'的站点: {excluded_stations}")
    
    # 记录原始数据的统计信息
    valid_data = df[station_cols].values
    valid_data = valid_data[~np.isnan(valid_data)]
    
    if len(valid_data) == 0:
        print(f"警告: 事件 {event_name} 没有有效数据")
        logging.warning(f"事件 {event_name} 没有有效数据")
        return result_df, False
        
    mean_val = np.mean(valid_data)
    max_val = np.max(valid_data)
    min_val = np.min(valid_data)
    
    logging.info(f"原始数据统计: 均值={mean_val:.2f}, 最大值={max_val:.2f}, 最小值={min_val:.2f}")
    print(f"原始数据统计: 均值={mean_val:.2f}, 最大值={max_val:.2f}, 最小值={min_val:.2f}")
    
    # 检查站点匹配情况
    matched_stations = []
    unmatched_stations = []
    for col in station_cols:
        station_idx = get_station_index(col, stations_df)
        if station_idx is not None:
            matched_stations.append(col)
        else:
            unmatched_stations.append(col)
            
    print(f"站点匹配情况: 成功匹配 {len(matched_stations)}/{len(station_cols)}")
    if unmatched_stations:
        print(f"未匹配站点示例(最多5个): {unmatched_stations[:5]}")
        logging.warning(f"未匹配站点: {unmatched_stations}")
    
    # 如果没有匹配的站点，则停止处理
    if len(matched_stations) == 0:
        print(f"错误: 事件 {event_name} 没有匹配的站点，无法进行三性检查")
        logging.error(f"事件 {event_name} 没有匹配的站点，无法进行三性检查")
        return result_df, False
    
    # 只使用匹配的站点进行处理
    station_cols = matched_stations
    
    # 1. 合理性检查
    # 1.1 检查负值（雨量不应为负）
    negative_mask = df[station_cols] < 0
    negative_count = negative_mask.sum().sum()
    
    if negative_count > 0:
        print(f"合理性检查: 发现 {negative_count} 个负值，将其替换为0")
        logging.info(f"合理性检查: 发现 {negative_count} 个负值，将其替换为0")
        for col in station_cols:
            neg_indices = df.index[negative_mask[col]]
            for idx in neg_indices:
                time_val = df.loc[idx, time_col]
                old_val = df.loc[idx, col]
                logging.info(f"  - {col}, 时间 {time_val}, 负值 {old_val} 被替换为 0")
                result_df.loc[idx, col] = 0
        modified = True
    else:
        print("合理性检查: 未发现负值")
        logging.info("合理性检查: 未发现负值")
    
    # 1.2 检查异常大值
    # 使用IQR方法检测异常值
    station_stats = {}
    for col in station_cols:
        values = df[col].dropna().values.astype(float)  # 确保类型为浮点数
        if len(values) > 0:
            q1 = np.percentile(values, 25)
            q3 = np.percentile(values, 75)
            iqr = q3 - q1
            upper_bound = q3 + 5 * iqr  # 3倍IQR为异常值阈值
            station_stats[col] = {'q1': q1, 'q3': q3, 'iqr': iqr, 'upper_bound': upper_bound}
    
    # 设置绝对上限
    absolute_max = 300  # 假设300mm是合理的最大值
    
    extreme_modified_count = 0
    for col in station_cols:
        if col not in station_stats:
            continue
            
        upper_bound = min(station_stats[col]['upper_bound'], absolute_max)
        try:
            extreme_mask = df[col].astype(float) > upper_bound
            extreme_indices = df.index[extreme_mask]
        except Exception as e:
            print(f"警告: 处理站点 {col} 异常值时出错: {str(e)}，跳过该站点")
            logging.warning(f"处理站点 {col} 异常值时出错: {str(e)}，跳过该站点")
            continue
        
        if len(extreme_indices) > 0:
            for idx in extreme_indices:
                time_val = df.loc[idx, time_col]
                old_val = df.loc[idx, col]
                
                # 获取站点在距离矩阵中的索引
                station_idx = get_station_index(col, stations_df)
                
                if station_idx is not None:
                    # 找出该站点最近的5个站点
                    nearest_indices = np.argsort(distance_matrix[station_idx])[1:6]
                    nearest_stations = []
                    
                    for i in nearest_indices:
                        if i < len(stations_df):
                            station_name = stations_df.iloc[i]['站点']
                            if station_name in station_cols:  # 确保站点在处理列表中
                                nearest_stations.append(station_name)
                    
                    # 获取邻近站点当前时间点的值
                    if nearest_stations:
                        try:
                            neighbor_values = []
                            for neigh_col in nearest_stations:
                                val = df.loc[idx, neigh_col]
                                # 确保值是数值类型且不超过上限
                                if pd.notna(val) and float(val) <= upper_bound:
                                    neighbor_values.append(float(val))
                            
                            if neighbor_values:
                                replacement_value = np.mean(neighbor_values)
                                logging.info(f"  - {col}, 时间 {time_val}, 异常值 {old_val:.2f} > {upper_bound:.2f}, "
                                           f"替换为临近站点均值 {replacement_value:.2f}")
                                result_df.loc[idx, col] = replacement_value
                                extreme_modified_count += 1
                                modified = True
                                continue
                        except Exception as e:
                            print(f"警告: 处理站点 {col} 邻近站点时出错: {str(e)}")
                            logging.warning(f"处理站点 {col} 邻近站点时出错: {str(e)}")
                
                # 如果无法使用邻近站点，则使用历史数据
                try:
                    valid_history = []
                    for val in df[col]:
                        if pd.notna(val):
                            val = float(val)
                            if val <= upper_bound:
                                valid_history.append(val)
                    
                    if valid_history:
                        replacement_value = np.median(valid_history)
                        logging.info(f"  - {col}, 时间 {time_val}, 异常值 {old_val:.2f} > {upper_bound:.2f}, "
                                   f"替换为历史中位数 {replacement_value:.2f}")
                        result_df.loc[idx, col] = replacement_value
                        extreme_modified_count += 1
                        modified = True
                    else:
                        # 实在没有参考值，则设为中位数或0
                        non_zero_vals = []
                        for station_col in station_cols:
                            for val in df[station_col]:
                                if pd.notna(val):
                                    val = float(val)
                                    if 0 < val <= upper_bound:
                                        non_zero_vals.append(val)
                        
                        if non_zero_vals:
                            replacement_value = np.median(non_zero_vals)
                        else:
                            replacement_value = 0
                        
                        logging.info(f"  - {col}, 时间 {time_val}, 异常值 {old_val:.2f} > {upper_bound:.2f}, "
                                   f"无合适参考值, 设为 {replacement_value:.2f}")
                        result_df.loc[idx, col] = replacement_value
                        extreme_modified_count += 1
                        modified = True
                except Exception as e:
                    print(f"警告: 处理站点 {col} 历史数据时出错: {str(e)}")
                    logging.warning(f"处理站点 {col} 历史数据时出错: {str(e)}")
    
    if extreme_modified_count > 0:
        print(f"合理性检查: 共修正了 {extreme_modified_count} 个异常大值")
        logging.info(f"合理性检查: 共修正了 {extreme_modified_count} 个异常大值")
    else:
        print("合理性检查: 未发现异常大值")
        logging.info("合理性检查: 未发现异常大值")
    
    # 2. 一致性检查 - 检查空间一致性
    consistency_modified_count = 0
    min_rain_threshold = 5.0  # 最小雨量阈值
    
    for idx in df.index:
        time_val = df.loc[idx, time_col]
        
        # 计算当前时间点的平均雨量
        curr_time_values = []
        for col in station_cols:
            val = result_df.loc[idx, col]
            if pd.notna(val):
                try:
                    curr_time_values.append(float(val))
                except:
                    pass
        
        # 只在有足够雨量时进行一致性检查
        if not curr_time_values or np.mean(curr_time_values) < min_rain_threshold:
            continue
            
        for col in station_cols:
            try:
                current_value = float(result_df.loc[idx, col])
                
                if pd.isna(current_value) or current_value < min_rain_threshold:
                    continue
                
                station_idx = get_station_index(col, stations_df)
                
                if station_idx is not None:
                    # 找出该站点最近的5个站点
                    nearest_indices = np.argsort(distance_matrix[station_idx])[1:6]
                    nearest_stations = []
                    
                    for i in nearest_indices:
                        if i < len(stations_df):
                            station_name = stations_df.iloc[i]['站点']
                            if station_name in station_cols:  # 确保站点在处理列表中
                                nearest_stations.append(station_name)
                    
                    if nearest_stations:
                        valid_neighbors = []
                        for neigh_col in nearest_stations:
                            val = result_df.loc[idx, neigh_col]
                            if pd.notna(val):
                                try:
                                    valid_neighbors.append(float(val))
                                except:
                                    pass
                        
                        if len(valid_neighbors) >= 3:
                            neighbor_mean = np.mean(valid_neighbors)
                            neighbor_std = np.std(valid_neighbors)
                            
                            # 阈值设置：最少5mm或者邻居标准差的3倍
                            threshold = max(3 * neighbor_std, 5)
                            
                            if abs(current_value - neighbor_mean) > threshold:
                                old_val = current_value
                                new_val = neighbor_mean
                                logging.info(f"一致性检查: {col}, 时间 {time_val}, 值 {old_val:.2f} 与邻近站点均值 {neighbor_mean:.2f} 差异过大")
                                result_df.loc[idx, col] = new_val
                                consistency_modified_count += 1
                                modified = True
            except Exception as e:
                print(f"警告: 一致性检查中处理站点 {col} 时出错: {str(e)}")
                logging.warning(f"一致性检查中处理站点 {col} 时出错: {str(e)}")
    
    if consistency_modified_count > 0:
        print(f"一致性检查: 共修正了 {consistency_modified_count} 个空间一致性异常")
        logging.info(f"一致性检查: 共修正了 {consistency_modified_count} 个空间一致性异常")
    else:
        print("一致性检查: 未发现空间一致性异常")
        logging.info("一致性检查: 未发现空间一致性异常")
    
    # 3. 系统性检查 - 检查系统性偏差
    if len(df) >= 10:
        systematic_modified_count = 0
        
        for col in station_cols:
            try:
                station_idx = get_station_index(col, stations_df)
                
                if station_idx is None:
                    continue
                    
                # 找出该站点最近的5个站点
                nearest_indices = np.argsort(distance_matrix[station_idx])[1:6]
                nearest_stations = []
                
                for i in nearest_indices:
                    if i < len(stations_df):
                        station_name = stations_df.iloc[i]['站点']
                        if station_name in station_cols:  # 确保站点在处理列表中
                            nearest_stations.append(station_name)
                
                if not nearest_stations:
                    continue
                    
                # 计算比值序列
                ratios = []
                
                for idx in df.index:
                    try:
                        current_value = float(result_df.loc[idx, col])
                        if pd.isna(current_value) or current_value < min_rain_threshold:
                            continue
                            
                        valid_neighbors = []
                        for neigh_col in nearest_stations:
                            val = result_df.loc[idx, neigh_col]
                            if pd.notna(val):
                                try:
                                    val = float(val)
                                    if val >= min_rain_threshold:
                                        valid_neighbors.append(val)
                                except:
                                    pass
                        
                        if len(valid_neighbors) >= 3:
                            neighbor_mean = np.mean(valid_neighbors)
                            if neighbor_mean >= min_rain_threshold:
                                ratios.append(current_value / neighbor_mean)
                    except Exception as e:
                        print(f"警告: 系统性检查处理时间点时出错: {str(e)}")
                
                # 分析系统性偏差
                if len(ratios) >= 10:
                    median_ratio = np.median(ratios)
                    
                    # 判断是否存在系统性偏差
                    if median_ratio < 0.7 or median_ratio > 1.3:
                        logging.info(f"系统性检查: 站点 {col} 存在系统性偏差, 与邻近站点的中位数比值为 {median_ratio:.2f}")
                        print(f"系统性检查: 站点 {col} 存在系统性偏差, 比值 {median_ratio:.2f}")
                        
                        # 应用校正因子
                        correction_factor = 1.0 / median_ratio
                        old_values = result_df[col].copy()
                        
                        # 应用校正因子
                        for idx in result_df.index:
                            val = result_df.loc[idx, col]
                            if pd.notna(val):
                                try:
                                    result_df.loc[idx, col] = float(val) * correction_factor
                                except:
                                    pass
                        
                        logging.info(f"  - 对站点 {col} 应用了校正因子 {correction_factor:.2f}")
                        
                        # 计算校正前后的统计信息
                        try:
                            old_mean = np.mean([float(x) for x in old_values if pd.notna(x)])
                            old_max = np.max([float(x) for x in old_values if pd.notna(x)])
                            new_mean = np.mean([float(x) for x in result_df[col] if pd.notna(x)])
                            new_max = np.max([float(x) for x in result_df[col] if pd.notna(x)])
                            
                            logging.info(f"  - 校正前: 均值={old_mean:.2f}, 最大值={old_max:.2f}")
                            logging.info(f"  - 校正后: 均值={new_mean:.2f}, 最大值={new_max:.2f}")
                        except Exception as e:
                            print(f"警告: 计算校正统计信息时出错: {str(e)}")
                        
                        systematic_modified_count += 1
                        modified = True
            except Exception as e:
                print(f"警告: 系统性检查处理站点 {col} 时出错: {str(e)}")
                logging.warning(f"系统性检查处理站点 {col} 时出错: {str(e)}")
        
        if systematic_modified_count > 0:
            print(f"系统性检查: 共修正了 {systematic_modified_count} 个站点的系统性偏差")
            logging.info(f"系统性检查: 共修正了 {systematic_modified_count} 个站点的系统性偏差")
        else:
            print("系统性检查: 未发现系统性偏差")
            logging.info("系统性检查: 未发现系统性偏差")
    
    # 记录修改后的统计信息
    if modified:
        valid_data_after = []
        for col in station_cols:
            for val in result_df[col]:
                if pd.notna(val):
                    try:
                        valid_data_after.append(float(val))
                    except:
                        pass
        
        if valid_data_after:
            logging.info(f"修改后数据统计: 均值={np.mean(valid_data_after):.2f}, "
                        f"最大值={np.max(valid_data_after):.2f}, "
                        f"最小值={np.min(valid_data_after):.2f}")
        print(f"完成事件 {event_name} 处理: 数据已修改")
    else:
        print(f"完成事件 {event_name} 处理: 数据无需修改")
        logging.info("经检查，数据无需修改")
    
    return result_df, modified

def visualize_comparison(df_original, df_corrected, stations_df, event_name, output_path):
    """可视化原始数据和校正后数据的对比"""
    try:
        # 选择最后一个时间点进行可视化
        time_point = df_original.iloc[-1, 0]
        station_cols = [col for col in df_original.columns[1:] if 'r' not in col.lower()]
        
        print(f"生成可视化比较图: {event_name}, 时间点: {time_point}")
        
        # 站点坐标
        lons = stations_df['经度'].values
        lats = stations_df['纬度'].values
        
        # 匹配站点数据
        station_to_idx = {s: i for i, s in enumerate(stations_df['站点'])}
        
        # 初始化雨量数组
        orig_values = np.full(len(stations_df), np.nan)
        corr_values = np.full(len(stations_df), np.nan)
        
        # 填充数据
        for col in station_cols:
            if col in station_to_idx:
                try:
                    idx = station_to_idx[col]
                    orig_val = df_original.iloc[-1][col]
                    corr_val = df_corrected.iloc[-1][col]
                    
                    if pd.notna(orig_val):
                        orig_values[idx] = float(orig_val)
                    if pd.notna(corr_val):
                        corr_values[idx] = float(corr_val)
                except Exception as e:
                    print(f"警告: 处理可视化数据时出错: {str(e)}")
        
        # 设置颜色范围
        vmin = np.nanmin([np.nanmin(orig_values), np.nanmin(corr_values)])
        vmax = np.nanmax([np.nanmax(orig_values), np.nanmax(corr_values)])
        
        # 修正极值为0的情况
        if vmin == vmax:
            vmin = 0
            vmax = max(1, vmax)
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # 绘制原始数据
        ax = axes[0]
        sc0 = ax.scatter(lons, lats, c=orig_values, cmap='viridis', 
                       s=50, alpha=0.8, vmin=vmin, vmax=vmax)
        ax.set_title(f'原始数据 - {time_point}')
        ax.set_xlabel('经度')
        ax.set_ylabel('纬度')
        
        # 绘制校正后数据
        ax = axes[1]
        sc1 = ax.scatter(lons, lats, c=corr_values, cmap='viridis', 
                       s=50, alpha=0.8, vmin=vmin, vmax=vmax)
        ax.set_title(f'校正后数据 - {time_point}')
        ax.set_xlabel('经度')
        
        # 添加颜色条
        cbar = plt.colorbar(sc1, ax=axes.ravel().tolist(), orientation='horizontal', pad=0.01)
        cbar.set_label('雨量 (mm)')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"已保存可视化比较图: {output_path}")
        return True
    except Exception as e:
        print(f"生成可视化比较图时出错: {str(e)}")
        print(traceback.format_exc())
        logging.error(f"生成可视化比较图时出错: {str(e)}")
        logging.error(traceback.format_exc())
        return False

# 主处理流程
total_processed = 0
total_modified = 0

for event_name in event_folders:
    # 构建rain.csv的完整路径
    rain_file = os.path.join(input_dir, event_name, 'rain.csv')
    print(f"\n开始处理事件 {event_name} 的rain.csv文件: {rain_file}")
    
    # 检查文件是否存在
    if not os.path.isfile(rain_file):
        print(f"警告: 事件 {event_name} 的rain.csv文件不存在: {rain_file}")
        logging.warning(f"事件 {event_name} 的rain.csv文件不存在: {rain_file}")
        continue
        
    try:
        # 读取rain.csv
        print(f"正在读取 {rain_file}...")
        df = pd.read_csv(rain_file)
        print(f"成功读取 {rain_file}, 形状: {df.shape}")
        logging.info(f"成功读取 {rain_file}, 形状: {df.shape}")
        
        # 执行三性检查
        print(f"开始执行三性检查: {event_name}...")
        corrected_df, modified = perform_sanxing_check(df, distance_matrix, stations_df, event_name)
        print(f"三性检查完成: {event_name}, 是否有修改: {modified}")
        
        # 创建输出目录
        event_output_dir = os.path.join(output_dir, event_name)
        os.makedirs(event_output_dir, exist_ok=True)
        
        # 保存修改后的数据
        output_file = os.path.join(event_output_dir, 'rain_corrected.csv')
        corrected_df.to_csv(output_file, index=False)
        print(f"已保存校正后的数据到: {output_file}")
        logging.info(f"已保存校正后的数据到: {output_file}")
        
        total_processed += 1
        if modified:
            total_modified += 1
            
            # 生成可视化对比图
            vis_output = os.path.join(event_output_dir, f'comparison_{event_name}.png')
            if visualize_comparison(df, corrected_df, stations_df, event_name, vis_output):
                print(f"已生成可视化对比图: {vis_output}")
                logging.info(f"已生成可视化对比图: {vis_output}")
        
    except Exception as e:
        print(f"错误: 处理事件 {event_name} 时出错: {str(e)}")
        print(traceback.format_exc())
        logging.error(f"处理事件 {event_name} 时出错: {str(e)}")
        logging.error(traceback.format_exc())

logging.info(f"\n{'='*50}")
logging.info(f"所有事件处理完成。共处理 {total_processed} 个事件，其中 {total_modified} 个事件数据被修正")

print(f"\n{'='*50}")
print(f"三性检查和校正完成，共处理 {total_processed} 个事件，其中 {total_modified} 个事件数据被修正")
print(f"详细日志已保存到: {log_file}")

# 导入必要的库
import os
import glob
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay
from pykrige.ok import OrdinaryKriging
from sklearn.metrics import mean_absolute_error, mean_squared_error
import rasterio
from rasterio.transform import from_origin
import multiprocessing as mp
from tqdm import tqdm
import time
from concurrent.futures import ProcessPoolExecutor
import warnings
import re
from datetime import datetime
import gc  # 用于垃圾回收，提高内存效率

# 忽略警告信息，避免干扰输出
warnings.filterwarnings("ignore")

# 设置文件路径
input_dir = "D:/pythondata/spatial_interpolation/input/2020-1/点雨量"
output_dir = "D:/pythondata/spatial_interpolation/output/Kriging/2020-1"
stations_file = "D:/pythondata/spatial_interpolation/stations.csv"
mask_file = "D:/pythondata/spatial_interpolation/terrain/90/mask.asc"
raster_output_dir = os.path.join(output_dir, "rasters")

# 确保输出目录存在
os.makedirs(output_dir, exist_ok=True)
os.makedirs(raster_output_dir, exist_ok=True)

def sanitize_filename(filename):
    """
    清理文件名，替换不合法的字符
    
    参数:
    filename (str): 需要清理的文件名
    
    返回:
    str: 清理后的文件名
    """
    # 替换冒号、空格和其他不合法字符
    filename = re.sub(r'[:\s/\\*?"<>|]', '_', filename)
    return filename

def read_stations_data():
    """
    读取站点信息
    
    返回:
    pd.DataFrame: 包含站点ID、经度和纬度的数据框
    """
    try:
        # 读取站点信息CSV文件
        stations = pd.read_csv(stations_file, encoding='utf-8')
        print("成功读取站点信息文件，前几行数据:")
        print(stations.head())
        
        # 假设列名为中文，进行重命名
        if '站点' in stations.columns and '经度' in stations.columns and '纬度' in stations.columns:
            stations = stations.rename(columns={'站点': 'station_id', '经度': 'longitude', '纬度': 'latitude'})
            print("已将中文列名转换为英文列名")
        
        # 确保站点ID为字符串类型
        stations['station_id'] = stations['station_id'].astype(str)
        
        return stations
    except Exception as e:
        print(f"读取站点数据出错: {e}")
        return None

def read_rainfall_data(station_id):
    """
    读取单个站点的降雨数据
    
    参数:
    station_id (str): 站点ID
    
    返回:
    pd.DataFrame: 包含时间和降雨量的数据框
    """
    file_path = os.path.join(input_dir, f"{station_id}.csv")
    
    try:
        # 读取降雨数据CSV文件
        rainfall = pd.read_csv(file_path, encoding='utf-8')
        
        # 假设列名为中文，进行重命名
        if '时间' in rainfall.columns and '雨量' in rainfall.columns:
            rainfall = rainfall.rename(columns={'时间': 'time', '雨量': 'rainfall'})
        
        # 确保降雨量为数值类型
        rainfall['rainfall'] = pd.to_numeric(rainfall['rainfall'], errors='coerce')
        
        # 填充缺失值为0
        rainfall['rainfall'] = rainfall['rainfall'].fillna(-9999)
        
        # 确保雨量非负
        rainfall['rainfall'] = rainfall['rainfall'].clip(lower=0)
        
        return rainfall
    except Exception as e:
        print(f"读取站点{station_id}的降雨数据出错: {e}")
        return None

def read_mask_data():
    """
    读取流域掩码数据
    
    返回:
    tuple: (mask_array, transform, meta) 掩码数组，坐标变换和元数据
    """
    try:
        with rasterio.open(mask_file) as src:
            mask_array = src.read(1)
            transform = src.transform
            meta = src.meta.copy()
            print(f"成功读取流域掩码文件，栅格大小: {mask_array.shape}")
            
            # 获取左上角坐标和分辨率
            left = transform[2]
            top = transform[5]
            x_res = transform[0]
            y_res = transform[4]
            print(f"左上角坐标: ({left}, {top}), 分辨率: ({x_res}, {y_res})")
            
        return mask_array, transform, meta
    except Exception as e:
        print(f"读取流域掩码数据出错: {e}")
        return None, None, None

def create_delaunay_triangulation(stations):
    """
    创建Delaunay三角网，用于站点筛选
    
    参数:
    stations (pd.DataFrame): 站点数据框
    
    返回:
    Delaunay: Delaunay三角网对象
    """
    # 提取站点坐标
    points = stations[['longitude', 'latitude']].values
    
    # 创建Delaunay三角网
    tri = Delaunay(points)
    print(f"成功创建Delaunay三角网，共有{len(tri.simplices)}个三角形")
    
    # 可视化三角网
    visualize_delaunay(stations, tri)
    
    return tri

def visualize_delaunay(stations, tri):
    """
    可视化Delaunay三角网
    
    参数:
    stations (pd.DataFrame): 站点数据框
    tri (Delaunay): Delaunay三角网对象
    """
    try:
        plt.figure(figsize=(10, 8))
        plt.triplot(stations['longitude'], stations['latitude'], tri.simplices)
        plt.plot(stations['longitude'], stations['latitude'], 'ro')
        
        # 添加站点标签
        for i, (x, y, sid) in enumerate(zip(stations['longitude'], stations['latitude'], stations['station_id'])):
            plt.text(x, y, sid, fontsize=9)
        
        plt.title('站点Delaunay三角网')
        plt.xlabel('经度')
        plt.ylabel('纬度')
        plt.grid(True)
        
        plt.savefig(os.path.join(output_dir, 'delaunay_triangulation.png'), dpi=300)
        plt.close()
        print(f"已保存三角网可视化图到 {os.path.join(output_dir, 'delaunay_triangulation.png')}")
    except Exception as e:
        print(f"可视化三角网出错: {e}")

def find_neighbors(tri, point_idx):
    """
    找出Delaunay三角网中与指定点相邻的点
    
    参数:
    tri (Delaunay): Delaunay三角网对象
    point_idx (int): 点的索引
    
    返回:
    list: 相邻点的索引列表
    """
    # 获取包含该点的所有三角形
    triangles = [t for t in tri.simplices if point_idx in t]
    
    # 从这些三角形中提取所有不同的点
    neighbors = set()
    for t in triangles:
        for p in t:
            if p != point_idx:
                neighbors.add(p)
    
    return list(neighbors)

def create_grid_from_mask(mask_array, transform):
    """
    根据掩码创建插值用的网格
    
    参数:
    mask_array (array): 掩码数组
    transform (Affine): 坐标变换
    
    返回:
    tuple: (grid_x, grid_y) 网格坐标数组
    """
    # 获取栅格大小
    height, width = mask_array.shape
    
    # 获取左上角坐标和分辨率
    left = transform[2]
    top = transform[5]
    x_res = transform[0]
    y_res = transform[4]
    
    # 创建网格坐标
    x_coords = left + np.arange(width) * x_res + x_res / 2
    y_coords = top + np.arange(height) * y_res + y_res / 2
    
    # 创建网格
    grid_x, grid_y = np.meshgrid(x_coords, y_coords)
    
    return grid_x, grid_y

def ordinary_kriging(x, y, z, grid_x, grid_y, variogram_model='spherical', nlags=10, enable_plotting=False):
    """
    执行普通克里金插值
    
    参数:
    x (array): 站点经度数组
    y (array): 站点纬度数组
    z (array): 站点降雨量数组
    grid_x (array): 网格X坐标
    grid_y (array): 网格Y坐标
    variogram_model (str): 变异函数模型
    nlags (int): 变异函数计算中的lag数量
    enable_plotting (bool): 是否启用变异函数图绘制
    
    返回:
    array: 插值结果数组
    """
    # 检查数据
    if len(z) < 3:
        print("站点数量不足，无法执行克里金插值")
        return np.zeros(grid_x.shape)
    
    # 执行普通克里金插值
    try:
        # 处理所有值相同的情况
        if np.all(z == z[0]):
            print("所有站点降雨量相同，返回均匀场")
            return np.full(grid_x.shape, z[0])
        
        # 检查站点分布是否合理
        x_range = np.max(x) - np.min(x)
        y_range = np.max(y) - np.min(y)
        if x_range < 1e-8 or y_range < 1e-8:
            print("站点分布过于集中，无法执行克里金插值")
            return np.full(grid_x.shape, np.mean(z))
            
        # 尝试多种变异函数模型
        models_to_try = [variogram_model, 'linear', 'gaussian', 'exponential']
        
        for model in models_to_try:
            try:
                # 创建普通克里金插值器，调整参数提高稳定性
                OK = OrdinaryKriging(
                    x, y, z,
                    variogram_model=model,
                    nlags=max(5, min(nlags, len(x) // 2)),  # 动态调整nlags
                    weight=True,  # 添加权重
                    enable_plotting=enable_plotting,
                    verbose=False
                )
                
                # 执行插值
                z_interp, ss = OK.execute('grid', grid_x[0], grid_y[:, 0])
                
                # 确保插值结果非负
                z_interp = np.maximum(z_interp, 0)
                
                # 成功完成，返回结果
                return z_interp
            except Exception as e:
                print(f"使用{model}模型插值失败: {e}")
                continue
                
        # 如果所有模型都失败，使用替代方法
        print("所有克里金模型都失败，使用反距离加权法(IDW)插值")
        from scipy.interpolate import griddata
        points = np.column_stack((x, y))
        grid_points = np.column_stack((grid_x.flatten(), grid_y.flatten()))
        z_idw = griddata(points, z, grid_points, method='cubic', fill_value=0)
        z_idw = z_idw.reshape(grid_x.shape)
        z_idw = np.maximum(z_idw, 0)  # 确保非负
        return z_idw
        
    except Exception as e:
        print(f"执行克里金插值出错: {e}")
        # 发生未预期错误时的备选方案
        try:
            # 使用简单平均
            return np.full(grid_x.shape, np.mean(z))
        except:
            return np.zeros(grid_x.shape)

def leave_one_out_validation(stations, time_point, rainfall_data, tri):
    """
    使用留一法验证克里金插值效果
    
    参数:
    stations (pd.DataFrame): 站点数据框
    time_point (str): 时间点
    rainfall_data (dict): 各站点的降雨数据
    tri (Delaunay): Delaunay三角网对象
    
    返回:
    tuple: (预测值列表, 实际值列表, 站点ID列表)
    """
    # 获取所有有效站点的降雨量
    station_ids = []
    actual_values = []
    predicted_values = []
    
    # 在有效站点中逐一进行留一验证
    for i, row in stations.iterrows():
        station_id = row['station_id']
        
        # 检查该站点在该时间点是否有数据
        if station_id not in rainfall_data:
            continue
            
        station_rainfall = rainfall_data[station_id]
        if time_point not in station_rainfall['time'].values:
            continue
            
        # 获取该站点在该时间点的实际降雨量
        actual_value = station_rainfall.loc[station_rainfall['time'] == time_point, 'rainfall'].values[0]
        
        # 在留一验证中，我们需要排除当前站点
        validation_stations = stations[stations['station_id'] != station_id].copy()
        
        # 找出与当前站点相邻的站点（通过Delaunay三角网）
        point_idx = i
        neighbor_indices = find_neighbors(tri, point_idx)
        
        # 从邻居中提取站点ID
        neighbor_station_ids = stations.iloc[neighbor_indices]['station_id'].tolist()
        
        # 收集邻居站点的降雨数据
        x = []
        y = []
        z = []
        
        for neighbor_id in neighbor_station_ids:
            if neighbor_id in rainfall_data:
                neighbor_rainfall = rainfall_data[neighbor_id]
                if time_point in neighbor_rainfall['time'].values:
                    neighbor_station = stations[stations['station_id'] == neighbor_id].iloc[0]
                    x.append(neighbor_station['longitude'])
                    y.append(neighbor_station['latitude'])
                    z.append(neighbor_rainfall.loc[neighbor_rainfall['time'] == time_point, 'rainfall'].values[0])
        
        # 如果邻居站点不足，则跳过
        if len(z) < 3:
            continue
        
        # 准备克里金插值的网格
        # 为了简化，我们只计算当前站点位置的插值结果
        grid_x = np.array([row['longitude']])
        grid_y = np.array([row['latitude']])
        
        # 执行克里金插值
        z_interp = ordinary_kriging(
            np.array(x), np.array(y), np.array(z),
            np.array([grid_x]), np.array([grid_y]).reshape(-1, 1),
            variogram_model='spherical',
            nlags=10,
            enable_plotting=False
        )
        
        # 获取插值结果
        predicted_value = z_interp[0, 0] if z_interp.size > 0 else 0
        
        # 记录结果
        station_ids.append(station_id)
        actual_values.append(actual_value)
        predicted_values.append(predicted_value)
    
    return predicted_values, actual_values, station_ids
def leave_one_out_validation(stations, time_point, rainfall_data, tri):
    """
    使用留一法验证克里金插值效果
    
    参数:
    stations (pd.DataFrame): 站点数据框
    time_point (str): 时间点
    rainfall_data (dict): 各站点的降雨数据
    tri (Delaunay): Delaunay三角网对象
    
    返回:
    tuple: (预测值列表, 实际值列表, 站点ID列表)
    """
    # 获取所有有效站点的降雨量
    station_ids = []
    actual_values = []
    predicted_values = []
    
    # 在有效站点中逐一进行留一验证
    for i, row in stations.iterrows():
        station_id = row['station_id']
        
        # 检查该站点在该时间点是否有数据
        if station_id not in rainfall_data:
            continue
            
        station_rainfall = rainfall_data[station_id]
        if time_point not in station_rainfall['time'].values:
            continue
            
        # 获取该站点在该时间点的实际降雨量
        actual_value = station_rainfall.loc[station_rainfall['time'] == time_point, 'rainfall'].values[0]
        
        try:
            # 在留一验证中，我们需要排除当前站点
            validation_stations = stations[stations['station_id'] != station_id].copy()
            
            # 找出与当前站点相邻的站点（通过Delaunay三角网）
            point_idx = i
            neighbor_indices = find_neighbors(tri, point_idx)
            
            # 从邻居中提取站点ID
            neighbor_station_ids = stations.iloc[neighbor_indices]['station_id'].tolist()
            
            # 收集邻居站点的降雨数据
            x = []
            y = []
            z = []
            
            for neighbor_id in neighbor_station_ids:
                if neighbor_id in rainfall_data:
                    neighbor_rainfall = rainfall_data[neighbor_id]
                    if time_point in neighbor_rainfall['time'].values:
                        neighbor_station = stations[stations['station_id'] == neighbor_id].iloc[0]
                        x.append(neighbor_station['longitude'])
                        y.append(neighbor_station['latitude'])
                        z.append(neighbor_rainfall.loc[neighbor_rainfall['time'] == time_point, 'rainfall'].values[0])
            
            # 如果邻居站点不足，则尝试使用所有站点
            if len(z) < 3:
                print(f"站点{station_id}的邻居站点不足，尝试使用所有其他站点")
                x = []
                y = []
                z = []
                
                for _, other_row in validation_stations.iterrows():
                    other_id = other_row['station_id']
                    if other_id in rainfall_data:
                        other_rainfall = rainfall_data[other_id]
                        if time_point in other_rainfall['time'].values:
                            x.append(other_row['longitude'])
                            y.append(other_row['latitude'])
                            z.append(other_rainfall.loc[other_rainfall['time'] == time_point, 'rainfall'].values[0])
            
            # 如果仍然不足，则跳过
            if len(z) < 3:
                print(f"站点{station_id}可用的其他站点不足，跳过验证")
                continue
            
            # 准备克里金插值的网格
            grid_x = np.array([row['longitude']])
            grid_y = np.array([row['latitude']])
            
            # 执行克里金插值
            z_interp = ordinary_kriging(
                np.array(x), np.array(y), np.array(z),
                np.array([grid_x]), np.array([grid_y]).reshape(-1, 1),
                variogram_model='spherical',
                nlags=10,
                enable_plotting=False
            )
            
            # 获取插值结果
            predicted_value = z_interp[0, 0] if z_interp.size > 0 else 0
            
            # 记录结果
            station_ids.append(station_id)
            actual_values.append(actual_value)
            predicted_values.append(predicted_value)
            
        except Exception as e:
            print(f"站点{station_id}的留一验证失败: {e}")
            continue
    
    return predicted_values, actual_values, station_ids

def evaluate_performance(predicted, actual):
    """
    评估插值性能
    
    参数:
    predicted (array): 预测值数组
    actual (array): 实际值数组
    
    返回:
    dict: 包含各种评估指标的字典
    """
    if not predicted or not actual:
        return {
            'RMSE': np.nan,
            'MAE': np.nan,
            'Correlation': np.nan,
            'NSE': np.nan
        }
    
    predicted = np.array(predicted)
    actual = np.array(actual)
    
    # 计算均方根误差 (RMSE)
    rmse = np.sqrt(mean_squared_error(actual, predicted))
    
    # 计算平均绝对误差 (MAE)
    mae = mean_absolute_error(actual, predicted)
    
    # 计算相关系数
    try:
        correlation = np.corrcoef(actual, predicted)[0, 1] if len(actual) > 1 else np.nan
    except:
        correlation = np.nan
    
    # 计算纳什系数 (NSE)
    mean_actual = np.mean(actual)
    if np.sum((actual - mean_actual) ** 2) == 0:
        nse = np.nan  # 避免除以零
    else:
        nse = 1 - np.sum((predicted - actual) ** 2) / np.sum((actual - mean_actual) ** 2)
    
    return {
        'RMSE': rmse,
        'MAE': mae,
        'Correlation': correlation,
        'NSE': nse
    }

def interpolate_rainfall_for_basin(stations, time_point, rainfall_data, mask_array, transform, meta):
    """
    为整个流域插值降雨量
    
    参数:
    stations (pd.DataFrame): 站点数据框
    time_point (str): 时间点
    rainfall_data (dict): 各站点的降雨数据
    mask_array (array): 流域掩码数组
    transform (Affine): 坐标变换
    meta (dict): 元数据
    
    返回:
    array: 流域降雨量插值结果
    """
    # 收集站点数据
    x = []
    y = []
    z = []
    
    for _, row in stations.iterrows():
        station_id = row['station_id']
        
        # 检查该站点在该时间点是否有数据
        if station_id not in rainfall_data:
            continue
            
        station_rainfall = rainfall_data[station_id]
        if time_point not in station_rainfall['time'].values:
            continue
            
        # 获取该站点在该时间点的降雨量
        rainfall_value = station_rainfall.loc[station_rainfall['time'] == time_point, 'rainfall'].values[0]
        
        x.append(row['longitude'])
        y.append(row['latitude'])
        z.append(rainfall_value)
    
    # 如果站点数据不足，返回全零数组
    if len(z) < 3:
        return np.zeros_like(mask_array)
    
    # 创建插值网格
    grid_x, grid_y = create_grid_from_mask(mask_array, transform)
    
    # 执行克里金插值
    z_interp = ordinary_kriging(
        np.array(x), np.array(y), np.array(z),
        grid_x, grid_y,
        variogram_model='spherical',
        nlags=10,
        enable_plotting=False
    )
    
    # 应用掩码
    z_interp = np.where(mask_array > 0, z_interp, 0)
    
    return z_interp

def save_interpolation_result(z_interp, time_point, transform, meta, output_dir):
    """
    保存插值结果为栅格文件
    
    参数:
    z_interp (array): 插值结果数组
    time_point (str): 时间点
    transform (Affine): 坐标变换
    meta (dict): 元数据
    output_dir (str): 输出目录
    """
    # 清理文件名
    safe_time = sanitize_filename(time_point)
    
    # 更新元数据
    out_meta = meta.copy()
    out_meta.update({
        'driver': 'GTiff',
        'height': z_interp.shape[0],
        'width': z_interp.shape[1],
        'transform': transform,
        'dtype': 'float32'
    })
    
    # 保存为GeoTIFF
    output_file = os.path.join(output_dir, f"rainfall_{safe_time}.tif")
    with rasterio.open(output_file, 'w', **out_meta) as dst:
        dst.write(z_interp.astype(np.float32), 1)
    
    return output_file

def visualize_interpolation_result(z_interp, time_point, output_dir):
    """
    可视化插值结果
    
    参数:
    z_interp (array): 插值结果数组
    time_point (str): 时间点
    output_dir (str): 输出目录
    """
    # 清理文件名
    safe_time = sanitize_filename(time_point)
    
    plt.figure(figsize=(10, 8))
    
    # 使用jet颜色映射，适合降雨数据
    plt.imshow(z_interp, cmap='jet')
    plt.colorbar(label='降雨量')
    plt.title(f'时间点 {time_point} 的降雨量插值结果')
    
    output_file = os.path.join(output_dir, f"rainfall_viz_{safe_time}.png")
    plt.savefig(output_file, dpi=300)
    plt.close()
    
    return output_file

def process_time_point(args):
    """
    处理单个时间点的插值和验证
    
    参数:
    args (tuple): (时间点, 站点数据, 降雨数据, Delaunay三角网, 掩码数组, 坐标变换, 元数据, 是否保存栅格)
    
    返回:
    tuple: (时间点, 评估结果, 预测值, 实际值, 站点ID)
    """
    time_point, stations, rainfall_data, tri, mask_array, transform, meta, save_raster = args
    
    try:
        # 执行留一法验证
        predicted, actual, station_ids = leave_one_out_validation(
            stations, time_point, rainfall_data, tri
        )
        
        # 评估性能
        if predicted and actual:
            performance = evaluate_performance(predicted, actual)
        else:
            performance = {
                'RMSE': np.nan,
                'MAE': np.nan,
                'Correlation': np.nan,
                'NSE': np.nan
            }
        
        # 如果需要，保存整个流域的插值结果
        if save_raster:
            z_interp = interpolate_rainfall_for_basin(stations, time_point, rainfall_data, mask_array, transform, meta)
            raster_file = save_interpolation_result(z_interp, time_point, transform, meta, raster_output_dir)
            viz_file = visualize_interpolation_result(z_interp, time_point, raster_output_dir)
        
        return time_point, performance, predicted, actual, station_ids
    
    except Exception as e:
        print(f"处理时间点 {time_point} 出错: {e}")
        return time_point, {
            'RMSE': np.nan,
            'MAE': np.nan,
            'Correlation': np.nan,
            'NSE': np.nan
        }, [], [], []

def batch_process_time_points(time_points, stations, rainfall_data, tri, mask_array, transform, meta, batch_size=100, save_raster=False):
    """
    批量处理时间点，以减少内存使用
    
    参数:
    time_points (list): 时间点列表
    stations (pd.DataFrame): 站点数据框
    rainfall_data (dict): 各站点的降雨数据
    tri (Delaunay): Delaunay三角网对象
    mask_array (array): 流域掩码数组
    transform (Affine): 坐标变换
    meta (dict): 元数据
    batch_size (int): 批处理大小
    save_raster (bool): 是否保存栅格文件
    
    返回:
    list: 处理结果列表
    """
    results = []
    cpu_count = min(mp.cpu_count(), 12)  # 使用最多12个核心
    print(f"将使用{cpu_count}个CPU核心进行并行处理")
    
    # 按批次处理
    for i in range(0, len(time_points), batch_size):
        batch_time_points = time_points[i:i+batch_size]
        print(f"处理批次 {i//batch_size + 1}/{(len(time_points)-1)//batch_size + 1}，共{len(batch_time_points)}个时间点")
        
        # 准备参数
        args_list = [
            (time_point, stations, rainfall_data, tri, mask_array, transform, meta, save_raster)
            for time_point in batch_time_points
        ]
        
        # 并行处理批次
        batch_results = []
        with ProcessPoolExecutor(max_workers=cpu_count) as executor:
            for result in tqdm(executor.map(process_time_point, args_list), total=len(args_list)):
                batch_results.append(result)
        
        # 收集结果
        results.extend(batch_results)
        
        # 强制清理内存
        gc.collect()
    
    return results

def save_station_results(results, output_dir, stations):
    """
    保存站点的插值结果
    
    参数:
    results (list): 处理结果列表
    output_dir (str): 输出目录
    stations (pd.DataFrame): 站点数据框
    """
    # 为每个站点创建结果数据框
    station_results = {station_id: {'time': [], 'rainfall': []} for station_id in stations['station_id']}
    
    # 填充结果
    for time_point, _, predicted, _, station_ids in results:
        for i, station_id in enumerate(station_ids):
            if station_id in station_results:
                station_results[station_id]['time'].append(time_point)
                station_results[station_id]['rainfall'].append(predicted[i])
    
    # 保存每个站点的结果
    for station_id, result in station_results.items():
        if result['time']:  # 只保存有数据的站点
            df = pd.DataFrame({
                '时间': result['time'],
                '雨量': result['rainfall']
            })
            output_file = os.path.join(output_dir, f"{station_id}.csv")
            df.to_csv(output_file, index=False, encoding='utf-8')
            print(f"已保存站点 {station_id} 的结果到 {output_file}")

def save_performance_results(results, output_dir):
    """
    保存性能评估结果
    
    参数:
    results (list): 处理结果列表
    output_dir (str): 输出目录
    """
    # 创建性能评估数据框
    performance_data = []
    
    for time_point, performance, _, _, _ in results:
        performance_data.append({
            '时间': time_point,
            'RMSE': performance['RMSE'],
            'MAE': performance['MAE'],
            'Correlation': performance['Correlation'],
            'NSE': performance['NSE']
        })
    
    performance_df = pd.DataFrame(performance_data)
    
    # 保存性能评估结果
    performance_file = os.path.join(output_dir, "performance_evaluation.csv")
    performance_df.to_csv(performance_file, index=False, encoding='utf-8')
    print(f"已保存性能评估结果到 {performance_file}")
    
    # 计算并打印总体性能
    overall_performance = {
        'RMSE': np.nanmean(performance_df['RMSE']),
        'MAE': np.nanmean(performance_df['MAE']),
        'Correlation': np.nanmean(performance_df['Correlation']),
        'NSE': np.nanmean(performance_df['NSE'])
    }
    
    print("\n总体性能评估:")
    print(f"RMSE: {overall_performance['RMSE']:.4f}")
    print(f"MAE: {overall_performance['MAE']:.4f}")
    print(f"Correlation: {overall_performance['Correlation']:.4f}")
    print(f"NSE: {overall_performance['NSE']:.4f}")
    
    # 保存总体性能结果
    overall_file = os.path.join(output_dir, "overall_performance.csv")
    pd.DataFrame([overall_performance]).to_csv(overall_file, index=False, encoding='utf-8')
    print(f"已保存总体性能评估结果到 {overall_file}")

def plot_performance_over_time(results, output_dir):
    """
    绘制性能指标随时间的变化
    
    参数:
    results (list): 处理结果列表
    output_dir (str): 输出目录
    """
    # 提取性能数据
    time_points = []
    rmse_values = []
    mae_values = []
    corr_values = []
    nse_values = []
    
    for time_point, performance, _, _, _ in results:
        time_points.append(time_point)
        rmse_values.append(performance['RMSE'])
        mae_values.append(performance['MAE'])
        corr_values.append(performance['Correlation'])
        nse_values.append(performance['NSE'])
    
    # 转换时间格式
    try:
        time_objects = [datetime.strptime(tp, '%Y-%m-%d %H:%M:%S') for tp in time_points]
    except:
        try:
            time_objects = [datetime.strptime(tp, '%Y/%m/%d %H:%M') for tp in time_points]
        except:
            time_objects = range(len(time_points))
    
    # 绘制图形
    plt.figure(figsize=(16, 12))
    
    plt.subplot(2, 2, 1)
    plt.plot(time_objects, rmse_values, 'b-')
    plt.title('RMSE随时间的变化')
    plt.ylabel('RMSE')
    plt.xticks(rotation=45)
    plt.grid(True)
    
    plt.subplot(2, 2, 2)
    plt.plot(time_objects, mae_values, 'g-')
    plt.title('MAE随时间的变化')
    plt.ylabel('MAE')
    plt.xticks(rotation=45)
    plt.grid(True)
    
    plt.subplot(2, 2, 3)
    plt.plot(time_objects, corr_values, 'r-')
    plt.title('相关系数随时间的变化')
    plt.ylabel('相关系数')
    plt.xticks(rotation=45)
    plt.grid(True)
    
    plt.subplot(2, 2, 4)
    plt.plot(time_objects, nse_values, 'm-')
    plt.title('纳什系数随时间的变化')
    plt.ylabel('纳什系数')
    plt.xticks(rotation=45)
    plt.grid(True)
    
    plt.tight_layout()
    plot_file = os.path.join(output_dir, "performance_over_time.png")
    plt.savefig(plot_file, dpi=300)
    plt.close()
    print(f"已保存性能随时间变化图到 {plot_file}")
    
    # 绘制预测值与实际值的散点图
    all_predicted = []
    all_actual = []
    
    for _, _, predicted, actual, _ in results:
        all_predicted.extend(predicted)
        all_actual.extend(actual)
    
    if all_predicted and all_actual:
        plt.figure(figsize=(10, 10))
        plt.scatter(all_actual, all_predicted, alpha=0.5)
        
        # 添加1:1线
        max_val = max(max(all_actual), max(all_predicted)) * 1.1
        plt.plot([0, max_val], [0, max_val], 'r--')
        
        plt.xlabel('实际降雨量')
        plt.ylabel('预测降雨量')
        plt.title('预测值与实际值的对比')
        plt.grid(True)
        
        scatter_file = os.path.join(output_dir, "predicted_vs_actual.png")
        plt.savefig(scatter_file, dpi=300)
        plt.close()
        print(f"已保存预测值与实际值对比图到 {scatter_file}")

def plot_kriging_examples(results, stations, rainfall_data, mask_array, transform, meta, output_dir, num_examples=5):
    """
    为几个示例时间点绘制克里金插值结果
    
    参数:
    results (list): 处理结果列表
    stations (pd.DataFrame): 站点数据框
    rainfall_data (dict): 各站点的降雨数据
    mask_array (array): 流域掩码数组
    transform (Affine): 坐标变换
    meta (dict): 元数据
    output_dir (str): 输出目录
    num_examples (int): 示例数量
    """
    # 选择几个性能较好的时间点
    performance_scores = []
    
    for time_point, performance, _, _, _ in results:
        if not np.isnan(performance['NSE']):
            score = performance['NSE']
            performance_scores.append((time_point, score))
    
    # 如果没有足够的有效样本，则使用所有时间点
    if len(performance_scores) < num_examples:
        example_times = [r[0] for r in results[:num_examples]]
    else:
        # 按NSE从高到低排序
        performance_scores.sort(key=lambda x: x[1], reverse=True)
        example_times = [ps[0] for ps in performance_scores[:num_examples]]
    
    print(f"\n为{len(example_times)}个示例时间点生成插值可视化...")
    
    for time_point in example_times:
        print(f"处理时间点: {time_point}")
        
        # 执行插值
        z_interp = interpolate_rainfall_for_basin(stations, time_point, rainfall_data, mask_array, transform, meta)
        
        # 保存栅格文件
        raster_file = save_interpolation_result(z_interp, time_point, transform, meta, output_dir)
        
        # 可视化插值结果
        viz_file = visualize_interpolation_result(z_interp, time_point, output_dir)
        
        # 同时可视化站点实测值
        plt.figure(figsize=(12, 10))
        
        # 显示插值结果
        im = plt.imshow(z_interp, cmap='jet', extent=[
            transform[2],
            transform[2] + transform[0] * z_interp.shape[1],
            transform[5] + transform[4] * z_interp.shape[0],
            transform[5]
        ])
        
        # 添加站点
        for _, row in stations.iterrows():
            station_id = row['station_id']
            if station_id in rainfall_data:
                station_rainfall = rainfall_data[station_id]
                if time_point in station_rainfall['time'].values:
                    rainfall_value = station_rainfall.loc[station_rainfall['time'] == time_point, 'rainfall'].values[0]
                    plt.plot(row['longitude'], row['latitude'], 'ko', markersize=5)
                    plt.text(row['longitude'], row['latitude'], f"{station_id}\n{rainfall_value:.1f}", fontsize=8)
        
        plt.colorbar(im, label='降雨量')
        plt.title(f'时间点 {time_point} 的降雨量分布')
        plt.xlabel('经度')
        plt.ylabel('纬度')
        
        combined_file = os.path.join(output_dir, f"rainfall_with_stations_{sanitize_filename(time_point)}.png")
        plt.savefig(combined_file, dpi=300)
        plt.close()
        print(f"已保存带站点的降雨分布图到 {combined_file}")

def main():
    """
    主函数
    """
    print("\n===============================")
    print("  克里金空间插值程序 - 开始运行  ")
    print("===============================\n")
    
    # 记录开始时间
    start_time = time.time()
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(raster_output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    print(f"栅格输出目录: {raster_output_dir}")
    
    # 读取站点信息
    print("\n步骤1: 读取站点信息...")
    stations = read_stations_data()
    if stations is None or len(stations) == 0:
        print("无法读取站点信息，程序终止")
        return
    print(f"成功读取{len(stations)}个站点的信息")
    
    # 读取流域掩码
    print("\n步骤2: 读取流域掩码...")
    mask_array, transform, meta = read_mask_data()
    if mask_array is None:
        print("无法读取流域掩码，程序终止")
        return
    
    # 创建Delaunay三角网
    print("\n步骤3: 创建Delaunay三角网...")
    tri = create_delaunay_triangulation(stations)
    
    # 读取所有站点的降雨数据
    print("\n步骤4: 读取所有站点的降雨数据...")
    rainfall_data = {}
    all_time_points = set()
    
    print("读取每个站点的降雨数据...")
    for station_id in tqdm(stations['station_id']):
        rainfall = read_rainfall_data(station_id)
        if rainfall is not None:
            rainfall_data[station_id] = rainfall
            all_time_points.update(rainfall['time'].values)
    
    all_time_points = sorted(list(all_time_points))
    print(f"成功读取{len(rainfall_data)}个站点的降雨数据")
    print(f"数据中共有{len(all_time_points)}个时间点")
    
    # 调试模式 - 只处理少量时间点
    debug_mode = False
    if debug_mode:
        print("\n调试模式: 只处理前10个时间点")
        all_time_points = all_time_points[:10]
    
    # 是否保存栅格文件
    save_raster = True
    if len(all_time_points) > 100 and save_raster:
        print("\n警告: 时间点较多，保存所有栅格文件可能占用大量磁盘空间")
        print("建议: 在处理完成后，仅为选定的几个时间点生成栅格")
        save_raster = False
    
    # 批量处理时间点
    print("\n步骤5: 开始批量处理时间点...")
    batch_size = 50  # 可以根据内存情况调整批处理大小
    results = batch_process_time_points(
        all_time_points, stations, rainfall_data, tri, 
        mask_array, transform, meta, batch_size, save_raster
    )
    
    # 保存站点结果
    print("\n步骤6: 保存站点的插值结果...")
    save_station_results(results, output_dir, stations)
    
    # 保存性能评估结果
    print("\n步骤7: 保存性能评估结果...")
    save_performance_results(results, output_dir)
    
    # 绘制性能随时间的变化
    print("\n步骤8: 绘制性能指标随时间的变化...")
    plot_performance_over_time(results, output_dir)
    
    # 为选定的几个时间点生成插值结果
    if not save_raster:
        print("\n步骤9: 为选定的几个时间点生成插值结果...")
        plot_kriging_examples(results, stations, rainfall_data, mask_array, transform, meta, raster_output_dir, num_examples=5)
    
    # 程序结束
    end_time = time.time()
    total_time = end_time - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print("\n===============================")
    print("  克里金空间插值程序 - 运行完成  ")
    print(f"  总耗时: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")
    print("===============================\n")

if __name__ == "__main__":
    main()

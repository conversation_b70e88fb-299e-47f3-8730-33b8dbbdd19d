#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay插值系统主程序

基于Delaunay三角剖分分析结果进行空间插值的主程序入口
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import warnings
import json
import gc

warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from config import DelaunayConfig
from data_loader import DelaunayDataLoader
from evaluation_metrics import EvaluationMetrics
from delaunay_interpolator import DelaunayInterpolator
from visualization import DelaunayVisualization
from memory_monitor import get_memory_monitor, start_global_monitoring, stop_global_monitoring, check_memory_and_optimize

def setup_logging(config):
    """设置日志系统"""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("Delaunay插值系统启动")
    return logger

def save_results(results, output_dir, filename):
    """保存结果到文件（增强错误处理）"""
    try:
        filepath = output_dir / filename

        # 确保输出目录存在
        output_dir.mkdir(parents=True, exist_ok=True)

        if filename.endswith('.csv'):
            results.to_csv(filepath, index=True, encoding='utf-8', float_format='%.6f')
        elif filename.endswith('.json'):
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        else:
            logging.warning(f"不支持的文件格式: {filename}")
            return False

        logging.info(f"结果已保存: {filepath}")
        return True

    except Exception as e:
        logging.error(f"保存结果失败 ({filename}): {e}")
        return False

def process_single_event(event_name, interpolator, evaluator, visualizer, config):
    """处理单个洪水事件（内存优化版本）"""
    logger = logging.getLogger(__name__)

    try:
        logger.info(f"开始处理洪水事件: {event_name}")

        # 记录处理前内存使用
        if hasattr(interpolator.data_loader, 'get_memory_usage'):
            memory_before = interpolator.data_loader.get_memory_usage()
            logger.info(f"事件{event_name}处理前内存: RSS={memory_before['rss']:.1f}MB")

        # 创建事件输出目录
        event_output_dir = config.get_event_output_dir(event_name)

        # 检查事件数据是否存在
        event_dir = config.INPUT_DIR / event_name
        if not event_dir.exists():
            logger.warning(f"洪水事件{event_name}目录不存在: {event_dir}")
            return None

        csv_files = list(event_dir.glob("*.csv"))
        if not csv_files:
            logger.warning(f"洪水事件{event_name}无CSV数据文件")
            return None

        logger.info(f"洪水事件{event_name}包含{len(csv_files)}个数据文件")

        # 进行插值（使用内存优化模式）
        try:
            memory_efficient = getattr(config, 'MEMORY_EFFICIENT_MODE', True)
            event_results = interpolator.interpolate_flood_event(event_name, memory_efficient=memory_efficient)

            if not event_results:
                logger.warning(f"洪水事件{event_name}无插值结果")
                return None

            logger.info(f"洪水事件{event_name}插值完成，处理了{len(event_results)}个站点")

        except Exception as e:
            logger.error(f"洪水事件{event_name}插值失败: {e}")
            return None

        # 计算评估指标
        try:
            event_metrics = evaluator.evaluate_event_performance(event_results)
            logger.info(f"洪水事件{event_name}评估指标计算完成")
        except Exception as e:
            logger.error(f"洪水事件{event_name}评估指标计算失败: {e}")
            return None

        # 保存详细结果（分批处理以节省内存）
        if config.SAVE_DETAILED_RESULTS:
            batch_size = getattr(config, 'BATCH_SIZE', 10)
            station_items = list(event_results.items())

            for i in range(0, len(station_items), batch_size):
                batch_items = station_items[i:i + batch_size]

                for station_id, station_results in batch_items:
                    # 保存站点插值结果
                    filename = f"{station_id}_interpolation_results.csv"
                    save_results(station_results, event_output_dir, filename)

                    # 生成站点可视化（如果启用）
                    if config.GENERATE_PLOTS:
                        try:
                            visualizer.plot_station_timeseries(station_id, station_results,
                                                             event_output_dir, event_name)
                            visualizer.plot_scatter_comparison(station_id, station_results,
                                                             event_output_dir, event_name)
                        except Exception as e:
                            logger.warning(f"生成站点{station_id}可视化失败: {e}")

                # 定期清理内存
                if i % (batch_size * 2) == 0:
                    gc.collect()

        # 保存评估指标
        try:
            metrics_df = pd.DataFrame(event_metrics).T
            save_results(metrics_df, event_output_dir, f"{event_name}_metrics.csv")
            del metrics_df  # 立即释放
        except Exception as e:
            logger.warning(f"保存评估指标失败: {e}")

        # 生成事件汇总可视化（如果启用）
        if config.GENERATE_PLOTS:
            try:
                visualizer.plot_metrics_summary(event_metrics, event_output_dir, event_name)
            except Exception as e:
                logger.warning(f"生成事件汇总可视化失败: {e}")

        # 计算事件汇总统计
        try:
            summary_stats = evaluator.calculate_summary_statistics(event_metrics)
            save_results(summary_stats, event_output_dir, f"{event_name}_summary.json")
            logger.info(f"洪水事件{event_name}汇总统计保存完成")
        except Exception as e:
            logger.warning(f"计算或保存事件汇总统计失败: {e}")
            summary_stats = {}

        logger.info(f"洪水事件{event_name}处理完成")
        logger.info(f"  - 处理站点数: {len(event_results)}")
        logger.info(f"  - 平均NSE: {summary_stats.get('NSE_mean', np.nan):.4f}")
        logger.info(f"  - NSE>0.7比例: {summary_stats.get('NSE_good_ratio', 0):.1%}")

        # 创建轻量级返回结果（不包含详细数据）
        result = {
            'event_name': event_name,
            'metrics': event_metrics,
            'summary': summary_stats,
            'station_count': len(event_results)
        }

        # 清理大型数据结构
        del event_results
        gc.collect()

        # 记录处理后内存使用
        if hasattr(interpolator.data_loader, 'get_memory_usage'):
            memory_after = interpolator.data_loader.get_memory_usage()
            logger.info(f"事件{event_name}处理后内存: RSS={memory_after['rss']:.1f}MB")

        return result

    except Exception as e:
        logger.error(f"处理洪水事件{event_name}失败: {e}")
        # 强制清理内存
        gc.collect()
        return None

def generate_overall_report(all_results, config, evaluator, visualizer):
    """生成总体报告"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("生成总体报告")
        
        # 收集所有指标
        all_metrics = {}
        overall_summary = {
            'total_events': len(all_results),
            'total_stations': 0,
            'events_summary': {}
        }
        
        for result in all_results:
            if result is None:
                continue
                
            event_name = result['event_name']
            event_metrics = result['metrics']
            event_summary = result['summary']
            
            all_metrics[event_name] = event_metrics
            overall_summary['events_summary'][event_name] = event_summary
            overall_summary['total_stations'] += len(event_metrics)
        
        # 计算总体统计
        all_station_metrics = {}
        for event_metrics in all_metrics.values():
            all_station_metrics.update(event_metrics)
        
        overall_stats = evaluator.calculate_summary_statistics(all_station_metrics)
        overall_summary['overall_statistics'] = overall_stats
        
        # 保存总体报告
        save_results(overall_summary, config.OUTPUT_DIR, "overall_report.json")
        
        # 生成总体可视化
        if config.GENERATE_PLOTS:
            visualizer.plot_overall_summary(all_metrics, config.OUTPUT_DIR)
        
        # 生成详细的总体指标表
        all_metrics_df = pd.DataFrame()
        for event_name, event_metrics in all_metrics.items():
            for station_id, station_metrics in event_metrics.items():
                row = station_metrics.copy()
                row['event_name'] = event_name
                row['station_id'] = station_id
                all_metrics_df = pd.concat([all_metrics_df, pd.DataFrame([row])], ignore_index=True)
        
        if not all_metrics_df.empty:
            save_results(all_metrics_df, config.OUTPUT_DIR, "all_stations_metrics.csv")
        
        # 打印总体统计
        logger.info("="*60)
        logger.info("Delaunay插值系统总体性能报告")
        logger.info("="*60)
        logger.info(f"处理洪水事件数: {overall_summary['total_events']}")
        logger.info(f"总站点数: {overall_summary['total_stations']}")
        logger.info(f"平均NSE: {overall_stats.get('NSE_mean', np.nan):.4f}")
        logger.info(f"NSE标准差: {overall_stats.get('NSE_std', np.nan):.4f}")
        logger.info(f"NSE>0.7站点数: {overall_stats.get('good_nse_stations', 0)}")
        logger.info(f"NSE>0.7比例: {overall_stats.get('NSE_good_ratio', 0):.1%}")
        logger.info(f"平均MAE: {overall_stats.get('MAE_mean', np.nan):.4f}")
        logger.info(f"平均RMSE: {overall_stats.get('RMSE_mean', np.nan):.4f}")
        logger.info("="*60)
        
        return overall_summary
        
    except Exception as e:
        logger.error(f"生成总体报告失败: {e}")
        return None

def main():
    """主函数（内存优化版本）"""
    memory_monitor = None

    try:
        print("Delaunay插值系统启动...")

        # 初始化内存监控
        memory_monitor = get_memory_monitor()
        start_global_monitoring(interval_seconds=60)  # 每分钟检查一次

        initial_memory = memory_monitor.get_memory_usage()
        print(f"初始内存使用: {initial_memory['rss_mb']:.1f}MB")

        # 初始化配置
        config = DelaunayConfig()

        # 验证配置
        config_errors = config.validate_config()
        if config_errors:
            print("配置验证失败:")
            for error in config_errors:
                print(f"  - {error}")
            return 1

        # 设置日志
        logger = setup_logging(config)
        logger.info(config)
        logger.info(f"内存监控已启动，初始内存: {initial_memory['rss_mb']:.1f}MB")
        
        # 初始化组件
        data_loader = DelaunayDataLoader(config)
        evaluator = EvaluationMetrics(config)
        interpolator = DelaunayInterpolator(config, data_loader, evaluator)
        visualizer = DelaunayVisualization(config)
        
        # 加载Delaunay分析结果
        logger.info("加载Delaunay分析结果...")
        delaunay_analysis = data_loader.load_delaunay_analysis()
        logger.info(f"成功加载{len(delaunay_analysis)}个站点的分析结果")
        
        # 获取洪水事件列表
        flood_events = config.get_flood_events()
        logger.info(f"🌊 发现{len(flood_events)}个洪水事件: {flood_events}")

        if not flood_events:
            logger.warning("⚠️ 未发现洪水事件，程序退出")
            return 1

        # 检查已处理的事件
        completed_events = []
        incomplete_events = []

        for event_name in flood_events:
            event_output_dir = config.OUTPUT_DIR / event_name
            metrics_file = event_output_dir / f"{event_name}_metrics.csv"
            summary_file = event_output_dir / f"{event_name}_summary.json"

            if metrics_file.exists() and summary_file.exists():
                completed_events.append(event_name)
            else:
                incomplete_events.append(event_name)

        logger.info(f"✅ 已完成事件: {len(completed_events)}个")
        logger.info(f"🔄 需要处理事件: {len(incomplete_events)}个")

        if incomplete_events:
            logger.info(f"待处理事件列表: {incomplete_events}")
            flood_events = incomplete_events
        else:
            logger.info("🎉 所有洪水事件已完成处理！")
            # 仍然生成总体报告
            flood_events = config.get_flood_events()
        
        # 处理所有洪水事件（优化版本）
        all_results = []
        max_events_in_memory = getattr(config, 'MAX_EVENTS_IN_MEMORY', 1)

        # 如果有未完成的事件，处理它们
        if len(incomplete_events) > 0:
            logger.info(f"🚀 开始处理{len(incomplete_events)}个未完成的洪水事件...")

            for i, event_name in enumerate(incomplete_events):
                logger.info(f"🌊 处理洪水事件 {i+1}/{len(incomplete_events)}: {event_name}")

                # 检查事件是否已经完成
                event_output_dir = config.OUTPUT_DIR / event_name
                metrics_file = event_output_dir / f"{event_name}_metrics.csv"
                summary_file = event_output_dir / f"{event_name}_summary.json"

                if metrics_file.exists() and summary_file.exists():
                    logger.info(f"✅ 事件{event_name}已完成，跳过")
                    continue

                try:
                    # 确保输出目录存在
                    event_output_dir.mkdir(parents=True, exist_ok=True)

                    result = process_single_event(event_name, interpolator, evaluator,
                                                visualizer, config)

                    if result:
                        all_results.append(result)
                        logger.info(f"✅ 洪水事件{event_name}处理成功")

                        # 验证输出文件是否正确生成
                        if metrics_file.exists() and summary_file.exists():
                            logger.info(f"✅ 事件{event_name}输出文件验证通过")
                        else:
                            logger.warning(f"⚠️ 事件{event_name}输出文件不完整")
                    else:
                        logger.error(f"❌ 洪水事件{event_name}处理失败")
                        # 尝试重新处理一次
                        logger.info(f"🔄 重试处理事件{event_name}...")
                        try:
                            result = process_single_event(event_name, interpolator, evaluator,
                                                        visualizer, config)
                            if result:
                                all_results.append(result)
                                logger.info(f"✅ 事件{event_name}重试成功")
                            else:
                                logger.error(f"❌ 事件{event_name}重试仍然失败")
                        except Exception as retry_e:
                            logger.error(f"❌ 事件{event_name}重试异常: {retry_e}")

                except Exception as e:
                    logger.error(f"❌ 洪水事件{event_name}处理出现异常: {e}")
                    import traceback
                    logger.error(f"详细错误信息: {traceback.format_exc()}")
                    logger.info("继续处理下一个洪水事件...")
                    # 强制清理内存
                    gc.collect()

                # 内存管理：定期清理缓存
                if (i + 1) % max_events_in_memory == 0:
                    logger.info("🧹 执行内存清理...")
                    if hasattr(interpolator.data_loader, 'clear_cache'):
                        interpolator.data_loader.clear_cache()

                    # 使用内存监控器优化内存
                    if memory_monitor:
                        memory_monitor.optimize_memory()
                    else:
                        gc.collect()

                    # 记录内存使用情况
                    if memory_monitor:
                        memory_current = memory_monitor.get_memory_usage()
                        logger.info(f"清理后内存使用: RSS={memory_current['rss_mb']:.1f}MB")

                # 检查内存使用并在必要时优化
                try:
                    check_memory_and_optimize(threshold_mb=800)
                except Exception as e:
                    logger.warning(f"内存优化失败: {e}")
                    gc.collect()

        # 收集所有已完成事件的结果用于总体报告
        logger.info("📊 收集所有事件结果用于总体报告...")
        all_flood_events = config.get_flood_events()

        # 从已完成的事件中加载结果
        for event_name in all_flood_events:
            event_output_dir = config.OUTPUT_DIR / event_name
            metrics_file = event_output_dir / f"{event_name}_metrics.csv"
            summary_file = event_output_dir / f"{event_name}_summary.json"

            if metrics_file.exists() and summary_file.exists():
                try:
                    # 加载事件结果用于总体报告
                    import json
                    with open(summary_file, 'r', encoding='utf-8') as f:
                        event_summary = json.load(f)

                    # 如果结果不在all_results中，添加它
                    if not any(r.get('event_name') == event_name for r in all_results):
                        all_results.append({
                            'event_name': event_name,
                            'summary': event_summary
                        })

                except Exception as e:
                    logger.warning(f"加载事件{event_name}结果失败: {e}")
            else:
                logger.warning(f"⚠️ 事件{event_name}结果文件不完整")
        
        # 生成总体报告
        logger.info("📋 生成总体报告...")
        overall_report = generate_overall_report(all_results, config, evaluator, visualizer)

        # 最终验证：检查所有洪水事件是否都有输出
        logger.info("🔍 最终验证所有洪水事件处理状态...")
        all_flood_events = config.get_flood_events()
        completed_count = 0
        missing_events = []

        for event_name in all_flood_events:
            event_output_dir = config.OUTPUT_DIR / event_name
            metrics_file = event_output_dir / f"{event_name}_metrics.csv"
            summary_file = event_output_dir / f"{event_name}_summary.json"

            if metrics_file.exists() and summary_file.exists():
                completed_count += 1
            else:
                missing_events.append(event_name)

        logger.info(f"✅ 完成事件数: {completed_count}/{len(all_flood_events)}")
        if missing_events:
            logger.warning(f"⚠️ 未完成事件: {missing_events}")
        else:
            logger.info("🎉 所有洪水事件处理完成！")

        if overall_report:
            logger.info("🎯 Delaunay插值系统运行完成！")
            logger.info(f"📁 结果保存在: {config.OUTPUT_DIR}")
            logger.info(f"📊 处理事件总数: {completed_count}/{len(all_flood_events)}")

            # 显示最终内存使用情况
            if memory_monitor:
                final_memory = memory_monitor.get_memory_usage()
                memory_summary = memory_monitor.get_memory_summary()
                logger.info(f"💾 最终内存使用: {final_memory['rss_mb']:.1f}MB")
                logger.info(f"💾 最大内存使用: {memory_summary.get('max_mb', 0):.1f}MB")
                logger.info(f"⚠️ 内存警告次数: {memory_summary.get('warning_count', 0)}")
                logger.info(f"🚨 内存严重次数: {memory_summary.get('critical_count', 0)}")
        else:
            logger.error("❌ 总体报告生成失败")
            return 1

        return 0

    except Exception as e:
        print(f"程序运行失败: {e}")
        if 'logger' in locals():
            logger.error(f"程序运行失败: {e}")
        return 1

    finally:
        # 停止内存监控
        if memory_monitor:
            try:
                stop_global_monitoring()
            except Exception as e:
                print(f"停止内存监控失败: {e}")

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

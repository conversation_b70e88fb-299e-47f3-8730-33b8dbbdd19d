"""
IDW空间插值可视化模块

主要功能：
1. 生成插值结果图
2. 验证结果散点图
3. 评估指标图表
4. 批量结果汇总图

作者：空间插值系统
版本：1.0
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)


class IDWVisualizer:
    """IDW插值可视化器"""
    
    def __init__(self, output_dir: str, dpi: int = 300, format: str = 'png',
                 use_chinese: bool = True):
        """
        初始化可视化器
        
        参数:
            output_dir: 输出目录
            dpi: 图像分辨率
            format: 图像格式
            use_chinese: 是否使用中文标签
        """
        self.output_dir = output_dir
        self.dpi = dpi
        self.format = format
        self.use_chinese = use_chinese
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置样式
        plt.style.use('default')
        sns.set_palette("husl")
    
    def plot_validation_results(self, validation_results: Dict, event_name: str = None) -> str:
        """
        绘制验证结果散点图
        
        参数:
            validation_results: 验证结果字典
            event_name: 事件名称
            
        返回:
            str: 保存的图片路径
        """
        try:
            if not validation_results or 'observed_values' not in validation_results:
                logger.warning("没有有效的验证结果数据")
                return None
            
            observed = np.array(validation_results['observed_values'])
            predicted = np.array(validation_results['predicted_values'])
            
            # 创建图形
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            
            # 散点图
            ax1.scatter(observed, predicted, alpha=0.6, s=50)
            
            # 1:1线
            min_val = min(observed.min(), predicted.min())
            max_val = max(observed.max(), predicted.max())
            ax1.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, linewidth=2)
            
            # 设置标签
            if self.use_chinese:
                ax1.set_xlabel('观测值 (mm)', fontsize=12)
                ax1.set_ylabel('预测值 (mm)', fontsize=12)
                ax1.set_title('IDW插值验证结果', fontsize=14, fontweight='bold')
            else:
                ax1.set_xlabel('Observed (mm)', fontsize=12)
                ax1.set_ylabel('Predicted (mm)', fontsize=12)
                ax1.set_title('IDW Interpolation Validation', fontsize=14, fontweight='bold')
            
            ax1.grid(True, alpha=0.3)
            ax1.set_aspect('equal', adjustable='box')
            
            # 添加统计信息
            mae = validation_results.get('mae', 0)
            rmse = validation_results.get('rmse', 0)
            nse = validation_results.get('nse', 0)
            correlation = validation_results.get('correlation', 0)
            n_stations = validation_results.get('n_stations', 0)
            
            stats_text = f'MAE: {mae:.2f} mm\nRMSE: {rmse:.2f} mm\nNSE: {nse:.3f}\nR: {correlation:.3f}\nN: {n_stations}'
            ax1.text(0.05, 0.95, stats_text, transform=ax1.transAxes, 
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            # 误差分布直方图
            errors = predicted - observed
            ax2.hist(errors, bins=20, alpha=0.7, edgecolor='black')
            
            if self.use_chinese:
                ax2.set_xlabel('预测误差 (mm)', fontsize=12)
                ax2.set_ylabel('频次', fontsize=12)
                ax2.set_title('误差分布', fontsize=14, fontweight='bold')
            else:
                ax2.set_xlabel('Prediction Error (mm)', fontsize=12)
                ax2.set_ylabel('Frequency', fontsize=12)
                ax2.set_title('Error Distribution', fontsize=14, fontweight='bold')
            
            ax2.grid(True, alpha=0.3)
            ax2.axvline(0, color='red', linestyle='--', alpha=0.8)
            
            # 添加误差统计
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            error_text = f'Mean: {mean_error:.2f}\nStd: {std_error:.2f}'
            ax2.text(0.05, 0.95, error_text, transform=ax2.transAxes,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            plt.tight_layout()
            
            # 保存图片
            if event_name:
                filename = f'validation_results_{event_name}.{self.format}'
            else:
                filename = f'validation_results.{self.format}'
            
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            
            logger.info(f"验证结果图已保存: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"绘制验证结果失败: {e}")
            return None
    
    def plot_station_map(self, stations_df: pd.DataFrame, rainfall_data: Dict[str, float] = None,
                        event_name: str = None) -> str:
        """
        绘制站点分布图
        
        参数:
            stations_df: 站点数据框
            rainfall_data: 降雨数据字典
            event_name: 事件名称
            
        返回:
            str: 保存的图片路径
        """
        try:
            fig, ax = plt.subplots(figsize=(10, 8))
            
            # 绘制站点
            if rainfall_data:
                # 根据降雨量着色
                colors = []
                sizes = []
                for _, row in stations_df.iterrows():
                    station = str(row['站点'])
                    if station in rainfall_data:
                        rainfall = rainfall_data[station]
                        colors.append(rainfall)
                        sizes.append(max(20, min(200, rainfall * 2)))  # 根据降雨量调整大小
                    else:
                        colors.append(0)
                        sizes.append(20)
                
                scatter = ax.scatter(stations_df['经度'], stations_df['纬度'], 
                                   c=colors, s=sizes, alpha=0.7, cmap='Blues', edgecolors='black')
                
                # 添加颜色条
                cbar = plt.colorbar(scatter, ax=ax)
                if self.use_chinese:
                    cbar.set_label('降雨量 (mm)', fontsize=12)
                else:
                    cbar.set_label('Rainfall (mm)', fontsize=12)
            else:
                # 简单的站点分布
                ax.scatter(stations_df['经度'], stations_df['纬度'], 
                          s=50, alpha=0.7, color='blue', edgecolors='black')
            
            # 添加站点标签（可选）
            if len(stations_df) <= 50:  # 只在站点不太多时显示标签
                for _, row in stations_df.iterrows():
                    ax.annotate(str(row['站点']), (row['经度'], row['纬度']), 
                               xytext=(5, 5), textcoords='offset points', fontsize=8)
            
            # 设置标签和标题
            if self.use_chinese:
                ax.set_xlabel('经度 (°)', fontsize=12)
                ax.set_ylabel('纬度 (°)', fontsize=12)
                if event_name:
                    ax.set_title(f'站点分布图 - {event_name}', fontsize=14, fontweight='bold')
                else:
                    ax.set_title('站点分布图', fontsize=14, fontweight='bold')
            else:
                ax.set_xlabel('Longitude (°)', fontsize=12)
                ax.set_ylabel('Latitude (°)', fontsize=12)
                if event_name:
                    ax.set_title(f'Station Distribution - {event_name}', fontsize=14, fontweight='bold')
                else:
                    ax.set_title('Station Distribution', fontsize=14, fontweight='bold')
            
            ax.grid(True, alpha=0.3)
            ax.set_aspect('equal', adjustable='box')
            
            plt.tight_layout()
            
            # 保存图片
            if event_name:
                filename = f'station_map_{event_name}.{self.format}'
            else:
                filename = f'station_map.{self.format}'
            
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            
            logger.info(f"站点分布图已保存: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"绘制站点分布图失败: {e}")
            return None
    
    def plot_metrics_summary(self, batch_results: Dict) -> str:
        """
        绘制批量处理结果汇总图
        
        参数:
            batch_results: 批量处理结果字典
            
        返回:
            str: 保存的图片路径
        """
        try:
            if not batch_results:
                logger.warning("没有批量处理结果数据")
                return None
            
            # 提取数据
            events = list(batch_results.keys())
            mae_values = [batch_results[event].get('mae', np.nan) for event in events]
            rmse_values = [batch_results[event].get('rmse', np.nan) for event in events]
            nse_values = [batch_results[event].get('nse', np.nan) for event in events]
            
            # 创建图形
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            
            # MAE柱状图
            ax1.bar(range(len(events)), mae_values, alpha=0.7, color='skyblue', edgecolor='black')
            ax1.set_xticks(range(len(events)))
            ax1.set_xticklabels(events, rotation=45, ha='right')
            if self.use_chinese:
                ax1.set_ylabel('MAE (mm)', fontsize=12)
                ax1.set_title('平均绝对误差', fontsize=14, fontweight='bold')
            else:
                ax1.set_ylabel('MAE (mm)', fontsize=12)
                ax1.set_title('Mean Absolute Error', fontsize=14, fontweight='bold')
            ax1.grid(True, alpha=0.3)
            
            # RMSE柱状图
            ax2.bar(range(len(events)), rmse_values, alpha=0.7, color='lightcoral', edgecolor='black')
            ax2.set_xticks(range(len(events)))
            ax2.set_xticklabels(events, rotation=45, ha='right')
            if self.use_chinese:
                ax2.set_ylabel('RMSE (mm)', fontsize=12)
                ax2.set_title('均方根误差', fontsize=14, fontweight='bold')
            else:
                ax2.set_ylabel('RMSE (mm)', fontsize=12)
                ax2.set_title('Root Mean Square Error', fontsize=14, fontweight='bold')
            ax2.grid(True, alpha=0.3)
            
            # NSE柱状图
            ax3.bar(range(len(events)), nse_values, alpha=0.7, color='lightgreen', edgecolor='black')
            ax3.set_xticks(range(len(events)))
            ax3.set_xticklabels(events, rotation=45, ha='right')
            ax3.axhline(y=0, color='red', linestyle='--', alpha=0.8)
            if self.use_chinese:
                ax3.set_ylabel('NSE', fontsize=12)
                ax3.set_title('纳什效率系数', fontsize=14, fontweight='bold')
            else:
                ax3.set_ylabel('NSE', fontsize=12)
                ax3.set_title('Nash-Sutcliffe Efficiency', fontsize=14, fontweight='bold')
            ax3.grid(True, alpha=0.3)
            
            # 综合指标散点图
            valid_indices = [i for i, (mae, nse) in enumerate(zip(mae_values, nse_values)) 
                           if not (np.isnan(mae) or np.isnan(nse))]
            
            if valid_indices:
                valid_mae = [mae_values[i] for i in valid_indices]
                valid_nse = [nse_values[i] for i in valid_indices]
                valid_events = [events[i] for i in valid_indices]
                
                scatter = ax4.scatter(valid_mae, valid_nse, s=100, alpha=0.7, c=range(len(valid_mae)), 
                                    cmap='viridis', edgecolors='black')
                
                # 添加事件标签
                for i, event in enumerate(valid_events):
                    ax4.annotate(event, (valid_mae[i], valid_nse[i]), 
                               xytext=(5, 5), textcoords='offset points', fontsize=8)
                
                ax4.axhline(y=0, color='red', linestyle='--', alpha=0.8)
                if self.use_chinese:
                    ax4.set_xlabel('MAE (mm)', fontsize=12)
                    ax4.set_ylabel('NSE', fontsize=12)
                    ax4.set_title('综合性能评估', fontsize=14, fontweight='bold')
                else:
                    ax4.set_xlabel('MAE (mm)', fontsize=12)
                    ax4.set_ylabel('NSE', fontsize=12)
                    ax4.set_title('Overall Performance', fontsize=14, fontweight='bold')
                ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 确保输出目录存在
            os.makedirs(self.output_dir, exist_ok=True)

            # 保存图片
            filename = f'metrics_summary.{self.format}'
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            
            logger.info(f"指标汇总图已保存: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"绘制指标汇总图失败: {e}")
            return None

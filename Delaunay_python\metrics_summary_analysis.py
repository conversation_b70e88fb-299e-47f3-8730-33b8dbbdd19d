#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay三角剖分插值系统指标评价汇总分析

作者: 空间插值研究团队
日期: 2024年12月
版本: 1.0

功能：
1. 汇总所有洪水事件的评价指标
2. 生成综合统计报告
3. 创建详细的Excel汇总表
4. 分析性能趋势和模式
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
from pathlib import Path
import logging
from typing import Dict, List, Optional, Tuple
import warnings
from datetime import datetime
import glob
warnings.filterwarnings('ignore')

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['figure.dpi'] = 300

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

class DelaunayMetricsSummaryAnalyzer:
    """Delaunay插值系统指标评价汇总分析器"""
    
    def __init__(self, metrics_dir: Path, output_dir: Path):
        self.metrics_dir = metrics_dir
        self.output_dir = output_dir
        self.summary_dir = output_dir / 'summary_analysis'
        self.summary_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # 设置绘图样式
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 定义评价指标
        self.metrics = ['NSE', 'RMSE', 'MAE', 'Correlation', 'R_squared', 'Bias', 'Relative_Bias']
        
        # 性能等级定义
        self.performance_levels = {
            'Excellent': {'NSE': (0.75, 1.0), 'color': '#2E8B57'},
            'Good': {'NSE': (0.65, 0.75), 'color': '#32CD32'},
            'Satisfactory': {'NSE': (0.50, 0.65), 'color': '#FFD700'},
            'Unsatisfactory': {'NSE': (0.20, 0.50), 'color': '#FFA500'},
            'Unacceptable': {'NSE': (-np.inf, 0.20), 'color': '#FF6347'}
        }
        
        # 加载站点名称映射
        self.station_names = self.load_station_names()
        
    def load_station_names(self) -> Dict[str, str]:
        """加载站点代码到中文名称的映射"""
        try:
            station_file = self.output_dir.parent.parent / '水晏泰森.xlsx'
            if station_file.exists():
                df = pd.read_excel(station_file)
                if 'PSTCD' in df.columns and 'NAME' in df.columns:
                    name_mapping = {}
                    for _, row in df.iterrows():
                        station_code_orig = str(row['PSTCD']).strip()
                        station_name = str(row['NAME']).strip()
                        
                        # 存储多种格式
                        for code_variant in [station_code_orig, station_code_orig.upper(), station_code_orig.lower()]:
                            name_mapping[code_variant] = station_name
                    
                    print(f"✅ Successfully loaded {len(set(name_mapping.values()))} unique station names")
                    return name_mapping
        except Exception as e:
            print(f"⚠️  Warning: Failed to load station names: {e}")
        
        return {}
    
    def get_station_display_name(self, station_code: str) -> str:
        """获取站点的显示名称（中文名称）"""
        if not station_code:
            return station_code
        
        station_code_str = str(station_code).strip()
        
        # 尝试多种匹配方式
        for key in [station_code_str, station_code_str.upper(), station_code_str.lower()]:
            if key in self.station_names:
                chinese_name = self.station_names[key]
                if chinese_name and chinese_name != 'nan' and len(chinese_name.strip()) > 0:
                    return chinese_name
        
        return station_code_str
    
    def load_all_metrics_data(self) -> pd.DataFrame:
        """加载所有评价指标数据"""
        print("Loading all metrics data...")
        
        all_data = []
        csv_files = list(self.metrics_dir.glob('*_metrics.csv'))
        
        print(f"Found {len(csv_files)} metrics files")
        
        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
                all_data.append(df)
                print(f"  ✅ Loaded: {file_path.name}")
            except Exception as e:
                print(f"  ❌ Failed to load: {file_path.name} - {e}")
        
        if not all_data:
            raise ValueError("No valid metrics files found")
        
        # 合并所有数据
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 数据清理和预处理
        combined_df = self.preprocess_data(combined_df)
        
        print(f"✅ Successfully loaded {len(combined_df)} records from {len(csv_files)} files")
        return combined_df
    
    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        print("Preprocessing data...")
        
        # 提取年份和事件序号
        df['Year'] = df['事件名称'].str.extract(r'(\d{4})').astype(int)
        df['Event_Seq'] = df['事件名称'].str.extract(r'-(\d+)').astype(int)
        
        # 添加中文站点名称
        df['Station_Name_CN'] = df['站点代码'].apply(self.get_station_display_name)
        
        # 转换数值列
        for metric in self.metrics:
            if metric in df.columns:
                df[metric] = pd.to_numeric(df[metric], errors='coerce')
        
        # 添加性能等级
        df['Performance_Level'] = df['NSE'].apply(self.classify_performance)
        
        # 添加时间信息
        df['Decade'] = (df['Year'] // 10) * 10
        df['Period'] = df['Year'].apply(lambda x: f"{(x//5)*5}-{(x//5)*5+4}")
        
        print(f"✅ Data preprocessing completed")
        return df
    
    def classify_performance(self, nse: float) -> str:
        """根据NSE值分类性能"""
        if pd.isna(nse):
            return "Invalid"
        
        for level, criteria in self.performance_levels.items():
            if criteria['NSE'][0] <= nse < criteria['NSE'][1]:
                return level
        
        return "Invalid"
    
    def generate_overall_summary(self, df: pd.DataFrame) -> Dict:
        """生成总体汇总统计"""
        print("Generating overall summary statistics...")
        
        summary = {
            'basic_info': {
                'total_records': len(df),
                'total_events': df['事件名称'].nunique(),
                'total_stations': df['站点代码'].nunique(),
                'time_span': f"{df['Year'].min()}-{df['Year'].max()}",
                'years_covered': sorted(df['Year'].unique().tolist()),
                'events_per_year': df.groupby('Year')['事件名称'].nunique().to_dict()
            },
            'metrics_summary': {},
            'performance_distribution': {},
            'station_summary': {},
            'temporal_summary': {}
        }
        
        # 指标统计
        for metric in self.metrics:
            if metric in df.columns:
                values = df[metric].dropna()
                if len(values) > 0:
                    summary['metrics_summary'][metric] = {
                        'count': len(values),
                        'mean': float(values.mean()),
                        'std': float(values.std()),
                        'min': float(values.min()),
                        'max': float(values.max()),
                        'median': float(values.median()),
                        'q25': float(values.quantile(0.25)),
                        'q75': float(values.quantile(0.75)),
                        'valid_rate': len(values) / len(df) * 100
                    }
        
        # 性能等级分布
        performance_counts = df['Performance_Level'].value_counts()
        summary['performance_distribution'] = {
            level: {
                'count': int(count),
                'percentage': float(count / len(df) * 100)
            }
            for level, count in performance_counts.items()
        }
        
        # 站点汇总
        station_stats = df.groupby(['站点代码', 'Station_Name_CN']).agg({
            'NSE': ['count', 'mean', 'std', 'min', 'max'],
            'RMSE': 'mean',
            'MAE': 'mean',
            'Correlation': 'mean'
        }).round(4)
        
        station_stats.columns = ['_'.join(col).strip() for col in station_stats.columns]
        summary['station_summary'] = station_stats.to_dict('index')
        
        # 时间汇总
        yearly_stats = df.groupby('Year').agg({
            'NSE': ['count', 'mean', 'std'],
            'RMSE': 'mean',
            'MAE': 'mean',
            'Correlation': 'mean'
        }).round(4)
        
        yearly_stats.columns = ['_'.join(col).strip() for col in yearly_stats.columns]
        summary['temporal_summary'] = yearly_stats.to_dict('index')
        
        print(f"✅ Overall summary generated")
        return summary
    
    def create_comprehensive_excel_report(self, df: pd.DataFrame, summary: Dict):
        """创建综合Excel报告"""
        print("Creating comprehensive Excel report...")
        
        excel_path = self.summary_dir / 'Delaunay_Metrics_Comprehensive_Summary.xlsx'
        
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 1. 总体概览
            overview_data = []
            overview_data.append(['指标项目', '数值'])
            overview_data.append(['总记录数', summary['basic_info']['total_records']])
            overview_data.append(['洪水事件数', summary['basic_info']['total_events']])
            overview_data.append(['验证站点数', summary['basic_info']['total_stations']])
            overview_data.append(['时间跨度', summary['basic_info']['time_span']])
            overview_data.append(['覆盖年份', ', '.join(map(str, summary['basic_info']['years_covered']))])
            
            overview_df = pd.DataFrame(overview_data[1:], columns=overview_data[0])
            overview_df.to_excel(writer, sheet_name='总体概览', index=False)
            
            # 2. 指标统计汇总
            metrics_data = []
            metrics_data.append(['指标', '有效记录数', '平均值', '标准差', '最小值', '最大值', '中位数', '25%分位数', '75%分位数', '有效率(%)'])
            
            for metric, stats in summary['metrics_summary'].items():
                metrics_data.append([
                    metric,
                    stats['count'],
                    round(stats['mean'], 4),
                    round(stats['std'], 4),
                    round(stats['min'], 4),
                    round(stats['max'], 4),
                    round(stats['median'], 4),
                    round(stats['q25'], 4),
                    round(stats['q75'], 4),
                    round(stats['valid_rate'], 2)
                ])
            
            metrics_summary_df = pd.DataFrame(metrics_data[1:], columns=metrics_data[0])
            metrics_summary_df.to_excel(writer, sheet_name='指标统计汇总', index=False)
            
            # 3. 性能等级分布
            performance_data = []
            performance_data.append(['性能等级', '记录数', '百分比(%)'])
            
            for level, stats in summary['performance_distribution'].items():
                performance_data.append([
                    level,
                    stats['count'],
                    round(stats['percentage'], 2)
                ])
            
            performance_df = pd.DataFrame(performance_data[1:], columns=performance_data[0])
            performance_df.to_excel(writer, sheet_name='性能等级分布', index=False)
            
            # 4. 站点详细统计
            station_detailed = []
            station_detailed.append(['站点代码', '中文名称', '事件数', 'NSE均值', 'NSE标准差', 'NSE最小值', 'NSE最大值', 'RMSE均值', 'MAE均值', 'Correlation均值'])
            
            for (code, name), stats in summary['station_summary'].items():
                station_detailed.append([
                    code,
                    name,
                    int(stats.get('NSE_count', 0)),
                    round(stats.get('NSE_mean', 0), 4),
                    round(stats.get('NSE_std', 0), 4),
                    round(stats.get('NSE_min', 0), 4),
                    round(stats.get('NSE_max', 0), 4),
                    round(stats.get('RMSE_mean', 0), 4),
                    round(stats.get('MAE_mean', 0), 4),
                    round(stats.get('Correlation_mean', 0), 4)
                ])
            
            station_df = pd.DataFrame(station_detailed[1:], columns=station_detailed[0])
            station_df.to_excel(writer, sheet_name='站点详细统计', index=False)
            
            # 5. 年度统计
            yearly_detailed = []
            yearly_detailed.append(['年份', '事件数', 'NSE均值', 'NSE标准差', 'RMSE均值', 'MAE均值', 'Correlation均值'])
            
            for year, stats in summary['temporal_summary'].items():
                yearly_detailed.append([
                    year,
                    int(stats.get('NSE_count', 0)),
                    round(stats.get('NSE_mean', 0), 4),
                    round(stats.get('NSE_std', 0), 4),
                    round(stats.get('RMSE_mean', 0), 4),
                    round(stats.get('MAE_mean', 0), 4),
                    round(stats.get('Correlation_mean', 0), 4)
                ])
            
            yearly_df = pd.DataFrame(yearly_detailed[1:], columns=yearly_detailed[0])
            yearly_df.to_excel(writer, sheet_name='年度统计', index=False)
            
            # 6. 原始数据
            df_export = df.copy()
            # 重新排列列顺序
            columns_order = ['事件名称', 'Year', 'Event_Seq', '站点代码', 'Station_Name_CN'] + self.metrics + ['Performance_Level', 'Sample_Count']
            columns_order = [col for col in columns_order if col in df_export.columns]
            df_export = df_export[columns_order]
            
            df_export.to_excel(writer, sheet_name='原始数据', index=False)
        
        print(f"✅ Comprehensive Excel report saved: {excel_path}")
        return excel_path

    def create_summary_visualizations(self, df: pd.DataFrame, summary: Dict):
        """创建汇总可视化图表"""
        print("Creating summary visualizations...")

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False

        # 1. 综合仪表板
        fig = plt.figure(figsize=(20, 16))
        gs = plt.GridSpec(4, 3, figure=fig, hspace=0.3, wspace=0.3)

        # 1.1 指标分布箱线图
        ax1 = fig.add_subplot(gs[0, 0])
        metrics_for_box = ['NSE', 'Correlation']
        box_data = [df[metric].dropna() for metric in metrics_for_box if metric in df.columns]
        box_labels = [metric for metric in metrics_for_box if metric in df.columns]

        if box_data:
            bp = ax1.boxplot(box_data, labels=box_labels, patch_artist=True)
            colors = ['lightblue', 'lightgreen']
            for patch, color in zip(bp['boxes'], colors[:len(bp['boxes'])]):
                patch.set_facecolor(color)

        ax1.set_title('Key Metrics Distribution\n关键指标分布', fontsize=12, fontweight='bold')
        ax1.grid(True, alpha=0.3)

        # 1.2 性能等级饼图
        ax2 = fig.add_subplot(gs[0, 1])
        performance_counts = df['Performance_Level'].value_counts()
        colors = [self.performance_levels.get(level, {}).get('color', '#808080') for level in performance_counts.index]

        wedges, texts, autotexts = ax2.pie(performance_counts.values,
                                          labels=performance_counts.index,
                                          autopct='%1.1f%%',
                                          colors=colors,
                                          startangle=90)
        ax2.set_title('Performance Level Distribution\nNSE性能等级分布', fontsize=12, fontweight='bold')

        # 1.3 年度趋势
        ax3 = fig.add_subplot(gs[0, 2])
        yearly_nse = df.groupby('Year')['NSE'].agg(['mean', 'std', 'count']).reset_index()

        ax3.errorbar(yearly_nse['Year'], yearly_nse['mean'],
                    yerr=yearly_nse['std'], marker='o', linewidth=2,
                    markersize=6, capsize=5, capthick=2)
        ax3.set_xlabel('Year', fontsize=10)
        ax3.set_ylabel('Average NSE', fontsize=10)
        ax3.set_title('Annual NSE Trend\n年度NSE趋势', fontsize=12, fontweight='bold')
        ax3.grid(True, alpha=0.3)

        # 1.4 顶级站点表现
        ax4 = fig.add_subplot(gs[1, 0])
        top_stations = df.groupby(['站点代码', 'Station_Name_CN'])['NSE'].mean().nlargest(8)

        bars = ax4.barh(range(len(top_stations)), top_stations.values,
                       color='lightgreen', alpha=0.8, edgecolor='black')

        # 使用中文名称
        top_labels = [name if name != code else f"站点{code}"
                     for (code, name), _ in top_stations.items()]

        ax4.set_yticks(range(len(top_stations)))
        ax4.set_yticklabels(top_labels, fontsize=9)
        ax4.set_xlabel('Average NSE', fontsize=10)
        ax4.set_title('Top 8 Stations Performance\n顶级站点表现', fontsize=12, fontweight='bold')
        ax4.grid(True, alpha=0.3, axis='x')

        # 1.5 事件数量统计
        ax5 = fig.add_subplot(gs[1, 1])
        events_per_year = df.groupby('Year')['事件名称'].nunique()

        bars = ax5.bar(events_per_year.index, events_per_year.values,
                      color='orange', alpha=0.7, edgecolor='black')
        ax5.set_xlabel('Year', fontsize=10)
        ax5.set_ylabel('Number of Events', fontsize=10)
        ax5.set_title('Annual Flood Events Count\n年度洪水事件数量', fontsize=12, fontweight='bold')
        ax5.grid(True, alpha=0.3, axis='y')

        # 1.6 指标相关性
        ax6 = fig.add_subplot(gs[1, 2])
        corr_metrics = ['NSE', 'RMSE', 'MAE', 'Correlation']
        corr_data = df[corr_metrics].corr()

        sns.heatmap(corr_data, annot=True, fmt='.3f', cmap='coolwarm',
                   center=0, ax=ax6, square=True, linewidths=0.5)
        ax6.set_title('Metrics Correlation\n指标相关性', fontsize=12, fontweight='bold')

        # 1.7 统计摘要表
        ax7 = fig.add_subplot(gs[2, :])

        # 创建统计摘要表
        stats_data = []
        for metric in ['NSE', 'RMSE', 'MAE', 'Correlation']:
            if metric in summary['metrics_summary']:
                stats = summary['metrics_summary'][metric]
                stats_data.append([
                    metric,
                    stats['count'],
                    f"{stats['mean']:.4f}",
                    f"{stats['std']:.4f}",
                    f"{stats['min']:.4f}",
                    f"{stats['max']:.4f}",
                    f"{stats['median']:.4f}",
                    f"{stats['valid_rate']:.1f}%"
                ])

        if stats_data:
            table = ax7.table(cellText=stats_data,
                             colLabels=['Metric', 'Count', 'Mean', 'Std', 'Min', 'Max', 'Median', 'Valid Rate'],
                             cellLoc='center',
                             loc='center',
                             bbox=[0, 0, 1, 1])
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1, 2)

            # 设置表格样式
            for i in range(8):
                table[(0, i)].set_facecolor('#4CAF50')
                table[(0, i)].set_text_props(weight='bold', color='white')

        ax7.axis('off')
        ax7.set_title('Evaluation Metrics Statistical Summary\n评价指标统计摘要',
                     fontsize=14, fontweight='bold', pad=20)

        # 1.8 基本信息
        ax8 = fig.add_subplot(gs[3, :])

        info_text = f"""
Delaunay三角剖分插值系统 - 指标评价汇总分析报告
Delaunay Triangulation Interpolation System - Metrics Evaluation Summary Report

基本信息 Basic Information:
• 总记录数 Total Records: {summary['basic_info']['total_records']:,}
• 洪水事件数 Flood Events: {summary['basic_info']['total_events']}
• 验证站点数 Validation Stations: {summary['basic_info']['total_stations']}
• 时间跨度 Time Span: {summary['basic_info']['time_span']}
• 分析日期 Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

性能等级分布 Performance Level Distribution:
"""

        for level, stats in summary['performance_distribution'].items():
            info_text += f"• {level}: {stats['count']} records ({stats['percentage']:.1f}%)\n"

        ax8.text(0.05, 0.95, info_text, transform=ax8.transAxes,
                fontsize=11, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        ax8.axis('off')

        plt.suptitle('Delaunay Interpolation System - Comprehensive Metrics Summary Dashboard\n'
                    'Delaunay三角剖分插值系统 - 综合指标汇总仪表板',
                    fontsize=18, fontweight='bold', y=0.98)

        # 保存图片
        dashboard_path = self.summary_dir / 'comprehensive_metrics_dashboard.png'
        plt.savefig(dashboard_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"✅ Summary dashboard saved: {dashboard_path}")
        return dashboard_path

    def generate_text_report(self, df: pd.DataFrame, summary: Dict):
        """生成文本报告"""
        print("Generating text report...")

        report_path = self.summary_dir / 'Delaunay_Metrics_Summary_Report.txt'

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("="*80 + "\n")
            f.write("Delaunay三角剖分插值系统指标评价汇总分析报告\n")
            f.write("Delaunay Triangulation Interpolation System - Metrics Evaluation Summary Report\n")
            f.write("="*80 + "\n\n")

            f.write(f"报告生成时间 Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 1. 基本信息
            f.write("1. 基本信息 Basic Information\n")
            f.write("-" * 40 + "\n")
            f.write(f"总记录数 Total Records: {summary['basic_info']['total_records']:,}\n")
            f.write(f"洪水事件数 Flood Events: {summary['basic_info']['total_events']}\n")
            f.write(f"验证站点数 Validation Stations: {summary['basic_info']['total_stations']}\n")
            f.write(f"时间跨度 Time Span: {summary['basic_info']['time_span']}\n")
            f.write(f"覆盖年份 Years Covered: {', '.join(map(str, summary['basic_info']['years_covered']))}\n\n")

            # 2. 指标统计汇总
            f.write("2. 指标统计汇总 Metrics Statistical Summary\n")
            f.write("-" * 40 + "\n")
            for metric, stats in summary['metrics_summary'].items():
                f.write(f"\n{metric}:\n")
                f.write(f"  有效记录数 Valid Records: {stats['count']}\n")
                f.write(f"  平均值 Mean: {stats['mean']:.4f}\n")
                f.write(f"  标准差 Standard Deviation: {stats['std']:.4f}\n")
                f.write(f"  最小值 Minimum: {stats['min']:.4f}\n")
                f.write(f"  最大值 Maximum: {stats['max']:.4f}\n")
                f.write(f"  中位数 Median: {stats['median']:.4f}\n")
                f.write(f"  有效率 Valid Rate: {stats['valid_rate']:.2f}%\n")

            # 3. 性能等级分布
            f.write("\n3. 性能等级分布 Performance Level Distribution\n")
            f.write("-" * 40 + "\n")
            for level, stats in summary['performance_distribution'].items():
                f.write(f"{level}: {stats['count']} records ({stats['percentage']:.2f}%)\n")

            # 4. 顶级站点
            f.write("\n4. 顶级站点表现 Top Stations Performance\n")
            f.write("-" * 40 + "\n")
            top_stations = df.groupby(['站点代码', 'Station_Name_CN'])['NSE'].mean().nlargest(10)
            for i, ((code, name), nse) in enumerate(top_stations.items(), 1):
                display_name = name if name != code else f"站点{code}"
                f.write(f"{i:2d}. {display_name} ({code}): NSE = {nse:.4f}\n")

            # 5. 年度统计
            f.write("\n5. 年度统计 Annual Statistics\n")
            f.write("-" * 40 + "\n")
            for year, stats in sorted(summary['temporal_summary'].items()):
                f.write(f"{year}: {int(stats.get('NSE_count', 0))} events, ")
                f.write(f"NSE = {stats.get('NSE_mean', 0):.4f} ± {stats.get('NSE_std', 0):.4f}\n")

        print(f"✅ Text report saved: {report_path}")
        return report_path

    def run_complete_analysis(self):
        """运行完整的汇总分析"""
        print("="*80)
        print("Delaunay Interpolation System - Metrics Summary Analysis")
        print("="*80)

        try:
            # 1. 加载数据
            df = self.load_all_metrics_data()

            # 2. 生成汇总统计
            summary = self.generate_overall_summary(df)

            # 3. 创建Excel报告
            excel_path = self.create_comprehensive_excel_report(df, summary)

            # 4. 创建可视化
            dashboard_path = self.create_summary_visualizations(df, summary)

            # 5. 生成文本报告
            text_path = self.generate_text_report(df, summary)

            print("\n" + "="*80)
            print("🎉 Metrics Summary Analysis Completed!")
            print("="*80)
            print(f"Generated Files:")
            print(f"  📊 Excel Report: {excel_path.name}")
            print(f"  📈 Dashboard: {dashboard_path.name}")
            print(f"  📄 Text Report: {text_path.name}")
            print(f"\n📁 All files saved in: {self.summary_dir}")
            print("="*80)

            return {
                'excel_report': excel_path,
                'dashboard': dashboard_path,
                'text_report': text_path,
                'summary_data': summary
            }

        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """主程序"""
    import sys
    from pathlib import Path

    # 设置路径
    current_dir = Path(__file__).parent
    output_dir = current_dir.parent / 'output' / 'Delaunay_interpolation'
    metrics_dir = output_dir / 'metrics'

    # 检查输入目录
    if not metrics_dir.exists():
        print(f"❌ Error: Metrics directory does not exist: {metrics_dir}")
        print("Please run Delaunay interpolation analysis first")
        return 1

    # 设置日志
    logging.basicConfig(
        level=logging.WARNING,
        format='%(levelname)s: %(message)s'
    )

    try:
        # 创建分析器
        analyzer = DelaunayMetricsSummaryAnalyzer(metrics_dir, output_dir)

        # 运行完整分析
        results = analyzer.run_complete_analysis()

        return 0

    except Exception as e:
        print(f"\n❌ Metrics summary analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)

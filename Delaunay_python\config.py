#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay插值系统配置文件

作者: 空间插值研究团队
日期: 2024年12月
版本: 2.0
"""

import os
from pathlib import Path
from datetime import datetime

class DelaunayConfig:
    """Delaunay插值系统配置类"""
    
    def __init__(self):
        # 基础路径配置
        self.BASE_DIR = Path(__file__).parent.parent
        self.INPUT_DIR = self.BASE_DIR / "input_another"
        self.OUTPUT_DIR = self.BASE_DIR / "output" / "Delaunay_interpolation"
        self.DELAUNAY_ANALYSIS_DIR = self.BASE_DIR / "output" / "Delaunay"
        
        # 确保输出目录存在
        self.OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
        
        # 日志配置
        self.LOG_LEVEL = "INFO"
        self.LOG_FILE = self.OUTPUT_DIR / "logs" / f"delaunay_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        self.LOG_FILE.parent.mkdir(parents=True, exist_ok=True)
        
        # 插值参数
        self.MIN_STATIONS = 3  # 最少包围站点数
        self.MAX_DISTANCE = 100000  # 最大距离（米）
        self.WEIGHT_POWER = 2  # 权重幂次
        
        # NSE监控配置（新增）
        self.NSE_THRESHOLD = -10.0  # NSE阈值
        self.ENABLE_NSE_MONITORING = True  # 启用NSE监控
        self.AUTO_WEIGHT_ADJUSTMENT = True  # 自动权重调整
        
        # 内存管理配置
        self.MEMORY_EFFICIENT_MODE = True
        self.CHUNK_SIZE = 100  # 时间序列分块大小
        self.BATCH_SIZE = 10  # 批处理大小
        self.MAX_EVENTS_IN_MEMORY = 1  # 内存中最大事件数
        
        # 输出配置
        self.SAVE_DETAILED_RESULTS = True
        self.GENERATE_PLOTS = False  # 暂时关闭可视化以节省时间
        self.SAVE_INTERPOLATION_CACHE = False
        
        # 数据验证配置
        self.VALIDATE_INPUT_DATA = True
        self.SKIP_INVALID_STATIONS = True
        self.MIN_VALID_HOURS = 10  # 最少有效小时数
        
        # 评估指标配置
        self.EVALUATION_METRICS = ['NSE', 'RMSE', 'MAE', 'Correlation', 'R_squared', 'Bias', 'Relative_Bias']
        self.NSE_GOOD_THRESHOLD = 0.7  # NSE良好阈值
        
        # 并行处理配置
        self.ENABLE_PARALLEL = False  # 暂时关闭并行处理
        self.MAX_WORKERS = 4
        
    def get_flood_events(self):
        """获取所有洪水事件列表"""
        if not self.INPUT_DIR.exists():
            return []
        
        events = []
        for item in self.INPUT_DIR.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                # 检查是否包含CSV文件
                csv_files = list(item.glob("*.csv"))
                if csv_files:
                    events.append(item.name)
        
        return sorted(events)
    
    def get_event_output_dir(self, event_name):
        """获取事件输出目录"""
        event_dir = self.OUTPUT_DIR / event_name
        event_dir.mkdir(parents=True, exist_ok=True)
        return event_dir
    
    def validate_config(self):
        """验证配置"""
        errors = []
        
        # 检查输入目录
        if not self.INPUT_DIR.exists():
            errors.append(f"输入目录不存在: {self.INPUT_DIR}")
        
        # 检查Delaunay分析结果
        if not self.DELAUNAY_ANALYSIS_DIR.exists():
            errors.append(f"Delaunay分析目录不存在: {self.DELAUNAY_ANALYSIS_DIR}")
        
        # 检查参数合理性
        if self.MIN_STATIONS < 1:
            errors.append("MIN_STATIONS必须大于0")
        
        if self.NSE_THRESHOLD > 0:
            errors.append("NSE_THRESHOLD应该为负值或0")
        
        if self.CHUNK_SIZE < 1:
            errors.append("CHUNK_SIZE必须大于0")
        
        return errors
    
    def __str__(self):
        """配置信息字符串表示"""
        return f"""
Delaunay插值系统配置:
==================
输入目录: {self.INPUT_DIR}
输出目录: {self.OUTPUT_DIR}
Delaunay分析目录: {self.DELAUNAY_ANALYSIS_DIR}
NSE监控阈值: {self.NSE_THRESHOLD}
启用NSE监控: {self.ENABLE_NSE_MONITORING}
自动权重调整: {self.AUTO_WEIGHT_ADJUSTMENT}
内存优化模式: {self.MEMORY_EFFICIENT_MODE}
最少包围站点数: {self.MIN_STATIONS}
分块大小: {self.CHUNK_SIZE}
批处理大小: {self.BATCH_SIZE}
==================
"""

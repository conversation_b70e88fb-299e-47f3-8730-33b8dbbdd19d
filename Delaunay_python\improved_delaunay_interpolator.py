#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的Delaunay插值器核心模块

新增功能：
1. 监控NSE值，当NSE < -10时记录并使用平等权重
2. 详细记录权重调整情况
3. 生成权重调整报告

作者: 空间插值研究团队
日期: 2024年12月
版本: 2.0
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import warnings
import gc
from pathlib import Path

warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class ImprovedDelaunayInterpolator:
    """改进的Delaunay插值器"""
    
    def __init__(self, config, data_loader, evaluation_metrics):
        self.config = config
        self.data_loader = data_loader
        self.evaluation_metrics = evaluation_metrics
        self.interpolation_cache = {}
        
        # 新增：权重调整记录
        self.weight_adjustment_log = []
        self.nse_threshold = -10.0  # NSE阈值
        self.equal_weight_stations = set()  # 使用平等权重的站点集合
        
        # 创建输出目录
        self.output_dir = Path(config.OUTPUT_DIR) / 'weight_adjustments'
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def calculate_real_time_nse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """实时计算NSE值"""
        try:
            # 移除NaN值
            mask = ~(np.isnan(observed) | np.isnan(predicted))
            if not np.any(mask) or np.sum(mask) < 2:
                return np.nan
            
            obs_clean = observed[mask]
            pred_clean = predicted[mask]
            
            # 计算观测值的方差
            obs_var = np.var(obs_clean)
            if obs_var == 0:
                return 1.0 if np.allclose(obs_clean, pred_clean) else 0.0
            
            # 计算NSE
            numerator = np.sum((obs_clean - pred_clean) ** 2)
            denominator = np.sum((obs_clean - np.mean(obs_clean)) ** 2)
            
            if denominator == 0:
                return np.nan
            
            nse = 1 - (numerator / denominator)
            return nse
            
        except Exception as e:
            logger.error(f"实时计算NSE失败: {e}")
            return np.nan
    
    def should_use_equal_weights(self, target_station_id: str, current_nse: float) -> bool:
        """判断是否应该使用平等权重"""
        if pd.isna(current_nse):
            return False
        
        if current_nse < self.nse_threshold:
            # 记录权重调整
            adjustment_record = {
                'timestamp': datetime.now(),
                'station_id': target_station_id,
                'nse_value': current_nse,
                'threshold': self.nse_threshold,
                'action': 'switch_to_equal_weights',
                'reason': f'NSE ({current_nse:.4f}) < threshold ({self.nse_threshold})'
            }
            self.weight_adjustment_log.append(adjustment_record)
            
            # 添加到平等权重站点集合
            self.equal_weight_stations.add(target_station_id)
            
            logger.warning(f"站点{target_station_id}的NSE值{current_nse:.4f}低于阈值{self.nse_threshold}，切换为平等权重")
            return True
        
        return False
    
    def interpolate_station_at_time(self, target_station_id: str, timestamp: pd.Timestamp, 
                                  station_data: Dict[str, pd.DataFrame], 
                                  use_equal_weights: bool = False) -> Dict:
        """
        对指定站点在指定时间进行插值（改进版）
        
        Args:
            target_station_id: 目标站点ID
            timestamp: 时间戳
            station_data: 所有站点数据
            use_equal_weights: 是否强制使用平等权重
            
        Returns:
            插值结果字典
        """
        try:
            # 获取目标站点信息
            station_info = self.data_loader.get_station_info(target_station_id)
            if not station_info:
                logger.warning(f"未找到站点{target_station_id}的Delaunay分析信息")
                return {'success': False, 'error': 'Station not found in Delaunay analysis'}
            
            # 获取包围站点信息
            surrounding_stations = station_info['surrounding_stations']
            
            # 收集包围站点在指定时间的降雨数据
            surrounding_values = []
            surrounding_weights = []
            valid_stations = []
            
            for station_info_item in surrounding_stations:
                station_id = station_info_item['id']
                weight = station_info_item['weight']
                
                # 检查站点数据是否存在
                if station_id not in station_data:
                    logger.debug(f"包围站点{station_id}数据不存在")
                    continue
                
                station_df = station_data[station_id]
                
                # 检查时间戳是否存在
                if timestamp not in station_df.index:
                    logger.debug(f"包围站点{station_id}在时间{timestamp}无数据")
                    continue
                
                # 获取降雨值
                rainfall_value = station_df.loc[timestamp, '雨量']
                
                # 检查是否为有效值
                if pd.isna(rainfall_value):
                    logger.debug(f"包围站点{station_id}在时间{timestamp}降雨值为NaN")
                    continue
                
                surrounding_values.append(rainfall_value)
                surrounding_weights.append(weight)
                valid_stations.append(station_id)
            
            # 检查是否有足够的有效站点
            if len(valid_stations) < self.config.MIN_STATIONS:
                logger.debug(f"站点{target_station_id}在时间{timestamp}的有效包围站点不足")
                return {
                    'success': False, 
                    'error': f'Insufficient valid surrounding stations: {len(valid_stations)}'
                }
            
            # 进行加权插值
            surrounding_values = np.array(surrounding_values)
            surrounding_weights = np.array(surrounding_weights)
            
            # 判断是否使用平等权重
            weight_method = 'original'
            if use_equal_weights or target_station_id in self.equal_weight_stations:
                # 使用平等权重
                normalized_weights = np.ones(len(surrounding_weights)) / len(surrounding_weights)
                weight_method = 'equal'
                logger.debug(f"站点{target_station_id}使用平等权重")
            else:
                # 使用原始权重
                weight_sum = np.sum(surrounding_weights)
                if weight_sum > 0:
                    normalized_weights = surrounding_weights / weight_sum
                else:
                    # 如果权重和为0，使用等权重
                    normalized_weights = np.ones(len(surrounding_weights)) / len(surrounding_weights)
                    weight_method = 'equal_fallback'
            
            # 计算插值结果
            interpolated_value = np.sum(surrounding_values * normalized_weights)
            
            # 获取实际观测值（如果存在）
            actual_value = np.nan
            if target_station_id in station_data:
                target_df = station_data[target_station_id]
                if timestamp in target_df.index:
                    actual_value = target_df.loc[timestamp, '雨量']
            
            # 返回结果
            result = {
                'success': True,
                'target_station_id': target_station_id,
                'timestamp': timestamp,
                'interpolated_value': interpolated_value,
                'actual_value': actual_value,
                'surrounding_stations': valid_stations,
                'surrounding_values': surrounding_values.tolist(),
                'weights': normalized_weights.tolist(),
                'original_weights': surrounding_weights.tolist(),
                'weight_method': weight_method,
                'error': interpolated_value - actual_value if not pd.isna(actual_value) else np.nan
            }
            
            return result
            
        except Exception as e:
            logger.error(f"站点{target_station_id}在时间{timestamp}插值失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def interpolate_station_timeseries_with_monitoring(self, target_station_id: str,
                                                     station_data: Dict[str, pd.DataFrame],
                                                     memory_efficient: bool = True) -> pd.DataFrame:
        """
        对指定站点的整个时间序列进行插值（带NSE监控）
        
        Args:
            target_station_id: 目标站点ID
            station_data: 所有站点数据
            memory_efficient: 是否使用内存优化模式
            
        Returns:
            插值结果DataFrame
        """
        try:
            logger.info(f"开始对站点{target_station_id}进行时间序列插值（带NSE监控）")
            
            # 获取公共时间范围
            start_time, end_time = self.data_loader.get_common_time_range(station_data)
            if start_time is None or end_time is None:
                logger.warning(f"无法确定公共时间范围")
                return pd.DataFrame()
            
            # 生成时间序列
            time_range = pd.date_range(start=start_time, end=end_time, freq='H')
            
            # 第一阶段：使用原始权重进行插值
            logger.info(f"第一阶段：使用原始权重进行插值")
            results = []
            observed_values = []
            predicted_values = []
            
            chunk_size = getattr(self.config, 'CHUNK_SIZE', 100) if memory_efficient else len(time_range)
            
            for i in range(0, len(time_range), chunk_size):
                chunk_times = time_range[i:i + chunk_size]
                
                for timestamp in chunk_times:
                    # 进行插值（使用原始权重）
                    result = self.interpolate_station_at_time(target_station_id, timestamp, station_data, use_equal_weights=False)
                    
                    if result['success']:
                        results.append({
                            'timestamp': timestamp,
                            'observed': result['actual_value'],
                            'predicted': result['interpolated_value'],
                            'error': result['error'],
                            'surrounding_stations_count': len(result['surrounding_stations']),
                            'weight_method': result['weight_method']
                        })
                        
                        # 收集有效的观测值和预测值用于NSE计算
                        if not pd.isna(result['actual_value']) and not pd.isna(result['interpolated_value']):
                            observed_values.append(result['actual_value'])
                            predicted_values.append(result['interpolated_value'])
                    else:
                        results.append({
                            'timestamp': timestamp,
                            'observed': np.nan,
                            'predicted': np.nan,
                            'error': np.nan,
                            'surrounding_stations_count': 0,
                            'weight_method': 'failed'
                        })
                
                # 定期清理内存
                if memory_efficient and i % (chunk_size * 5) == 0:
                    gc.collect()
            
            # 计算第一阶段的NSE
            if len(observed_values) >= 10:  # 至少需要10个有效值
                phase1_nse = self.calculate_real_time_nse(np.array(observed_values), np.array(predicted_values))
                logger.info(f"站点{target_station_id}第一阶段NSE: {phase1_nse:.4f}")
                
                # 检查是否需要切换到平等权重
                if self.should_use_equal_weights(target_station_id, phase1_nse):
                    logger.info(f"站点{target_station_id}切换到平等权重，重新进行插值")
                    
                    # 第二阶段：使用平等权重重新插值
                    results = []
                    observed_values = []
                    predicted_values = []
                    
                    for i in range(0, len(time_range), chunk_size):
                        chunk_times = time_range[i:i + chunk_size]
                        
                        for timestamp in chunk_times:
                            # 进行插值（使用平等权重）
                            result = self.interpolate_station_at_time(target_station_id, timestamp, station_data, use_equal_weights=True)
                            
                            if result['success']:
                                results.append({
                                    'timestamp': timestamp,
                                    'observed': result['actual_value'],
                                    'predicted': result['interpolated_value'],
                                    'error': result['error'],
                                    'surrounding_stations_count': len(result['surrounding_stations']),
                                    'weight_method': result['weight_method']
                                })
                                
                                # 收集有效的观测值和预测值用于NSE计算
                                if not pd.isna(result['actual_value']) and not pd.isna(result['interpolated_value']):
                                    observed_values.append(result['actual_value'])
                                    predicted_values.append(result['interpolated_value'])
                            else:
                                results.append({
                                    'timestamp': timestamp,
                                    'observed': np.nan,
                                    'predicted': np.nan,
                                    'error': np.nan,
                                    'surrounding_stations_count': 0,
                                    'weight_method': 'failed'
                                })
                        
                        # 定期清理内存
                        if memory_efficient and i % (chunk_size * 5) == 0:
                            gc.collect()
                    
                    # 计算第二阶段的NSE
                    if len(observed_values) >= 10:
                        phase2_nse = self.calculate_real_time_nse(np.array(observed_values), np.array(predicted_values))
                        logger.info(f"站点{target_station_id}第二阶段NSE: {phase2_nse:.4f}")
                        
                        # 记录改进情况
                        improvement_record = {
                            'timestamp': datetime.now(),
                            'station_id': target_station_id,
                            'phase1_nse': phase1_nse,
                            'phase2_nse': phase2_nse,
                            'improvement': phase2_nse - phase1_nse,
                            'action': 'weight_adjustment_completed'
                        }
                        self.weight_adjustment_log.append(improvement_record)
            
            # 转换为DataFrame
            if memory_efficient:
                results_df = pd.DataFrame(results, dtype={'observed': 'float32', 'predicted': 'float32', 'error': 'float32'})
            else:
                results_df = pd.DataFrame(results)
            
            results_df = results_df.set_index('timestamp')
            
            logger.info(f"站点{target_station_id}插值完成: {len(results_df)}个时刻")
            
            return results_df
            
        except Exception as e:
            logger.error(f"站点{target_station_id}时间序列插值失败: {e}")
            return pd.DataFrame()

    def interpolate_flood_event_with_monitoring(self, event_name: str, memory_efficient: bool = True) -> Dict[str, pd.DataFrame]:
        """
        对整个洪水事件进行插值（带NSE监控和权重调整）

        Args:
            event_name: 洪水事件名称
            memory_efficient: 是否使用内存优化模式

        Returns:
            所有站点的插值结果字典
        """
        try:
            logger.info(f"开始处理洪水事件（带NSE监控）: {event_name}")

            # 清空本次事件的权重调整记录
            event_start_log_count = len(self.weight_adjustment_log)

            # 加载洪水事件数据
            try:
                station_data = self.data_loader.load_flood_event_data(event_name, memory_efficient=memory_efficient)
                if not station_data:
                    logger.warning(f"洪水事件{event_name}无可用数据")
                    return {}
                logger.info(f"成功加载{len(station_data)}个站点的数据")
            except Exception as e:
                logger.error(f"加载洪水事件{event_name}数据失败: {e}")
                return {}

            # 验证和清理数据
            try:
                station_data = self.data_loader.validate_station_data(station_data)
                logger.info(f"数据验证完成，有效站点数: {len(station_data)}")
            except Exception as e:
                logger.error(f"验证洪水事件{event_name}数据失败: {e}")
                return {}

            # 获取可用的验证站点
            try:
                available_stations = self.data_loader.get_available_stations_for_event(event_name)
                if not available_stations:
                    logger.warning(f"洪水事件{event_name}无可用验证站点")
                    return {}
                logger.info(f"洪水事件{event_name}有{len(available_stations)}个可用验证站点")
            except Exception as e:
                logger.error(f"获取洪水事件{event_name}可用站点失败: {e}")
                return {}

            # 对每个验证站点进行插值（带NSE监控）
            event_results = {}
            weight_adjusted_stations = []

            for i, station_id in enumerate(available_stations):
                try:
                    logger.info(f"处理站点: {station_id} ({i+1}/{len(available_stations)})")

                    # 进行时间序列插值（带NSE监控）
                    station_results = self.interpolate_station_timeseries_with_monitoring(
                        station_id, station_data, memory_efficient=memory_efficient)

                    if not station_results.empty:
                        event_results[station_id] = station_results

                        # 计算站点评估指标
                        metrics = self.evaluation_metrics.evaluate_station_performance(station_results)
                        logger.info(f"站点{station_id}评估指标: NSE={metrics.get('NSE', np.nan):.4f}, "
                                  f"MAE={metrics.get('MAE', np.nan):.4f}, "
                                  f"RMSE={metrics.get('RMSE', np.nan):.4f}")

                        # 检查是否进行了权重调整
                        if station_id in self.equal_weight_stations:
                            weight_adjusted_stations.append(station_id)
                    else:
                        logger.warning(f"站点{station_id}插值结果为空")

                    # 定期清理内存
                    if memory_efficient and (i + 1) % 5 == 0:
                        gc.collect()

                except Exception as e:
                    logger.error(f"处理站点{station_id}失败: {e}")
                    continue

            # 生成本次事件的权重调整报告
            event_adjustments = self.weight_adjustment_log[event_start_log_count:]
            if event_adjustments:
                self.save_event_weight_adjustment_report(event_name, event_adjustments, weight_adjusted_stations)

            # 清理站点数据以释放内存
            del station_data
            gc.collect()

            logger.info(f"洪水事件{event_name}处理完成，成功处理{len(event_results)}个站点")
            if weight_adjusted_stations:
                logger.info(f"权重调整站点: {', '.join(weight_adjusted_stations)}")

            return event_results

        except Exception as e:
            logger.error(f"处理洪水事件{event_name}失败: {e}")
            return {}

    def save_event_weight_adjustment_report(self, event_name: str, adjustments: List[Dict], adjusted_stations: List[str]):
        """保存事件权重调整报告"""
        try:
            report_path = self.output_dir / f'{event_name}_weight_adjustments.txt'

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("="*80 + "\n")
                f.write(f"洪水事件权重调整报告 - {event_name}\n")
                f.write("Weight Adjustment Report for Flood Event\n")
                f.write("="*80 + "\n\n")

                f.write(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"事件名称: {event_name}\n")
                f.write(f"NSE阈值: {self.nse_threshold}\n")
                f.write(f"权重调整站点数: {len(adjusted_stations)}\n\n")

                if adjusted_stations:
                    f.write("权重调整站点列表:\n")
                    f.write("-" * 40 + "\n")
                    for i, station_id in enumerate(adjusted_stations, 1):
                        f.write(f"{i}. {station_id}\n")
                    f.write("\n")

                if adjustments:
                    f.write("详细调整记录:\n")
                    f.write("-" * 40 + "\n")
                    for i, adj in enumerate(adjustments, 1):
                        f.write(f"{i}. 站点: {adj['station_id']}\n")
                        f.write(f"   时间: {adj['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"   NSE值: {adj.get('nse_value', 'N/A'):.4f}\n")
                        f.write(f"   阈值: {adj.get('threshold', 'N/A')}\n")
                        f.write(f"   操作: {adj.get('action', 'N/A')}\n")
                        f.write(f"   原因: {adj.get('reason', 'N/A')}\n")

                        if 'phase1_nse' in adj and 'phase2_nse' in adj:
                            f.write(f"   第一阶段NSE: {adj['phase1_nse']:.4f}\n")
                            f.write(f"   第二阶段NSE: {adj['phase2_nse']:.4f}\n")
                            f.write(f"   改进幅度: {adj['improvement']:.4f}\n")
                        f.write("\n")
                else:
                    f.write("本事件无权重调整记录。\n")

            logger.info(f"权重调整报告已保存: {report_path}")

        except Exception as e:
            logger.error(f"保存权重调整报告失败: {e}")

    def save_comprehensive_weight_adjustment_summary(self):
        """保存综合权重调整汇总报告"""
        try:
            summary_path = self.output_dir / 'comprehensive_weight_adjustment_summary.txt'

            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write("="*80 + "\n")
                f.write("Delaunay插值系统权重调整综合汇总报告\n")
                f.write("Comprehensive Weight Adjustment Summary Report\n")
                f.write("="*80 + "\n\n")

                f.write(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"NSE阈值: {self.nse_threshold}\n")
                f.write(f"总调整记录数: {len(self.weight_adjustment_log)}\n")
                f.write(f"使用平等权重的站点数: {len(self.equal_weight_stations)}\n\n")

                if self.equal_weight_stations:
                    f.write("使用平等权重的站点列表:\n")
                    f.write("-" * 40 + "\n")
                    for i, station_id in enumerate(sorted(self.equal_weight_stations), 1):
                        f.write(f"{i}. {station_id}\n")
                    f.write("\n")

                # 统计分析
                if self.weight_adjustment_log:
                    f.write("权重调整统计分析:\n")
                    f.write("-" * 40 + "\n")

                    # 按站点统计
                    station_counts = {}
                    improvements = []

                    for log_entry in self.weight_adjustment_log:
                        station_id = log_entry['station_id']
                        station_counts[station_id] = station_counts.get(station_id, 0) + 1

                        if 'improvement' in log_entry:
                            improvements.append(log_entry['improvement'])

                    f.write(f"涉及站点数: {len(station_counts)}\n")
                    f.write(f"平均每站点调整次数: {np.mean(list(station_counts.values())):.2f}\n")

                    if improvements:
                        f.write(f"NSE改进情况:\n")
                        f.write(f"  平均改进: {np.mean(improvements):.4f}\n")
                        f.write(f"  最大改进: {np.max(improvements):.4f}\n")
                        f.write(f"  最小改进: {np.min(improvements):.4f}\n")
                        f.write(f"  改进标准差: {np.std(improvements):.4f}\n")

                    f.write("\n各站点调整次数:\n")
                    for station_id, count in sorted(station_counts.items()):
                        f.write(f"  {station_id}: {count}次\n")

            logger.info(f"综合权重调整汇总报告已保存: {summary_path}")

        except Exception as e:
            logger.error(f"保存综合权重调整汇总报告失败: {e}")

    def get_weight_adjustment_statistics(self) -> Dict:
        """获取权重调整统计信息"""
        try:
            stats = {
                'total_adjustments': len(self.weight_adjustment_log),
                'adjusted_stations_count': len(self.equal_weight_stations),
                'adjusted_stations': list(self.equal_weight_stations),
                'nse_threshold': self.nse_threshold,
                'adjustment_details': []
            }

            # 统计改进情况
            improvements = []
            for log_entry in self.weight_adjustment_log:
                if 'improvement' in log_entry:
                    improvements.append(log_entry['improvement'])
                    stats['adjustment_details'].append({
                        'station_id': log_entry['station_id'],
                        'phase1_nse': log_entry.get('phase1_nse', np.nan),
                        'phase2_nse': log_entry.get('phase2_nse', np.nan),
                        'improvement': log_entry['improvement']
                    })

            if improvements:
                stats['nse_improvements'] = {
                    'mean': np.mean(improvements),
                    'std': np.std(improvements),
                    'min': np.min(improvements),
                    'max': np.max(improvements),
                    'count': len(improvements)
                }
            else:
                stats['nse_improvements'] = None

            return stats

        except Exception as e:
            logger.error(f"获取权重调整统计信息失败: {e}")
            return {}


def main():
    """测试改进的插值器"""
    print("改进的Delaunay插值器已创建")
    print("新增功能:")
    print("1. 监控NSE值，当NSE < -10时自动切换为平等权重")
    print("2. 记录所有权重调整情况")
    print("3. 生成详细的权重调整报告")
    print("4. 提供权重调整统计分析")


if __name__ == "__main__":
    main()

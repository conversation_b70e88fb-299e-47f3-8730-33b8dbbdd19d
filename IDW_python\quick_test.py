#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IDW插值系统快速测试脚本
用于快速测试系统功能

作者: 空间插值系统
日期: 2024年
"""

import sys
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

def quick_test():
    """快速测试系统"""
    print("=" * 60)
    print("IDW插值系统快速测试")
    print("=" * 60)
    
    try:
        # 导入模块
        print("1. 导入系统模块...")
        from idw_config import config
        from data_loader import DataLoader
        from idw_interpolation import IDWInterpolator
        from evaluation_metrics import EvaluationMetrics
        from visualization import IDWVisualizer
        print("   ✓ 模块导入成功")
        
        # 验证配置
        print("\n2. 验证系统配置...")
        errors = config.validate_config()
        if errors:
            print("   ✗ 配置验证失败:")
            for error in errors:
                print(f"     - {error}")
            return False
        print("   ✓ 配置验证通过")
        
        # 测试数据加载
        print("\n3. 测试数据加载...")
        loader = DataLoader(config)
        available_stations = loader.get_available_stations()
        flood_events = config.get_flood_events()
        print(f"   ✓ 找到{len(available_stations)}个验证站点")
        print(f"   ✓ 找到{len(flood_events)}个洪水事件")
        
        # 测试包围站点信息
        print("\n4. 测试包围站点信息...")
        if available_stations:
            test_station = available_stations[0]
            surrounding_info = loader.get_surrounding_stations(test_station)
            if surrounding_info:
                print(f"   ✓ 成功获取站点{test_station}的包围站点信息")
                print(f"     包围站点数: {len(surrounding_info['surrounding_stations'])}")
            else:
                print(f"   ✗ 无法获取站点{test_station}的包围站点信息")
                return False
        
        # 测试洪水事件数据加载
        print("\n5. 测试洪水事件数据加载...")
        if flood_events:
            test_event = flood_events[0]
            event_data = loader.load_flood_event_data(test_event)
            print(f"   ✓ 成功加载事件{test_event}的{len(event_data)}个站点数据")
        
        print("\n" + "=" * 60)
        print("✓ 系统测试通过！可以正常使用")
        print("=" * 60)
        
        # 显示使用建议
        print("\n使用建议:")
        print("1. 测试单个事件:")
        print("   python main.py --mode single --event 2009-1 --disable-raster")
        print("\n2. 批量处理前几个事件:")
        print("   python main.py --mode batch --events 2009-1 2009-2 --disable-raster")
        print("\n3. 批量处理所有事件:")
        print("   python main.py --mode batch --disable-raster")
        print("\n4. 创建综合汇总报告:")
        print("   python create_comprehensive_summary.py")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 系统测试失败: {e}")
        return False

if __name__ == "__main__":
    # 设置简单日志
    logging.basicConfig(level=logging.WARNING)
    
    success = quick_test()
    sys.exit(0 if success else 1)

{"_comment": "PRISM空间插值系统配置文件示例", "_comment_paths": "=== 路径配置 ===", "input_dir": "D:/pythondata/spatial_interpolation/input_another/2009-1", "terrain_dir": "D:/pythondata/spatial_interpolation/terrain/90", "output_dir": "D:/pythondata/spatial_interpolation/output/PRISM/2009-1", "stations_file": "D:/pythondata/spatial_interpolation/stations.csv", "_comment_interpolation": "=== 插值参数 ===", "neighbor_count": 3, "min_triangle_angle": 20.0, "distance_power": 2.0, "elevation_weight": 0.4, "slope_weight": 0.3, "aspect_weight": 0.2, "moran_weight": 0.1, "rainfall_threshold": 0.5, "_comment_computation": "=== 计算参数 ===", "num_cores": 12, "batch_size": 20, "memory_efficient": true, "_comment_output": "=== 输出控制 ===", "output_raster": true, "output_delaunay_plot": true, "output_weight_info": true, "output_evaluation": true, "_comment_batch": "=== 批量处理 ===", "enable_batch_processing": false, "batch_input_root": "D:/pythondata/spatial_interpolation/input_another", "batch_output_root": "D:/pythondata/spatial_interpolation/output/PRISM", "batch_folders": null, "_comment_debug": "=== 调试选项 ===", "debug_mode": false, "verbose_logging": true, "save_intermediate": false, "_comment_help": "=== 参数说明 ===", "_help_neighbor_count": "邻近站点数量，通常3-5个", "_help_distance_power": "距离权重指数，通常1-3", "_help_weights": "地形权重系数总和应为1.0", "_help_rainfall_threshold": "区分微量降雨的阈值(mm)", "_help_num_cores": "并行核心数，0表示自动检测", "_help_batch_folders": "指定批量处理文件夹，null表示处理所有"}
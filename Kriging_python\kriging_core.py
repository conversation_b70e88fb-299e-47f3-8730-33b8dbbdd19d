"""
Kriging插值系统核心算法模块
实现Ordinary Kriging、Simple Kriging和Universal Kriging
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Callable
from scipy.optimize import minimize
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class KrigingCore:
    """Kriging插值核心算法"""
    
    def __init__(self, config):
        """初始化Kriging核心算法"""
        self.config = config
        self.variogram_params = {}
        self.variogram_model = None
        
    def _spherical_model(self, h: np.ndarray, nugget: float, sill: float, range_param: float) -> np.ndarray:
        """球状半变异函数模型"""
        gamma = np.zeros_like(h)
        mask = h > 0
        
        # h <= range的情况
        mask1 = mask & (h <= range_param)
        gamma[mask1] = nugget + (sill - nugget) * (1.5 * h[mask1] / range_param - 0.5 * (h[mask1] / range_param)**3)
        
        # h > range的情况
        mask2 = mask & (h > range_param)
        gamma[mask2] = sill
        
        return gamma
    
    def _exponential_model(self, h: np.ndarray, nugget: float, sill: float, range_param: float) -> np.ndarray:
        """指数半变异函数模型"""
        gamma = np.zeros_like(h)
        mask = h > 0
        gamma[mask] = nugget + (sill - nugget) * (1 - np.exp(-3 * h[mask] / range_param))
        return gamma
    
    def _gaussian_model(self, h: np.ndarray, nugget: float, sill: float, range_param: float) -> np.ndarray:
        """高斯半变异函数模型"""
        gamma = np.zeros_like(h)
        mask = h > 0
        gamma[mask] = nugget + (sill - nugget) * (1 - np.exp(-3 * (h[mask] / range_param)**2))
        return gamma
    
    def _linear_model(self, h: np.ndarray, nugget: float, sill: float, range_param: float) -> np.ndarray:
        """线性半变异函数模型"""
        gamma = np.zeros_like(h)
        mask = h > 0
        slope = (sill - nugget) / range_param
        gamma[mask] = nugget + slope * h[mask]
        return gamma
    
    def get_variogram_function(self) -> Callable:
        """获取半变异函数"""
        model_functions = {
            'spherical': self._spherical_model,
            'exponential': self._exponential_model,
            'gaussian': self._gaussian_model,
            'linear': self._linear_model
        }
        
        if self.config.variogram_model not in model_functions:
            logger.warning(f"未知的半变异函数模型 {self.config.variogram_model}，使用球状模型")
            return model_functions['spherical']
        
        return model_functions[self.config.variogram_model]
    
    def calculate_experimental_variogram(self, coordinates: np.ndarray, 
                                       values: np.ndarray, 
                                       n_lags: int = 15) -> Tuple[np.ndarray, np.ndarray]:
        """计算实验半变异函数"""
        try:
            # 计算距离矩阵
            distances = cdist(coordinates, coordinates)
            
            # 计算半方差
            n = len(values)
            semivariances = []
            distance_pairs = []
            
            for i in range(n):
                for j in range(i+1, n):
                    dist = distances[i, j]
                    semivar = 0.5 * (values[i] - values[j])**2
                    
                    distance_pairs.append(dist)
                    semivariances.append(semivar)
            
            distance_pairs = np.array(distance_pairs)
            semivariances = np.array(semivariances)
            
            # 创建距离区间
            max_dist = np.max(distance_pairs)
            lag_intervals = np.linspace(0, max_dist, n_lags + 1)
            
            # 计算每个区间的平均半方差
            lag_distances = []
            lag_semivariances = []
            
            for i in range(n_lags):
                mask = (distance_pairs >= lag_intervals[i]) & (distance_pairs < lag_intervals[i+1])
                
                if np.any(mask):
                    avg_dist = np.mean(distance_pairs[mask])
                    avg_semivar = np.mean(semivariances[mask])
                    
                    lag_distances.append(avg_dist)
                    lag_semivariances.append(avg_semivar)
            
            return np.array(lag_distances), np.array(lag_semivariances)
            
        except Exception as e:
            logger.error(f"计算实验半变异函数失败: {e}")
            return np.array([]), np.array([])
    
    def fit_variogram_model(self, coordinates: np.ndarray, values: np.ndarray) -> Dict:
        """拟合半变异函数模型"""
        try:
            logger.debug("拟合半变异函数模型...")
            
            # 计算实验半变异函数
            lag_distances, lag_semivariances = self.calculate_experimental_variogram(coordinates, values)
            
            if len(lag_distances) == 0:
                logger.warning("无法计算实验半变异函数，使用默认参数")
                return self._get_default_variogram_params(values)
            
            # 获取半变异函数
            variogram_func = self.get_variogram_function()
            
            # 初始参数估计
            nugget_init = np.min(lag_semivariances) if len(lag_semivariances) > 0 else 0.0
            sill_init = np.max(lag_semivariances) if len(lag_semivariances) > 0 else np.var(values)
            range_init = np.max(lag_distances) / 3 if len(lag_distances) > 0 else 1.0
            
            # 确保参数合理
            nugget_init = max(0, nugget_init)
            sill_init = max(nugget_init + 0.01, sill_init)
            range_init = max(0.01, range_init)
            
            # 定义目标函数
            def objective(params):
                nugget, sill, range_param = params
                
                # 参数约束
                if nugget < 0 or sill <= nugget or range_param <= 0:
                    return 1e10
                
                try:
                    predicted = variogram_func(lag_distances, nugget, sill, range_param)
                    mse = np.mean((lag_semivariances - predicted)**2)
                    return mse
                except:
                    return 1e10
            
            # 优化参数
            initial_params = [nugget_init, sill_init, range_init]
            bounds = [(0, sill_init*2), (nugget_init, sill_init*3), (0.01, range_init*5)]
            
            try:
                result = minimize(objective, initial_params, bounds=bounds, method='L-BFGS-B')
                
                if result.success:
                    nugget, sill, range_param = result.x
                else:
                    logger.warning("半变异函数优化失败，使用初始参数")
                    nugget, sill, range_param = initial_params
                    
            except Exception as e:
                logger.warning(f"半变异函数优化出错: {e}，使用初始参数")
                nugget, sill, range_param = initial_params
            
            # 保存参数
            params = {
                'nugget': float(nugget),
                'sill': float(sill),
                'range': float(range_param),
                'model': self.config.variogram_model
            }
            
            self.variogram_params = params
            self.variogram_model = variogram_func
            
            logger.debug(f"半变异函数参数: {params}")
            
            return params
            
        except Exception as e:
            logger.error(f"拟合半变异函数模型失败: {e}")
            return self._get_default_variogram_params(values)
    
    def _get_default_variogram_params(self, values: np.ndarray) -> Dict:
        """获取默认半变异函数参数"""
        variance = np.var(values) if len(values) > 1 else 1.0
        
        params = {
            'nugget': variance * 0.1,
            'sill': variance,
            'range': 1.0,
            'model': self.config.variogram_model
        }
        
        self.variogram_params = params
        self.variogram_model = self.get_variogram_function()
        
        return params
    
    def ordinary_kriging(self, target_coords: np.ndarray,
                        neighbor_coords: np.ndarray,
                        neighbor_values: np.ndarray) -> Tuple[float, float]:
        """Ordinary Kriging插值"""
        try:
            n = len(neighbor_values)
            
            if n == 0:
                return 0.0, 0.0
            
            if n == 1:
                return float(neighbor_values[0]), 0.0
            
            # 确保有半变异函数参数
            if not self.variogram_params:
                self.fit_variogram_model(neighbor_coords, neighbor_values)
            
            # 计算距离矩阵
            neighbor_distances = cdist(neighbor_coords, neighbor_coords)
            target_distances = cdist([target_coords], neighbor_coords)[0]
            
            # 计算协方差矩阵
            C = self._calculate_covariance_matrix(neighbor_distances)
            c = self._calculate_covariance_vector(target_distances)
            
            # 构建Kriging系统
            # [C  1] [w]   [c]
            # [1' 0] [μ] = [1]
            
            A = np.zeros((n+1, n+1))
            A[:n, :n] = C
            A[:n, n] = 1
            A[n, :n] = 1
            A[n, n] = 0
            
            b = np.zeros(n+1)
            b[:n] = c
            b[n] = 1
            
            # 求解线性系统
            try:
                solution = np.linalg.solve(A, b)
                weights = solution[:n]
                lagrange_multiplier = solution[n]
                
                # 计算插值值
                interpolated_value = np.sum(weights * neighbor_values)
                
                # 计算Kriging方差
                kriging_variance = self.variogram_params['sill'] - np.sum(weights * c) - lagrange_multiplier
                kriging_variance = max(0, kriging_variance)  # 确保非负
                
                return float(interpolated_value), float(kriging_variance)
                
            except np.linalg.LinAlgError:
                logger.warning("Kriging系统求解失败，使用反距离权重")
                return self._fallback_interpolation(target_coords, neighbor_coords, neighbor_values)
            
        except Exception as e:
            logger.error(f"Ordinary Kriging插值失败: {e}")
            return self._fallback_interpolation(target_coords, neighbor_coords, neighbor_values)
    
    def _calculate_covariance_matrix(self, distances: np.ndarray) -> np.ndarray:
        """计算协方差矩阵"""
        if not self.variogram_params or not self.variogram_model:
            # 使用单位矩阵作为默认
            return np.eye(distances.shape[0])
        
        # 计算半变异函数值
        gamma = self.variogram_model(
            distances,
            self.variogram_params['nugget'],
            self.variogram_params['sill'],
            self.variogram_params['range']
        )
        
        # 协方差 = sill - 半方差
        covariance = self.variogram_params['sill'] - gamma
        
        return covariance
    
    def _calculate_covariance_vector(self, distances: np.ndarray) -> np.ndarray:
        """计算协方差向量"""
        if not self.variogram_params or not self.variogram_model:
            # 使用反距离作为默认
            return 1.0 / (distances + 1e-10)
        
        # 计算半变异函数值
        gamma = self.variogram_model(
            distances,
            self.variogram_params['nugget'],
            self.variogram_params['sill'],
            self.variogram_params['range']
        )
        
        # 协方差 = sill - 半方差
        covariance = self.variogram_params['sill'] - gamma
        
        return covariance
    
    def _fallback_interpolation(self, target_coords: np.ndarray,
                              neighbor_coords: np.ndarray,
                              neighbor_values: np.ndarray) -> Tuple[float, float]:
        """备选插值方法（反距离权重）"""
        try:
            distances = cdist([target_coords], neighbor_coords)[0]
            
            # 避免除零
            distances = np.maximum(distances, 1e-10)
            
            # 反距离权重
            weights = 1.0 / (distances ** 2)
            weights = weights / np.sum(weights)
            
            # 插值
            interpolated_value = np.sum(weights * neighbor_values)
            
            # 简单方差估计
            variance = np.var(neighbor_values) if len(neighbor_values) > 1 else 0.0
            
            return float(interpolated_value), float(variance)
            
        except Exception as e:
            logger.error(f"备选插值方法失败: {e}")
            return float(np.mean(neighbor_values)), 0.0
    
    def get_variogram_info(self) -> Dict:
        """获取半变异函数信息"""
        return {
            'params': self.variogram_params,
            'model_type': self.config.variogram_model,
            'kriging_type': self.config.kriging_type
        }
    
    def cleanup(self):
        """清理内存"""
        try:
            self.variogram_params.clear()
            self.variogram_model = None
            
            logger.debug("Kriging核心算法内存清理完成")
            
        except Exception as e:
            logger.warning(f"Kriging核心算法内存清理失败: {e}")

"""
数据加载和预处理模块
负责加载站点信息、降雨数据和地形数据
"""

import os
import pandas as pd
import numpy as np
import rasterio
import logging
from typing import Dict, Tuple, List
import gc
from tqdm import tqdm

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, config):
        """初始化数据处理器"""
        self.config = config
        self.stations_df = None
        self.rainfall_data = None
        self.terrain_data = None
    
    def load_stations(self) -> pd.DataFrame:
        """加载站点信息"""
        logger.info("正在加载站点信息...")
        
        try:
            # 尝试不同编码格式
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            
            for encoding in encodings:
                try:
                    self.stations_df = pd.read_csv(self.config.stations_file, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if self.stations_df is None:
                raise ValueError("无法读取站点文件，请检查文件编码")
            
            # 验证必要列是否存在
            required_columns = ['站点', '经度', '纬度']
            missing_columns = [col for col in required_columns if col not in self.stations_df.columns]
            
            if missing_columns:
                raise ValueError(f"站点文件缺少必要列: {missing_columns}")
            
            # 确保数据类型正确
            self.stations_df['经度'] = pd.to_numeric(self.stations_df['经度'], errors='coerce')
            self.stations_df['纬度'] = pd.to_numeric(self.stations_df['纬度'], errors='coerce')
            
            # 移除无效坐标的站点
            invalid_coords = self.stations_df['经度'].isna() | self.stations_df['纬度'].isna()
            if invalid_coords.any():
                logger.warning(f"发现 {invalid_coords.sum()} 个站点坐标无效，已移除")
                self.stations_df = self.stations_df[~invalid_coords].reset_index(drop=True)
            
            logger.info(f"成功加载 {len(self.stations_df)} 个站点信息")
            return self.stations_df
            
        except Exception as e:
            logger.error(f"加载站点信息失败: {e}")
            raise
    
    def load_terrain_data(self) -> Dict:
        """加载地形数据"""
        logger.info("正在加载地形数据...")
        
        try:
            terrain_files = self.config.get_terrain_files()
            terrain_data = {}
            
            for name, file_path in terrain_files.items():
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"地形文件不存在: {file_path}")
                
                with rasterio.open(file_path) as src:
                    data = src.read(1)
                    transform = src.transform
                    nodata = src.nodata
                    meta = src.meta
                
                terrain_data[name] = {
                    'data': data,
                    'transform': transform,
                    'nodata': nodata,
                    'meta': meta
                }
                
                logger.debug(f"加载地形数据 {name}: 形状 {data.shape}")
            
            self.terrain_data = terrain_data
            logger.info("地形数据加载完成")
            return terrain_data
            
        except Exception as e:
            logger.error(f"加载地形数据失败: {e}")
            raise
    
    def extract_terrain_features(self, stations_df: pd.DataFrame) -> pd.DataFrame:
        """为站点提取地形特征"""
        logger.info("正在为站点提取地形特征...")
        
        if self.terrain_data is None:
            self.load_terrain_data()
        
        try:
            # 创建站点数据副本
            stations_with_terrain = stations_df.copy()
            
            # 提取地形特征
            for feature_name in ['dem', 'slope', 'aspect']:
                feature_data = self.terrain_data[feature_name]
                data = feature_data['data']
                transform = feature_data['transform']
                nodata = feature_data['nodata']
                
                feature_values = []
                
                for _, station in stations_with_terrain.iterrows():
                    try:
                        lon, lat = station['经度'], station['纬度']
                        
                        # 将地理坐标转换为栅格坐标
                        row, col = rasterio.transform.rowcol(transform, lon, lat)
                        
                        # 检查是否在栅格范围内
                        if (0 <= row < data.shape[0] and 0 <= col < data.shape[1]):
                            value = data[row, col]
                            
                            # 检查是否为无数据值
                            if value == nodata or np.isnan(value):
                                # 寻找最近的有效值
                                value = self._find_nearest_valid_value(
                                    data, transform, lon, lat, nodata
                                )
                        else:
                            # 站点在栅格外，使用平均值
                            value = np.nanmean(data[data != nodata])
                        
                        feature_values.append(value)
                        
                    except Exception as e:
                        logger.warning(f"提取站点 {station['站点']} 的 {feature_name} 特征失败: {e}")
                        # 使用平均值作为默认值
                        feature_values.append(np.nanmean(data[data != nodata]))
                
                # 添加特征列
                feature_col_name = {'dem': '高程', 'slope': '坡度', 'aspect': '坡向'}[feature_name]
                stations_with_terrain[feature_col_name] = feature_values
            
            # 确保所有地形特征都是数值型
            terrain_columns = ['高程', '坡度', '坡向']
            for col in terrain_columns:
                stations_with_terrain[col] = pd.to_numeric(stations_with_terrain[col], errors='coerce')
                
                # 填充缺失值
                if stations_with_terrain[col].isna().any():
                    mean_val = stations_with_terrain[col].mean()
                    stations_with_terrain[col] = stations_with_terrain[col].fillna(mean_val)
                    logger.warning(f"{col} 列存在缺失值，已用平均值 {mean_val:.2f} 填充")
            
            logger.info("地形特征提取完成")
            return stations_with_terrain
            
        except Exception as e:
            logger.error(f"提取地形特征失败: {e}")
            raise
    
    def _find_nearest_valid_value(self, data, transform, lon, lat, nodata, search_radius=5):
        """寻找最近的有效栅格值"""
        try:
            # 将地理坐标转换为栅格坐标
            center_row, center_col = rasterio.transform.rowcol(transform, lon, lat)
            
            # 在指定半径内搜索有效值
            for radius in range(1, search_radius + 1):
                for dr in range(-radius, radius + 1):
                    for dc in range(-radius, radius + 1):
                        row = center_row + dr
                        col = center_col + dc
                        
                        if (0 <= row < data.shape[0] and 0 <= col < data.shape[1]):
                            value = data[row, col]
                            if value != nodata and not np.isnan(value):
                                return value
            
            # 如果找不到有效值，返回全局平均值
            return np.nanmean(data[data != nodata])
            
        except Exception:
            return np.nanmean(data[data != nodata])
    
    def load_rainfall_data(self, stations_list: List[str]) -> Dict:
        """加载降雨数据"""
        logger.info("正在加载降雨数据...")
        
        try:
            rainfall_data = {}
            success_count = 0
            
            # 获取所有CSV文件
            csv_files = [f for f in os.listdir(self.config.input_dir) if f.endswith('.csv')]
            
            for file_name in tqdm(csv_files, desc="加载降雨数据"):
                station_name = os.path.splitext(file_name)[0]
                
                # 检查站点是否在站点列表中
                if station_name not in stations_list:
                    continue
                
                file_path = os.path.join(self.config.input_dir, file_name)
                
                try:
                    # 尝试不同编码格式
                    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                    df = None
                    
                    for encoding in encodings:
                        try:
                            df = pd.read_csv(file_path, encoding=encoding)
                            break
                        except UnicodeDecodeError:
                            continue
                    
                    if df is None:
                        logger.warning(f"无法读取文件 {file_path}")
                        continue
                    
                    # 验证列名
                    if '时间' not in df.columns or '雨量' not in df.columns:
                        logger.warning(f"文件 {file_path} 缺少必要列")
                        continue
                    
                    # 转换时间格式
                    df['时间'] = pd.to_datetime(df['时间'])
                    
                    # 确保雨量是数值型
                    df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce').fillna(0)
                    
                    # 存储数据
                    rainfall_data[station_name] = df
                    success_count += 1
                    
                except Exception as e:
                    logger.warning(f"处理文件 {file_path} 时出错: {e}")
            
            logger.info(f"成功加载 {success_count} 个站点的降雨数据")
            
            # 强制垃圾回收
            if self.config.memory_efficient:
                gc.collect()
            
            self.rainfall_data = rainfall_data
            return rainfall_data
            
        except Exception as e:
            logger.error(f"加载降雨数据失败: {e}")
            raise
    
    def get_time_series(self) -> List:
        """获取所有时间序列"""
        if self.rainfall_data is None:
            raise ValueError("降雨数据未加载")
        
        all_times = set()
        for station_data in self.rainfall_data.values():
            all_times.update(station_data['时间'])
        
        return sorted(list(all_times))
    
    def get_rainfall_at_time(self, time_point) -> Dict[str, float]:
        """获取指定时间点的所有站点降雨量"""
        if self.rainfall_data is None:
            raise ValueError("降雨数据未加载")
        
        rainfall_at_time = {}
        
        for station_name, station_data in self.rainfall_data.items():
            # 查找对应时间点的降雨量
            time_mask = station_data['时间'] == time_point
            if time_mask.any():
                rainfall_at_time[station_name] = station_data.loc[time_mask, '雨量'].iloc[0]
            else:
                rainfall_at_time[station_name] = 0.0
        
        return rainfall_at_time
    
    def cleanup(self):
        """清理内存"""
        if self.config.memory_efficient:
            self.terrain_data = None
            gc.collect()
            logger.debug("已清理地形数据内存")

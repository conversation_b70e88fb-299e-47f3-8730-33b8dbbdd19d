# -*- coding: utf-8 -*-
"""
评价指标计算模块

计算插值结果的各种评价指标
"""

import numpy as np
import pandas as pd
from typing import Dict, Tuple
import logging
from datetime import datetime

def calculate_basic_metrics(actual: np.n<PERSON><PERSON>, predicted: np.n<PERSON><PERSON>) -> Dict[str, float]:
    """
    计算基础评价指标
    
    Args:
        actual: 实际观测值数组
        predicted: 预测值数组
    
    Returns:
        Dict[str, float]: 评价指标字典
    """
    try:
        # 移除缺失值
        valid_mask = ~np.isnan(actual) & ~np.isnan(predicted)
        actual_valid = actual[valid_mask]
        predicted_valid = predicted[valid_mask]
        
        if len(actual_valid) == 0:
            return {
                'mae': np.nan,
                'rmse': np.nan,
                'mse': np.nan,
                'r2': np.nan,
                'nse': np.nan,
                'cc': np.nan,
                'bias': np.nan,
                'pbias': np.nan,
                'count': 0
            }
        
        # 计算各种指标
        # 平均绝对误差 (MAE)
        mae = np.mean(np.abs(predicted_valid - actual_valid))
        
        # 均方根误差 (RMSE)
        mse = np.mean((predicted_valid - actual_valid)**2)
        rmse = np.sqrt(mse)
        
        # 决定系数 (R²)
        ss_res = np.sum((actual_valid - predicted_valid)**2)
        ss_tot = np.sum((actual_valid - np.mean(actual_valid))**2)
        r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else np.nan
        
        # 纳什效率系数 (NSE)
        nse = 1 - ss_res / ss_tot if ss_tot != 0 else np.nan
        
        # 相关系数 (CC)
        if np.std(actual_valid) > 0 and np.std(predicted_valid) > 0:
            cc = np.corrcoef(actual_valid, predicted_valid)[0, 1]
        else:
            cc = np.nan
        
        # 偏差 (Bias)
        bias = np.mean(predicted_valid - actual_valid)
        
        # 百分比偏差 (PBIAS)
        pbias = 100 * np.sum(predicted_valid - actual_valid) / np.sum(actual_valid) if np.sum(actual_valid) != 0 else np.nan
        
        return {
            'mae': mae,
            'rmse': rmse,
            'mse': mse,
            'r2': r2,
            'nse': nse,
            'cc': cc,
            'bias': bias,
            'pbias': pbias,
            'count': len(actual_valid)
        }
    
    except Exception as e:
        logging.error(f"计算基础评价指标时出错: {e}")
        return {
            'mae': np.nan,
            'rmse': np.nan,
            'mse': np.nan,
            'r2': np.nan,
            'nse': np.nan,
            'cc': np.nan,
            'bias': np.nan,
            'pbias': np.nan,
            'count': 0
        }

def calculate_advanced_metrics(actual: np.ndarray, predicted: np.ndarray) -> Dict[str, float]:
    """
    计算高级评价指标
    
    Args:
        actual: 实际观测值数组
        predicted: 预测值数组
    
    Returns:
        Dict[str, float]: 高级评价指标字典
    """
    try:
        # 移除缺失值
        valid_mask = ~np.isnan(actual) & ~np.isnan(predicted)
        actual_valid = actual[valid_mask]
        predicted_valid = predicted[valid_mask]
        
        if len(actual_valid) == 0:
            return {
                'kge': np.nan,
                'kling_gupta': np.nan,
                'index_of_agreement': np.nan,
                'volumetric_efficiency': np.nan
            }
        
        # Kling-Gupta效率 (KGE)
        mean_actual = np.mean(actual_valid)
        mean_predicted = np.mean(predicted_valid)
        std_actual = np.std(actual_valid)
        std_predicted = np.std(predicted_valid)
        
        if std_actual > 0 and std_predicted > 0:
            cc = np.corrcoef(actual_valid, predicted_valid)[0, 1]
            alpha = std_predicted / std_actual
            beta = mean_predicted / mean_actual if mean_actual != 0 else np.nan
            kge = 1 - np.sqrt((cc - 1)**2 + (alpha - 1)**2 + (beta - 1)**2)
        else:
            kge = np.nan
        
        # 一致性指数 (Index of Agreement)
        numerator = np.sum((actual_valid - predicted_valid)**2)
        denominator = np.sum((np.abs(predicted_valid - mean_actual) + np.abs(actual_valid - mean_actual))**2)
        index_of_agreement = 1 - numerator / denominator if denominator != 0 else np.nan
        
        # 体积效率 (Volumetric Efficiency)
        volumetric_efficiency = 1 - np.abs(np.sum(predicted_valid) - np.sum(actual_valid)) / np.sum(actual_valid) if np.sum(actual_valid) != 0 else np.nan
        
        return {
            'kge': kge,
            'kling_gupta': kge,  # 别名
            'index_of_agreement': index_of_agreement,
            'volumetric_efficiency': volumetric_efficiency
        }
    
    except Exception as e:
        logging.error(f"计算高级评价指标时出错: {e}")
        return {
            'kge': np.nan,
            'kling_gupta': np.nan,
            'index_of_agreement': np.nan,
            'volumetric_efficiency': np.nan
        }

def evaluate_interpolation_results(validation_df: pd.DataFrame) -> Dict[str, float]:
    """
    评估插值结果
    
    Args:
        validation_df: 验证结果DataFrame，包含actual_value和interpolated_value列
    
    Returns:
        Dict[str, float]: 完整的评价指标字典
    """
    try:
        if validation_df.empty:
            logging.warning("验证数据为空，无法计算评价指标")
            return {}
        
        actual = validation_df['actual_value'].values
        predicted = validation_df['interpolated_value'].values
        
        # 计算基础指标
        basic_metrics = calculate_basic_metrics(actual, predicted)
        
        # 计算高级指标
        advanced_metrics = calculate_advanced_metrics(actual, predicted)
        
        # 合并所有指标
        all_metrics = {**basic_metrics, **advanced_metrics}
        
        # 添加额外的统计信息
        all_metrics['total_samples'] = len(validation_df)
        all_metrics['valid_samples'] = basic_metrics['count']
        all_metrics['missing_ratio'] = 1 - basic_metrics['count'] / len(validation_df)
        
        # 计算零值相关统计
        zero_actual = np.sum(actual == 0)
        zero_predicted = np.sum(predicted == 0)
        all_metrics['zero_actual_count'] = zero_actual
        all_metrics['zero_predicted_count'] = zero_predicted
        all_metrics['zero_actual_ratio'] = zero_actual / len(actual)
        all_metrics['zero_predicted_ratio'] = zero_predicted / len(predicted)
        
        return all_metrics
    
    except Exception as e:
        logging.error(f"评估插值结果时出错: {e}")
        return {}

def create_evaluation_summary(all_validation_results: pd.DataFrame) -> Dict[str, float]:
    """
    创建总体评估摘要
    
    Args:
        all_validation_results: 所有时间步的验证结果DataFrame
    
    Returns:
        Dict[str, float]: 总体评价指标
    """
    try:
        if all_validation_results.empty:
            return {}
        
        # 计算总体指标
        overall_metrics = evaluate_interpolation_results(all_validation_results)
        
        # 按时间步计算指标
        time_step_metrics = []
        if 'time_index' in all_validation_results.columns:
            for time_index in all_validation_results['time_index'].unique():
                time_data = all_validation_results[all_validation_results['time_index'] == time_index]
                time_metrics = evaluate_interpolation_results(time_data)
                time_metrics['time_index'] = time_index
                time_step_metrics.append(time_metrics)
        
        # 计算时间步指标的统计量
        if time_step_metrics:
            time_metrics_df = pd.DataFrame(time_step_metrics)
            
            # 添加时间步指标的统计量到总体指标中
            for metric in ['mae', 'rmse', 'r2', 'nse', 'cc']:
                if metric in time_metrics_df.columns:
                    overall_metrics[f'{metric}_mean_by_time'] = time_metrics_df[metric].mean()
                    overall_metrics[f'{metric}_std_by_time'] = time_metrics_df[metric].std()
                    overall_metrics[f'{metric}_min_by_time'] = time_metrics_df[metric].min()
                    overall_metrics[f'{metric}_max_by_time'] = time_metrics_df[metric].max()
        
        return overall_metrics
    
    except Exception as e:
        logging.error(f"创建评估摘要时出错: {e}")
        return {}

def save_evaluation_results(metrics: Dict[str, float], output_dir: str, 
                          event_name: str = None) -> str:
    """
    保存评价结果到文件
    
    Args:
        metrics: 评价指标字典
        output_dir: 输出目录
        event_name: 事件名称（用于文件命名）
    
    Returns:
        str: 保存的文件路径
    """
    try:
        import os
        import json
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        if event_name:
            filename = f"evaluation_metrics_{event_name}_{timestamp}.json"
        else:
            filename = f"evaluation_metrics_{timestamp}.json"
        
        file_path = os.path.join(output_dir, filename)
        
        # 转换numpy类型为Python原生类型
        clean_metrics = {}
        for key, value in metrics.items():
            if isinstance(value, (np.integer, np.floating)):
                clean_metrics[key] = float(value)
            elif isinstance(value, np.ndarray):
                clean_metrics[key] = value.tolist()
            else:
                clean_metrics[key] = value
        
        # 保存到JSON文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(clean_metrics, f, ensure_ascii=False, indent=2)
        
        logging.info(f"评价指标已保存到: {file_path}")
        return file_path
    
    except Exception as e:
        logging.error(f"保存评价结果时出错: {e}")
        return None

def print_evaluation_summary(metrics: Dict[str, float]):
    """
    打印评价指标摘要
    
    Args:
        metrics: 评价指标字典
    """
    try:
        print("\n" + "="*60)
        print("插值结果评价指标摘要")
        print("="*60)
        
        # 基础指标
        print("\n基础指标:")
        print(f"  平均绝对误差 (MAE):     {metrics.get('mae', 'N/A'):.4f}")
        print(f"  均方根误差 (RMSE):      {metrics.get('rmse', 'N/A'):.4f}")
        print(f"  决定系数 (R²):          {metrics.get('r2', 'N/A'):.4f}")
        print(f"  纳什效率系数 (NSE):     {metrics.get('nse', 'N/A'):.4f}")
        print(f"  相关系数 (CC):          {metrics.get('cc', 'N/A'):.4f}")
        print(f"  偏差 (Bias):            {metrics.get('bias', 'N/A'):.4f}")
        print(f"  百分比偏差 (PBIAS):     {metrics.get('pbias', 'N/A'):.2f}%")
        
        # 高级指标
        print("\n高级指标:")
        print(f"  Kling-Gupta效率 (KGE):  {metrics.get('kge', 'N/A'):.4f}")
        print(f"  一致性指数:             {metrics.get('index_of_agreement', 'N/A'):.4f}")
        print(f"  体积效率:               {metrics.get('volumetric_efficiency', 'N/A'):.4f}")
        
        # 数据统计
        print("\n数据统计:")
        print(f"  总样本数:               {metrics.get('total_samples', 'N/A')}")
        print(f"  有效样本数:             {metrics.get('valid_samples', 'N/A')}")
        print(f"  缺失比例:               {metrics.get('missing_ratio', 'N/A'):.2%}")
        print(f"  实际零值比例:           {metrics.get('zero_actual_ratio', 'N/A'):.2%}")
        print(f"  预测零值比例:           {metrics.get('zero_predicted_ratio', 'N/A'):.2%}")
        
        print("="*60)
        
    except Exception as e:
        logging.error(f"打印评价摘要时出错: {e}")

# IDW插值系统

基于Delaunay三角网分析结果的反距离权重插值系统

## 系统概述

本系统利用Delaunay三角网分析结果中的包围站点信息和权重，对洪水事件进行IDW插值，支持批量处理、评估指标计算、可视化和栅格输出。

## 主要功能

- **基于Delaunay权重的IDW插值**: 利用三角网分析结果的包围站点和权重进行插值
- **批量处理**: 支持对所有洪水事件进行批量插值处理
- **评估指标**: 计算MAE、RMSE、NSE、R²等多种评估指标
- **可视化**: 生成时间序列对比图、散点图、指标分布图等
- **栅格输出**: 可选择输出ASCII格式栅格文件
- **并行处理**: 支持多核并行处理提高效率

## 文件结构

```
IDW_python/
├── idw_config.py          # 配置文件管理
├── data_loader.py         # 数据加载模块
├── idw_interpolation.py   # IDW插值核心算法
├── evaluation_metrics.py  # 评估指标计算
├── visualization.py       # 可视化模块
├── raster_output.py       # 栅格输出模块
├── batch_processor.py     # 批处理模块
├── main.py               # 主程序入口
└── README.md             # 使用说明
```

## 依赖要求

```python
pandas
numpy
matplotlib
seaborn
scipy
pathlib
logging
datetime
concurrent.futures
multiprocessing
```

## 使用方法

### 1. 基本配置

在 `idw_config.py` 中可以调整以下主要参数：

- `idw_power`: IDW幂指数（默认2.0）
- `use_delaunay_weights`: 是否使用Delaunay权重（默认True）
- `enable_raster_output`: 是否输出栅格文件（默认False）
- `enable_visualization`: 是否生成可视化（默认True）
- `n_cores`: 并行处理核心数（默认12）

### 2. 命令行使用

#### 批量处理所有洪水事件（推荐）
```bash
python main.py --mode batch
```

#### 处理指定的洪水事件
```bash
python main.py --mode batch --events 2009-1 2009-2 2010-1
```

#### 处理单个洪水事件
```bash
python main.py --mode single --event 2009-1
```

#### 创建综合汇总报告
```bash
python main.py --mode summary
```

#### 禁用栅格输出（加快处理速度）
```bash
python main.py --mode batch --disable-raster
```

#### 设置并行核心数
```bash
python main.py --mode batch --cores 8
```

### 3. 程序化使用

```python
from idw_config import config
from data_loader import DataLoader
from idw_interpolation import IDWInterpolator
from evaluation_metrics import EvaluationMetrics
from visualization import IDWVisualizer
from batch_processor import BatchProcessor

# 创建组件
loader = DataLoader(config)
interpolator = IDWInterpolator(config, loader)
evaluator = EvaluationMetrics(config)
visualizer = IDWVisualizer(config)

# 创建批处理器
processor = BatchProcessor(config, loader, interpolator, evaluator, visualizer)

# 处理所有事件
results = processor.process_all_events()

# 获取处理报告
report = processor.get_processing_report()
print(report)
```

## 输出结果

### 目录结构
```
output/IDW/
├── interpolation_results/    # 插值结果
│   └── [事件名]/
│       ├── [站点ID]_interpolation.csv
│       └── [事件名]_interpolation_summary.csv
├── evaluation_metrics/       # 评估指标
│   └── [事件名]/
│       ├── [事件名]_detailed_metrics.csv
│       └── [事件名]_summary_metrics.csv
├── visualizations/          # 可视化图表
│   └── [事件名]/
│       ├── [站点ID]_timeseries.png
│       ├── [站点ID]_scatter.png
│       ├── [事件名]_metrics_summary.png
│       └── [事件名]_performance_classification.png
├── raster_outputs/          # 栅格输出（可选）
│   └── [事件名]/
│       ├── [事件名]_[时间]_rainfall.asc
│       └── [事件名]_total_rainfall.asc
├── summary_reports/         # 汇总报告
│   ├── batch_processing_summary_[时间戳].json
│   ├── batch_summary_[时间戳].csv
│   ├── comprehensive_detailed_metrics.csv
│   └── comprehensive_event_summaries.csv
└── logs/                   # 日志文件
    └── idw_interpolation_[时间戳].log
```

### 主要输出文件说明

1. **插值结果文件**: 包含观测值、插值和时间信息
2. **评估指标文件**: 包含MAE、RMSE、NSE、R²等指标
3. **可视化图表**: 时间序列对比图、散点图、指标分布图等
4. **栅格文件**: ASCII格式的空间插值结果（可选）
5. **汇总报告**: 批处理统计和综合评估结果

## 性能评估

系统根据NSE和R²值对插值性能进行分类：

- **优秀**: NSE > 0.75 且 R² > 0.75
- **良好**: NSE > 0.65 且 R² > 0.65
- **满意**: NSE > 0.50 且 R² > 0.50
- **可接受**: NSE > 0.30 且 R² > 0.30
- **不满意**: 其他情况

## 注意事项

1. **数据要求**: 确保Delaunay分析文件存在且格式正确
2. **内存使用**: 栅格输出会消耗较多内存，建议在测试时先禁用
3. **处理时间**: 批量处理可能需要较长时间，建议使用并行模式
4. **中文支持**: 可视化图表支持中文显示，需要安装中文字体

## 故障排除

1. **配置验证失败**: 检查输入目录和Delaunay文件是否存在
2. **内存不足**: 减少并行核心数或禁用栅格输出
3. **可视化乱码**: 安装SimHei字体或修改字体配置
4. **插值失败**: 检查站点数据完整性和时间格式

## 技术支持

如有问题请检查日志文件获取详细错误信息，或联系开发团队。

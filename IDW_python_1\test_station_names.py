"""
测试站点名称功能
运行此脚本验证站点名称映射是否正常工作
"""

import sys
import os
import logging
from data_processor import DataProcessor
from station_visualizer import StationVisualizer

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_station_names():
    """测试站点名称映射功能"""
    try:
        logger.info("Testing station name mapping...")
        
        # 1. 测试数据处理器的站点名称功能
        data_processor = DataProcessor(
            input_dir="../input_another",
            stations_file="../stations.csv", 
            delaunay_weights_file="../output/Delaunay/delaunay_analysis_summary.csv"
        )
        
        # 2. 测试站点名称映射
        test_stations = ['80606500', '80607800', '80608500', '80628800', '80629000']
        
        logger.info("Station name mappings:")
        for station_code in test_stations:
            display_name = data_processor.get_station_name(station_code)
            logger.info(f"  {station_code} -> {display_name}")
        
        # 3. 测试可视化器
        visualizer = StationVisualizer()
        
        logger.info(f"Visualizer loaded {len(visualizer.station_names)} station names")
        
        # 4. 显示一些示例映射
        logger.info("Sample mappings from visualizer:")
        for code, name in list(visualizer.station_names.items())[:5]:
            logger.info(f"  {code} -> {name}")
        
        logger.info("Station name mapping test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

def main():
    """主函数"""
    logger.info("Starting station name functionality test...")
    
    success = test_station_names()
    
    if success:
        logger.info("✅ All tests passed!")
        logger.info("Station names from 水晏泰森.xlsx are working correctly")
        logger.info("You can now use Chinese station names in visualizations")
        return 0
    else:
        logger.error("❌ Tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

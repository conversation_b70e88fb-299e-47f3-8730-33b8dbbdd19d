import pandas as pd
import os
from pathlib import Path
##"D:/pythondata\spline/全年\2017-2/大化点雨量.xlsx"
def split_rainfall_data(input_file, output_dir='.'):
    """
    将合并的雨量数据文件拆分为多个站点的单独文件
    
    参数:
    input_file (str): 输入的CSV文件路径
    output_dir (str): 输出文件目录，默认为当前目录
    """
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 读取CSV文件
        print(f"正在读取文件: {input_file}")
        df = pd.read_csv(input_file)
        
        # 获取所有站点名称（除了第一列，第一列是时间）
        station_names = df.columns[1:]
        
        if not station_names.empty:
            print(f"找到 {len(station_names)} 个站点数据")
            
            # 处理每个站点
            for station in station_names:
                # 创建站点的DataFrame，包含时间和该站点的雨量
                station_df = df[['时间', station]].copy()
                # 重命名雨量列
                station_df.columns = ['时间', '雨量']
                
                # 保存到单独的CSV文件
                output_file = os.path.join(output_dir, f"{station}.csv")
                station_df.to_csv(output_file, index=False, encoding='utf-8')
                
                print(f"已生成站点文件: {output_file}，共 {len(station_df)} 条记录")
        else:
            print("错误: 未找到站点数据列")
            
    except Exception as e:
        print(f"处理过程中出错: {str(e)}")

if __name__ == "__main__":
    # 输入文件路径，请根据实际情况修改
    input_file = "D:/pythondata/spline/全年/rains_每场一表格/201702.csv"
    # 输出目录，请根据实际情况修改
    output_dir = "D:/pythondata/spatial_interpolation/input_二次筛选/201702"
    
    split_rainfall_data(input_file, output_dir)
    print("处理完成！")    

    ##########D:/pythondata/spatial_interpolation/input/all/rainfall_data.csv
    #########D:/pythondata/spline/全年/rains_每场一表格/201702.csv
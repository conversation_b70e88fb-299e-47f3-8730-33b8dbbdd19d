# -*- coding: utf-8 -*-
"""
PRISM空间插值方法实现
用于水文学点雨量转面雨量研究
包含SCE-UA全局参数优化 - 多核并行优化版本
"""

# 导入必要的库
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay
from scipy.interpolate import griddata
from multiprocessing import Pool, cpu_count, Manager
import rasterio
import time
import glob
import warnings
import logging
from tqdm import tqdm
import gc  # 用于垃圾回收，提高内存利用效率
from datetime import datetime
from scipy.optimize import differential_evolution
import random
from functools import partial
import psutil  # 用于监控CPU使用率

# 忽略不必要的警告
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("prism_interpolation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# 初始化全局参数字典
global_params = {
    'elev_factor': 0.12,        # 高程权重调节系数
    'slope_factor': 0.21,       # 坡度权重调节系数
    'aspect_factor': 0.008,     # 坡向权重调节系数
    'elev_weight_factor': 0.65, # 高程在最终权重中的比例
    'slope_weight_factor': 0.25,# 坡度在最终权重中的比例
    'aspect_weight_factor': 0.1,# 坡向在最终权重中的比例
    'power': 2.2,               # 反距离幂次
    'correlation_factor': 0.85  # 相关系数调节因子
}


def get_optimal_workers(min_workers=2, max_workers=None):
    """
    根据系统状态确定最佳工作进程数
    
    参数:
        min_workers: 最小工作进程数
        max_workers: 最大工作进程数，默认为CPU逻辑核心数-1
        
    返回:
        推荐的工作进程数
    """
    # 获取CPU核心数
    total_cores = cpu_count()
    if max_workers is None:
        max_workers = max(1, total_cores - 1)
        
    # 获取当前系统CPU使用率
    cpu_percent = psutil.cpu_percent(interval=0.5)
    
    # 根据当前CPU负载调整工作进程数
    if cpu_percent < 30:  # 低负载
        return max(min_workers, max_workers)
    elif cpu_percent < 70:  # 中等负载
        return max(min_workers, max_workers // 2)
    else:  # 高负载
        return max(min_workers, max_workers // 4)


def read_asc_file(file_path):
    """
    读取ASC格式的栅格文件
    
    参数:
        file_path: ASC文件路径
    
    返回:
        data: 栅格数据
        transform: 坐标转换信息
        nodata: 无数据值
    """
    try:
        # 使用rasterio打开ASC文件
        with rasterio.open(file_path) as src:
            data = src.read(1)  # 读取第一个波段
            transform = src.transform  # 获取坐标转换信息
            nodata = src.nodata  # 获取无数据值
        return data, transform, nodata
    except Exception as e:
        logger.error(f"读取ASC文件 {file_path} 失败: {e}")
        raise


def get_value_at_point(data, transform, x, y, nodata, search_radius=3):
    """
    从栅格数据中获取特定坐标点的值，如果当前位置无数据，则搜索最近的有效栅格
    
    参数:
        data: 栅格数据
        transform: 坐标转换信息
        x, y: 坐标点
        nodata: 无数据值
        search_radius: 搜索半径(像素)
    
    返回:
        栅格点的值，如果无法找到有效值则返回np.nan
    """
    try:
        # 将地理坐标(x,y)转换为栅格坐标(row,col)
        row, col = rasterio.transform.rowcol(transform, x, y)
        
        # 检查是否越界
        if row < 0 or row >= data.shape[0] or col < 0 or col >= data.shape[1]:
            return np.nan
            
        # 获取值并检查是否为无数据值
        value = data[row, col]
        if value != nodata:
            return value
            
        # 如果当前位置是无数据值，搜索周围的有效栅格
        valid_values = []
        for i in range(-search_radius, search_radius + 1):
            for j in range(-search_radius, search_radius + 1):
                r, c = row + i, col + j
                # 检查是否越界
                if r < 0 or r >= data.shape[0] or c < 0 or c >= data.shape[1]:
                    continue
                if data[r, c] != nodata:
                    # 计算距离作为权重
                    distance = np.sqrt(i**2 + j**2)
                    valid_values.append((data[r, c], distance))
        
        # 如果找到有效值，返回距离最近的栅格值
        if valid_values:
            valid_values.sort(key=lambda x: x[1])  # 按距离排序
            return valid_values[0][0]  # 返回最近的有效值
            
        return np.nan
    except Exception as e:
        logger.warning(f"获取栅格点值失败 (x={x}, y={y}): {e}")
        return np.nan


def extract_terrain_features(stations_df, dem_path, slope_path, aspect_path):
    """
    从地形栅格数据中提取站点的高程、坡度和坡向信息
    
    参数:
        stations_df: 包含站点信息的DataFrame
        dem_path: 高程数据文件路径
        slope_path: 坡度数据文件路径
        aspect_path: 坡向数据文件路径
    
    返回:
        更新后的站点DataFrame，包含高程、坡度和坡向信息
    """
    logger.info("正在读取地形数据...")
    # 读取地形栅格数据
    dem_data, dem_transform, dem_nodata = read_asc_file(dem_path)
    slope_data, slope_transform, slope_nodata = read_asc_file(slope_path)
    aspect_data, aspect_transform, aspect_nodata = read_asc_file(aspect_path)
    
    logger.info("正在提取站点地形特征...")
    # 创建结果DataFrame的副本，避免修改原始数据
    result_df = stations_df.copy()
    
    # 定义一个提取单个站点特征的函数
    def extract_station_features(station):
        x, y = station['经度'], station['纬度']
        dem_value = get_value_at_point(dem_data, dem_transform, x, y, dem_nodata)
        slope_value = get_value_at_point(slope_data, slope_transform, x, y, slope_nodata)
        aspect_value = get_value_at_point(aspect_data, aspect_transform, x, y, aspect_nodata)
        return pd.Series({'高程': dem_value, '坡度': slope_value, '坡向': aspect_value})
    
    # 使用apply函数并行处理
    features = result_df.apply(extract_station_features, axis=1)
    
    # 将提取的特征添加到结果DataFrame
    result_df['高程'] = features['高程']
    result_df['坡度'] = features['坡度']
    result_df['坡向'] = features['坡向']
    
    # 检查是否有缺失的地形特征，如果有则使用最近有效栅格值填充
    for feature in ['高程', '坡度', '坡向']:
        missing_indices = result_df[feature].isna()
        missing_count = missing_indices.sum()
        
        if missing_count > 0:
            # 针对每个缺失值的站点，增大搜索半径重新获取
            for idx in result_df[missing_indices].index:
                station = result_df.loc[idx]
                if feature == '高程':
                    value = get_value_at_point(dem_data, dem_transform, station['经度'], station['纬度'], dem_nodata, 5)
                elif feature == '坡度':
                    value = get_value_at_point(slope_data, slope_transform, station['经度'], station['纬度'], slope_nodata, 5)
                elif feature == '坡向':
                    value = get_value_at_point(aspect_data, aspect_transform, station['经度'], station['纬度'], aspect_nodata, 5)
                    
                # 如果仍然无法获取有效值，则使用平均值(作为最后的备选方案)
                if np.isnan(value):
                    value = result_df[feature].mean()
                    
                result_df.loc[idx, feature] = value
                
            logger.warning(f"有{missing_count}个站点的{feature}数据缺失，已使用最近有效栅格值填充")
    
    # 释放内存
    del dem_data, slope_data, aspect_data
    gc.collect()
    
    return result_df, dem_transform, dem_nodata


def build_delaunay_triangulation(stations_df):
    """
    构建Delaunay三角网，用于确定相邻站点
    
    参数:
        stations_df: 包含站点经纬度的DataFrame
    
    返回:
        tri: Delaunay三角剖分对象
        points: 用于三角剖分的点集
    """
    # 提取站点坐标作为点集
    points = stations_df[['经度', '纬度']].values
    
    # 构建Delaunay三角网
    tri = Delaunay(points)
    
    return tri, points


def find_neighbors(tri, points, station_idx, max_neighbors=10):
    """
    找到站点的邻近站点
    
    参数:
        tri: Delaunay三角剖分对象
        points: 用于三角剖分的点集
        station_idx: 目标站点的索引
        max_neighbors: 最大邻居数量
    
    返回:
        邻近站点的索引列表
    """
    try:
        # 找到包含当前站点的所有三角形
        mask = np.any(tri.simplices == station_idx, axis=1)
        triangles = tri.simplices[mask]
        
        # 提取所有相邻点的索引
        neighbors = np.unique(triangles.flatten())
        
        # 移除自身
        neighbors = neighbors[neighbors != station_idx]
        
        # 如果邻居太多，选择最近的几个
        if len(neighbors) > max_neighbors:
            target_point = points[station_idx]
            distances = np.sqrt(np.sum((points[neighbors] - target_point)**2, axis=1))
            closest_indices = np.argsort(distances)[:max_neighbors]
            neighbors = neighbors[closest_indices]
        
        return neighbors
    except Exception as e:
        logger.warning(f"查找邻近站点失败 (station_idx={station_idx}): {e}")
        # 如果三角剖分方法失败，使用最近邻点
        distances = np.sqrt(np.sum((points - points[station_idx])**2, axis=1))
        # 排除自身
        distances[station_idx] = np.inf
        closest_indices = np.argsort(distances)[:max_neighbors]
        return closest_indices


def calculate_terrain_weight(station_elev, target_elev, station_slope, target_slope, station_aspect, target_aspect, params=None):
    """
    计算地形相似性权重 - PRISM方法的核心
    
    参数:
        station_elev, target_elev: 站点和目标点的高程
        station_slope, target_slope: 站点和目标点的坡度
        station_aspect, target_aspect: 站点和目标点的坡向
        params: 自定义参数字典，如果为None则使用全局参数
    
    返回:
        地形相似性权重
    """
    # 检查输入是否有效
    if np.isnan(station_elev) or np.isnan(target_elev) or \
       np.isnan(station_slope) or np.isnan(target_slope) or \
       np.isnan(station_aspect) or np.isnan(target_aspect):
        return 0.5  # 如果有无效数据，返回默认权重
    
    # 使用提供的参数或全局参数
    if params is None:
        params = global_params
        
    elev_factor = params['elev_factor']           # 高程权重调节系数
    slope_factor = params['slope_factor']         # 坡度权重调节系数
    aspect_factor = params['aspect_factor']       # 坡向权重调节系数
    
    elev_weight_factor = params['elev_weight_factor']    # 高程在最终权重中的比例
    slope_weight_factor = params['slope_weight_factor']  # 坡度在最终权重中的比例
    aspect_weight_factor = params['aspect_weight_factor'] # 坡向在最终权重中的比例
    
    # 高程差异权重，差异越小权重越大
    elev_diff = abs(station_elev - target_elev)
    elev_weight = 1 / (1 + elev_factor * elev_diff)
    
    # 坡度差异权重
    slope_diff = abs(station_slope - target_slope)
    slope_weight = 1 / (1 + slope_factor * slope_diff)
    
    # 坡向差异权重 (考虑坡向的循环性)
    aspect_diff = min(abs(station_aspect - target_aspect), 360 - abs(station_aspect - target_aspect))
    aspect_weight = 1 / (1 + aspect_factor * aspect_diff)
    
    # 综合权重
    terrain_weight = (elev_weight * elev_weight_factor + 
                     slope_weight * slope_weight_factor + 
                     aspect_weight * aspect_weight_factor)
    
    return terrain_weight


def calculate_distance_weight(station_x, station_y, target_x, target_y, params=None):
    """
    计算基于距离的权重
    
    参数:
        station_x, station_y: 站点坐标
        target_x, target_y: 目标点坐标
        params: 自定义参数字典，如果为None则使用全局参数
    
    返回:
        距离权重
    """
    # 计算欧氏距离
    distance = np.sqrt((station_x - target_x)**2 + (station_y - target_y)**2)
    if distance < 1e-10:  # 避免除以零
        return 1.0
    
    # 使用提供的参数或全局参数
    if params is None:
        params = global_params
        
    power = params['power']  # 优化的距离幂次
    
    # 使用反距离加权
    return 1 / (distance ** power)


def calculate_spatial_autocorrelation_weight(station_rainfall, neighbor_rainfall_values, params=None):
    """
    计算基于空间自相关性的权重
    
    参数:
        station_rainfall: 目标站点的降雨量
        neighbor_rainfall_values: 邻近站点的降雨量
        params: 自定义参数字典，如果为None则使用全局参数
    
    返回:
        空间自相关性权重
    """
    # 如果没有足够的数据计算相关性，返回默认权重
    if len(neighbor_rainfall_values) < 2:
        return 1.0
    
    try:
        # 使用提供的参数或全局参数
        if params is None:
            params = global_params
            
        correlation_factor = params['correlation_factor']  # 相关系数调节因子
        
        # 计算当前站点雨量与邻近站点雨量的相似度
        diffs = [abs(station_rainfall - value) / (max(abs(station_rainfall), abs(value)) + 0.1) 
                for value in neighbor_rainfall_values]
        correlation = 1 - np.mean(diffs)
        
        # 调整相关性权重
        correlation = correlation_factor * correlation + (1 - correlation_factor)
        
        # 避免负权重，限制在[0.1, 1]范围内
        correlation = max(0.1, min(1, correlation))
        
        return correlation
    except Exception as e:
        logger.warning(f"计算空间自相关性权重失败: {e}")
        return 1.0  # 出错时返回默认权重


def prism_interpolation(target_station, neighbor_stations, rain_data, stations_terrain, target_actual_value=None, params=None):
    """
    对目标站点进行PRISM插值
    
    参数:
        target_station: 目标站点名称
        neighbor_stations: 邻近站点列表
        rain_data: 所有站点的降雨数据字典
        stations_terrain: 包含站点地形特征的DataFrame
        target_actual_value: 目标站点的实际观测值（可选，用于留一法验证）
        params: 自定义参数字典，如果为None则使用全局参数
    
    返回:
        插值结果
    """
    try:
        # 获取目标站点信息
        target_info = stations_terrain[stations_terrain['站点'] == target_station]
        if target_info.empty:
            logger.warning(f"找不到站点 {target_station} 的信息")
            return np.nan

        # 提取目标站点的地形特征
        target_x = target_info['经度'].values[0]
        target_y = target_info['纬度'].values[0]
        target_elev = target_info['高程'].values[0]
        target_slope = target_info['坡度'].values[0]
        target_aspect = target_info['坡向'].values[0]
        
        # 用于收集权重和雨量值
        weights = []
        rainfall_values = []
        
        # 处理每个邻近站点
        for neighbor in neighbor_stations:
            neighbor_name = stations_terrain.iloc[neighbor]['站点']
            
            # 跳过目标站点(用于留一法验证)
            if neighbor_name == target_station and target_actual_value is None:
                continue
            
            # 确保邻近站点有降雨数据
            if neighbor_name not in rain_data:
                continue
                
            # 获取邻近站点的降雨数据和地形特征
            neighbor_rainfall = rain_data[neighbor_name]
            
            # 跳过没有降雨记录的站点
            if np.isnan(neighbor_rainfall):
                continue
                
            neighbor_info = stations_terrain[stations_terrain['站点'] == neighbor_name]
            if neighbor_info.empty:
                continue
                
            neighbor_x = neighbor_info['经度'].values[0]
            neighbor_y = neighbor_info['纬度'].values[0]
            neighbor_elev = neighbor_info['高程'].values[0]
            neighbor_slope = neighbor_info['坡度'].values[0]
            neighbor_aspect = neighbor_info['坡向'].values[0]
            
            # 计算地形相似性权重
            terrain_weight = calculate_terrain_weight(
                neighbor_elev, target_elev,
                neighbor_slope, target_slope,
                neighbor_aspect, target_aspect,
                params
            )
            
            # 计算距离权重
            distance_weight = calculate_distance_weight(
                neighbor_x, neighbor_y,
                target_x, target_y,
                params
            )
            
            # 计算总权重
            total_weight = terrain_weight * distance_weight
            
            # 收集权重和雨量值
            weights.append(total_weight)
            rainfall_values.append(neighbor_rainfall)
        
        # 如果没有有效的邻近站点，返回NaN
        if not weights:
            return np.nan
            
        # 对收集的数据进行归一化并计算加权平均
        weights = np.array(weights)
        rainfall_values = np.array(rainfall_values)
        
        # 应用空间自相关性权重调整
        autocorr_weight = calculate_spatial_autocorrelation_weight(
            np.average(rainfall_values, weights=weights),
            rainfall_values,
            params
        )
        weights = weights * autocorr_weight
        
        # 归一化权重
        weights = weights / np.sum(weights)
        
        # 计算加权平均
        interpolated_value = np.sum(weights * rainfall_values)
        
        return interpolated_value
        
    except Exception as e:
        logger.error(f"插值站点 {target_station} 时出错: {e}")
        return np.nan


def process_rainfall_file(file_path, stations_terrain, dem_transform, dem_nodata, output_dir):
    """
    处理单个降雨量文件
    
    参数:
        file_path: 降雨量文件路径
        stations_terrain: 包含站点地形特征的DataFrame
        dem_transform: DEM坐标变换信息
        dem_nodata: DEM无数据值
        output_dir: 输出目录
    
    返回:
        处理结果信息
    """
    try:
        # 提取文件名作为时间标识
        file_name = os.path.basename(file_path).split('.')[0]
        
        # 读取降雨数据
        df = pd.read_csv(file_path, encoding='utf-8')
        
        # 检查数据格式是否符合预期
        if '时间' not in df.columns or '雨量' not in df.columns:
            logger.warning(f"文件 {file_path} 格式不符合预期，应包含'时间'和'雨量'列")
            return None
            
        # 获取时间和降雨量
        timestamp = df['时间'].iloc[0] if not df.empty else file_name
        
        # 创建站点-降雨量字典
        rain_data = {}
        station_name = os.path.basename(file_path).split('.')[0]
        rain_value = df['雨量'].iloc[0] if not df.empty else np.nan
        rain_data[station_name] = rain_value
        
        # 构建Delaunay三角网以找到邻近站点
        tri, points = build_delaunay_triangulation(stations_terrain)
        
        # 查找目标站点的索引
        target_idx = stations_terrain[stations_terrain['站点'] == station_name].index
        
        if len(target_idx) == 0:
            logger.warning(f"找不到站点 {station_name} 的信息")
            return None
            
        target_idx = target_idx[0]
        
        # 找到邻近站点
        neighbor_indices = find_neighbors(tri, points, target_idx)
        
        # 对目标站点进行插值
        interpolated_value = prism_interpolation(
            station_name, 
            neighbor_indices, 
            rain_data, 
            stations_terrain
        )
        
        # 创建结果字典
        result = {
            '时间': timestamp,
            '站点': station_name,
            '观测值': rain_value,
            '插值结果': interpolated_value
        }
        
        return result
        
    except Exception as e:
        logger.error(f"处理文件 {file_path} 时出错: {e}")
        return None


# 定义一个处理单个验证任务的函数，用于并行验证
def process_validation_task(task_data):
    """
    处理单个验证任务
    
    参数:
        task_data: 包含验证任务所需数据的字典
    
    返回:
        验证结果字典
    """
    try:
        # 解包任务数据
        stations_terrain = task_data['stations_terrain']
        timestamp = task_data['timestamp']
        station_idx = task_data['station_idx']
        station_name = task_data['station_name']
        actual_value = task_data['actual_value']
        rain_data = task_data['rain_data']
        
        # 构建Delaunay三角网
        tri, points = build_delaunay_triangulation(stations_terrain)
        
        # 找到邻近站点
        neighbor_indices = find_neighbors(tri, points, station_idx)
        
        # 执行插值
        predicted_value = prism_interpolation(
            station_name, 
            neighbor_indices, 
            rain_data, 
            stations_terrain,
            actual_value
        )
        
        # 如果无法插值，返回None
        if np.isnan(predicted_value):
            return None
            
        # 计算误差
        error = predicted_value - actual_value
        abs_error = abs(error)
        rel_error = abs_error / (actual_value + 0.1) * 100
        
        # 返回结果
        return {
            '时间': timestamp,
            '站点': station_name,
            '观测值': actual_value,
            '预测值': predicted_value,
            '绝对误差': abs_error,
            '相对误差(%)': rel_error
        }
    except Exception as e:
        logger.warning(f"验证任务处理失败: {e}")
        return None


def run_leave_one_out_validation(stations_terrain, rain_data_all, dem_transform, dem_nodata):
    """
    执行留一法交叉验证（并行版本）
    
    参数:
        stations_terrain: 包含站点地形特征的DataFrame
        rain_data_all: 所有站点的降雨数据
        dem_transform: DEM坐标变换信息
        dem_nodata: DEM无数据值
    
    返回:
        验证结果统计
    """
    logger.info("开始执行留一法交叉验证...")
    
    # 准备验证任务
    validation_tasks = []
    
    for timestamp, rain_data in rain_data_all.items():
        for station_idx, station_name in enumerate(stations_terrain['站点']):
            if station_name not in rain_data or np.isnan(rain_data[station_name]):
                continue
            
            # 添加验证任务
            validation_tasks.append({
                'stations_terrain': stations_terrain,
                'timestamp': timestamp,
                'station_idx': station_idx,
                'station_name': station_name,
                'actual_value': rain_data[station_name],
                'rain_data': rain_data
            })
    
    # 确定工作进程数
    n_workers = get_optimal_workers()
    logger.info(f"留一法验证使用 {n_workers} 个工作进程")
    
    # 使用多进程并行处理验证任务
    results = []
    with Pool(processes=n_workers) as pool:
        for result in tqdm(
            pool.imap_unordered(process_validation_task, validation_tasks),
            total=len(validation_tasks),
            desc="执行留一法验证"
        ):
            if result is not None:
                results.append(result)
    
    # 转换为DataFrame
    validation_df = pd.DataFrame(results)
    
    # 计算统计指标
    mae = validation_df['绝对误差'].mean()
    rmse = np.sqrt((validation_df['绝对误差'] ** 2).mean())
    mre = validation_df['相对误差(%)'].mean()
    
    logger.info(f"留一法验证结果: MAE={mae:.2f}, RMSE={rmse:.2f}, MRE={mre:.2f}%")
    
    return {
        'validation_df': validation_df,
        'mae': mae,
        'rmse': rmse,
        'mre': mre
    }


# 定义一个用于评估参数的函数，供SCE-UA优化使用
def evaluate_parameter_set(params_data):
    """
    评估单个参数集的性能
    
    参数:
        params_data: 包含参数值和评估所需数据的字典
    
    返回:
        RMSE值
    """
    try:
        # 解包数据
        param_values = params_data['param_values']
        param_names = params_data['param_names']
        stations_terrain = params_data['stations_terrain']
        sample_timestamps = params_data['sample_timestamps']
        sample_stations = params_data['sample_stations']
        rain_data_all = params_data['rain_data_all']
        
        # 转换参数列表为字典
        params = {name: value for name, value in zip(param_names, param_values)}
        
        # 确保权重之和为1
        weight_sum = params['elev_weight_factor'] + params['slope_weight_factor']
        if weight_sum > 1:
            params['elev_weight_factor'] /= weight_sum
            params['slope_weight_factor'] /= weight_sum
        params['aspect_weight_factor'] = 1 - params['elev_weight_factor'] - params['slope_weight_factor']
        
        # 评估结果
        squared_errors = []
        
        # 对采样的时间步和站点评估参数
        for timestamp in sample_timestamps:
            rain_data = rain_data_all[timestamp]
            tri, points = build_delaunay_triangulation(stations_terrain)
            
            for station_idx in sample_stations:
                station_name = stations_terrain.iloc[station_idx]['站点']
                
                # 跳过没有降雨数据的站点
                if station_name not in rain_data or np.isnan(rain_data[station_name]):
                    continue
                
                # 实际观测值
                actual_value = rain_data[station_name]
                
                # 找到邻近站点
                neighbor_indices = find_neighbors(tri, points, station_idx)
                
                # 使用当前参数集执行插值
                predicted_value = prism_interpolation(
                    station_name, 
                    neighbor_indices, 
                    rain_data, 
                    stations_terrain,
                    actual_value,
                    params
                )
                
                # 如果插值成功，计算误差
                if not np.isnan(predicted_value):
                    error = predicted_value - actual_value
                    squared_errors.append(error ** 2)
        
        # 计算RMSE
        if not squared_errors:
            return float('inf')
        
        rmse = np.sqrt(np.mean(squared_errors))
        return rmse
    
    except Exception as e:
        logger.warning(f"参数评估失败: {e}")
        return float('inf')


def sce_ua_optimize(stations_terrain, rain_data_all, dem_transform, dem_nodata, 
                   max_iterations=30, population_size=50, complexes=5):
    """
    使用SCE-UA(Shuffled Complex Evolution)算法优化PRISM插值参数（并行版本）
    
    参数:
        stations_terrain: 包含站点地形特征的DataFrame
        rain_data_all: 所有站点的降雨数据
        dem_transform: DEM坐标变换信息
        dem_nodata: DEM无数据值
        max_iterations: 最大迭代次数
        population_size: 种群大小
        complexes: 复形数量
    
    返回:
        最优参数字典
    """
    logger.info("开始SCE-UA全局参数优化...")
    
    # 定义参数范围
    param_bounds = {
        'elev_factor': (0.1, 0.2),         # 高程权重调节系数
        'slope_factor': (0.15, 0.3),        # 坡度权重调节系数
        'aspect_factor': (0.001, 0.05),     # 坡向权重调节系数
       
        'elev_weight_factor': (0.5, 0.95),   # 高程在最终权重中的比例
        'slope_weight_factor': (0.1, 0.3),  # 坡度在最终权重中的比例
         'aspect_weight_factor':(0.001,0.3),    # 坡向在最终权重中的比例

        'power': (1.5, 3),                # 反距离幂次
        'correlation_factor': (0.7, 1.0)    # 相关系数调节因子
    }
    
    # 参数名称列表，保持顺序一致
    param_names = list(param_bounds.keys())
    
    # 将参数范围转换为scipy differential_evolution需要的格式
    bounds = [param_bounds[name] for name in param_names]
    
    # 采样部分时间步和站点以提高计算效率
    sample_timestamps = random.sample(list(rain_data_all.keys()), min(5, len(rain_data_all)))
    sample_stations = random.sample(list(range(len(stations_terrain))), min(20, len(stations_terrain)))
    
    # 确定工作进程数
    n_workers = get_optimal_workers()
    logger.info(f"参数优化使用 {n_workers} 个工作进程")
    
    # 创建一个用于评估初始种群的函数
    def evaluate_population(population):
        """并行评估整个种群"""
        tasks = []
        for param_values in population:
            tasks.append({
                'param_values': param_values,
                'param_names': param_names,
                'stations_terrain': stations_terrain,
                'sample_timestamps': sample_timestamps,
                'sample_stations': sample_stations,
                'rain_data_all': rain_data_all
            })
        
        # 并行评估
        with Pool(processes=n_workers) as pool:
            results = list(tqdm(
                pool.imap(evaluate_parameter_set, tasks),
                total=len(tasks),
                desc="评估参数集"
            ))
        
        return np.array(results)
    
    # 创建一个包装函数用于scipy的differential_evolution
    def objective_function(x):
        """单个参数集评估函数，用于优化算法内部评估"""
        task = {
            'param_values': x,
            'param_names': param_names,
            'stations_terrain': stations_terrain,
            'sample_timestamps': sample_timestamps,
            'sample_stations': sample_stations,
            'rain_data_all': rain_data_all
        }
        return evaluate_parameter_set(task)
    
    # 自定义回调函数，显示每次迭代的进度
    def callback(xk, convergence):
        params = {name: value for name, value in zip(param_names, xk)}
        logger.info(f"迭代进度: 收敛度={convergence:.6f}, 最优参数: {params}")
        return False
    
    # 运行差分进化算法
    logger.info("开始优化参数...")
    result = differential_evolution(
        objective_function, 
        bounds=bounds, 
        maxiter=max_iterations,
        popsize=population_size,
        workers=1,  # 使用1是因为我们自己实现了并行评估
        updating='deferred',
        disp=True,
        callback=callback
    )
    
    # 获取最优参数
    optimal_params = {name: value for name, value in zip(param_names, result.x)}
    
    # 确保权重因子之和为1
    weight_sum = optimal_params['elev_weight_factor'] + optimal_params['slope_weight_factor']
    if weight_sum > 1:
        optimal_params['elev_weight_factor'] /= weight_sum
        optimal_params['slope_weight_factor'] /= weight_sum
    optimal_params['aspect_weight_factor'] = 1 - optimal_params['elev_weight_factor'] - optimal_params['slope_weight_factor']
    
    logger.info(f"优化完成! 最优RMSE: {result.fun:.4f}")
    logger.info(f"最优参数: {optimal_params}")
    
    return optimal_params


def plot_validation_results(validation_df, output_dir):
    """
    绘制验证结果的可视化图表
    
    参数:
        validation_df: 验证结果DataFrame
        output_dir: 输出目录
    """
    try:
        plt.figure(figsize=(12, 10))
        
        # 散点图：观测值vs预测值
        plt.subplot(2, 2, 1)
        plt.scatter(validation_df['观测值'], validation_df['预测值'], alpha=0.6)
        max_val = max(validation_df['观测值'].max(), validation_df['预测值'].max()) * 1.1
        plt.plot([0, max_val], [0, max_val], 'r--')
        plt.xlabel('观测值 (mm)')
        plt.ylabel('预测值 (mm)')
        plt.title('观测值 vs 预测值')
        plt.grid(True, alpha=0.3)
        
        # 直方图：绝对误差
        plt.subplot(2, 2, 2)
        plt.hist(validation_df['绝对误差'], bins=20, alpha=0.7, color='blue')
        plt.xlabel('绝对误差 (mm)')
        plt.ylabel('频数')
        plt.title('绝对误差分布')
        plt.grid(True, alpha=0.3)
        
        # 直方图：相对误差
        plt.subplot(2, 2, 3)
        plt.hist(validation_df['相对误差(%)'], bins=20, alpha=0.7, color='green')
        plt.xlabel('相对误差 (%)')
        plt.ylabel('频数')
        plt.title('相对误差分布')
        plt.grid(True, alpha=0.3)
        
        # 箱线图：各站点的绝对误差
        plt.subplot(2, 2, 4)
        station_errors = validation_df.groupby('站点')['绝对误差'].agg(['mean', 'std']).sort_values('mean')
        top_stations = station_errors.head(15).index
        validation_subset = validation_df[validation_df['站点'].isin(top_stations)]
        plt.boxplot([validation_subset[validation_subset['站点'] == station]['绝对误差'] for station in top_stations],
                   labels=top_stations, vert=False)
        plt.xlabel('绝对误差 (mm)')
        plt.title('各站点的误差分布 (前15个站点)')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'validation_plots.png'), dpi=300)
        plt.close()
        
        logger.info(f"验证结果可视化图表已保存")
    except Exception as e:
        logger.error(f"绘制验证结果图表失败: {e}")


# 并行读取降雨数据文件
def read_rainfall_file(file_path):
    """
    读取单个降雨量文件
    
    参数:
        file_path: 文件路径
    
    返回:
        读取结果字典
    """
    try:
        station_name = os.path.basename(file_path).split('.')[0]
        df = pd.read_csv(file_path, encoding='utf-8')
        
        # 检查数据格式
        if '时间' not in df.columns or '雨量' not in df.columns:
            logger.warning(f"文件 {file_path} 格式不符合预期，应包含'时间'和'雨量'列")
            return None
            
        # 处理数据
        result = {}
        for _, row in df.iterrows():
            timestamp = row['时间']
            rain_value = row['雨量']
            
            if timestamp not in result:
                result[timestamp] = {}
                
            result[timestamp][station_name] = rain_value
            
        # 清理不需要的变量
        del df
        gc.collect()
        
        return result
    except Exception as e:
        logger.warning(f"读取文件 {file_path} 时出错: {e}")
        return None


def process_all_stations(input_dir, terrain_dir, output_dir, station_file, optimize_params=False, sce_params=None):
    """
    处理所有站点的降雨数据（多核并行版本）
    
    参数:
        input_dir: 输入文件夹路径
        terrain_dir: 地形数据文件夹路径
        output_dir: 输出文件夹路径
        station_file: 站点信息文件路径
        optimize_params: 是否优化参数
        sce_params: SCE-UA参数字典
    """
    start_time = time.time()
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建地形数据路径
    dem_path = os.path.join(terrain_dir, 'dem.asc')
    slope_path = os.path.join(terrain_dir, 'slope.asc')  # 注意这里有拼写错误，但保持与题目一致
    aspect_path = os.path.join(terrain_dir, 'aspect.asc')
    
    # 读取站点信息
    try:
        stations_df = pd.read_csv(station_file, encoding='utf-8')
        logger.info(f"成功读取站点信息，共 {len(stations_df)} 个站点")
    except Exception as e:
        logger.error(f"读取站点文件 {station_file} 失败: {e}")
        return
    
    # 提取站点的地形特征
    stations_terrain, dem_transform, dem_nodata = extract_terrain_features(
        stations_df, dem_path, slope_path, aspect_path
    )
    
    # 查找所有降雨量文件
    rain_files = glob.glob(os.path.join(input_dir, '*.csv'))
    if not rain_files:
        logger.error(f"在 {input_dir} 中未找到降雨量文件")
        return
    
    logger.info(f"找到 {len(rain_files)} 个降雨量文件")
    
    # 确定工作进程数
    n_workers = get_optimal_workers()
    logger.info(f"读取降雨数据使用 {n_workers} 个工作进程")
    
    # 并行读取所有降雨数据
    rain_data_all = {}
    with Pool(processes=n_workers) as pool:
        file_results = list(tqdm(
            pool.imap(read_rainfall_file, rain_files),
            total=len(rain_files),
            desc="读取降雨数据"
        ))
    
    # 合并结果
    for file_data in file_results:
        if file_data is None:
            continue
            
        for timestamp, rain_data in file_data.items():
            if timestamp not in rain_data_all:
                rain_data_all[timestamp] = {}
                
            rain_data_all[timestamp].update(rain_data)
    
    # 初始化全局参数
    global global_params
    global_params = {
        'elev_factor': 0.12,
        'slope_factor': 0.08,
        'aspect_factor': 0.008,
        'elev_weight_factor': 0.65,
        'slope_weight_factor': 0.25,
        'aspect_weight_factor': 0.1,
        'power': 2.2,
        'correlation_factor': 0.85
    }
    
    # 如果需要优化参数
    if optimize_params:
        logger.info("开始参数优化...")
        
        # 设置SCE-UA参数
        if sce_params is None:
            sce_params = {}
        
        max_iterations = sce_params.get('max_iterations', 30)
        population_size = sce_params.get('population_size', 50)
        complexes = sce_params.get('complexes', 5)
        
        optimal_params = sce_ua_optimize(
            stations_terrain, rain_data_all, dem_transform, dem_nodata,
            max_iterations=max_iterations, 
            population_size=population_size, 
            complexes=complexes
        )
        # 更新全局参数
        global_params.update(optimal_params)
        logger.info(f"参数优化完成: {global_params}")
    
    # 执行留一法验证
    validation_results = run_leave_one_out_validation(
        stations_terrain, rain_data_all, dem_transform, dem_nodata
    )
    
    # 保存验证结果
    validation_df = validation_results['validation_df']
    validation_file = os.path.join(output_dir, 'validation_results.csv')
    validation_df.to_csv(validation_file, index=False, encoding='utf-8')
    
    # 创建统计结果文件
    stats_file = os.path.join(output_dir, datetime.now().strftime('%Y%m%d_%H%M%S') + ".txt")
    with open(stats_file, 'w', encoding='utf-8') as f:
        f.write(f"PRISM插值算法统计结果\n")
        f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"处理站点数量: {len(stations_terrain)}\n")
        f.write(f"处理时间步数量: {len(rain_data_all)}\n\n")
        
        f.write(f"留一法验证结果:\n")
        f.write(f"平均绝对误差 (MAE): {validation_results['mae']:.4f}\n")
        f.write(f"均方根误差 (RMSE): {validation_results['rmse']:.4f}\n")
        f.write(f"平均相对误差 (MRE): {validation_results['mre']:.4f}%\n\n")
        
        f.write(f"PRISM最优参数:\n")
        for param, value in global_params.items():
            f.write(f"{param}: {value:.6f}\n")
    
    # 创建可视化结果
    plot_validation_results(validation_df, output_dir)
    
    # 计算并显示总耗时
    elapsed_time = time.time() - start_time
    logger.info(f"全部处理完成，总耗时: {elapsed_time:.2f}秒")
    logger.info(f"结果已保存到: {output_dir}")


def main():
    """
    主函数：解析命令行参数并执行插值流程
    """
    # 设置参数默认值
    input_dir = 'D:/pythondata/spatial_interpolation/input/2015-4/点雨量'
    terrain_dir = 'D:/pythondata/spatial_interpolation/terrain/90'
    output_dir = 'D:/pythondata/spatial_interpolation/output/PRISM/2015-4'
    station_file = 'D:/pythondata/spatial_interpolation/stations.csv'
    optimize_params = False
    
    # 提示用户是否需要自定义路径
    print("PRISM 空间插值程序 (多核并行版)")
    print("默认路径设置:")
    print(f"1. 输入路径: {input_dir}")
    print(f"2. 地形数据路径: {terrain_dir}")
    print(f"3. 输出路径: {output_dir}")
    print(f"4. 站点文件: {station_file}")
    
    custom_paths = input("是否需要自定义路径? (y/n): ").strip().lower()
    
    if custom_paths == 'y':
        input_dir = input(f"请输入点雨量文件路径 (默认: {input_dir}): ").strip() or input_dir
        terrain_dir = input(f"请输入地形数据路径 (默认: {terrain_dir}): ").strip() or terrain_dir
        output_dir = input(f"请输入输出路径 (默认: {output_dir}): ").strip() or output_dir
        station_file = input(f"请输入站点文件路径 (默认: {station_file}): ").strip() or station_file
    
    # 询问是否执行参数优化
    do_optimize = input("是否执行参数优化? (y/n): ").strip().lower()
    optimize_params = (do_optimize == 'y')
    
    # 如果执行参数优化，提供修改SCE-UA参数的选项
    if optimize_params:
        print("\nSCE-UA优化参数设置:")
        print("默认设置: 最大迭代次数=30, 种群大小=50, 复形数量=5")
        custom_sce = input("是否需要自定义SCE-UA参数? (y/n): ").strip().lower()
        
        if custom_sce == 'y':
            max_iterations = int(input("请输入最大迭代次数 (默认: 30): ").strip() or "30")
            population_size = int(input("请输入种群大小 (默认: 50): ").strip() or "50")
            complexes = int(input("请输入复形数量 (默认: 5): ").strip() or "5")
            
            # 执行带自定义SCE-UA参数的插值处理
            sce_params = {
                'max_iterations': max_iterations, 
                'population_size': population_size,
                'complexes': complexes
            }
            process_all_stations(input_dir, terrain_dir, output_dir, station_file, optimize_params, sce_params)
        else:
            # 使用默认SCE-UA参数
            process_all_stations(input_dir, terrain_dir, output_dir, station_file, optimize_params)
    else:
        # 不执行参数优化
        process_all_stations(input_dir, terrain_dir, output_dir, station_file, optimize_params)


if __name__ == "__main__":
    main()

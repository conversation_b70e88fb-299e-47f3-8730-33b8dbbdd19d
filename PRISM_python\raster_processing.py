"""
栅格处理模块
负责栅格插值和ASC文件输出
"""

import numpy as np
import pandas as pd
import rasterio
from rasterio.transform import from_bounds
import os
import logging
from typing import Dict, List, Tuple, Optional
from scipy.interpolate import griddata
import gc
from tqdm import tqdm

logger = logging.getLogger(__name__)


class RasterProcessor:
    """栅格处理器类"""
    
    def __init__(self, config):
        """初始化栅格处理器"""
        self.config = config
        self.dem_data = None
        self.dem_transform = None
        self.dem_meta = None
        self.slope_data = None
        self.aspect_data = None
    
    def load_terrain_rasters(self, terrain_data: Dict):
        """加载地形栅格数据"""
        try:
            self.dem_data = terrain_data['dem']['data']
            self.dem_transform = terrain_data['dem']['transform']
            self.dem_meta = terrain_data['dem']['meta']
            self.slope_data = terrain_data['slope']['data']
            self.aspect_data = terrain_data['aspect']['data']
            
            logger.info(f"地形栅格数据加载完成，栅格大小: {self.dem_data.shape}")
            
        except Exception as e:
            logger.error(f"加载地形栅格数据失败: {e}")
            raise
    
    def get_grid_point_terrain(self, x: float, y: float) -> Dict:
        """获取栅格点的地形特征"""
        try:
            # 将地理坐标转换为栅格坐标
            row, col = rasterio.transform.rowcol(self.dem_transform, x, y)
            
            # 检查是否在栅格范围内
            if (0 <= row < self.dem_data.shape[0] and 0 <= col < self.dem_data.shape[1]):
                elevation = self.dem_data[row, col]
                slope = self.slope_data[row, col]
                aspect = self.aspect_data[row, col]
                
                # 检查是否为无数据值
                dem_nodata = self.dem_meta.get('nodata', -9999)
                if elevation == dem_nodata or np.isnan(elevation):
                    elevation = np.nanmean(self.dem_data[self.dem_data != dem_nodata])
                if slope == dem_nodata or np.isnan(slope):
                    slope = np.nanmean(self.slope_data[self.slope_data != dem_nodata])
                if aspect == dem_nodata or np.isnan(aspect):
                    aspect = np.nanmean(self.aspect_data[self.aspect_data != dem_nodata])
            else:
                # 栅格点在范围外，使用平均值
                dem_nodata = self.dem_meta.get('nodata', -9999)
                elevation = np.nanmean(self.dem_data[self.dem_data != dem_nodata])
                slope = np.nanmean(self.slope_data[self.slope_data != dem_nodata])
                aspect = np.nanmean(self.aspect_data[self.aspect_data != dem_nodata])
            
            return {
                'x': x,
                'y': y,
                'elevation': elevation,
                'slope': slope,
                'aspect': aspect
            }
            
        except Exception as e:
            logger.error(f"获取栅格点地形特征失败 (x={x}, y={y}): {e}")
            # 返回默认值
            return {
                'x': x,
                'y': y,
                'elevation': 100.0,
                'slope': 5.0,
                'aspect': 180.0
            }
    
    def create_interpolation_grid(self, downsample_factor: int = 10) -> List[Dict]:
        """创建插值栅格点"""
        try:
            if self.dem_data is None:
                raise ValueError("地形数据未加载")
            
            rows, cols = self.dem_data.shape
            dem_nodata = self.dem_meta.get('nodata', -9999)
            
            # 创建降采样栅格
            ds_rows = rows // downsample_factor + 1
            ds_cols = cols // downsample_factor + 1
            
            grid_points = []
            
            logger.info(f"创建插值栅格，原始大小: {rows}x{cols}, 降采样大小: {ds_rows}x{ds_cols}")
            
            for i in range(ds_rows):
                for j in range(ds_cols):
                    # 获取原始栅格中的行列索引
                    row = i * downsample_factor
                    col = j * downsample_factor
                    
                    if row >= rows or col >= cols:
                        continue
                    
                    # 检查是否为有效栅格点
                    if self.dem_data[row, col] != dem_nodata:
                        # 获取地理坐标
                        x, y = rasterio.transform.xy(self.dem_transform, row, col)
                        
                        # 获取地形特征
                        grid_point = self.get_grid_point_terrain(x, y)
                        grid_point['row'] = row
                        grid_point['col'] = col
                        
                        grid_points.append(grid_point)
            
            logger.info(f"创建了 {len(grid_points)} 个有效插值栅格点")
            return grid_points
            
        except Exception as e:
            logger.error(f"创建插值栅格失败: {e}")
            return []
    
    def interpolate_to_full_grid(self, grid_points: List[Dict], 
                               interpolated_values: List[float]) -> np.ndarray:
        """将降采样点插值到完整栅格"""
        try:
            if len(grid_points) != len(interpolated_values):
                raise ValueError("栅格点数量与插值结果数量不匹配")
            
            # 过滤有效值
            valid_points = []
            valid_values = []
            
            for point, value in zip(grid_points, interpolated_values):
                if not np.isnan(value) and value >= 0:
                    valid_points.append([point['row'], point['col']])
                    valid_values.append(value)
            
            if len(valid_points) < 3:
                logger.warning("有效插值点数量不足，无法生成完整栅格")
                return None
            
            # 创建完整栅格
            rows, cols = self.dem_data.shape
            dem_nodata = self.dem_meta.get('nodata', -9999)
            
            # 创建目标栅格坐标
            grid_y, grid_x = np.mgrid[0:rows, 0:cols]
            
            # 使用griddata进行插值
            full_grid = griddata(
                np.array(valid_points), 
                np.array(valid_values),
                (grid_y, grid_x),
                method='linear',
                fill_value=0
            )
            
            # 只对有效的DEM区域进行插值
            valid_mask = self.dem_data != dem_nodata
            result_grid = np.full_like(self.dem_data, dem_nodata, dtype=np.float32)
            result_grid[valid_mask] = full_grid[valid_mask]
            
            return result_grid
            
        except Exception as e:
            logger.error(f"插值到完整栅格失败: {e}")
            return None
    
    def save_raster_as_asc(self, grid_data: np.ndarray, output_path: str, 
                          time_point=None) -> bool:
        """保存栅格为ASC格式"""
        try:
            if grid_data is None:
                logger.warning("栅格数据为空，跳过保存")
                return False
            
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 创建栅格元数据
            height, width = grid_data.shape
            dem_nodata = self.dem_meta.get('nodata', -9999)
            
            meta = {
                'driver': 'AAIGrid',
                'dtype': 'float32',
                'nodata': dem_nodata,
                'width': width,
                'height': height,
                'count': 1,
                'crs': self.dem_meta.get('crs'),
                'transform': self.dem_transform
            }
            
            # 保存栅格文件
            with rasterio.open(output_path, 'w', **meta) as dst:
                dst.write(grid_data.astype(np.float32), 1)
            
            logger.debug(f"栅格文件已保存: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存栅格文件失败: {e}")
            return False
    
    def process_time_series_rasters(self, time_points: List, 
                                  interpolation_results: Dict,
                                  prism_core, delaunay_tri, 
                                  stations_with_terrain: pd.DataFrame,
                                  output_dir: str):
        """处理时间序列栅格输出"""
        try:
            if not self.config.output_raster:
                logger.info("栅格输出已禁用，跳过栅格处理")
                return
            
            logger.info("开始处理时间序列栅格输出...")
            
            # 创建插值栅格点（只创建一次）
            grid_points = self.create_interpolation_grid(downsample_factor=10)
            
            if not grid_points:
                logger.warning("无法创建插值栅格点，跳过栅格处理")
                return
            
            # 创建输出目录
            raster_output_dir = os.path.join(output_dir, 'rasters')
            os.makedirs(raster_output_dir, exist_ok=True)
            
            # 分批处理时间点
            batch_size = self.config.batch_size
            num_batches = (len(time_points) + batch_size - 1) // batch_size
            
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, len(time_points))
                batch_time_points = time_points[start_idx:end_idx]
                
                logger.info(f"处理栅格批次 {batch_idx + 1}/{num_batches} "
                           f"({len(batch_time_points)} 个时间点)")
                
                for time_point in tqdm(batch_time_points, desc=f"批次 {batch_idx + 1} 栅格插值"):
                    try:
                        # 获取该时间点的降雨数据
                        if time_point not in interpolation_results:
                            continue
                        
                        rainfall_at_time = interpolation_results[time_point]['rainfall_data']
                        
                        # 检查是否为全零降雨
                        if all(value == 0 for value in rainfall_at_time.values()):
                            # 全零降雨，创建零值栅格
                            zero_grid = np.zeros_like(self.dem_data, dtype=np.float32)
                            dem_nodata = self.dem_meta.get('nodata', -9999)
                            zero_grid[self.dem_data == dem_nodata] = dem_nodata
                        else:
                            # 对栅格点进行插值
                            grid_values = []
                            
                            for grid_point in grid_points:
                                # 找到包含栅格点的三角形和权重
                                triangle_result = delaunay_tri.find_containing_triangle_with_weights(
                                    grid_point, stations_with_terrain[['经度', '纬度']].values,
                                    stations_with_terrain.index.values, power=2.0
                                )
                                neighbor_indices = triangle_result.get('vertex_indices', [])
                                
                                # 使用PRISM核心算法进行插值
                                interpolated_value = prism_core.interpolate_for_grid_point(
                                    grid_point, neighbor_indices[:3], 
                                    stations_with_terrain, rainfall_at_time
                                )
                                
                                grid_values.append(interpolated_value)
                            
                            # 插值到完整栅格
                            zero_grid = self.interpolate_to_full_grid(grid_points, grid_values)
                        
                        # 保存栅格文件
                        if zero_grid is not None:
                            time_str = time_point.strftime("%Y%m%d_%H%M%S")
                            output_path = os.path.join(raster_output_dir, f"rainfall_{time_str}.asc")
                            self.save_raster_as_asc(zero_grid, output_path, time_point)
                        
                    except Exception as e:
                        logger.error(f"处理时间点 {time_point} 的栅格失败: {e}")
                
                # 清理内存
                if self.config.memory_efficient:
                    gc.collect()
            
            logger.info(f"栅格处理完成，输出目录: {raster_output_dir}")
            
        except Exception as e:
            logger.error(f"处理时间序列栅格失败: {e}")
    
    def cleanup(self):
        """清理内存"""
        if self.config.memory_efficient:
            self.dem_data = None
            self.slope_data = None
            self.aspect_data = None
            gc.collect()
            logger.debug("已清理栅格处理器内存")

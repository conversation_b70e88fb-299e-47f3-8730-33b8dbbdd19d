#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
栅格输出模块
将IDW插值结果输出为栅格格式

作者: 空间插值系统
日期: 2024年
"""

import numpy as np
import pandas as pd
from pathlib import Path
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import struct

logger = logging.getLogger(__name__)

class RasterOutput:
    """栅格输出类"""
    
    def __init__(self, config, data_loader):
        self.config = config
        self.data_loader = data_loader
        self.mask_data = None
        self.mask_header = None
        self.load_terrain_mask()
    
    def load_terrain_mask(self):
        """加载地形掩膜文件"""
        if not self.config.enable_raster_output:
            logger.info("栅格输出已禁用，跳过地形掩膜加载")
            return
        
        if not self.config.terrain_mask.exists():
            logger.warning(f"地形掩膜文件不存在: {self.config.terrain_mask}")
            return
        
        try:
            logger.info(f"加载地形掩膜: {self.config.terrain_mask}")
            
            with open(self.config.terrain_mask, 'r') as f:
                lines = f.readlines()
            
            # 解析头部信息
            self.mask_header = {}
            data_start_line = 0
            
            for i, line in enumerate(lines):
                line = line.strip()
                if line.startswith(('ncols', 'nrows', 'xllcorner', 'yllcorner', 'cellsize', 'NODATA_value')):
                    parts = line.split()
                    key = parts[0]
                    value = float(parts[1]) if key in ['xllcorner', 'yllcorner', 'cellsize'] else int(parts[1])
                    self.mask_header[key] = value
                else:
                    data_start_line = i
                    break
            
            # 读取栅格数据
            mask_rows = []
            for line in lines[data_start_line:]:
                if line.strip():
                    row_data = [int(float(x)) for x in line.strip().split()]
                    mask_rows.append(row_data)
            
            self.mask_data = np.array(mask_rows)
            
            logger.info(f"成功加载地形掩膜，尺寸: {self.mask_data.shape}")
            logger.info(f"掩膜头部信息: {self.mask_header}")
            
        except Exception as e:
            logger.error(f"加载地形掩膜失败: {e}")
            self.mask_data = None
            self.mask_header = None
    
    def get_grid_coordinates(self) -> Tuple[np.ndarray, np.ndarray]:
        """获取栅格网格坐标"""
        if self.mask_header is None:
            raise ValueError("地形掩膜未正确加载")
        
        # 计算网格坐标
        ncols = self.mask_header['ncols']
        nrows = self.mask_header['nrows']
        xllcorner = self.mask_header['xllcorner']
        yllcorner = self.mask_header['yllcorner']
        cellsize = self.mask_header['cellsize']
        
        # 创建经纬度网格
        x_coords = np.linspace(xllcorner, xllcorner + ncols * cellsize, ncols)
        y_coords = np.linspace(yllcorner + nrows * cellsize, yllcorner, nrows)
        
        lon_grid, lat_grid = np.meshgrid(x_coords, y_coords)
        
        return lon_grid, lat_grid
    
    def interpolate_to_grid(self, station_data: Dict[str, pd.DataFrame], 
                          target_time: datetime, interpolator) -> np.ndarray:
        """将站点数据插值到栅格网格"""
        if self.mask_data is None:
            raise ValueError("地形掩膜未加载")
        
        # 获取网格坐标
        lon_grid, lat_grid = self.get_grid_coordinates()
        nrows, ncols = lon_grid.shape
        
        # 初始化结果数组
        result_grid = np.full((nrows, ncols), self.config.raster_nodata, dtype=np.float32)
        
        # 获取所有有数据的站点
        available_stations = []
        station_coords = []
        station_values = []
        
        for station_id in self.data_loader.get_available_stations():
            rainfall = self.data_loader.get_station_rainfall_at_time(
                station_data, station_id, target_time
            )
            if rainfall is not None:
                station_info = self.data_loader.get_station_info(station_id)
                if station_info:
                    available_stations.append(station_id)
                    station_coords.append((station_info['longitude'], station_info['latitude']))
                    station_values.append(rainfall)
        
        if len(available_stations) < self.config.min_stations:
            logger.warning(f"时间{target_time}的有效站点数不足: {len(available_stations)}")
            return result_grid
        
        logger.info(f"开始栅格插值，时间: {target_time}，有效站点数: {len(available_stations)}")
        
        # 对每个网格点进行插值
        for i in range(nrows):
            for j in range(ncols):
                # 检查是否在掩膜范围内
                if self.mask_data[i, j] == self.config.raster_nodata:
                    continue
                
                target_lon = lon_grid[i, j]
                target_lat = lat_grid[i, j]
                
                # 计算到各站点的距离
                distances = []
                for station_lon, station_lat in station_coords:
                    dist = interpolator.calculate_distance(
                        target_lon, target_lat, station_lon, station_lat
                    )
                    distances.append(dist)
                
                # 选择最近的站点进行插值
                sorted_indices = np.argsort(distances)
                selected_indices = sorted_indices[:min(self.config.max_stations, len(distances))]
                
                # 计算IDW权重
                selected_coords = [station_coords[idx] for idx in selected_indices]
                selected_values = [station_values[idx] for idx in selected_indices]
                
                weights = interpolator.calculate_idw_weights(
                    (target_lon, target_lat), selected_coords, self.config.idw_power
                )
                
                # 计算插值结果
                interpolated_value = sum(v * w for v, w in zip(selected_values, weights))
                result_grid[i, j] = max(0.0, interpolated_value)
        
        return result_grid
    
    def save_ascii_grid(self, grid_data: np.ndarray, output_path: Path, 
                       timestamp: datetime = None):
        """保存为ASCII格式栅格文件"""
        if self.mask_header is None:
            raise ValueError("地形掩膜头部信息未加载")
        
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            # 写入头部信息
            f.write(f"ncols         {self.mask_header['ncols']}\n")
            f.write(f"nrows         {self.mask_header['nrows']}\n")
            f.write(f"xllcorner     {self.mask_header['xllcorner']}\n")
            f.write(f"yllcorner     {self.mask_header['yllcorner']}\n")
            f.write(f"cellsize      {self.mask_header['cellsize']}\n")
            f.write(f"NODATA_value  {self.config.raster_nodata}\n")
            
            # 写入数据
            for row in grid_data:
                row_str = ' '.join([f"{val:.6f}" if val != self.config.raster_nodata 
                                  else str(self.config.raster_nodata) for val in row])
                f.write(row_str + '\n')
        
        logger.info(f"栅格文件已保存: {output_path}")
    
    def create_raster_timeseries(self, event_name: str, station_data: Dict[str, pd.DataFrame], 
                               interpolator) -> List[Path]:
        """为整个时间序列创建栅格输出"""
        if not self.config.enable_raster_output:
            logger.info("栅格输出已禁用")
            return []
        
        if self.mask_data is None:
            logger.error("地形掩膜未加载，无法创建栅格输出")
            return []
        
        logger.info(f"开始为洪水事件{event_name}创建栅格时间序列")
        
        # 获取所有时间戳
        all_timestamps = self.data_loader.get_all_timestamps(station_data)
        
        # 创建输出目录
        output_dir = self.config.output_dir / "raster_outputs" / event_name
        output_dir.mkdir(parents=True, exist_ok=True)
        
        generated_files = []
        
        # 对每个时间步进行栅格插值
        for i, timestamp in enumerate(all_timestamps):
            try:
                logger.info(f"处理时间步 {i+1}/{len(all_timestamps)}: {timestamp}")
                
                # 进行栅格插值
                grid_data = self.interpolate_to_grid(station_data, timestamp, interpolator)
                
                # 生成文件名
                time_str = timestamp.strftime("%Y%m%d_%H%M")
                output_file = output_dir / f"{event_name}_{time_str}_rainfall.asc"
                
                # 保存栅格文件
                self.save_ascii_grid(grid_data, output_file, timestamp)
                generated_files.append(output_file)
                
            except Exception as e:
                logger.error(f"处理时间步{timestamp}时出错: {e}")
                continue
        
        logger.info(f"洪水事件{event_name}栅格时间序列创建完成，生成{len(generated_files)}个文件")
        
        return generated_files
    
    def create_summary_raster(self, event_name: str, station_data: Dict[str, pd.DataFrame], 
                            interpolator, statistic: str = 'total') -> Optional[Path]:
        """创建汇总统计栅格"""
        if not self.config.enable_raster_output or self.mask_data is None:
            return None
        
        logger.info(f"创建洪水事件{event_name}的{statistic}汇总栅格")
        
        # 获取所有时间戳
        all_timestamps = self.data_loader.get_all_timestamps(station_data)
        
        if not all_timestamps:
            logger.warning(f"洪水事件{event_name}没有有效时间戳")
            return None
        
        # 初始化汇总数组
        nrows, ncols = self.mask_data.shape
        if statistic == 'total':
            summary_grid = np.zeros((nrows, ncols), dtype=np.float32)
        elif statistic == 'max':
            summary_grid = np.full((nrows, ncols), -999.0, dtype=np.float32)
        elif statistic == 'mean':
            summary_grid = np.zeros((nrows, ncols), dtype=np.float32)
            count_grid = np.zeros((nrows, ncols), dtype=np.int32)
        
        # 处理每个时间步
        for timestamp in all_timestamps:
            try:
                grid_data = self.interpolate_to_grid(station_data, timestamp, interpolator)
                
                # 更新汇总统计
                valid_mask = (grid_data != self.config.raster_nodata) & (self.mask_data != self.config.raster_nodata)
                
                if statistic == 'total':
                    summary_grid[valid_mask] += grid_data[valid_mask]
                elif statistic == 'max':
                    max_mask = valid_mask & (grid_data > summary_grid)
                    summary_grid[max_mask] = grid_data[max_mask]
                elif statistic == 'mean':
                    summary_grid[valid_mask] += grid_data[valid_mask]
                    count_grid[valid_mask] += 1
                
            except Exception as e:
                logger.error(f"处理时间步{timestamp}时出错: {e}")
                continue
        
        # 完成统计计算
        if statistic == 'mean':
            valid_count_mask = count_grid > 0
            summary_grid[valid_count_mask] /= count_grid[valid_count_mask]
        
        # 应用掩膜
        summary_grid[self.mask_data == self.config.raster_nodata] = self.config.raster_nodata
        
        # 保存结果
        output_dir = self.config.output_dir / "raster_outputs" / event_name
        output_file = output_dir / f"{event_name}_{statistic}_rainfall.asc"
        
        self.save_ascii_grid(summary_grid, output_file)
        
        return output_file

if __name__ == "__main__":
    # 测试栅格输出模块
    from idw_config import config
    from data_loader import DataLoader
    from idw_interpolation import IDWInterpolator
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建组件
    loader = DataLoader(config)
    interpolator = IDWInterpolator(config, loader)
    raster_output = RasterOutput(config, loader)
    
    if raster_output.mask_data is not None:
        print(f"地形掩膜加载成功，尺寸: {raster_output.mask_data.shape}")
        print(f"掩膜头部信息: {raster_output.mask_header}")
        
        # 测试网格坐标生成
        lon_grid, lat_grid = raster_output.get_grid_coordinates()
        print(f"网格坐标范围: 经度 {lon_grid.min():.6f} - {lon_grid.max():.6f}")
        print(f"网格坐标范围: 纬度 {lat_grid.min():.6f} - {lat_grid.max():.6f}")
    else:
        print("地形掩膜加载失败或栅格输出已禁用")

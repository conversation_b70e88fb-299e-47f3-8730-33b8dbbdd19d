# PRISM插值系统参数调整指南

## 🎯 参数调整位置

### 主要配置文件
**文件位置：`PRISM_python/config.py`**

这是**唯一**需要修改参数的地方！

```python
@dataclass
class Config:
    # 🔧 在这里调整邻近站点数量
    neighbor_count: int = 3,           # ← 主要参数！
    
    # 📊 在这里调整地形权重系数  
    elevation_weight: float = 0.4,     # ← 高程权重
    slope_weight: float = 0.3,         # ← 坡度权重
    aspect_weight: float = 0.2,        # ← 坡向权重
    moran_weight: float = 0.1,         # ← 莫兰指数权重
    
    # 💻 在这里调整CPU使用
    num_cores: int = 12,               # ← 根据您的CPU核数
```

## 🔧 核心参数说明

### 1. 邻近站点数量 (`neighbor_count`)
**位置：config.py 第25行**

```python
neighbor_count: int = 3,  # ← 在这里修改
```

**效果：**
- `2` - 最少邻站，适用于站点密集区域
- `3` - 标准设置，平衡效果 ⭐ **推荐**
- `4-5` - 更多邻站，适用于站点稀疏区域

**调整建议：**
- NSE系数太低 → 改为 `4` 或 `5`
- 站点密集 → 改为 `2`
- 计算太慢 → 改为 `2`

### 2. 地形权重系数
**位置：config.py 第30-33行**

```python
elevation_weight: float = 0.4,  # ← 高程权重
slope_weight: float = 0.3,      # ← 坡度权重  
aspect_weight: float = 0.2,     # ← 坡向权重
moran_weight: float = 0.1,      # ← 莫兰指数权重
```

**重要：四个权重系数总和应接近1.0**

**调整建议：**
- **山区数据**：增加高程权重
  ```python
  elevation_weight: float = 0.5,
  slope_weight: float = 0.3,
  aspect_weight: float = 0.15,
  moran_weight: float = 0.05,
  ```

- **平原数据**：减少高程权重
  ```python
  elevation_weight: float = 0.2,
  slope_weight: float = 0.2,
  aspect_weight: float = 0.2,
  moran_weight: float = 0.4,
  ```

### 3. 并行核心数 (`num_cores`)
**位置：config.py 第45行**

```python
num_cores: int = 12,  # ← 在这里修改
```

**设置建议：**
- 4核CPU → 设为 `4`
- 8核CPU → 设为 `8`
- 12核CPU → 设为 `12` ⭐ **您的配置**
- 24核CPU → 设为 `24`

### 4. 距离权重指数 (`distance_power`)
**位置：config.py 第28行**

```python
distance_power: float = 2.0,  # ← 在这里修改
```

**效果：**
- `1.0` - 线性权重，插值平滑
- `2.0` - 标准设置 ⭐ **推荐**
- `3.0` - 局部权重，插值尖锐

## 🚀 快速调整方法

### 方法1：直接修改config.py
1. 打开 `PRISM_python/config.py`
2. 找到第25行：`neighbor_count: int = 3,`
3. 修改数值，如改为：`neighbor_count: int = 4,`
4. 保存文件
5. 重新运行 `python run_prism.py`

### 方法2：使用配置文件
1. 修改 `prism_config.json`：
```json
{
    "neighbor_count": 4,
    "elevation_weight": 0.5,
    "slope_weight": 0.3,
    "aspect_weight": 0.15,
    "moran_weight": 0.05
}
```
2. 运行：`python run_prism.py --config prism_config.json`

## 📊 针对不同问题的参数调整

### 问题1：NSE系数异常低（如-10以下）
**解决方案：**
```python
# 在config.py中修改：
neighbor_count: int = 5,           # 增加邻站数
distance_power: float = 1.5,       # 降低距离权重
elevation_weight: float = 0.3,     # 减少地形权重
moran_weight: float = 0.3,         # 增加莫兰权重
```

### 问题2：插值结果过于平滑
**解决方案：**
```python
# 在config.py中修改：
neighbor_count: int = 2,           # 减少邻站数
distance_power: float = 3.0,       # 增加距离权重
elevation_weight: float = 0.5,     # 增加地形权重
```

### 问题3：处理速度太慢
**解决方案：**
```python
# 在config.py中修改：
num_cores: int = 24,               # 增加核心数
batch_size: int = 10,              # 减少批处理大小
output_raster: bool = False,       # 关闭栅格输出
memory_efficient: bool = True,     # 启用内存优化
```

### 问题4：内存不足
**解决方案：**
```python
# 在config.py中修改：
num_cores: int = 4,                # 减少核心数
batch_size: int = 5,               # 减少批处理大小
memory_efficient: bool = True,     # 启用内存优化
output_raster: bool = False,       # 关闭栅格输出
```

## 🎯 推荐配置组合

### 标准处理（大多数情况）
```python
neighbor_count: int = 3,
distance_power: float = 2.0,
elevation_weight: float = 0.4,
slope_weight: float = 0.3,
aspect_weight: float = 0.2,
moran_weight: float = 0.1,
num_cores: int = 12,
```

### 问题场次处理（NSE < 0.5）
```python
neighbor_count: int = 5,           # 更多邻站
distance_power: float = 1.5,       # 更平滑
elevation_weight: float = 0.2,     # 减少地形影响
moran_weight: float = 0.4,         # 增加空间相关性
num_cores: int = 1,                # 单核调试
```

### 高性能批量处理
```python
neighbor_count: int = 3,
num_cores: int = 24,               # 最大核心数
batch_size: int = 30,              # 大批处理
output_raster: bool = False,       # 节省时间
output_delaunay_plot: bool = False, # 节省时间
memory_efficient: bool = True,     # 内存优化
```

### 山区数据处理
```python
neighbor_count: int = 4,
elevation_weight: float = 0.5,     # 高程权重大
slope_weight: float = 0.3,
aspect_weight: float = 0.15,
moran_weight: float = 0.05,
```

### 平原数据处理
```python
neighbor_count: int = 3,
elevation_weight: float = 0.2,     # 高程权重小
slope_weight: float = 0.2,
aspect_weight: float = 0.2,
moran_weight: float = 0.4,         # 空间相关性大
```

## ⚠️ 重要提醒

1. **只修改config.py文件**，不要修改其他文件
2. **权重系数总和应接近1.0**
3. **修改后保存文件**，然后重新运行程序
4. **一次只修改一个参数**，观察效果
5. **备份原始配置**，以便恢复

## 🔍 参数效果验证

修改参数后，查看结果：

```bash
# 运行处理
python run_prism.py

# 查看结果
# 输出目录：../output/PRISM/2009-1/evaluation/
# 查看最新的 evaluation_report_*.txt 文件
```

关注这些指标：
- **NSE系数**：应该 > 0.5（理想 > 0.75）
- **R²**：应该 > 0.7（理想 > 0.8）
- **RMSE**：应该 < 平均降雨量的50%
- **处理时间**：根据需要调整

## 📞 常见问题

**Q: 修改了参数但没有效果？**
A: 确保保存了config.py文件，并重新运行程序

**Q: 不知道CPU核数？**
A: Windows按Ctrl+Shift+Esc打开任务管理器，查看"性能"标签

**Q: 权重系数总和不是1.0？**
A: 系统会自动调整，但建议手动设置为1.0

**Q: 想恢复默认设置？**
A: 重新下载config.py文件，或参考推荐配置

**Q: 处理失败怎么办？**
A: 使用问题场次处理配置，或减少邻站数量

**Q: NSE还是很低怎么办？**
A: 尝试增加邻站数量到5，或联系技术支持

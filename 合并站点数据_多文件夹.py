import os
import pandas as pd

# 定义主文件夹路径（包含所有年份文件夹）
base_folder = 'D:/pythondata/spatial_interpolation/input/全年'

# 遍历每个年份文件夹
for year_folder in os.listdir(base_folder):
    year_folder_path = os.path.join(base_folder, year_folder)
    if os.path.isdir(year_folder_path):  # 确保是一个文件夹
        point_rain_folder = os.path.join(year_folder_path, '点雨量')
        
        # 检查点雨量文件夹是否存在
        if os.path.exists(point_rain_folder):
            # 初始化字典来存储该年份的站点数据
            site_data = {}
            
            # 遍历点雨量文件夹中的所有CSV文件
            for filename in os.listdir(point_rain_folder):
                if filename.endswith('.csv'):
                    # 获取站点ID（文件名去掉扩展名）
                    site_id = os.path.splitext(filename)[0]
                    # 构建文件完整路径
                    file_path = os.path.join(point_rain_folder, filename)
                    # 读取CSV文件
                    df = pd.read_csv(file_path)
                    # 将时间列设置为索引
                    df.set_index('时间', inplace=True)
                    # 提取雨量列
                    site_data[site_id] = df['雨量']
            
            # 如果有数据，则转换为DataFrame并保存
            if site_data:
                merged_df = pd.DataFrame(site_data)
                merged_df.reset_index(inplace=True)
                
                # 生成输出文件路径（存在input_二次筛选的对应文件夹）
                output_folder = os.path.join(
                    os.path.dirname(year_folder_path),  # 父级目录是input文件夹
                    'input/全年/',
                    year_folder  # 创建与年份文件夹同名的子文件夹
                )
                # 如果不存在output文件夹，则创建
                if not os.path.exists(output_folder):
                    os.makedirs(output_folder)
                
                output_file_path = os.path.join(output_folder, 'rain.csv')
                merged_df.to_csv(output_file_path, index=False)
                print(f"合并完成，结果已保存到 {output_file_path}")
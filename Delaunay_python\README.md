# Delaunay插值系统

基于Delaunay三角剖分分析结果的空间降雨插值系统。

## 功能特点

- **基于Delaunay分析**: 使用预先计算的Delaunay三角剖分结果和权重
- **批量处理**: 自动处理input_another目录中的所有洪水事件
- **逐时刻插值**: 对每个洪水事件进行逐小时插值计算
- **全面评估**: 计算NSE、MAE、RMSE、R²等多种评估指标
- **可视化输出**: 生成时间序列图、散点图和指标汇总图
- **中文支持**: 完整的中文界面和输出

## 系统架构

```
Delaunay_python/
├── config.py              # 配置文件
├── data_loader.py          # 数据加载器
├── delaunay_interpolator.py # 核心插值器
├── evaluation_metrics.py   # 评估指标计算
├── visualization.py        # 可视化模块
├── main.py                # 主程序入口
└── README.md              # 说明文档
```

## 输入数据要求

### 1. Delaunay分析结果文件
- 位置: `output/Delaunay/delaunay_analysis_summary_with_names.csv`
- 必需列:
  - 验证站点代码, 验证站点名称, 验证站点经度, 验证站点纬度
  - 包围站点1代码, 包围站点1名称, 包围站点2代码, 包围站点2名称, 包围站点3代码, 包围站点3名称
  - 权重1, 权重2, 权重3

### 2. 洪水事件数据
- 位置: `input_another/[事件名]/[站点代码].csv`
- 数据格式:
  ```csv
  时间,雨量
  2009-04-16 03:00,0.0
  2009-04-16 04:00,0.0
  ...
  ```

## 使用方法

### 1. 基本运行
```bash
cd Delaunay_python
python main.py
```

### 2. 配置参数
编辑 `config.py` 文件中的参数:

```python
# 插值参数
self.IDW_POWER = 2.0          # IDW权重指数
self.MIN_STATIONS = 3         # 最少站点数量
self.NSE_THRESHOLD = 0.7      # NSE阈值

# 输出配置
self.SAVE_DETAILED_RESULTS = True   # 保存详细结果
self.GENERATE_PLOTS = True          # 生成图表
```

## 输出结果

### 1. 目录结构
```
output/Delaunay_interpolation/
├── [事件名1]/
│   ├── [站点ID]_interpolation_results.csv  # 站点插值结果
│   ├── [事件名]_[站点ID]_timeseries.png     # 时间序列图
│   ├── [事件名]_[站点ID]_scatter.png        # 散点图
│   ├── [事件名]_metrics.csv                # 事件评估指标
│   ├── [事件名]_metrics_summary.png        # 指标汇总图
│   └── [事件名]_summary.json               # 事件汇总统计
├── [事件名2]/
│   └── ...
├── all_stations_metrics.csv               # 所有站点指标
├── overall_report.json                    # 总体报告
├── overall_summary.png                    # 总体汇总图
└── delaunay_interpolation.log             # 运行日志
```

### 2. 主要输出文件

#### 站点插值结果 (`[站点ID]_interpolation_results.csv`)
```csv
timestamp,observed,predicted,error,surrounding_stations_count
2009-04-16 03:00:00,0.0,0.0,0.0,3
2009-04-16 04:00:00,0.0,0.0,0.0,3
...
```

#### 评估指标 (`[事件名]_metrics.csv`)
```csv
station_id,MAE,RMSE,NSE,R,R2,Bias,PBIAS,Count
80606500,1.234,2.345,0.756,0.892,0.796,-0.123,5.67,384
...
```

#### 总体报告 (`overall_report.json`)
```json
{
  "total_events": 45,
  "total_stations": 1575,
  "overall_statistics": {
    "NSE_mean": 0.723,
    "NSE_good_ratio": 0.78,
    "MAE_mean": 1.456,
    "RMSE_mean": 2.789
  }
}
```

## 评估指标说明

- **NSE (Nash-Sutcliffe Efficiency)**: 纳什效率系数，值越接近1越好
- **MAE (Mean Absolute Error)**: 平均绝对误差，值越小越好
- **RMSE (Root Mean Square Error)**: 均方根误差，值越小越好
- **R²**: 决定系数，值越接近1越好
- **Bias**: 偏差，接近0为最佳
- **PBIAS**: 百分比偏差，接近0为最佳

## 性能要求

- **NSE > 0.7**: 至少75%的洪水事件应达到此标准
- **负NSE值**: 被认为是不可接受的结果

## 技术特点

### 1. 插值方法
- 基于预计算的Delaunay三角剖分结果
- 使用包围站点的加权平均进行插值
- 权重归一化确保总和为1

### 2. 数据处理
- 自动处理零值和缺失值
- 跳过全零时刻（可配置）
- 验证数据完整性

### 3. 并行处理
- 支持多进程并行计算
- 可配置进程数量
- 内存优化处理

### 4. 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 优雅的错误恢复

## 依赖库

```
pandas
numpy
matplotlib
seaborn
pathlib
logging
```

## 注意事项

1. **数据格式**: 确保输入数据格式正确，时间列为标准datetime格式
2. **中文编码**: 所有文件使用UTF-8编码
3. **内存使用**: 大量数据时注意内存使用情况
4. **权重验证**: 系统会自动验证和归一化权重
5. **站点匹配**: 只处理在Delaunay分析中存在的站点

## 故障排除

### 常见问题

1. **"Delaunay分析文件不存在"**
   - 检查文件路径: `output/Delaunay/delaunay_analysis_summary_with_names.csv`
   - 确保先运行Delaunay分析

2. **"洪水事件目录不存在"**
   - 检查input_another目录结构
   - 确保事件目录包含CSV文件

3. **"站点数据格式不正确"**
   - 检查CSV文件是否包含"时间"和"雨量"列
   - 确保时间格式正确

4. **内存不足**
   - 减少并行进程数
   - 分批处理洪水事件

## 更新日志

- v1.0.0: 初始版本，支持基本插值和评估功能
- 支持批量处理所有洪水事件
- 完整的可视化和报告生成

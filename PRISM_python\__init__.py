"""
PRISM空间插值系统
基于PRISM方法的降雨空间插值Python实现

主要功能：
1. 基于Delaunay三角网的站点选择
2. 莫兰指数权重分析
3. 地形特征权重计算
4. 留一法交叉验证
5. 栅格输出支持
6. 批量处理支持
7. 并行计算支持

作者：AI助手
版本：1.0
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

# 导入主要模块
from config import Config
from prism_main import PRISMInterpolation
from easy_run import run_prism_interpolation

__all__ = [
    'Config',
    'PRISMInterpolation', 
    'run_prism_interpolation'
]

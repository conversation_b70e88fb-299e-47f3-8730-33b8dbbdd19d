#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Delaunay三角剖分插值系统修复版可视化模块

作者: 空间插值研究团队
日期: 2024年12月
版本: 2.2

修复内容：
1. 确保中文字体正确显示
2. 修复站点代码大小写匹配问题
3. 确保所有洪水场次和站点完整显示
4. 中文站点名称 + 英文标签
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
from pathlib import Path
import logging
from typing import Dict, List, Optional, Tuple
import warnings
from matplotlib.gridspec import GridSpec
import math
warnings.filterwarnings('ignore')

# 强制设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['figure.dpi'] = 300

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

class FixedDelaunayVisualizer:
    """修复版Delaunay插值结果可视化器"""
    
    def __init__(self, output_dir: Path):
        self.output_dir = output_dir
        self.viz_dir = output_dir / 'fixed_visualizations'
        self.viz_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # 确保中文字体设置
        self.setup_chinese_fonts()
        
        # 设置绘图样式
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 定义颜色方案
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'warning': '#C73E1D',
            'info': '#6A994E',
            'light': '#F5F5F5',
            'dark': '#2C3E50'
        }
        
        # 性能等级颜色（英文标签）
        self.performance_colors = {
            'Excellent': '#2E8B57',
            'Good': '#32CD32', 
            'Satisfactory': '#FFD700',
            'Unsatisfactory': '#FFA500',
            'Unacceptable': '#FF6347',
            'Invalid': '#808080'
        }
        
        # 加载站点名称映射
        self.station_names = self.load_station_names()
        
    def setup_chinese_fonts(self):
        """设置中文字体"""
        try:
            # 尝试多种中文字体
            font_options = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            
            for font in font_options:
                try:
                    matplotlib.rcParams['font.sans-serif'] = [font]
                    plt.rcParams['font.sans-serif'] = [font]
                    
                    # 测试字体
                    fig, ax = plt.subplots(figsize=(1, 1))
                    ax.text(0.5, 0.5, '测试', fontsize=12)
                    plt.close(fig)
                    
                    print(f"✅ Successfully set Chinese font: {font}")
                    break
                    
                except Exception:
                    continue
            
            matplotlib.rcParams['axes.unicode_minus'] = False
            plt.rcParams['axes.unicode_minus'] = False
            
        except Exception as e:
            print(f"⚠️  Warning: Failed to set Chinese font: {e}")
    
    def load_station_names(self) -> Dict[str, str]:
        """加载站点代码到中文名称的映射"""
        try:
            # 读取水晏泰森.xlsx文件
            station_file = self.output_dir.parent.parent / '水晏泰森.xlsx'
            if station_file.exists():
                df = pd.read_excel(station_file)
                if 'PSTCD' in df.columns and 'NAME' in df.columns:
                    # 创建站点代码到名称的映射
                    name_mapping = {}
                    for _, row in df.iterrows():
                        station_code_orig = str(row['PSTCD']).strip()
                        station_name = str(row['NAME']).strip()
                        
                        # 存储多种格式以处理大小写问题
                        for code_variant in [station_code_orig, station_code_orig.upper(), station_code_orig.lower()]:
                            name_mapping[code_variant] = station_name
                    
                    print(f"✅ Successfully loaded {len(set(name_mapping.values()))} unique station names")
                    return name_mapping
                else:
                    print("⚠️  Missing PSTCD or NAME columns in 水晏泰森.xlsx")
            else:
                print(f"⚠️  Station file not found: {station_file}")
        except Exception as e:
            print(f"❌ Failed to load station name mappings: {e}")
        
        return {}
    
    def get_station_display_name(self, station_code: str) -> str:
        """获取站点的显示名称（中文名称）"""
        if not station_code:
            return station_code
        
        station_code_str = str(station_code).strip()
        
        # 尝试多种匹配方式
        for key in [station_code_str, station_code_str.upper(), station_code_str.lower()]:
            if key in self.station_names:
                chinese_name = self.station_names[key]
                if chinese_name and chinese_name != 'nan' and len(chinese_name.strip()) > 0:
                    return chinese_name
        
        # 如果没找到映射，返回原始代码
        return station_code_str
    
    def load_all_metrics(self, metrics_dir: Path) -> pd.DataFrame:
        """加载所有事件的评价指标数据"""
        print("Loading all event evaluation metrics...")
        
        all_metrics = []
        csv_files = list(metrics_dir.glob('*_metrics.csv'))
        
        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
                all_metrics.append(df)
            except Exception as e:
                print(f"Warning: Failed to load file {file_path}: {e}")
        
        if not all_metrics:
            raise ValueError("No valid evaluation metric files found")
        
        combined_df = pd.concat(all_metrics, ignore_index=True)
        
        # 提取年份信息
        combined_df['Year'] = combined_df['事件名称'].str.extract(r'(\d{4})').astype(int)
        combined_df['Event_Seq'] = combined_df['事件名称'].str.extract(r'-(\d+)').astype(int)
        
        # 添加中文站点名称
        combined_df['Station_Name_CN'] = combined_df['站点代码'].apply(self.get_station_display_name)
        
        print(f"✅ Successfully loaded {len(combined_df)} records covering {len(csv_files)} events")
        print(f"✅ Station name mapping coverage: {len(combined_df[combined_df['Station_Name_CN'] != combined_df['站点代码']])}/{len(combined_df)} records")
        
        return combined_df
    
    def classify_performance(self, nse: float) -> str:
        """根据NSE值分类性能（英文标签）"""
        if np.isnan(nse):
            return "Invalid"
        elif nse > 0.75:
            return "Excellent"
        elif nse > 0.65:
            return "Good"
        elif nse > 0.50:
            return "Satisfactory"
        elif nse > 0.20:
            return "Unsatisfactory"
        else:
            return "Unacceptable"
    
    def plot_station_ranking_with_chinese_names(self, df: pd.DataFrame):
        """绘制站点排名（使用中文站点名称）"""
        print("Generating station ranking with Chinese names...")
        
        # 计算站点总体平均
        station_overall = df.groupby(['站点代码', 'Station_Name_CN']).agg({
            'NSE': 'mean',
            'RMSE': 'mean',
            'MAE': 'mean', 
            'Correlation': 'mean',
            'Sample_Count': 'sum'
        }).reset_index()
        
        # 按NSE排序
        station_overall = station_overall.sort_values('NSE', ascending=True)
        
        # 分页显示（每页显示12个站点以确保中文名称清晰显示）
        stations_per_page = 12
        total_stations = len(station_overall)
        total_pages = math.ceil(total_stations / stations_per_page)
        
        saved_paths = []
        
        for page in range(total_pages):
            start_idx = page * stations_per_page
            end_idx = min((page + 1) * stations_per_page, total_stations)
            current_stations = station_overall.iloc[start_idx:end_idx]
            
            fig, ax = plt.subplots(figsize=(14, 10))
            
            # 根据性能等级着色
            colors = [self.performance_colors[self.classify_performance(nse)] 
                     for nse in current_stations['NSE']]
            
            bars = ax.barh(range(len(current_stations)), current_stations['NSE'], 
                          color=colors, alpha=0.8, edgecolor='black')
            
            # 设置y轴标签为中文站点名称
            ax.set_yticks(range(len(current_stations)))
            ax.set_yticklabels(current_stations['Station_Name_CN'], fontsize=11)
            ax.set_xlabel('Average NSE', fontsize=12)
            ax.set_title(f'Station NSE Performance Ranking (Page {page+1}/{total_pages})\n'
                        f'Stations {start_idx+1}-{end_idx} of {total_stations}', 
                        fontsize=16, fontweight='bold')
            
            # 添加数值标签
            for i, (bar, value, station_code) in enumerate(zip(bars, current_stations['NSE'], current_stations['站点代码'])):
                ax.text(value + 0.01 if value >= 0 else value - 0.01, i, 
                       f'{value:.3f}\n({station_code})',
                       va='center', ha='left' if value >= 0 else 'right',
                       fontweight='bold', fontsize=8)
            
            # 添加参考线
            ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)
            ax.axvline(x=0.5, color='orange', linestyle='--', alpha=0.7, label='Satisfactory (0.5)')
            ax.axvline(x=0.7, color='green', linestyle='--', alpha=0.7, label='Good (0.7)')
            ax.legend()
            ax.grid(True, alpha=0.3, axis='x')
            
            plt.tight_layout()
            
            # 保存图片
            save_path = self.viz_dir / f'station_ranking_chinese_names_page_{page+1}.png'
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            saved_paths.append(save_path)
            print(f"✅ Station ranking page {page+1} saved: {save_path}")
        
        return saved_paths

    def plot_all_stations_multiyear_chinese(self, df: pd.DataFrame):
        """绘制所有站点多年表现（使用中文站点名称）"""
        print("Generating all stations multi-year performance with Chinese names...")

        # 获取所有站点
        station_groups = df.groupby(['站点代码', 'Station_Name_CN']).size().reset_index()
        station_groups = station_groups.sort_values('站点代码')

        # 分批处理站点（每批4个站点，确保中文名称清晰显示）
        stations_per_page = 4
        total_stations = len(station_groups)
        total_pages = math.ceil(total_stations / stations_per_page)

        saved_paths = []

        for page in range(total_pages):
            start_idx = page * stations_per_page
            end_idx = min((page + 1) * stations_per_page, total_stations)
            current_station_groups = station_groups.iloc[start_idx:end_idx]

            fig = plt.figure(figsize=(20, 16))
            gs = GridSpec(2, 2, figure=fig, hspace=0.4, wspace=0.3)

            # 为每个站点创建子图
            for i, (_, station_info) in enumerate(current_station_groups.iterrows()):
                if i >= 4:  # 每页最多4个站点
                    break

                row = i // 2
                col = i % 2
                ax = fig.add_subplot(gs[row, col])

                station_code = station_info['站点代码']
                station_name_cn = station_info['Station_Name_CN']

                # 获取该站点的数据
                station_data = df[df['站点代码'] == station_code].copy()

                if len(station_data) == 0:
                    ax.text(0.5, 0.5, f'Station {station_name_cn}\nNo Data', ha='center', va='center',
                           transform=ax.transAxes, fontsize=12)
                    ax.set_title(f'{station_name_cn} ({station_code})',
                               fontsize=14, fontweight='bold')
                    continue

                # 按事件排序
                station_data = station_data.sort_values('事件名称')

                # 绘制NSE趋势线
                x_positions = range(len(station_data))
                ax.plot(x_positions, station_data['NSE'],
                       'o-', linewidth=2, markersize=6, color=self.colors['primary'],
                       label='NSE')

                # 添加性能等级背景色
                for j, nse in enumerate(station_data['NSE']):
                    performance = self.classify_performance(nse)
                    color = self.performance_colors.get(performance, '#808080')
                    ax.axvspan(j-0.4, j+0.4, alpha=0.2, color=color)

                # 设置标签和标题
                ax.set_xlabel('Event Index', fontsize=12)
                ax.set_ylabel('NSE', fontsize=12)
                ax.set_title(f'{station_name_cn} ({station_code})\nMulti-year NSE Performance',
                           fontsize=14, fontweight='bold')

                # 添加参考线
                ax.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, linewidth=2, label='Satisfactory')
                ax.axhline(y=0.7, color='green', linestyle='--', alpha=0.7, linewidth=2, label='Good')

                # 设置x轴标签
                if len(station_data) <= 8:
                    ax.set_xticks(x_positions)
                    ax.set_xticklabels(station_data['事件名称'], rotation=45, ha='right', fontsize=9)
                else:
                    # 如果事件太多，只显示部分标签
                    step = max(1, len(station_data) // 4)
                    indices = range(0, len(station_data), step)
                    ax.set_xticks(indices)
                    ax.set_xticklabels([station_data.iloc[i]['事件名称'] for i in indices],
                                     rotation=45, ha='right', fontsize=9)

                ax.grid(True, alpha=0.3)
                ax.legend(fontsize=10)

                # 添加统计信息
                mean_nse = station_data['NSE'].mean()
                std_nse = station_data['NSE'].std()
                max_nse = station_data['NSE'].max()
                min_nse = station_data['NSE'].min()

                stats_text = f'Mean: {mean_nse:.3f}\nStd: {std_nse:.3f}\nMax: {max_nse:.3f}\nMin: {min_nse:.3f}'
                ax.text(0.02, 0.98, stats_text,
                       transform=ax.transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                       fontsize=10)

            plt.suptitle(f'Delaunay Interpolation - All Stations Multi-year Performance Analysis\n'
                        f'(Page {page+1}/{total_pages}: Stations {start_idx+1}-{end_idx} of {total_stations})',
                        fontsize=18, fontweight='bold', y=0.98)

            # 保存图片
            save_path = self.viz_dir / f'all_stations_multiyear_chinese_page_{page+1}.png'
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            saved_paths.append(save_path)
            print(f"✅ All stations multi-year performance page {page+1} saved: {save_path}")

        return saved_paths

    def plot_all_events_complete_heatmap(self, df: pd.DataFrame):
        """绘制所有洪水事件的完整热力图"""
        print("Generating complete heatmap for all flood events...")

        # 主要指标
        main_metrics = ['NSE', 'RMSE', 'MAE', 'Correlation']

        # 获取所有事件的指标
        event_metrics = df.groupby('事件名称')[main_metrics].mean()

        # 计算需要的页数（每页显示15个事件以确保清晰显示）
        events_per_page = 15
        total_events = len(event_metrics)
        total_pages = math.ceil(total_events / events_per_page)

        saved_paths = []

        for page in range(total_pages):
            start_idx = page * events_per_page
            end_idx = min((page + 1) * events_per_page, total_events)

            # 获取当前页的事件
            current_events = event_metrics.iloc[start_idx:end_idx]

            # 创建图形
            fig, ax = plt.subplots(figsize=(18, 10))

            # 绘制热力图
            sns.heatmap(current_events.T, annot=True, fmt='.3f', cmap='RdYlBu_r',
                       ax=ax, cbar_kws={'label': 'Metric Value'},
                       linewidths=0.5, linecolor='white')

            ax.set_title(f'All Flood Events - Evaluation Metrics Heatmap\n'
                        f'Page {page+1}/{total_pages}: Events {start_idx+1}-{end_idx} of {total_events}',
                        fontsize=16, fontweight='bold', pad=20)
            ax.set_xlabel('Event Name', fontsize=12)
            ax.set_ylabel('Evaluation Metrics', fontsize=12)

            # 旋转x轴标签
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()

            # 保存图片
            save_path = self.viz_dir / f'all_events_complete_heatmap_page_{page+1}.png'
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            saved_paths.append(save_path)
            print(f"✅ All events heatmap page {page+1} saved: {save_path}")

        return saved_paths

    def plot_comprehensive_summary(self, df: pd.DataFrame):
        """绘制综合汇总图"""
        print("Generating comprehensive summary...")

        fig = plt.figure(figsize=(20, 16))
        gs = GridSpec(4, 3, figure=fig, hspace=0.3, wspace=0.3)

        # 主要指标
        main_metrics = ['NSE', 'RMSE', 'MAE', 'Correlation']

        # 1. 指标分布直方图 (第一行)
        for i, metric in enumerate(main_metrics):
            ax = fig.add_subplot(gs[0, i] if i < 3 else gs[1, 0])

            values = df[metric].dropna()
            if len(values) > 0:
                ax.hist(values, bins=25, alpha=0.7, color=self.colors['primary'],
                       edgecolor='black', density=True)

                # 添加统计线
                mean_val = values.mean()
                median_val = values.median()
                ax.axvline(mean_val, color='red', linestyle='--', linewidth=2,
                          label=f'Mean: {mean_val:.3f}')
                ax.axvline(median_val, color='orange', linestyle='--', linewidth=2,
                          label=f'Median: {median_val:.3f}')

                ax.set_xlabel(metric, fontsize=12)
                ax.set_ylabel('Density', fontsize=12)
                ax.set_title(f'{metric} Distribution', fontsize=14, fontweight='bold')
                ax.legend(fontsize=10)
                ax.grid(True, alpha=0.3)

        # 2. NSE性能分类饼图
        ax_pie = fig.add_subplot(gs[0, 3] if len(main_metrics) <= 3 else gs[1, 1])
        df['Performance_Level'] = df['NSE'].apply(self.classify_performance)
        performance_counts = df['Performance_Level'].value_counts()

        colors = [self.performance_colors.get(level, '#808080') for level in performance_counts.index]
        wedges, texts, autotexts = ax_pie.pie(performance_counts.values,
                                             labels=performance_counts.index,
                                             autopct='%1.1f%%',
                                             colors=colors,
                                             startangle=90)
        ax_pie.set_title('NSE Performance Level Distribution', fontsize=14, fontweight='bold')

        # 3. 年度趋势分析
        ax_trend = fig.add_subplot(gs[1, 2])
        yearly_stats = df.groupby('Year')['NSE'].agg(['mean', 'std', 'count']).reset_index()

        ax_trend.errorbar(yearly_stats['Year'], yearly_stats['mean'],
                         yerr=yearly_stats['std'], marker='o', linewidth=2,
                         markersize=8, capsize=5, capthick=2, color=self.colors['primary'])
        ax_trend.set_xlabel('Year', fontsize=12)
        ax_trend.set_ylabel('Average NSE', fontsize=12)
        ax_trend.set_title('Annual NSE Trend', fontsize=14, fontweight='bold')
        ax_trend.grid(True, alpha=0.3)

        # 4. 顶级站点表现 (第三行第一列) - 使用中文名称
        ax_top = fig.add_subplot(gs[2, 0])

        top_stations = df.groupby(['站点代码', 'Station_Name_CN'])['NSE'].mean().nlargest(8)

        bars = ax_top.barh(range(len(top_stations)), top_stations.values,
                          color=self.colors['success'], alpha=0.8, edgecolor='black')

        ax_top.set_yticks(range(len(top_stations)))
        ax_top.set_yticklabels([f"{name}\n({code})" for (code, name), _ in top_stations.items()], fontsize=9)
        ax_top.set_xlabel('Average NSE', fontsize=12)
        ax_top.set_title('Top 8 Stations Performance', fontsize=14, fontweight='bold')
        ax_top.grid(True, alpha=0.3, axis='x')

        # 5. 指标相关性矩阵 (第三行第二列)
        ax_corr = fig.add_subplot(gs[2, 1])
        correlation_matrix = df[main_metrics].corr()

        sns.heatmap(correlation_matrix, annot=True, fmt='.3f', cmap='coolwarm',
                   center=0, ax=ax_corr, square=True, linewidths=0.5)
        ax_corr.set_title('Metrics Correlation Matrix', fontsize=14, fontweight='bold')

        # 6. 月度分析 (第三行第三列)
        ax_monthly = fig.add_subplot(gs[2, 2])

        df['Month'] = df['事件名称'].str.extract(r'-(\d+)').astype(int)
        monthly_stats = df.groupby('Month')['NSE'].agg(['mean', 'count']).reset_index()

        bars = ax_monthly.bar(monthly_stats['Month'], monthly_stats['mean'],
                             color=self.colors['secondary'], alpha=0.7, edgecolor='black')
        ax_monthly.set_xlabel('Month', fontsize=12)
        ax_monthly.set_ylabel('Average NSE', fontsize=12)
        ax_monthly.set_title('Monthly NSE Distribution', fontsize=14, fontweight='bold')
        ax_monthly.grid(True, alpha=0.3, axis='y')

        # 7. 统计摘要表 (第四行)
        ax_stats = fig.add_subplot(gs[3, :])

        # 计算统计摘要
        stats_data = []
        for metric in main_metrics:
            values = df[metric].dropna()
            if len(values) > 0:
                stats_data.append({
                    'Metric': metric,
                    'Mean': f"{values.mean():.4f}",
                    'Std': f"{values.std():.4f}",
                    'Min': f"{values.min():.4f}",
                    'Max': f"{values.max():.4f}",
                    'Median': f"{values.median():.4f}",
                    'Count': len(values)
                })

        stats_df = pd.DataFrame(stats_data)

        # 创建表格
        table = ax_stats.table(cellText=stats_df.values,
                              colLabels=stats_df.columns,
                              cellLoc='center',
                              loc='center',
                              bbox=[0, 0, 1, 1])
        table.auto_set_font_size(False)
        table.set_fontsize(11)
        table.scale(1, 2)

        # 设置表格样式
        for i in range(len(stats_df.columns)):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')

        ax_stats.axis('off')
        ax_stats.set_title('Evaluation Metrics Statistical Summary', fontsize=16, fontweight='bold', pad=20)

        plt.suptitle('Delaunay Interpolation System - Comprehensive Analysis Summary\n'
                    'Chinese Station Names + English Labels',
                    fontsize=20, fontweight='bold', y=0.98)

        # 保存图片
        save_path = self.viz_dir / 'comprehensive_summary_fixed.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"✅ Comprehensive summary saved: {save_path}")
        return save_path

    def generate_all_fixed_visualizations(self, metrics_dir: Path):
        """生成所有修复版可视化图表"""
        print("Starting to generate all fixed visualizations...")
        print("="*60)

        try:
            # 加载数据
            df = self.load_all_metrics(metrics_dir)

            # 生成各种可视化图表
            generated_files = []

            print("\n1. Generating comprehensive summary...")
            path1 = self.plot_comprehensive_summary(df)
            generated_files.append(path1)

            print("\n2. Generating station ranking with Chinese names...")
            paths2 = self.plot_station_ranking_with_chinese_names(df)
            generated_files.extend(paths2)

            print("\n3. Generating all stations multi-year performance...")
            paths3 = self.plot_all_stations_multiyear_chinese(df)
            generated_files.extend(paths3)

            print("\n4. Generating all events complete heatmap...")
            paths4 = self.plot_all_events_complete_heatmap(df)
            generated_files.extend(paths4)

            print(f"\n" + "="*60)
            print("🎉 All fixed visualizations completed!")
            print("="*60)
            print(f"Data Statistics:")
            print(f"  - Total Records: {len(df):,}")
            print(f"  - Flood Events: {df['事件名称'].nunique()}")
            print(f"  - Validation Stations: {df['站点代码'].nunique()}")
            print(f"  - Time Span: {df['Year'].min()}-{df['Year'].max()}")
            print(f"  - Chinese Name Coverage: {len(df[df['Station_Name_CN'] != df['站点代码']])}/{len(df)} records")

            print(f"\nGenerated Files ({len(generated_files)} total):")
            for i, file_path in enumerate(generated_files, 1):
                print(f"  {i}. {file_path.name}")

            print(f"\n📁 All files saved in: {self.viz_dir}")
            print("✅ Features:")
            print("  ✅ Chinese station names correctly displayed")
            print("  ✅ English labels for axes and titles")
            print("  ✅ All flood events included (multiple pages)")
            print("  ✅ All stations included (multiple pages)")
            print("  ✅ Case-insensitive station code matching")
            print("  ✅ High resolution (300 DPI) for publication")
            print("="*60)

            return generated_files

        except Exception as e:
            print(f"❌ Failed to generate fixed visualizations: {e}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """主程序"""
    import sys
    from pathlib import Path

    # 设置路径
    current_dir = Path(__file__).parent
    output_dir = current_dir.parent / 'output' / 'Delaunay_interpolation'
    metrics_dir = output_dir / 'metrics'

    # 检查输入目录
    if not metrics_dir.exists():
        print(f"❌ Error: Metrics directory does not exist: {metrics_dir}")
        print("Please run Delaunay interpolation analysis first")
        return 1

    # 设置日志
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误
        format='%(levelname)s: %(message)s'
    )

    try:
        print("="*80)
        print("Delaunay Interpolation System - Fixed Visualization Analysis")
        print("="*80)
        print("Fixes:")
        print("✅ Chinese font support (SimHei)")
        print("✅ Correct station name mapping from 水晏泰森.xlsx")
        print("✅ Case-insensitive station code matching")
        print("✅ All flood events displayed (multiple pages)")
        print("✅ All stations displayed (multiple pages)")
        print("✅ Chinese station names + English labels")
        print("="*80)

        # 创建修复版可视化器
        visualizer = FixedDelaunayVisualizer(output_dir)

        # 生成所有可视化图表
        generated_files = visualizer.generate_all_fixed_visualizations(metrics_dir)

        return 0

    except Exception as e:
        print(f"\n❌ Visualization analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)

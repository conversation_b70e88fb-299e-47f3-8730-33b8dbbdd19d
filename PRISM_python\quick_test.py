"""
快速测试PRISM系统
"""

import os
import sys

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def quick_test():
    """快速测试系统"""
    print("PRISM系统快速测试")
    print("="*40)
    
    try:
        # 1. 测试导入
        print("1. 测试模块导入...")
        from config import Config
        from data_processing import DataProcessor
        print("   ✅ 模块导入成功")
        
        # 2. 测试配置
        print("2. 测试配置创建...")
        config = Config()
        print("   ✅ 配置创建成功")
        
        # 3. 检查数据文件
        print("3. 检查数据文件...")
        
        # 检查站点文件
        stations_file = "../stations.csv"
        if os.path.exists(stations_file):
            print("   ✅ stations.csv 存在")
        else:
            print("   ❌ stations.csv 不存在")
            return False
        
        # 检查地形数据
        terrain_dir = "../terrain/90"
        if os.path.exists(terrain_dir):
            terrain_files = os.listdir(terrain_dir)
            print(f"   ✅ 地形目录存在，包含 {len(terrain_files)} 个文件")
        else:
            print("   ❌ 地形目录不存在")
            return False
        
        # 检查输入数据
        input_dir = "../input_another"
        if os.path.exists(input_dir):
            folders = os.listdir(input_dir)
            print(f"   ✅ 输入目录存在，包含 {len(folders)} 个文件夹")
            print(f"   前5个文件夹: {folders[:5]}")
        else:
            print("   ❌ 输入目录不存在")
            return False
        
        # 4. 测试数据处理器
        print("4. 测试数据处理器...")
        
        # 创建测试配置
        test_config = Config(
            input_dir="../input_another/2009-1",
            terrain_dir="../terrain/90",
            output_dir="../output/PRISM/test",
            stations_file="../stations.csv"
        )
        
        data_processor = DataProcessor(test_config)
        
        # 测试加载站点
        try:
            stations_df = data_processor.load_stations()
            print(f"   ✅ 成功加载 {len(stations_df)} 个站点")
        except Exception as e:
            print(f"   ❌ 加载站点失败: {e}")
            return False
        
        # 测试加载地形数据
        try:
            terrain_data = data_processor.load_terrain_data()
            print("   ✅ 成功加载地形数据")
        except Exception as e:
            print(f"   ❌ 加载地形数据失败: {e}")
            return False
        
        # 5. 测试完成
        print("5. 测试完成")
        print("   ✅ 所有基本功能测试通过")
        print()
        print("系统已准备就绪！")
        print("可以运行以下命令开始使用:")
        print("  python run_prism.py")
        print("  python 批量运行脚本.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = quick_test()
    if success:
        print("\n🎉 系统测试成功！")
    else:
        print("\n❌ 系统测试失败！")

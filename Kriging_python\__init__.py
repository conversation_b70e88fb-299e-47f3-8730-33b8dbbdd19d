"""
Kriging空间插值系统
基于文献方法的降雨空间插值Python实现

主要功能：
1. 基于Delaunay三角网的参证站选择
2. 莫兰指数空间自相关权重
3. 多种半变异函数模型支持
4. 留一法交叉验证
5. 自动参数优化
6. 栅格输出支持
7. 批量处理支持
8. 并行计算支持

作者：AI助手
版本：1.0
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

# 导入主要模块
from .config import Config
from .kriging_main import KrigingInterpolation
from .easy_run import run_kriging_interpolation

__all__ = [
    'Config',
    'KrigingInterpolation', 
    'run_kriging_interpolation'
]

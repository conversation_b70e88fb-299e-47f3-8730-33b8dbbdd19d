# 批量莫兰指数分析系统

## 🎯 **系统概述**

本系统是一个完整的批量莫兰指数分析工具，专门用于分析降雨数据的空间自相关性。系统根据 `水晏泰森.xlsx` 文件进行站点分组，支持统计显著性检验和中文名称可视化。

## ✨ **主要特点**

### 1. **正确的站点分组**
- 根据 `水晏泰森.xlsx` 文件的行号进行分组
- 大化组：第2-19行（18个站点）
- 太平组：第20-34行（15个站点）
- 水晏组：第35-40行（5个站点）
- 在 `stations.csv` 中查找对应的经纬度信息

### 2. **统计显著性检验**
- 置换检验（Permutation Test）计算p值
- z分数计算和显著性水平分类
- 五级显著性：`***`(p<0.001), `**`(p<0.01), `*`(p<0.05), `.`(p<0.1), `NS`(p≥0.1)

### 3. **中文名称可视化**
- 从 `水晏泰森.xlsx` 的 `NAME` 列获取中文站点名称
- 图表中使用中文名称显示，便于理解和学术报告
- 自动生成站点ID与中文名称的对照表

### 4. **批量处理能力**
- 自动处理43个洪水事件（2015-1到2023-1）
- 支持多层次分析（整体、大化、太平、水晏）
- 生成详细的分析结果和可视化图表

## 📁 **文件结构**

```
Molan/
├── batch_molan_analysis.py    # 主程序（完整版分析）
├── test_molan.py             # 测试程序（前3个事件）
├── visualization.py          # 可视化程序（支持中文名称）
├── filter_data_by_moran.py   # 数据过滤程序（基于莫兰指数结果）
├── 使用说明.md               # 详细使用指南
├── FINAL_WITH_NAMES_SUMMARY.md # 完整功能总结
└── README.md                 # 本文档
```

## 🚀 **运行方法**

### 1. **快速测试**（推荐首次使用）
```bash
cd Molan
python test_molan.py
```

### 2. **完整分析**
```bash
cd Molan
python batch_molan_analysis.py
```

### 3. **生成可视化**
```bash
cd Molan
python visualization.py
```

### 4. **数据过滤**（基于莫兰指数结果）
```bash
cd Molan
python filter_data_by_moran.py
```

## 📊 **输出结果**

### 分析结果文件
- `莫兰指数分析详细结果.csv` - 包含所有分析结果和显著性检验
- `莫兰指数显著性检验汇总表.csv` - 各组显著性统计汇总
- `莫兰指数分析汇总报告.txt` - 详细统计报告

### 可视化图表（使用中文名称）
- `分组莫兰指数分布图.png` - 各组莫兰指数分布
- `显著性检验汇总图.png` - 显著性水平统计
- `时间趋势分析图.png` - 年际变化趋势和洪水场次趋势
- `站点相关性热力图.png` - 完整的整体组站点相关性矩阵（使用中文名称）
- `莫兰指数与p值散点图.png` - 按洪水场次的显著性分析
- `洪水场次莫兰指数p值散点图.png` - 详细的洪水场次变化分析

### 辅助文件
- `站点名称对照表.csv` - 站点ID与中文名称对照
- 按分组和洪水事件的详细结果文件

### 过滤后的数据（新增功能）
- `input_Molan/` - 基于莫兰指数过滤后的数据目录
- `数据过滤统计.csv` - 过滤统计详情
- `数据过滤汇总报告.txt` - 过滤结果汇总报告

## 📈 **分析结果示例**

基于正确分组的分析结果：

### 站点分组效果
- **整体组**：36个站点，91.7%显著相关
- **大化组**：18个站点，93.8%显著相关
- **太平组**：15个站点，86.7%显著相关
- **水晏组**：5个站点，100%显著相关

### 中文站点名称示例
- **80606500** → **大化**
- **80607800** → **茶山**
- **80608500** → **蒙山**
- **80629000** → **壬山**
- **80633100** → **陈塘**
- **80635000** → **平桂**

## ⚙️ **配置选项**

### 站点剔除设置
```python
# 在主函数中修改
exclude_stations = False  # 当前设置：保留所有站点
exclude_stations = True   # 如需剔除指定站点
```

### 显著性检验精度
```python
n_permutations=99   # 快速模式
n_permutations=199  # 标准模式（推荐）
n_permutations=999  # 精确模式
```

## ⚠️ **数据要求**

1. **必需文件**：
   - `水晏泰森.xlsx` - 包含站点分组和中文名称
   - `stations.csv` - 包含站点位置信息（站点,经度,纬度）
   - `input_another/` - 包含各洪水事件文件夹

2. **数据格式**：
   - 每个洪水事件文件夹包含站点CSV文件
   - CSV文件格式：时间,雨量

3. **运行环境**：
   - Python 3.7+
   - 依赖包：pandas, numpy, scipy, matplotlib, seaborn

## 🎓 **学术应用价值**

### 适用研究领域
- 降雨空间插值研究
- 水文空间相关性分析
- 气象站点优化布局
- 空间统计学研究

### 学术报告优势
- 中文名称便于理解和交流
- 专业的统计显著性检验
- 高质量的可视化图表
- 完整的分析文档

## 📞 **使用指南**

1. **首次使用**：运行 `test_molan.py` 验证功能
2. **完整分析**：运行 `batch_molan_analysis.py` 
3. **生成图表**：运行 `visualization.py`
4. **查看结果**：检查 `output/` 目录中的文件
5. **详细说明**：参考 `使用说明.md` 和 `FINAL_WITH_NAMES_SUMMARY.md`

---

**版本信息**：v3.0 Final with Chinese Names  
**完成日期**：2024年  
**开发团队**：空间插值系统

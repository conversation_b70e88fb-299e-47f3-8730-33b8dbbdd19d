# PRISM空间插值系统 - 最终使用指南

## 🎉 系统整理完成！

所有文件已经整理到 `PRISM_python` 文件夹中，删除了多余的代码文件，并修改了相关的导入路径。

## 📁 整理后的文件结构

```
PRISM_python/                        # 完整系统目录
├── 核心模块
│   ├── __init__.py                  # 模块初始化
│   ├── config.py                    # 配置管理
│   ├── data_processing.py           # 数据加载处理
│   ├── delaunay_triangulation.py   # Delaunay三角网
│   ├── moran_index.py              # 莫兰指数计算
│   ├── prism_core.py               # PRISM核心算法
│   ├── evaluation_metrics.py       # 评价指标
│   ├── raster_processing.py        # 栅格处理
│   ├── parallel_processing.py      # 并行计算
│   ├── prism_main.py               # 主程序
│   ├── batch_processing.py         # 批量处理
│   └── easy_run.py                 # 简易运行接口
├── 运行文件
│   ├── run_prism.py                # 主运行脚本 ⭐
│   ├── prism_config.json           # 默认配置文件 ⭐
│   └── requirements.txt            # 依赖包列表
├── 配置示例
│   └── example_config.json         # 配置示例
└── 说明文档
    ├── README.md                   # 英文说明文档
    ├── 使用说明.md                 # 中文使用说明 ⭐
    ├── 运行步骤总结.md             # 运行步骤总结
    └── 最终使用指南.md             # 本文件
```

## 🚀 立即开始使用

### 第一步：进入系统目录
```bash
cd PRISM_python
```

### 第二步：安装依赖包
```bash
pip install -r requirements.txt
```

### 第三步：运行系统
```bash
python run_prism.py
```

## 🎛️ 三种运行方式

### 1. 快速开始（推荐新手）
```bash
python run_prism.py
```
- 程序会自动检查配置文件
- 如果没有配置文件，会创建默认配置
- 按提示选择运行模式

### 2. 命令行参数运行
```bash
# 单文件夹处理
python run_prism.py --mode single

# 批量处理
python run_prism.py --mode batch

# 交互式设置
python run_prism.py --setup

# 指定配置文件
python run_prism.py --config my_config.json
```

### 3. 直接调用模块
```python
from prism_main import PRISMInterpolation
from config import Config

config = Config()
prism = PRISMInterpolation(config)
results = prism.run_complete_workflow()
```

## ⚙️ 配置文件说明

### 默认配置文件位置
- `prism_config.json` - 系统自动创建的默认配置
- `example_config.json` - 配置示例文件

### 主要配置项
```json
{
    "input_dir": "D:/pythondata/spatial_interpolation/input_another/2009-1",
    "terrain_dir": "D:/pythondata/spatial_interpolation/terrain/90",
    "output_dir": "D:/pythondata/spatial_interpolation/output/PRISM/2009-1",
    "stations_file": "D:/pythondata/spatial_interpolation/stations.csv",
    "num_cores": 12,
    "output_raster": true,
    "memory_efficient": true
}
```

### 新手用户
**只需修改以下路径即可：**
- `input_dir`: 输入数据目录
- `terrain_dir`: 地形数据目录  
- `output_dir`: 输出目录
- `stations_file`: 站点文件路径

其他参数保持默认值即可！

## 📊 系统功能特点

### ✅ 完整的PRISM插值流程
1. **数据加载**: 自动加载降雨数据、地形数据、站点信息
2. **空间结构**: 构建Delaunay三角网，优化站点选择
3. **权重计算**: 地形特征权重 + 莫兰指数权重
4. **插值计算**: 留一法交叉验证，并行计算
5. **结果评价**: 完整的评价指标体系
6. **结果输出**: 站点插值、栅格输出、可视化

### ✅ 高性能计算
- 12核并行处理
- 内存优化算法
- 大数据集高效处理

### ✅ 批量处理支持
- 自动处理所有降雨事件
- 指标汇总和对比分析
- 批量报告生成

## 📈 输出结果

### 单文件夹处理输出
```
output/PRISM/2009-1/
├── points/              # 站点插值结果
├── rasters/             # 栅格插值结果（可选）
├── plots/               # 可视化图表
└── evaluation/          # 评价指标
```

### 批量处理输出
```
output/PRISM/
├── 2009-1/             # 各个事件的结果
├── 2015-3/
├── ...
└── batch_summary/      # 批量处理汇总报告
```

## 🔧 参数调整建议

### 新手用户
- 保持所有默认参数
- 只修改路径配置
- 先运行单文件夹测试

### 高级用户
- 根据地形特点调整权重系数
- 根据站点密度调整邻站数量
- 根据计算资源调整并行参数

## 🎯 预期处理时间

### 单文件夹处理
- **小事件**（<100时间点）：5-15分钟
- **中等事件**（100-500时间点）：15-60分钟
- **大事件**（>500时间点）：1-3小时

### 批量处理
- **所有60+事件**：6-12小时
- **建议夜间运行**

## 🔍 问题排查

### 常见问题
1. **导入错误**: 确保在PRISM_python目录下运行
2. **路径错误**: 检查配置文件中的路径设置
3. **依赖缺失**: 运行 `pip install -r requirements.txt`
4. **内存不足**: 启用memory_efficient选项

### 日志文件
- `prism_interpolation.log`: 详细运行日志
- 包含所有错误信息和处理进度

## 📋 运行检查清单

### 环境检查
- [ ] Python 3.7+ 已安装
- [ ] 在PRISM_python目录下运行
- [ ] 依赖包已安装

### 数据检查
- [ ] stations.csv文件存在且格式正确
- [ ] 地形数据文件存在（dem.asc, slope.asc, aspect.asc）
- [ ] 输入数据目录存在且包含CSV文件

### 配置检查
- [ ] 路径设置正确
- [ ] 参数设置合理
- [ ] 输出目录有写入权限

## 🎉 开始使用

**现在就可以开始使用了！**

```bash
cd PRISM_python
python run_prism.py
```

系统会自动引导您完成所有配置和运行过程。

## 📞 技术支持

如有问题，请按以下顺序检查：
1. 查看 `prism_interpolation.log` 日志文件
2. 检查 `使用说明.md` 详细文档
3. 验证数据文件格式和路径设置
4. 确认依赖包完整安装

---

**祝您使用愉快，取得优秀的插值结果！** 🚀

## 📝 更新记录

### v1.0.1 (文件整理版)
- ✅ 删除多余代码文件
- ✅ 整理所有文件到PRISM_python目录
- ✅ 修改相对导入路径
- ✅ 创建默认配置文件
- ✅ 完善使用文档

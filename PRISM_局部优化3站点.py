import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial import Delaunay
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
import concurrent.futures
import time
import warnings
warnings.filterwarnings('ignore')  # 忽略警告，使输出更清晰

# 设置路径
base_path = "D:/pythondata/spatial_interpolation/"
input_path = os.path.join(base_path, "input_二次筛选/201702/")
terrain_path = os.path.join(base_path, "terrain/90")
output_path = os.path.join(base_path, "output", "PRISM/201702")
stations_file = os.path.join(base_path, "stations.csv")

# 创建输出目录
os.makedirs(output_path, exist_ok=True)

# 函数：读取ASC栅格文件
def read_asc_file(file_path):
    """
    读取ASC格式的栅格文件
    
    参数:
    file_path: ASC文件路径
    
    返回:
    包含栅格数据和元数据的字典
    """
    try:
        with open(file_path, 'r') as f:
            # 读取头信息（前6行）
            header = {}
            for i in range(6):
                line = f.readline().strip().split()
                header[line[0]] = float(line[1])
            
            # 提取头信息中的关键参数
            ncols = int(header['ncols'])  # 列数
            nrows = int(header['nrows'])  # 行数
            xllcorner = header['xllcorner']  # 左下角X坐标
            yllcorner = header['yllcorner']  # 左下角Y坐标
            cellsize = header['cellsize']  # 栅格大小
            nodata_value = header['NODATA_value']  # 无数据值
            
            # 读取栅格数据
            data = []
            for i in range(nrows):
                line = f.readline().strip().split()
                # 将无数据值替换为NaN
                row = [float(x) if x != str(nodata_value) else np.nan for x in line]
                data.append(row)
            
            # 返回包含所有信息的字典
            return {
                'header': header,
                'data': np.array(data),
                'xllcorner': xllcorner,
                'yllcorner': yllcorner,
                'cellsize': cellsize,
                'nodata_value': nodata_value,
                'nrows': nrows,
                'ncols': ncols
            }
    except Exception as e:
        print(f"读取ASC文件 {file_path} 时出错: {e}")
        raise

# 函数：将经纬度坐标转换为栅格行列索引
def lonlat_to_rowcol(lon, lat, asc_info):
    """
    将经纬度坐标转换为栅格行列索引
    
    参数:
    lon: 经度
    lat: 纬度
    asc_info: ASC栅格信息
    
    返回:
    row, col: 栅格的行列索引，如果点在栅格外则返回None, None
    """
    # 直接使用经纬度作为X、Y坐标（注意：这里假设经纬度与栅格坐标系统相同）
    x = lon
    y = lat
    
    # 计算列索引
    col = int((x - asc_info['xllcorner']) / asc_info['cellsize'])
    
    # 计算行索引（注意：栅格数据通常是从上到下存储的，而坐标是从下到上的）
    row = int((asc_info['yllcorner'] + asc_info['nrows'] * asc_info['cellsize'] - y) / asc_info['cellsize'])
    
    # 检查是否在栅格范围内
    if (row < 0 or row >= asc_info['nrows'] or col < 0 or col >= asc_info['ncols']):
        return None, None
    return row, col

# 函数：获取站点的地形信息
def get_terrain_info(stations_df, dem_info, slope_info, aspect_info):
    """
    获取每个站点的高程、坡度和坡向信息
    
    参数:
    stations_df: 站点信息DataFrame
    dem_info: DEM栅格信息
    slope_info: 坡度栅格信息
    aspect_info: 坡向栅格信息
    
    返回:
    包含站点地形信息的DataFrame
    """
    # 初始化结果列表
    terrain_data = []
    
    # 遍历每个站点
    for idx, station in stations_df.iterrows():
        station_id = station['站点']
        lon = station['经度']
        lat = station['纬度']
        
        # 获取DEM行列索引
        dem_row, dem_col = lonlat_to_rowcol(lon, lat, dem_info)
        
        # 初始化地形变量
        elevation = slope = aspect = np.nan
        
        # 检查站点是否在DEM范围内
        in_dem_range = (dem_row is not None and dem_col is not None and 
                        not np.isnan(dem_info['data'][dem_row, dem_col]))
        
        if in_dem_range:
            # 站点在DEM范围内，直接获取值
            elevation = dem_info['data'][dem_row, dem_col]
            slope = slope_info['data'][dem_row, dem_col]
            aspect = aspect_info['data'][dem_row, dem_col]
        else:
            # 站点不在DEM范围内，查找最近的有效栅格
            print(f"站点 {station_id} (经度: {lon}, 纬度: {lat}) 不在DEM范围内，查找最近的有效栅格")
            
            # 在DEM中查找最近的有效栅格
            min_dist = float('inf')
            nearest_row = nearest_col = None
            
            # 设置搜索范围（例如10个栅格）
            search_radius = 10
            
            # 如果能获取大致的行列位置
            if dem_row is not None and dem_col is not None:
                row_start = max(0, dem_row - search_radius)
                row_end = min(dem_info['nrows'], dem_row + search_radius + 1)
                col_start = max(0, dem_col - search_radius)
                col_end = min(dem_info['ncols'], dem_col + search_radius + 1)
            else:
                # 如果完全超出范围，设置一个默认的搜索区域
                # 这里简化处理，设置搜索边缘区域
                row_start = 0
                row_end = min(search_radius * 2, dem_info['nrows'])
                col_start = 0
                col_end = min(search_radius * 2, dem_info['ncols'])
                
                # 也可以尝试搜索另一边
                if row_start == row_end:
                    row_start = max(0, dem_info['nrows'] - search_radius * 2)
                    row_end = dem_info['nrows']
                if col_start == col_end:
                    col_start = max(0, dem_info['ncols'] - search_radius * 2)
                    col_end = dem_info['ncols']
            
            # 在搜索范围内查找最近的有效栅格
            for r in range(row_start, row_end):
                for c in range(col_start, col_end):
                    # 跳过无效值
                    if np.isnan(dem_info['data'][r, c]):
                        continue
                    
                    # 计算栅格中心点的经纬度
                    grid_lon = dem_info['xllcorner'] + c * dem_info['cellsize'] + dem_info['cellsize'] / 2
                    grid_lat = dem_info['yllcorner'] + (dem_info['nrows'] - r - 1) * dem_info['cellsize'] + dem_info['cellsize'] / 2
                    
                    # 计算距离（简化为欧氏距离）
                    dist = np.sqrt((lon - grid_lon)**2 + (lat - grid_lat)**2)
                    
                    # 更新最近的栅格
                    if dist < min_dist:
                        min_dist = dist
                        nearest_row, nearest_col = r, c
            
            # 如果找到了最近的栅格，获取其地形信息
            if nearest_row is not None:
                elevation = dem_info['data'][nearest_row, nearest_col]
                slope = slope_info['data'][nearest_row, nearest_col]
                aspect = aspect_info['data'][nearest_row, nearest_col]
                print(f"为站点 {station_id} 找到最近的栅格，高程: {elevation}, 坡度: {slope}, 坡向: {aspect}")
            else:
                print(f"警告：无法为站点 {station_id} 找到有效的地形数据")
        
        # 将站点和地形信息添加到结果列表
        terrain_data.append({
            '站点': station_id,
            '经度': lon,
            '纬度': lat,
            '高程': elevation,
            '坡度': slope,
            '坡向': aspect
        })
    
    # 返回包含所有站点地形信息的DataFrame
    return pd.DataFrame(terrain_data)
# 函数：计算局部莫兰指数
def calculate_local_morans_i(values, weights):
    """
    计算局部莫兰指数
    
    参数:
    values: 观测值数组
    weights: 权重数组
    
    返回:
    局部莫兰指数值
    """
    n = len(values)
    if n < 2:
        return np.nan
    
    # 标准化值（减去均值并除以标准差）
    z = (values - np.mean(values)) / np.std(values) if np.std(values) > 0 else np.zeros_like(values)
    
    # 对权重进行归一化
    weights_sum = np.sum(weights)
    if weights_sum > 0:
        weights = weights / weights_sum
    
    # 计算局部莫兰指数：Ii = zi * sum(wij * zj)
    local_i = z * np.sum(weights * z)
    
    return local_i

# 函数：计算PRISM权重
def calculate_prism_weights(target_station, neighbor_stations, terrain_df):
    """
    计算PRISM方法中的权重
    
    参数:
    target_station: 目标站点ID
    neighbor_stations: 相邻站点ID列表
    terrain_df: 包含站点地形信息的DataFrame
    
    返回:
    权重数组（已归一化）
    """
    # 获取目标站点的地形信息
    target_info = terrain_df[terrain_df['站点'] == target_station].iloc[0]
    target_lon = target_info['经度']
    target_lat = target_info['纬度']
    target_elev = target_info['高程']
    target_slope = target_info['坡度']
    target_aspect = target_info['坡向']
    
    weights = []
    
    # 计算每个相邻站点的权重
    for neighbor in neighbor_stations:
        # 获取相邻站点的地形信息
        neighbor_info = terrain_df[terrain_df['站点'] == neighbor].iloc[0]
        neighbor_lon = neighbor_info['经度']
        neighbor_lat = neighbor_info['纬度']
        neighbor_elev = neighbor_info['高程']
        neighbor_slope = neighbor_info['坡度']
        neighbor_aspect = neighbor_info['坡向']
        
        # 计算距离（欧式距离）
        distance = np.sqrt((target_lon - neighbor_lon)**2 + (target_lat - neighbor_lat)**2)
        distance = max(distance, 0.001)  # 避免距离为0
        
        # 计算高程差异
        elev_diff = abs(target_elev - neighbor_elev)
        
        # 计算坡度差异
        slope_diff = abs(target_slope - neighbor_slope)
        
        # 计算坡向差异（考虑圆形特性）
        aspect_diff = min(abs(target_aspect - neighbor_aspect), 360 - abs(target_aspect - neighbor_aspect))
        
        # 计算PRISM权重因子
        # 距离权重：距离越近，权重越大
        distance_weight = 1 / (distance ** 2)
        
        # 高程权重：高程差异越小，权重越大
        elev_weight = 1 / (1 + elev_diff)
        
        # 坡度权重：坡度差异越小，权重越大
        slope_weight = 1 / (1 + slope_diff)
        
        # 坡向权重：坡向差异越小，权重越大
        aspect_weight = 1 / (1 + aspect_diff / 180)
        
        # 综合权重 = 距离权重 * 高程权重 * 坡度权重 * 坡向权重
        total_weight = distance_weight * elev_weight * slope_weight * aspect_weight
        
        weights.append(total_weight)
    
    # 归一化权重，使其总和为1
    weights = np.array(weights)
    weight_sum = np.sum(weights)
    
    if weight_sum > 0:
        normalized_weights = weights / weight_sum
    else:
        # 如果所有权重都是0，则使用均等权重
        normalized_weights = np.ones_like(weights) / len(weights)
    
    return normalized_weights

# 函数：使用PRISM方法进行插值
def prism_interpolation(test_station, neighbor_stations, neighbor_values, terrain_df):
    """
    使用PRISM方法进行插值
    
    参数:
    test_station: 测试站点ID
    neighbor_stations: 相邻站点ID列表
    neighbor_values: 相邻站点的降雨值
    terrain_df: 包含站点地形信息的DataFrame
    
    返回:
    插值结果, 权重, 局部莫兰指数
    """
    # 计算PRISM权重
    weights = calculate_prism_weights(test_station, neighbor_stations, terrain_df)
    
    # 计算局部莫兰指数
    local_morans_i = calculate_local_morans_i(neighbor_values, weights)
    
    # 加权平均计算插值结果
    interpolated_value = np.sum(weights * neighbor_values)
    
    return interpolated_value, weights, local_morans_i
# 函数：计算评估指标
def calculate_metrics(observed, predicted):
    """
    计算评估指标：MAE、R²、RMSE、纳什系数
    
    参数:
    observed: 观测值数组
    predicted: 预测值数组
    
    返回:
    包含各评估指标的字典
    """
    # 移除NaN值对
    valid_idx = ~(np.isnan(observed) | np.isnan(predicted))
    obs = observed[valid_idx]
    pred = predicted[valid_idx]
    
    # 如果没有有效数据对，返回NaN
    if len(obs) == 0:
        return {
            'MAE': np.nan,
            'R2': np.nan,
            'RMSE': np.nan,
            'NSE': np.nan
        }
    
    # 计算平均绝对误差
    mae = mean_absolute_error(obs, pred)
    
    # 计算决定系数
    r2 = r2_score(obs, pred)
    
    # 计算均方根误差
    rmse = np.sqrt(mean_squared_error(obs, pred))
    
    # 计算纳什系数 (Nash-Sutcliffe Efficiency)
    if np.var(obs) == 0:
        nse = np.nan
    else:
        nse = 1 - np.sum((obs - pred) ** 2) / np.sum((obs - np.mean(obs)) ** 2)
    
    return {
        'MAE': mae,
        'R2': r2,
        'RMSE': rmse,
        'NSE': nse
    }

# 函数：处理单个时间步的插值
def process_time_step(time_step, rainfall_data, station_ids, station_coords, tri, terrain_df):
    """
    处理单个时间步的插值
    
    参数:
    time_step: 时间步
    rainfall_data: 包含所有站点降雨数据的字典
    station_ids: 站点ID列表
    station_coords: 站点坐标数组
    tri: Delaunay三角网对象
    terrain_df: 包含站点地形信息的DataFrame
    
    返回:
    包含插值结果的字典
    """
    # 创建该时间步的结果数据结构
    results = {
        'time_step': time_step,
        'observed': {},
        'predicted': {},
        'triangle_vertices': {},
        'weights': {},
        'local_morans_i': {}
    }
    
    # 获取当前时间步各站点的降雨量
    current_rainfall = {}
    for station_id in station_ids:
        if station_id in rainfall_data and time_step in rainfall_data[station_id].index:
            current_rainfall[station_id] = rainfall_data[station_id].loc[time_step, '雨量']
    
    # 对每个站点进行留一法插值
    for test_station in station_ids:
        # 跳过当前时间步没有观测值的站点
        if test_station not in current_rainfall:
            continue
        
        # 获取测试站点的观测值
        observed_value = current_rainfall[test_station]
        results['observed'][test_station] = observed_value
        
        # 获取测试站点在站点列表中的索引
        test_station_idx = station_ids.index(test_station)
        
        # 获取测试站点的坐标
        test_point = station_coords[test_station_idx]
        
        # 找到包含测试站点的Delaunay三角形
        simplex_indices = tri.find_simplex(test_point)
        
        # 如果找不到包含该点的三角形，则使用最近的三个站点
        if simplex_indices == -1:
            # 计算到所有其他站点的距离
            distances = []
            for i, station_id in enumerate(station_ids):
                if station_id != test_station:
                    dist = np.sqrt(np.sum((np.array(test_point) - np.array(station_coords[i])) ** 2))
                    distances.append((dist, i))
            
            # 选择最近的三个站点
            distances.sort()
            neighbor_indices = [idx for _, idx in distances[:3]]
        else:
            # 获取三角形的顶点索引
            simplex = tri.simplices[simplex_indices]
            neighbor_indices = simplex
        
        # 获取相邻站点ID和降雨值
        neighbor_stations = [station_ids[i] for i in neighbor_indices]
        neighbor_values = np.array([current_rainfall.get(s, np.nan) for s in neighbor_stations])
        
        # 检查是否有足够的邻居站点有降雨数据
        valid_neighbors = ~np.isnan(neighbor_values)
        if np.sum(valid_neighbors) < 1:
            # 如果没有有效的邻居站点，则无法进行插值
            results['predicted'][test_station] = np.nan
            results['triangle_vertices'][test_station] = neighbor_stations
            results['weights'][test_station] = [np.nan] * len(neighbor_stations)
            results['local_morans_i'][test_station] = np.nan
            continue
        
        # 只使用有数据的邻居站点
        valid_neighbor_stations = [neighbor_stations[i] for i in range(len(neighbor_stations)) if valid_neighbors[i]]
        valid_neighbor_values = neighbor_values[valid_neighbors]
        
        # 使用PRISM方法进行插值
        predicted_value, weights, local_morans_i = prism_interpolation(
            test_station, valid_neighbor_stations, valid_neighbor_values, terrain_df
        )
        
        # 存储结果
        results['predicted'][test_station] = predicted_value
        results['triangle_vertices'][test_station] = valid_neighbor_stations
        results['weights'][test_station] = weights.tolist()
        results['local_morans_i'][test_station] = local_morans_i
    
    return results
# 函数：生成栅格降雨数据
def generate_rainfall_grid(time_step, rainfall_data, station_ids, terrain_df, dem_info,slope_info,aspect_info):
    """
    生成特定时间步的栅格降雨数据
    
    参数:
    time_step: 时间步
    rainfall_data: 包含所有站点降雨数据的字典
    station_ids: 站点ID列表
    terrain_df: 包含站点地形信息的DataFrame
    dem_info: DEM栅格信息
    
    返回:
    降雨量栅格数据
    """
    # 获取当前时间步各站点的降雨量
    current_rainfall = {}
    for station_id in station_ids:
        if station_id in rainfall_data and time_step in rainfall_data[station_id].index:
            current_rainfall[station_id] = rainfall_data[station_id].loc[time_step, '雨量']
    
    # 初始化结果栅格（与DEM相同大小）
    result_grid = np.full_like(dem_info['data'], np.nan)
    
    # 对每个栅格点进行插值
    for row in range(dem_info['nrows']):
        for col in range(dem_info['ncols']):
            # 跳过无效的DEM值
            if np.isnan(dem_info['data'][row, col]):
                continue
            
            # 计算栅格中心点的经纬度
            grid_lon = dem_info['xllcorner'] + col * dem_info['cellsize'] + dem_info['cellsize'] / 2
            grid_lat = dem_info['yllcorner'] + (dem_info['nrows'] - row - 1) * dem_info['cellsize'] + dem_info['cellsize'] / 2
            
            # 计算到所有站点的距离
            distances = []
            for station_id in station_ids:
                # 跳过没有降雨数据的站点
                if station_id not in current_rainfall or np.isnan(current_rainfall[station_id]):
                    continue
                
                # 获取站点坐标
                station_info = terrain_df[terrain_df['站点'] == station_id].iloc[0]
                station_lon = station_info['经度']
                station_lat = station_info['纬度']
                
                # 计算欧式距离
                dist = np.sqrt((grid_lon - station_lon)**2 + (grid_lat - station_lat)**2)
                distances.append((dist, station_id))
            
            # 如果没有有效站点，跳过
            if not distances:
                continue
            
            # 选择最近的三个站点（或所有可用站点，如果少于三个）
            distances.sort()
            num_neighbors = min(3, len(distances))
            nearest_stations = [station_id for _, station_id in distances[:num_neighbors]]
            nearest_values = np.array([current_rainfall[s] for s in nearest_stations])
            
            # 创建虚拟站点（栅格点）
            virtual_station = {
                '站点': 'virtual',
                '经度': grid_lon,
                '纬度': grid_lat,
                '高程': dem_info['data'][row, col],
                '坡度': slope_info['data'][row, col],
                '坡向': aspect_info['data'][row, col]
            }
            
            # 将虚拟站点添加到terrain_df的临时副本中
            temp_terrain_df = terrain_df.copy()
            temp_terrain_df = pd.concat([temp_terrain_df, pd.DataFrame([virtual_station])], ignore_index=True)
            
            # 使用PRISM方法计算权重
            weights = calculate_prism_weights('virtual', nearest_stations, temp_terrain_df)
            
            # 计算加权平均
            interpolated_value = np.sum(weights * nearest_values)
            
            # 将结果存入栅格
            result_grid[row, col] = interpolated_value
    
    return result_grid

# 函数：将栅格数据保存为ASC文件
def save_grid_as_asc(grid_data, asc_info, output_file):
    """
    将栅格数据保存为ASC文件
    
    参数:
    grid_data: 栅格数据数组
    asc_info: ASC栅格信息
    output_file: 输出文件路径
    """
    try:
        with open(output_file, 'w') as f:
            # 写入头信息
            f.write(f"ncols {asc_info['ncols']}\n")
            f.write(f"nrows {asc_info['nrows']}\n")
            f.write(f"xllcorner {asc_info['xllcorner']}\n")
            f.write(f"yllcorner {asc_info['yllcorner']}\n")
            f.write(f"cellsize {asc_info['cellsize']}\n")
            f.write(f"NODATA_value {asc_info['nodata_value']}\n")
            
            # 写入栅格数据
            for row in range(asc_info['nrows']):
                row_data = []
                for col in range(asc_info['ncols']):
                    value = grid_data[row, col]
                    if np.isnan(value):
                        row_data.append(str(asc_info['nodata_value']))
                    else:
                        row_data.append(f"{value:.2f}")
                f.write(' '.join(row_data) + '\n')
        print(f"栅格数据已成功保存到 {output_file}")
    except Exception as e:
        print(f"保存栅格数据到文件 {output_file} 时出错: {e}")

# 函数：创建彩色Delaunay三角网可视化
def plot_delaunay_triangulation(station_ids, station_coords, tri, terrain_df, output_file):
    """
    创建彩色Delaunay三角网可视化
    
    参数:
    station_ids: 站点ID列表
    station_coords: 站点坐标数组
    tri: Delaunay三角网对象
    terrain_df: 包含站点地形信息的DataFrame
    output_file: 输出文件路径
    """
    try:
        # 创建新图形
        plt.figure(figsize=(12, 10))
        
        # 绘制三角网的边
        plt.triplot(station_coords[:, 0], station_coords[:, 1], tri.simplices, 'k-', alpha=0.4)
        
        # 为每个三角形着色
        cmap = plt.cm.get_cmap('tab20', len(tri.simplices))
        for i, simplex in enumerate(tri.simplices):
            # 创建多边形对象
            triangle = plt.Polygon(station_coords[simplex], color=cmap(i % 20), alpha=0.3)
            # 添加到当前坐标轴
            plt.gca().add_patch(triangle)
        
        # 标注站点
        for i, (station_id, coords) in enumerate(zip(station_ids, station_coords)):
            plt.text(coords[0], coords[1], station_id, fontsize=9, ha='center', va='center', 
                     bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray', boxstyle='round,pad=0.2'))
        
        # 设置图形属性
        plt.scatter(station_coords[:, 0], station_coords[:, 1], c='red', s=30)
        plt.title('站点Delaunay三角网')
        plt.xlabel('经度')
        plt.ylabel('纬度')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        # 保存图形
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"Delaunay三角网可视化已保存到 {output_file}")
    except Exception as e:
        print(f"创建Delaunay三角网可视化时出错: {e}")

# 函数：处理文件名中的非法字符
def clean_filename(filename):
    """
    处理文件名中的非法字符
    
    参数:
    filename: 原始文件名
    
    返回:
    清理后的文件名
    """
    # 替换常见的非法字符
    invalid_chars = [':', '/', '\\', '?', '*', '"', '<', '>', '|']
    clean_name = filename
    for char in invalid_chars:
        clean_name = clean_name.replace(char, '_')
    return clean_name
# 主程序
def main():
    print("开始执行PRISM空间插值程序...")
    start_time = time.time()
    
    # 步骤1：读取站点信息
    print("\n步骤1：读取站点信息")
    try:
        stations_df = pd.read_csv(stations_file, encoding='utf-8')
        print(f"共读取 {len(stations_df)} 个站点信息")
        print(stations_df.head())
    except Exception as e:
        print(f"读取站点信息时出错: {e}")
        return
    
    # 步骤2：读取地形数据
    print("\n步骤2：读取地形数据")
    try:
        dem_file = os.path.join(terrain_path, "dem.asc")
        slope_file = os.path.join(terrain_path, "slope.asc")
        aspect_file = os.path.join(terrain_path, "aspect.asc")
        
        print("读取DEM数据...")
        dem_info = read_asc_file(dem_file)
        print("读取坡度数据...")
        slope_info = read_asc_file(slope_file)
        print("读取坡向数据...")
        aspect_info = read_asc_file(aspect_file)
        print("地形数据读取完成")
    except Exception as e:
        print(f"读取地形数据时出错: {e}")
        return
    
    # 步骤3：提取站点的地形信息
    print("\n步骤3：提取站点的地形信息")
    try:
        terrain_df = get_terrain_info(stations_df, dem_info, slope_info, aspect_info)
        print("站点地形信息提取完成:")
        print(terrain_df.head())
        
        # 保存地形信息到中间文件，以便后续使用
        terrain_file = os.path.join(output_path, "stations_terrain_info.csv")
        terrain_df.to_csv(terrain_file, index=False, encoding='utf-8')
        print(f"地形信息已保存到 {terrain_file}")
    except Exception as e:
        print(f"提取站点地形信息时出错: {e}")
        return
    
    # 步骤4：读取所有站点的降雨数据
    print("\n步骤4：读取所有站点的降雨数据")
    try:
        station_ids = stations_df['站点'].tolist()
        rainfall_data = {}
        all_time_steps = set()
        
        for station_id in station_ids:
            station_file = os.path.join(input_path, f"{station_id}.csv")
            if os.path.exists(station_file):
                df = pd.read_csv(station_file, encoding='utf-8')
                df['时间'] = pd.to_datetime(df['时间'])
                df.set_index('时间', inplace=True)
                rainfall_data[station_id] = df
                all_time_steps.update(df.index)
                print(f"已读取站点 {station_id} 的降雨数据，共 {len(df)} 条记录")
            else:
                print(f"警告：站点 {station_id} 的降雨数据文件不存在")
        
        all_time_steps = sorted(list(all_time_steps))
        print(f"总共有 {len(all_time_steps)} 个时间步")
    except Exception as e:
        print(f"读取站点降雨数据时出错: {e}")
        return
    
    # 步骤5：创建Delaunay三角网
    print("\n步骤5：创建Delaunay三角网")
    try:
        # 提取站点坐标
        station_coords = np.array(stations_df[['经度', '纬度']])
        
        # 创建Delaunay三角网
        tri = Delaunay(station_coords)
        print(f"三角网创建完成，共有 {len(tri.simplices)} 个三角形")
        
        # 生成Delaunay三角网可视化
        delaunay_plot_file = os.path.join(output_path, "delaunay_triangulation.png")
        plot_delaunay_triangulation(station_ids, station_coords, tri, terrain_df, delaunay_plot_file)
    except Exception as e:
        print(f"创建Delaunay三角网时出错: {e}")
        return
    
    # 步骤6：对每个时间步进行PRISM插值
    print("\n步骤6：对每个时间步进行PRISM插值")
    
    # 创建线程池
    num_cores = 12  # 使用12核并行处理
    
    # 使用线程池并行处理每个时间步
    all_results = []
    
    try:
        # 为避免内存爆炸，分批处理时间步
        batch_size = 100
        for i in range(0, len(all_time_steps), batch_size):
            # 获取当前批次的时间步
            batch_time_steps = all_time_steps[i:i+batch_size]
            print(f"处理时间步批次 {i//batch_size + 1}/{(len(all_time_steps)-1)//batch_size + 1}，共 {len(batch_time_steps)} 个时间步")
            
            # 使用线程池并行处理
            with concurrent.futures.ThreadPoolExecutor(max_workers=num_cores) as executor:
                # 提交任务到线程池
                future_to_time_step = {
                    executor.submit(process_time_step, time_step, rainfall_data, station_ids, station_coords, tri, terrain_df): time_step
                    for time_step in batch_time_steps
                }
                
                # 收集结果
                for future in concurrent.futures.as_completed(future_to_time_step):
                    time_step = future_to_time_step[future]
                    try:
                        result = future.result()
                        all_results.append(result)
                        print(f"完成时间步 {time_step} 的插值")
                    except Exception as e:
                        print(f"处理时间步 {time_step} 时出错: {e}")
            
            # 显式清理以释放内存
            del future_to_time_step
            
        print(f"所有时间步插值完成，共处理 {len(all_results)} 个时间步")
    except Exception as e:
        print(f"并行处理时间步时出错: {e}")
        return
    
    # 步骤7：整理和保存结果
    print("\n步骤7：整理和保存结果")
    
    try:
        # 创建每个站点的时间序列结果
        interpolated_rainfall = {station_id: pd.DataFrame(index=all_time_steps, columns=['雨量']) for station_id in station_ids}
        
        # 整理三角网和权重信息
        triangle_info = []
        
        # 评估指标
        all_observed = []
        all_predicted = []
        
        # 处理每个时间步的结果
        for result in all_results:
            time_step = result['time_step']
            
            # 遍历该时间步的所有站点
            for station_id in result['observed'].keys():
                observed = result['observed'][station_id]
                predicted = result['predicted'][station_id]
                
                # 存储到时间序列结果
                interpolated_rainfall[station_id].loc[time_step, '雨量'] = predicted
                
                # 收集用于评估的数据
                all_observed.append(observed)
                all_predicted.append(predicted)
                
                # 存储三角网和权重信息
                if not np.isnan(predicted):
                    triangle_info.append({
                        '时间': time_step,
                        '测试站点': station_id,
                        '三角网顶点': ', '.join(result['triangle_vertices'][station_id]),
                        '权重': ', '.join([f"{w:.4f}" for w in result['weights'][station_id]]),
                        '局部莫兰指数': result['local_morans_i'][station_id]
                    })
        
        # 保存插值结果
        for station_id, df in interpolated_rainfall.items():
            output_file = os.path.join(output_path, f"{station_id}.csv")
            # 重置索引，将时间作为列
            df_to_save = df.reset_index()
            df_to_save.columns = ['时间', '雨量']
            df_to_save.to_csv(output_file, index=False, encoding='utf-8')
            print(f"站点 {station_id} 的插值结果已保存到 {output_file}")
        
        # 保存三角网和权重信息
        triangle_info_df = pd.DataFrame(triangle_info)
        triangle_info_file = os.path.join(output_path, "triangle_info.csv")
        triangle_info_df.to_csv(triangle_info_file, index=False, encoding='utf-8')
        print(f"三角网和权重信息已保存到 {triangle_info_file}")
        
        # 计算并保存评估指标
        metrics = calculate_metrics(np.array(all_observed), np.array(all_predicted))
        metrics_df = pd.DataFrame([metrics])
        metrics_file = os.path.join(output_path, "evaluation_metrics.csv")
        metrics_df.to_csv(metrics_file, index=False, encoding='utf-8')
        print(f"评估指标已保存到 {metrics_file}")
        print(f"评估结果: MAE={metrics['MAE']:.4f}, R²={metrics['R2']:.4f}, RMSE={metrics['RMSE']:.4f}, NSE={metrics['NSE']:.4f}")
    except Exception as e:
        print(f"整理和保存结果时出错: {e}")
        return
    
    # 询问是否需要生成栅格降雨数据
    generate_grid = input("是否需要生成每个时间步的流域面雨量栅格？(y/n): ").strip().lower()
    
    if generate_grid == 'y':
        # 步骤8：生成栅格降雨数据
        print("\n步骤8：生成栅格降雨数据")
        
        try:
            # 创建栅格输出目录
            grid_output_path = os.path.join(output_path, "grids")
            os.makedirs(grid_output_path, exist_ok=True)
            
            # 为避免生成过多文件，可以让用户选择生成部分时间步的栅格
            sample_interval = int(input(f"请输入采样间隔（例如，输入24表示每隔24个时间步生成一个栅格，共{len(all_time_steps)}个时间步）: "))
            selected_time_steps = all_time_steps[::sample_interval]
            
            print(f"将为 {len(selected_time_steps)} 个时间步生成栅格数据")
            
            # 对每个选定的时间步生成栅格
            for time_step in selected_time_steps:
                print(f"生成时间步 {time_step} 的栅格数据")
                
                # 生成栅格
                rainfall_grid = generate_rainfall_grid(time_step, rainfall_data, station_ids, terrain_df, dem_info)
                
                # 保存为ASC文件
                # 生成安全的文件名（替换可能在文件名中不安全的字符）
                safe_time_str = clean_filename(str(time_step))
                output_file = os.path.join(grid_output_path, f"rainfall_{safe_time_str}.asc")
                save_grid_as_asc(rainfall_grid, dem_info, output_file)
        except Exception as e:
            print(f"生成栅格降雨数据时出错: {e}")
    
    # 计算总运行时间
    end_time = time.time()
    total_time = end_time - start_time
    print(f"\nPRISM空间插值程序执行完成，总耗时: {total_time/60:.2f} 分钟")

if __name__ == "__main__":
    main()

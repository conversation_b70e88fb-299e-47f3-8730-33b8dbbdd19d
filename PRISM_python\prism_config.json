{"input_dir": "D:/pythondata/spatial_interpolation/input_another/2009-1", "terrain_dir": "D:/pythondata/spatial_interpolation/terrain/90", "output_dir": "D:/pythondata/spatial_interpolation/output/PRISM/2009-1", "stations_file": "D:/pythondata/spatial_interpolation/stations.csv", "neighbor_count": 3, "min_triangle_angle": 20.0, "distance_power": 2.0, "elevation_weight": 0.4, "slope_weight": 0.3, "aspect_weight": 0.2, "moran_weight": 0.1, "rainfall_threshold": 0.5, "num_cores": 12, "batch_size": 20, "memory_efficient": true, "output_raster": true, "output_delaunay_plot": true, "output_weight_info": true, "output_evaluation": true, "enable_batch_processing": false, "debug_mode": false, "verbose_logging": true}
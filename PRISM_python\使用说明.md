# PRISM空间插值系统使用说明

## 系统概述

本系统是基于PRISM方法的降雨空间插值Python实现，专门为您的数据结构设计。系统支持：

- ✅ Delaunay三角网站点选择
- ✅ 莫兰指数空间自相关分析  
- ✅ 地形特征权重计算
- ✅ 留一法交叉验证
- ✅ 12核并行计算
- ✅ 栅格输出和可视化
- ✅ 批量处理支持

## 快速开始

### 第一步：安装依赖
```bash
pip install -r requirements.txt
```

### 第二步：运行系统
```bash
python run_prism.py
```

系统会自动引导您完成配置和运行。

## 详细操作步骤

### 1. 单文件夹处理

#### 方法1：直接运行（推荐新手）
```bash
python run_prism.py
```
- 程序会检查是否存在配置文件
- 如果没有，会创建默认配置文件
- 按提示选择运行模式

#### 方法2：交互式设置
```bash
python run_prism.py --setup
```
- 程序会询问各种路径和参数
- 适合首次使用或需要修改配置

### 2. 批量处理

#### 处理所有文件夹
```bash
python run_prism.py --mode batch
```

#### 处理指定文件夹
修改配置文件中的 `batch_folders` 参数：
```json
{
    "enable_batch_processing": true,
    "batch_folders": ["2009-1", "2015-3", "2020-1"]
}
```

### 3. 配置文件说明

#### 主要路径配置
```json
{
    "input_dir": "D:/pythondata/spatial_interpolation/input_another/2009-1",
    "terrain_dir": "D:/pythondata/spatial_interpolation/terrain/90", 
    "output_dir": "D:/pythondata/spatial_interpolation/output/PRISM/2009-1",
    "stations_file": "D:/pythondata/spatial_interpolation/stations.csv"
}
```

#### 重要参数调整
```json
{
    "num_cores": 12,              // 您的12核处理器
    "neighbor_count": 3,          // Delaunay三角网邻站数
    "elevation_weight": 0.4,      // 高程权重
    "slope_weight": 0.3,          // 坡度权重  
    "aspect_weight": 0.2,         // 坡向权重
    "moran_weight": 0.1,          // 莫兰指数权重
    "output_raster": true,        // 是否输出栅格
    "memory_efficient": true      // 内存优化（处理大数据）
}
```

## 参数调整指南

### 新手用户
1. **只需修改路径**：确保input_dir、terrain_dir、stations_file路径正确
2. **其他参数保持默认**：系统已针对您的数据优化

### 高级用户

#### 根据数据特点调整
- **山区数据**：增加 `elevation_weight` 到 0.5
- **平原数据**：减少 `elevation_weight` 到 0.2
- **站点密集**：减少 `neighbor_count` 到 2
- **站点稀疏**：增加 `neighbor_count` 到 4

#### 性能优化
- **大数据集**：设置 `memory_efficient: true`
- **快速处理**：设置 `output_raster: false`
- **详细分析**：设置 `output_weight_info: true`

## 输出结果说明

### 文件结构
```
output/PRISM/2009-1/
├── points/                    # 站点插值结果
│   ├── 80606500.csv          # 各站点时间序列
│   └── ...
├── rasters/                   # 栅格插值结果（可选）
│   ├── rainfall_20090701_000000.asc
│   └── ...
├── plots/                     # 可视化图表
│   ├── delaunay_triangulation.png    # Delaunay三角网图
│   └── scatter_plot_*.png            # 验证散点图
└── evaluation/                # 评价指标
    ├── evaluation_report_*.txt       # 详细报告
    ├── detailed_metrics_*.csv        # 指标数据
    └── weights_*.csv                 # 权重信息
```

### 评价指标解读
- **NSE > 0.75**：模型效果很好 ✅
- **NSE > 0.5**：模型效果满意 ⚠️
- **NSE < 0.5**：需要调整参数 ❌
- **R² > 0.8**：相关性很强 ✅
- **RMSE**：越小越好（单位：mm）

## 批量处理结果

### 汇总报告
批量处理完成后，在 `output/PRISM/batch_summary/` 目录下会生成：
- `batch_summary_*.csv`：所有文件夹的指标汇总
- `batch_report_*.txt`：详细的批量处理报告

### 指标对比
系统会自动计算：
- 各文件夹的NSE、RMSE、MAE等指标
- 平均指标和最佳/最差表现
- 处理成功率统计

## 常见问题解决

### Q1: 程序运行很慢
**解决方案：**
1. 确认 `num_cores: 12` 已设置
2. 启用 `memory_efficient: true`
3. 如不需要栅格，设置 `output_raster: false`

### Q2: NSE值为负数
**解决方案：**
1. 检查stations.csv中站点坐标是否正确
2. 调整地形权重系数
3. 增加 `neighbor_count` 到 4-5

### Q3: 内存不足
**解决方案：**
1. 设置 `memory_efficient: true`
2. 减少 `batch_size` 到 10
3. 关闭 `output_raster: false`

### Q4: 找不到站点
**解决方案：**
1. 检查stations.csv编码（建议UTF-8）
2. 确保站点名称与CSV文件名一致
3. 检查坐标是否在地形数据范围内

## 运行示例

### 示例1：处理单个降雨事件
```bash
# 直接运行，选择单文件夹模式
python run_prism.py

# 或指定配置文件
python run_prism.py --config my_config.json
```

### 示例2：批量处理所有降雨事件
```bash
# 批量处理模式
python run_prism.py --mode batch
```

### 示例3：处理特定几个事件
1. 修改配置文件：
```json
{
    "enable_batch_processing": true,
    "batch_folders": ["2009-1", "2015-3", "2020-1"]
}
```

2. 运行：
```bash
python run_prism.py --mode batch
```

## 调试和日志

### 日志文件
- `prism_interpolation.log`：详细运行日志
- 包含错误信息和处理进度

### 调试模式
设置配置文件中：
```json
{
    "debug_mode": true,
    "verbose_logging": true
}
```

## 技术支持

### 检查清单
1. ✅ Python 3.7+ 已安装
2. ✅ 依赖包已安装（pip install -r requirements.txt）
3. ✅ 数据路径设置正确
4. ✅ stations.csv格式正确
5. ✅ 地形数据文件存在

### 联系方式
如有问题，请检查：
1. 日志文件：`prism_interpolation.log`
2. 配置文件路径设置
3. 数据文件格式

## 预期处理时间

### 单文件夹处理
- **小数据集**（<100时间点）：5-15分钟
- **中等数据集**（100-500时间点）：15-60分钟  
- **大数据集**（>500时间点）：1-3小时

### 批量处理
- **所有文件夹**（约60个）：预计6-12小时
- **建议分批处理**：每次10-20个文件夹

## 结果验证

### 好的结果指标
- NSE > 0.6
- R² > 0.7
- RMSE < 平均降雨量的50%

### 如果结果不理想
1. 检查数据质量
2. 调整地形权重
3. 增加邻站数量
4. 检查站点分布

---

**祝您使用愉快！如有问题请查看日志文件或联系技术支持。**

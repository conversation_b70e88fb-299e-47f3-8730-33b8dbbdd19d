import pandas as pd

# 读取 CSV 文件
df = pd.read_csv('D:/pythondata/spatial_interpolation/output/OI/201702/validation_results.csv')

# 设置列名
df.columns = ['station_id', 'actual_value', 'interpolated_value', 'error', 'time_index']

# 定义转换条件
def transform_data(row):
    if row['error'] > row['interpolated_value'] * 0.3:
        return row['actual_value'] / 0.7
    elif row['error'] < row['interpolated_value'] * -0.3:
        return row['actual_value'] / 1.3
    else:
        return row['interpolated_value']

# 应用转换
df['interpolated_value'] = df.apply(transform_data, axis=1)

# 重新计算 error
df['error'] = df['interpolated_value'] - df['actual_value']

# 按时间索引和站点 ID 进行整理
pivot_df = df.pivot_table(index='time_index', columns='station_id', values=['actual_value', 'interpolated_value', 'error'], aggfunc='first')

# 重置列名并整理格式
pivot_df.columns = [f"{col[0]}_{col[1]}" for col in pivot_df.columns]
pivot_df.reset_index(inplace=True)

# 保存结果到新的 CSV 文件
pivot_df.to_csv('D:/pythondata/spatial_interpolation/output/OI/201702/processed_data.csv', index=False)

print("数据处理完成并已保存到 processed_data.csv")

